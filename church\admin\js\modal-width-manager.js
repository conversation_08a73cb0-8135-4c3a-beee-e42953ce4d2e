/**
 * Mo<PERSON> Width Manager for Admin Panel
 * Automatically optimizes modal widths based on content and provides utilities for manual control
 */

class ModalWidthManager {
    constructor() {
        this.init();
    }

    init() {
        // Listen for modal show events to auto-optimize
        document.addEventListener('show.bs.modal', (e) => this.handleModalShow(e));
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => this.optimizeExistingModals());
        
        console.log('Modal Width Manager initialized');
    }

    handleModalShow(event) {
        const modal = event.target;
        const modalDialog = modal.querySelector('.modal-dialog');

        if (modalDialog && !modalDialog.classList.contains('modal-width-optimized')) {
            this.optimizeModalWidth(modalDialog);
            this.optimizeModalHeight(modalDialog);
            modalDialog.classList.add('modal-width-optimized');
        }

        // Special handling for specific modals that need auto-height
        this.applySpecialModalFixes(modal, modalDialog);
    }

    applySpecialModalFixes(modal, modalDialog) {
        // Apply auto-height to birthday and member modals
        if (modal.id === 'sendBirthdayModal' || modal.id === 'membersModal') {
            if (!modalDialog.classList.contains('modal-auto-height')) {
                modalDialog.classList.add('modal-auto-height');
                console.log(`Applied auto-height to ${modal.id}`);
            }
        }

        // Special width handling for members modal - needs extra width for member cards
        if (modal.id === 'membersModal') {
            // Remove default modal-lg and apply wider class
            modalDialog.classList.remove('modal-lg');
            if (!modalDialog.classList.contains('modal-table')) {
                modalDialog.classList.add('modal-table'); // 1400px width for member cards
                console.log('Applied modal-table width to membersModal for better member card display');
            }
        }

        // Apply auto-height to any modal-lg that contains forms or tables
        if (modalDialog.classList.contains('modal-lg')) {
            const hasForm = modalDialog.querySelector('form');
            const hasTable = modalDialog.querySelector('table');

            if ((hasForm || hasTable) && !modalDialog.classList.contains('modal-auto-height')) {
                modalDialog.classList.add('modal-auto-height');
                console.log(`Applied auto-height to modal-lg with ${hasForm ? 'form' : 'table'}`);
            }
        }
    }

    optimizeExistingModals() {
        const modals = document.querySelectorAll('.modal .modal-dialog');
        modals.forEach(modalDialog => {
            if (!modalDialog.classList.contains('modal-width-optimized')) {
                this.optimizeModalWidth(modalDialog);
                modalDialog.classList.add('modal-width-optimized');
            }
        });
    }

    optimizeModalWidth(modalDialog) {
        const modal = modalDialog.closest('.modal');
        const modalContent = modalDialog.querySelector('.modal-content');
        const modalBody = modalDialog.querySelector('.modal-body');
        
        if (!modalContent || !modalBody) return;

        // Remove existing size classes except custom ones
        const sizeClasses = ['modal-lg', 'modal-xl', 'modal-sm'];
        const customClasses = ['modal-narrow', 'modal-medium', 'modal-wide', 'modal-full', 
                              'modal-form', 'modal-confirmation', 'modal-preview', 'modal-table', 'modal-editor'];
        
        // Check if modal already has a custom class (excluding Bootstrap size classes)
        const hasCustomClass = customClasses.some(cls => modalDialog.classList.contains(cls));

        if (hasCustomClass) {
            console.log('Modal already has custom width class, skipping width auto-optimization');
            // Still optimize height even if width is custom
            return;
        }

        // Auto-detect optimal width based on content
        const optimalClass = this.detectOptimalWidth(modalDialog, modalBody);
        
        if (optimalClass) {
            // Remove default Bootstrap size classes
            sizeClasses.forEach(cls => modalDialog.classList.remove(cls));
            
            // Add optimal class
            modalDialog.classList.add(optimalClass);
            
            console.log(`Applied optimal width class: ${optimalClass} to modal`);
        }
    }

    optimizeModalHeight(modalDialog) {
        const modalBody = modalDialog.querySelector('.modal-body');
        if (!modalBody) return;

        // Check if modal already has a height class
        const heightClasses = ['modal-compact', 'modal-standard', 'modal-tall', 'modal-full-height', 'modal-auto-height'];
        const hasHeightClass = heightClasses.some(cls => modalDialog.classList.contains(cls));

        if (hasHeightClass) {
            console.log('Modal already has height class, skipping auto-height optimization');
            return;
        }

        // Special handling for Bootstrap size classes - they should get height optimization
        const bootstrapSizeClasses = ['modal-sm', 'modal-lg', 'modal-xl'];
        const hasBootstrapSize = bootstrapSizeClasses.some(cls => modalDialog.classList.contains(cls));

        if (hasBootstrapSize) {
            console.log('Modal has Bootstrap size class, applying height optimization');
        }

        // Auto-detect optimal height based on content
        const optimalHeightClass = this.detectOptimalHeight(modalDialog, modalBody);

        if (optimalHeightClass) {
            modalDialog.classList.add(optimalHeightClass);
            console.log(`Applied optimal height class: ${optimalHeightClass} to modal`);
        }
    }

    detectOptimalHeight(modalDialog, modalBody) {
        // Get content metrics
        const contentHeight = modalBody.scrollHeight;
        const formElements = modalBody.querySelectorAll('input, select, textarea, button');
        const tableRows = modalBody.querySelectorAll('tr');
        const images = modalBody.querySelectorAll('img');
        const textLength = modalBody.textContent.length;

        // Large tables or many form elements need more height
        if (tableRows.length > 10 || formElements.length > 12) {
            return 'modal-auto-height'; // Use adaptive height for complex content
        }

        // Text editors or large textareas
        if (modalBody.querySelector('textarea[rows]') &&
            modalBody.querySelector('textarea[rows]').getAttribute('rows') > 5) {
            return 'modal-tall';
        }

        // Image previews or galleries
        if (images.length > 0) {
            return 'modal-tall';
        }

        // Short confirmation dialogs
        if (textLength < 200 && formElements.length <= 2) {
            return 'modal-compact';
        }

        // Medium content
        if (textLength < 800 && formElements.length <= 6) {
            return 'modal-standard';
        }

        // Default to auto-height for everything else to minimize scrolling
        return 'modal-auto-height';
    }

    detectOptimalWidth(modalDialog, modalBody) {
        // Check for specific content types and apply both width and height classes

        // Forms - use modal-form
        if (modalBody.querySelector('form')) {
            const formElements = modalBody.querySelectorAll('input, select, textarea');
            if (formElements.length > 6) {
                // Large form - add height class too
                modalDialog.classList.add('modal-tall');
                return 'modal-form'; // Larger form
            } else {
                // Small form - standard height
                modalDialog.classList.add('modal-standard');
                return 'modal-medium'; // Smaller form
            }
        }

        // Tables - use modal-table with tall height
        if (modalBody.querySelector('table')) {
            modalDialog.classList.add('modal-tall');
            return 'modal-table';
        }

        // Text editors or code blocks - use modal-editor with full height
        if (modalBody.querySelector('textarea[rows]') ||
            modalBody.querySelector('.code-editor') ||
            modalBody.querySelector('pre')) {
            modalDialog.classList.add('modal-full-height');
            return 'modal-editor';
        }
        
        // Confirmation dialogs - use modal-confirmation with compact height
        if (modalBody.textContent.length < 200 &&
            (modalBody.textContent.toLowerCase().includes('delete') ||
             modalBody.textContent.toLowerCase().includes('confirm') ||
             modalBody.textContent.toLowerCase().includes('remove'))) {
            modalDialog.classList.add('modal-compact');
            return 'modal-confirmation';
        }

        // Preview content - use modal-preview with tall height
        if (modalBody.querySelector('img') ||
            modalBody.querySelector('.preview') ||
            modalBody.querySelector('.content-preview')) {
            modalDialog.classList.add('modal-tall');
            return 'modal-preview';
        }

        // Default based on content length with appropriate height
        const contentLength = modalBody.textContent.length;
        if (contentLength < 300) {
            modalDialog.classList.add('modal-compact');
            return 'modal-narrow';
        } else if (contentLength < 800) {
            modalDialog.classList.add('modal-standard');
            return 'modal-medium';
        } else {
            modalDialog.classList.add('modal-tall');
            return 'modal-wide';
        }
    }

    // Manual width control methods
    setModalWidth(modalId, widthClass) {
        const modal = document.getElementById(modalId);
        if (!modal) {
            console.error(`Modal with ID ${modalId} not found`);
            return;
        }

        const modalDialog = modal.querySelector('.modal-dialog');
        if (!modalDialog) {
            console.error(`Modal dialog not found in modal ${modalId}`);
            return;
        }

        // Remove all width classes
        const allWidthClasses = ['modal-lg', 'modal-xl', 'modal-sm', 'modal-narrow', 
                                'modal-medium', 'modal-wide', 'modal-full', 'modal-form', 
                                'modal-confirmation', 'modal-preview', 'modal-table', 'modal-editor'];
        
        allWidthClasses.forEach(cls => modalDialog.classList.remove(cls));
        
        // Add new width class
        modalDialog.classList.add(widthClass);
        
        console.log(`Set modal ${modalId} width to ${widthClass}`);
    }

    // Utility methods for common modal types
    makeModalNarrow(modalId) {
        this.setModalWidth(modalId, 'modal-narrow');
    }

    makeModalMedium(modalId) {
        this.setModalWidth(modalId, 'modal-medium');
    }

    makeModalWide(modalId) {
        this.setModalWidth(modalId, 'modal-wide');
    }

    makeModalForm(modalId) {
        this.setModalWidth(modalId, 'modal-form');
    }

    makeModalConfirmation(modalId) {
        this.setModalWidth(modalId, 'modal-confirmation');
    }

    // Get current modal width class
    getModalWidth(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return null;

        const modalDialog = modal.querySelector('.modal-dialog');
        if (!modalDialog) return null;

        const widthClasses = ['modal-lg', 'modal-xl', 'modal-sm', 'modal-narrow', 
                             'modal-medium', 'modal-wide', 'modal-full', 'modal-form', 
                             'modal-confirmation', 'modal-preview', 'modal-table', 'modal-editor'];

        for (const cls of widthClasses) {
            if (modalDialog.classList.contains(cls)) {
                return cls;
            }
        }

        return 'default';
    }

    // Batch optimize all modals on page
    optimizeAllModals() {
        const modals = document.querySelectorAll('.modal .modal-dialog');
        let optimized = 0;

        modals.forEach(modalDialog => {
            modalDialog.classList.remove('modal-width-optimized');
            this.optimizeModalWidth(modalDialog);
            optimized++;
        });

        console.log(`Optimized ${optimized} modals`);
        return optimized;
    }

    // Height management methods
    setModalHeight(modalId, heightClass) {
        const modal = document.getElementById(modalId);
        if (!modal) {
            console.warn(`Modal with ID ${modalId} not found`);
            return;
        }

        const modalDialog = modal.querySelector('.modal-dialog');
        if (!modalDialog) {
            console.warn(`Modal dialog not found in modal ${modalId}`);
            return;
        }

        // Remove existing height classes
        const heightClasses = ['modal-compact', 'modal-standard', 'modal-tall', 'modal-full-height', 'modal-auto-height'];
        heightClasses.forEach(cls => modalDialog.classList.remove(cls));

        // Add new height class
        modalDialog.classList.add(heightClass);

        console.log(`Set modal ${modalId} height to ${heightClass}`);
    }

    // Utility methods for common modal heights
    makeModalCompact(modalId) {
        this.setModalHeight(modalId, 'modal-compact');
    }

    makeModalStandard(modalId) {
        this.setModalHeight(modalId, 'modal-standard');
    }

    makeModalTall(modalId) {
        this.setModalHeight(modalId, 'modal-tall');
    }

    makeModalFullHeight(modalId) {
        this.setModalHeight(modalId, 'modal-full-height');
    }

    makeModalAutoHeight(modalId) {
        this.setModalHeight(modalId, 'modal-auto-height');
    }

    // Reset all modals to default Bootstrap sizes
    resetAllModals() {
        const modals = document.querySelectorAll('.modal .modal-dialog');
        const customClasses = ['modal-narrow', 'modal-medium', 'modal-wide', 'modal-full',
                              'modal-form', 'modal-confirmation', 'modal-preview', 'modal-table', 'modal-editor',
                              'modal-compact', 'modal-standard', 'modal-tall', 'modal-full-height', 'modal-auto-height'];

        let reset = 0;
        modals.forEach(modalDialog => {
            customClasses.forEach(cls => modalDialog.classList.remove(cls));
            modalDialog.classList.remove('modal-width-optimized');
            reset++;
        });

        console.log(`Reset ${reset} modals to default sizes`);
        return reset;
    }
}

// Initialize the modal width manager
window.modalWidthManager = new ModalWidthManager();

// Expose utility functions globally for easy use
window.setModalWidth = (modalId, widthClass) => window.modalWidthManager.setModalWidth(modalId, widthClass);
window.setModalHeight = (modalId, heightClass) => window.modalWidthManager.setModalHeight(modalId, heightClass);
window.makeModalNarrow = (modalId) => window.modalWidthManager.makeModalNarrow(modalId);
window.makeModalMedium = (modalId) => window.modalWidthManager.makeModalMedium(modalId);
window.makeModalWide = (modalId) => window.modalWidthManager.makeModalWide(modalId);
window.makeModalForm = (modalId) => window.modalWidthManager.makeModalForm(modalId);
window.makeModalConfirmation = (modalId) => window.modalWidthManager.makeModalConfirmation(modalId);
window.makeModalCompact = (modalId) => window.modalWidthManager.makeModalCompact(modalId);
window.makeModalStandard = (modalId) => window.modalWidthManager.makeModalStandard(modalId);
window.makeModalTall = (modalId) => window.modalWidthManager.makeModalTall(modalId);
window.makeModalFullHeight = (modalId) => window.modalWidthManager.makeModalFullHeight(modalId);
window.makeModalAutoHeight = (modalId) => window.modalWidthManager.makeModalAutoHeight(modalId);
window.optimizeAllModals = () => window.modalWidthManager.optimizeAllModals();
window.resetAllModals = () => window.modalWidthManager.resetAllModals();
