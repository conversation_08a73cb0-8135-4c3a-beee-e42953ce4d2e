<?php
/**
 * Granular Permission-Based Access Control Sidebar
 * Uses granular permissions instead of role-based filtering
 */

// Include RBAC system and granular permissions
require_once 'rbac_access_control.php';
require_once 'rbac_access_control_granular.php';

// Get current page for active state
$current_page = basename($_SERVER['PHP_SELF']);

// Initialize RBAC if not already done
if (!isset($rbac) || !($rbac instanceof RBACAccessControl)) {
    global $pdo;

    // Ensure database connection exists
    if (!isset($pdo)) {
        require_once __DIR__ . '/../config.php';
    }

    try {
        $rbac = new RBACAccessControl($pdo, $_SESSION['admin_id'] ?? null);
    } catch (Exception $e) {
        // Fallback to original sidebar if RBAC fails
        include 'sidebar_simple.php';
        return;
    }
}

// Get user role for access control
$primary_role = $rbac->getPrimaryRole();
$user_roles = $rbac->getUserRoles();
$current_user_id = $_SESSION['admin_id'] ?? null;

// Note: admin_url_for function is already defined in config.php

// Helper function to check if current page is active
if (!function_exists('is_active')) {
    function is_active($page_name) {
        global $current_page;
        return ($current_page == $page_name) ? 'active' : '';
    }
}

// Helper function to get member term
if (!function_exists('get_member_term')) {
    function get_member_term($plural = false) {
        $term = get_site_setting('member_term', 'Member');
        return $plural ? $term . 's' : $term;
    }
}

// Helper function to check if user can access a page using granular permissions
function canAccessPage($page) {
    global $current_user_id;

    // Super admins have access to everything - bypass all permission checks
    if (isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === 'super_admin') {
        return true;
    }

    // Map pages to granular permissions
    $page_permissions = [
        'dashboard.php' => 'dashboard.view',
        'members.php' => 'members.view',
        'add_member.php' => 'members.add',
        'edit_member.php' => 'members.edit',
        'member_skills.php' => 'members.skills',
        'family_management.php' => 'members.family_management',
        'requests.php' => 'members.requests',
        'prayer_requests.php' => 'members.requests',
        'volunteer_opportunities.php' => 'members.volunteers',
        'volunteer_applications.php' => 'members.volunteers',
        'volunteer_hours.php' => 'members.volunteers',
        'events.php' => 'events.view',
        'add_event.php' => 'events.create',
        'edit_event.php' => 'events.edit',
        'event_attendance.php' => 'events.attendance',
        'event_categories.php' => 'events.categories',
        'event_sessions.php' => 'events.sessions',
        'event_reports.php' => 'events.reports',
        'universal_ai_dashboard.php' => 'ai.predictions',
        'universal_analytics_dashboard.php' => 'analytics.view',
        'universal_organization_setup.php' => 'admin.organization_setup',
        'realtime_dashboard.php' => 'events.realtime',
        'email_templates.php' => 'email.templates',
        'email_scheduler.php' => 'email.scheduler',
        'email_contacts.php' => 'email.contacts',
        'contact_groups.php' => 'email.contact_groups',
        'birthday_messages.php' => 'email.birthday_messages',
        'send_birthday_emails.php' => 'email.send_birthday_emails',
        'test_birthday_emails.php' => 'email.test_birthday_emails',
        'bulk_email.php' => 'email.bulk_send',
        'manage_user_permissions.php' => 'admin.user_permissions',
        'create_admin_users.php' => 'admin.create_users',
        'system_test_dashboard.php' => 'admin.system_testing',
        'super_admin_navigation.php' => 'admin.navigation_guide',
    ];

    // Check if page has a specific permission requirement
    if (isset($page_permissions[$page])) {
        return hasUserPermission($current_user_id, $page_permissions[$page]);
    }

    // Fallback to role-based check for unmapped pages
    global $rbac;
    return $rbac->canAccessPage($page);
}

// SVG Icon Helper Function (same as sidebar_simple.php)
if (!function_exists('get_svg_icon')) {
    function get_svg_icon($icon_name, $size = 16) {
        $icons = [
            'dashboard' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/></svg>',
            'users' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M16 7c0-2.21-1.79-4-4-4S8 4.79 8 7s1.79 4 4 4 4-1.79 4-4zm-4 6c-2.67 0-8 1.34-8 4v3h16v-3c0-2.66-5.33-4-8-4z"/></svg>',
            'user-plus' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M15 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm-9-2V7H4v3H1v2h3v3h2v-3h3v-2H6zm9 4c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>',
            'calendar' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/></svg>',
            'email' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/></svg>',
            'sms' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 9h12v2H6V9zm8 5H6v-2h8v2zm4-6H6V6h12v2z"/></svg>',
            'settings' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/></svg>',
            'chevron-left' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/></svg>',
            'chevron-right' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/></svg>',
            'menu' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/></svg>',
            'logout' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/></svg>',
            'integrations' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>',
            'profile' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>',
            'brush' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M7 14c-1.66 0-3 1.34-3 3 0 1.31-1.16 2-2 2 .92 1.22 2.49 2 4 2 2.21 0 4-1.79 4-4 0-1.66-1.34-3-3-3zm13.71-9.37l-1.34-1.34c-.39-.39-1.02-.39-1.41 0L9 12.25 11.75 15l8.96-8.96c.39-.39.39-1.02 0-1.41z"/></svg>',
            'palette' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 3c-4.97 0-9 4.03-9 9 0 4.97 4.03 9 9 9 .83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8zm-5.5 9c-.83 0-1.5-.67-1.5-1.5S5.67 9 6.5 9 8 9.67 8 10.5 7.33 12 6.5 12zm3-4C8.67 8 8 7.33 8 6.5S8.67 5 9.5 5s1.5.67 1.5 1.5S10.33 8 9.5 8zm5 0c-.83 0-1.5-.67-1.5-1.5S13.67 5 14.5 5s1.5.67 1.5 1.5S15.33 8 14.5 8zm3 4c-.83 0-1.5-.67-1.5-1.5S16.67 9 17.5 9s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/></svg>',
            'group' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A3.01 3.01 0 0 0 17.1 7H16.9c-.8 0-1.54.37-2.01 1l-2.54 7.63H15v6h5zM12.5 11.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S11 9.17 11 10s.67 1.5 1.5 1.5zm1.5 1h-2c-.83 0-1.5.67-1.5 1.5v6h5v-6c0-.83-.67-1.5-1.5-1.5zM6 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5L10 8.37A3.01 3.01 0 0 0 7.1 7H6.9c-.8 0-1.54.37-2.01 1L2.5 16H5v6h5z"/></svg>',
            'gift' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M20 6h-2.18c.11-.31.18-.65.18-1a2.996 2.996 0 0 0-5.5-1.65l-.5.67-.5-.68C10.96 2.54 10.05 2 9 2 7.34 2 6 3.34 6 5c0 .35.07.69.18 1H4c-1.11 0-2 .89-2 2v4c0 1.11.89 2 2 2h1v6c0 1.11.89 2 2 2h10c1.11 0 2-.89 2-2v-6h1c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-5-2c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zM9 4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1z"/></svg>',
            'template' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/></svg>',
            'chart' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z"/></svg>',
            'analytics' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/></svg>',
            'dollar' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/></svg>',
            'table' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M10 10.02h5V21h-5zM17 21h3c1.1 0 2-.9 2-2v-9h-5v11zm3-18H5c-1.1 0-2 .9-2 2v3h19V5c0-1.1-.9-2-2-2zM3 19c0 1.1.9 2 2 2h3V10H3v9z"/></svg>',
            'check' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg>',
            'tools' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z"/></svg>',
            'bug' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M20 8h-2.81c-.45-.78-1.07-1.45-1.82-1.96L17 4.41 15.59 3l-2.17 2.17C12.96 5.06 12.49 5 12 5c-.49 0-.96.06-1.41.17L8.41 3 7 4.41l1.62 1.63C7.88 6.55 7.26 7.22 6.81 8H4v2h2.09c-.05.33-.09.66-.09 1v1H4v2h2v1c0 .34.04.67.09 1H4v2h2.81c1.04 1.79 2.97 3 5.19 3s4.15-1.21 5.19-3H20v-2h-2.09c.05-.33.09-.66.09-1v-1h2v-2h-2v-1c0-.34-.04-.67-.09-1H20V8z"/></svg>',
            'database' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 3C7.58 3 4 4.79 4 7s3.58 4 8 4 8-1.79 8-4-3.58-4-8-4zM4 9v3c0 2.21 3.58 4 8 4s8-1.79 8-4V9c0 2.21-3.58 4-8 4s-8-1.79-8-4zM4 16v3c0 2.21 3.58 4 8 4s8-1.79 8-4v-3c0 2.21-3.58 4-8 4s-8-1.79-8-4z"/></svg>',
            'info' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/></svg>',
            'image' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/></svg>',
            'shield' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"/></svg>',
            'audit' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/></svg>',
            'attendance' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A3.01 3.01 0 0 0 17.1 7H16.9c-.8 0-1.54.37-2.01 1l-2.54 7.63H15v6h5z"/></svg>',
            'category' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>',
            'report' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/></svg>',
            'birthday-send' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/></svg>',
            'birthday-test' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>',
            'notification' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/></svg>',
            'auto-template' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>',
            'whatsapp' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/></svg>',
            'campaign' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>',
            'social' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A3.01 3.01 0 0 0 17.1 7H16.9c-.8 0-1.54.37-2.01 1l-2.54 7.63H15v6h5z"/></svg>',
            'integration' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>',
            'payment' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M20 4H4c-1.11 0-1.99.9-1.99 2L2 18c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"/></svg>',
            'custom-field' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>',
            'upload' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/></svg>',
            'heart' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>',
            'hand-heart' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M13,14C9.64,14 8.54,15.35 8.18,16.24C9.25,16.7 10,17.76 10,19A3,3 0 0,1 7,22A3,3 0 0,1 4,19C4,17.69 4.83,16.58 6,16.17V7.5A2.5,2.5 0 0,1 8.5,5A2.5,2.5 0 0,1 11,7.5V16H13A4,4 0 0,1 17,20A4,4 0 0,1 13,24C10.79,24 9,22.21 9,20H13C14.1,20 15,19.1 15,18C15,16.9 14.1,16 13,16Z"/></svg>',
            'file-person' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/></svg>',
            'clock' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.7L16.2,16.2Z"/></svg>',
            'language' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/></svg>',
            'heart' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>',
            'hands-helping' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>',
            'person-gear' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/><path d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z"/></svg>',
            'gear-wide-connected' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm8.94 3c-.46-4.17-3.77-7.48-7.94-7.94V1h-2v2.06C6.83 3.52 3.52 6.83 3.06 11H1v2h2.06c.46 4.17 3.77 7.48 7.94 7.94V23h2v-2.06c4.17-.46 7.48-3.77 7.94-7.94H23v-2h-2.06zM12 19c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z"/></svg>'
        ];

        return isset($icons[$icon_name]) ? $icons[$icon_name] : $icons['settings'];
    }
}
?>

<style>
/* Sidebar collapse styles */
.sidebar {
    position: relative;
}

.sidebar-header {
    position: relative;
    min-height: 60px;
    overflow: visible;
}

.sidebar.collapsed {
    width: 60px !important;
}

.sidebar.collapsed .sidebar-header .logo-container {
    display: none;
}

.sidebar.collapsed .sidebar-initials-container {
    display: block !important;
}

.sidebar.collapsed .menu-text {
    display: none;
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 0.75rem 0.5rem;
}

.sidebar.collapsed .nav-icon {
    margin-right: 0;
}

/* Toggle button styles are handled by external CSS file (admin-style.css) */

.sidebar-initials-container {
    text-align: center;
    padding: 10px 0;
}

.sidebar-initials {
    background: var(--bs-primary);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    margin: 0 auto;
}

/* Hide notification badges in collapsed mode */
.sidebar.collapsed .badge {
    display: none;
}

/* Tooltip for collapsed sidebar items */
.sidebar.collapsed .nav-link {
    position: relative;
}

.sidebar.collapsed .nav-link:hover::after {
    content: attr(title);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    white-space: nowrap;
    z-index: 1000;
    margin-left: 10px;
    font-size: 12px;
}

/* Smooth transitions */
.sidebar {
    transition: width 0.3s ease;
}

.sidebar .logo-container,
.sidebar .menu-text,
.sidebar .sidebar-initials-container {
    transition: opacity 0.3s ease;
}

.main-content {
    transition: margin-left 0.3s ease;
}

.main-content.expanded {
    margin-left: 60px;
}
</style>

<!-- Sidebar -->
<div class="col-auto sidebar themed-sidebar" id="sidebar">
    <div class="sidebar-header">
        <!-- Toggle button for desktop -->
        <button class="sidebar-toggle-btn d-none d-md-block" id="sidebarToggleDesktop" title="Toggle Sidebar">
            <?php echo get_svg_icon('chevron-left', 18); ?>
        </button>

        <!-- Mobile toggle button -->
        <button class="d-md-none btn btn-sm btn-outline-light" id="sidebarToggle">
            <?php echo get_svg_icon('menu', 18); ?>
        </button>
        <?php
        // Get organization name for initials
        $organizationName = get_organization_name();
        $siteInitials = '';
        if (!empty($organizationName)) {
            $words = explode(' ', $organizationName);
            foreach ($words as $word) {
                if (!empty($word)) {
                    $siteInitials .= strtoupper(substr($word, 0, 1));
                }
            }
            // Limit to 3 characters max
            $siteInitials = substr($siteInitials, 0, 3);
        }
        if (empty($siteInitials)) {
            $siteInitials = 'CA'; // Default fallback
        }
        ?>
        
        <!-- Logo/Title Container -->
        <div class="logo-container">
            <?php
            // Use the existing logo management system
            $headerLogo = get_site_setting('header_logo', '');
            $mainLogo = get_site_setting('main_logo', '');
            $logoToUse = !empty($headerLogo) ? $headerLogo : $mainLogo;
            ?>

            <?php if (!empty($logoToUse) && file_exists(__DIR__ . '/../../' . $logoToUse)): ?>
                <div class="navbar-brand mb-0 logo-container">
                    <img src="<?php echo get_base_url() . '/' . htmlspecialchars($logoToUse); ?>"
                         alt="<?php echo get_admin_title(); ?>"
                         class="sidebar-logo">
                </div>
            <?php else: ?>
                <div class="sidebar-logo-text">
                    <span class="navbar-brand-text"><?php echo get_admin_title(); ?></span>
                </div>
            <?php endif; ?>

            <div class="sidebar-initials-container" style="display: none;">
                <div class="sidebar-initials"><?php echo $siteInitials; ?></div>
            </div>
        </div>

        <!-- Removed duplicate toggle buttons -->
    </div>
    
    <div class="sidebar-content">
        <ul class="nav flex-column">
            <!-- Dashboard -->
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('dashboard.php'); ?>" href="<?php echo admin_url_for('dashboard.php'); ?>" title="Dashboard">
                    <span class="nav-icon"><?php echo get_svg_icon('dashboard', 18); ?></span>
                    <span class="menu-text">Dashboard</span>
                </a>
            </li>

            <!-- Insight Dashboard (Super Admin Only) -->
            <?php if ($primary_role === 'super_admin' && canAccessPage('super_admin_dashboard.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('super_admin_dashboard.php'); ?>" href="<?php echo admin_url_for('super_admin_dashboard.php'); ?>" title="Insight">
                    <span class="nav-icon"><?php echo get_svg_icon('eye', 18); ?></span>
                    <span class="menu-text">Insight</span>
                </a>
            </li>
            <?php endif; ?>

            <!-- Universal Platform Section -->
            <li class="nav-item nav-section-header">
                <span class="nav-section-title">
                    <span class="nav-icon"><?php echo get_svg_icon('robot', 18); ?></span>
                    <span class="menu-text">Universal Platform</span>
                </span>
            </li>
            <?php if (canAccessPage('universal_ai_dashboard.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('universal_ai_dashboard.php'); ?>" href="<?php echo admin_url_for('universal_ai_dashboard.php'); ?>" title="AI Predictions">
                    <span class="nav-icon"><?php echo get_svg_icon('robot', 18); ?></span>
                    <span class="menu-text">AI Predictions</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('universal_analytics_dashboard.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('universal_analytics_dashboard.php'); ?>" href="<?php echo admin_url_for('universal_analytics_dashboard.php'); ?>" title="Universal Analytics">
                    <span class="nav-icon"><?php echo get_svg_icon('analytics', 18); ?></span>
                    <span class="menu-text">Universal Analytics</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('realtime_dashboard.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('realtime_dashboard.php'); ?>" href="<?php echo admin_url_for('realtime_dashboard.php'); ?>" title="Real-Time Dashboard">
                    <span class="nav-icon"><?php echo get_svg_icon('broadcast', 18); ?></span>
                    <span class="menu-text">Real-Time Dashboard</span>
                </a>
            </li>
            <?php endif; ?>
            <li class="nav-item">
                <a class="nav-link" href="<?php echo admin_url_for('pwa/'); ?>" title="Mobile PWA" target="_blank">
                    <span class="nav-icon"><?php echo get_svg_icon('phone', 18); ?></span>
                    <span class="menu-text">Mobile PWA</span>
                    <span class="badge bg-success ms-2" style="font-size: 0.6rem;">NEW</span>
                </a>
            </li>

            <!-- Notifications -->
            <?php if (canAccessPage('notifications.php')): ?>
            <li class="nav-item">
                <a class="nav-link position-relative <?php echo is_active('notifications.php'); ?>" href="<?php echo admin_url_for('notifications.php'); ?>" title="Notifications">
                    <span class="nav-icon"><?php echo get_svg_icon('notification', 18); ?></span>
                    <span class="menu-text">Notifications</span>
                    <?php
                    // Get unread notification count
                    if (isset($_SESSION['admin_id']) && function_exists('getAdminUnreadNotificationCount')) {
                        try {
                            $unreadCount = getAdminUnreadNotificationCount($pdo, $_SESSION['admin_id']);
                            if ($unreadCount > 0):
                    ?>
                        <span class="badge bg-danger rounded-pill position-absolute" style="font-size: 0.65rem; top: -8px; right: 8px;">
                            <?php echo $unreadCount > 99 ? '99+' : $unreadCount; ?>
                        </span>
                    <?php
                            endif;
                        } catch (Exception $e) {
                            // Silently handle any errors
                        }
                    }
                    ?>
                </a>
            </li>
            <?php endif; ?>

            <!-- Member Management Section -->
            <li class="nav-item nav-section-header">
                <span class="nav-section-title">
                    <span class="nav-icon"><?php echo get_svg_icon('users', 18); ?></span>
                    <span class="menu-text"><?php echo get_member_term(); ?> Management</span>
                </span>
            </li>
            <?php if (canAccessPage('members.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('members.php'); ?>" href="<?php echo admin_url_for('members.php'); ?>" title="<?php echo get_member_term(true); ?>">
                    <span class="nav-icon"><?php echo get_svg_icon('users', 18); ?></span>
                    <span class="menu-text"><?php echo get_member_term(true); ?></span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('add_member.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('add_member.php'); ?>" href="<?php echo admin_url_for('add_member.php'); ?>" title="Add <?php echo get_member_term(); ?>">
                    <span class="nav-icon"><?php echo get_svg_icon('user-plus', 18); ?></span>
                    <span class="menu-text">Add <?php echo get_member_term(); ?></span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('member_skills.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('member_skills.php'); ?>" href="<?php echo admin_url_for('member_skills.php'); ?>" title="Member Skills">
                    <span class="nav-icon"><?php echo get_svg_icon('tools', 18); ?></span>
                    <span class="menu-text">Member Skills</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('family_management.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('family_management.php'); ?>" href="<?php echo admin_url_for('family_management.php'); ?>" title="Family Management">
                    <span class="nav-icon"><?php echo get_svg_icon('users', 18); ?></span>
                    <span class="menu-text">Family Management</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('requests.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('requests.php'); ?>" href="<?php echo admin_url_for('requests.php'); ?>" title="Requests">
                    <span class="nav-icon"><?php echo get_svg_icon('heart', 18); ?></span>
                    <span class="menu-text">Requests</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('volunteer_opportunities.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('volunteer_opportunities.php'); ?>" href="<?php echo admin_url_for('volunteer_opportunities.php'); ?>" title="Volunteer Opportunities">
                    <span class="nav-icon"><?php echo get_svg_icon('hand-heart', 18); ?></span>
                    <span class="menu-text">Volunteer Opportunities</span>
                </a>
            </li>
            <?php endif; ?>


            <!-- Events Management Section -->
            <li class="nav-item nav-section-header">
                <span class="nav-section-title">
                    <span class="nav-icon"><?php echo get_svg_icon('calendar', 18); ?></span>
                    <span class="menu-text">Events Management</span>
                </span>
            </li>
            <?php if (canAccessPage('events.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('events.php'); ?>" href="<?php echo admin_url_for('events.php'); ?>" title="Events">
                    <span class="nav-icon"><?php echo get_svg_icon('calendar', 18); ?></span>
                    <span class="menu-text">Events</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('event_attendance.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('event_attendance.php'); ?>" href="<?php echo admin_url_for('event_attendance.php'); ?>" title="Event Attendance">
                    <span class="nav-icon"><?php echo get_svg_icon('attendance', 18); ?></span>
                    <span class="menu-text">Event Attendance</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('event_categories.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('event_categories.php'); ?>" href="<?php echo admin_url_for('event_categories.php'); ?>" title="Event Categories">
                    <span class="nav-icon"><?php echo get_svg_icon('category', 18); ?></span>
                    <span class="menu-text">Event Categories</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('event_reports.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('event_reports.php'); ?>" href="<?php echo admin_url_for('event_reports.php'); ?>" title="Event Reports">
                    <span class="nav-icon"><?php echo get_svg_icon('report', 18); ?></span>
                    <span class="menu-text">Event Reports</span>
                </a>
            </li>
            <?php endif; ?>



            <!-- Email Management Section -->
            <li class="nav-item nav-section-header">
                <span class="nav-section-title">
                    <span class="nav-icon"><?php echo get_svg_icon('email', 18); ?></span>
                    <span class="menu-text">Email Management</span>
                </span>
            </li>
            <?php if (canAccessPage('bulk_email.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('bulk_email.php'); ?>" href="<?php echo admin_url_for('bulk_email.php'); ?>" title="Bulk Email">
                    <span class="nav-icon"><?php echo get_svg_icon('email', 18); ?></span>
                    <span class="menu-text">Bulk Email</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('email_scheduler.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('email_scheduler.php'); ?>" href="<?php echo admin_url_for('email_scheduler.php'); ?>" title="Email Scheduler">
                    <span class="nav-icon"><?php echo get_svg_icon('calendar', 18); ?></span>
                    <span class="menu-text">Email Scheduler</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('contacts.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('contacts.php'); ?>" href="<?php echo admin_url_for('contacts.php'); ?>" title="Contacts">
                    <span class="nav-icon"><?php echo get_svg_icon('users', 18); ?></span>
                    <span class="menu-text">Contacts</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('contact_groups.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('contact_groups.php'); ?>" href="<?php echo admin_url_for('contact_groups.php'); ?>" title="Contact Groups">
                    <span class="nav-icon"><?php echo get_svg_icon('group', 18); ?></span>
                    <span class="menu-text">Contact Groups</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('birthday.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('birthday.php'); ?>" href="<?php echo admin_url_for('birthday.php'); ?>" title="Birthday Messages">
                    <span class="nav-icon"><?php echo get_svg_icon('gift', 18); ?></span>
                    <span class="menu-text">Birthday Messages</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('send_birthday_emails.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('send_birthday_emails.php'); ?>" href="<?php echo admin_url_for('send_birthday_emails.php'); ?>" title="Send Bulk Birthday Emails">
                    <span class="nav-icon"><?php echo get_svg_icon('birthday-send', 18); ?></span>
                    <span class="menu-text">Send Bulk Birthday Emails</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('test_birthday_email.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('test_birthday_email.php'); ?>" href="<?php echo admin_url_for('test_birthday_email.php'); ?>" title="Test Birthday Emails">
                    <span class="nav-icon"><?php echo get_svg_icon('birthday-test', 18); ?></span>
                    <span class="menu-text">Test Birthday Emails</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('send_birthday_notification.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('send_birthday_notification.php'); ?>" href="<?php echo admin_url_for('send_birthday_notification.php'); ?>" title="Send Birthday Notifications">
                    <span class="nav-icon"><?php echo get_svg_icon('notification', 18); ?></span>
                    <span class="menu-text">Send Birthday Notifications</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('email_templates.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('email_templates.php'); ?>" href="<?php echo admin_url_for('email_templates.php'); ?>" title="Email Templates">
                    <span class="nav-icon"><?php echo get_svg_icon('template', 18); ?></span>
                    <span class="menu-text">Email Templates</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('automated_templates.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('automated_templates.php'); ?>" href="<?php echo admin_url_for('automated_templates.php'); ?>" title="Automated Templates">
                    <span class="nav-icon"><?php echo get_svg_icon('auto-template', 18); ?></span>
                    <span class="menu-text">Automated Templates</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('whatsapp_templates.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('whatsapp_templates.php'); ?>" href="<?php echo admin_url_for('whatsapp_templates.php'); ?>" title="WhatsApp Templates">
                    <span class="nav-icon"><?php echo get_svg_icon('whatsapp', 18); ?></span>
                    <span class="menu-text">WhatsApp Templates</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('whatsapp_messages.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('whatsapp_messages.php'); ?>" href="<?php echo admin_url_for('whatsapp_messages.php'); ?>" title="WhatsApp Messages">
                    <span class="nav-icon"><?php echo get_svg_icon('whatsapp', 18); ?></span>
                    <span class="menu-text">WhatsApp Messages</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('email_analytics.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('email_analytics.php'); ?>" href="<?php echo admin_url_for('email_analytics.php'); ?>" title="Email Analytics">
                    <span class="nav-icon"><?php echo get_svg_icon('analytics', 18); ?></span>
                    <span class="menu-text">Email Analytics</span>
                </a>
            </li>
            <?php endif; ?>


            <!-- SMS Management Section -->
            <li class="nav-item nav-section-header">
                <span class="nav-section-title">
                    <span class="nav-icon"><?php echo get_svg_icon('sms', 18); ?></span>
                    <span class="menu-text">SMS Management</span>
                </span>
            </li>
            <?php if (canAccessPage('single_sms.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('single_sms.php'); ?>" href="<?php echo admin_url_for('single_sms.php'); ?>" title="Single SMS">
                    <span class="nav-icon"><?php echo get_svg_icon('sms', 18); ?></span>
                    <span class="menu-text">Single SMS</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('bulk_sms.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('bulk_sms.php'); ?>" href="<?php echo admin_url_for('bulk_sms.php'); ?>" title="Bulk SMS">
                    <span class="nav-icon"><?php echo get_svg_icon('sms', 18); ?></span>
                    <span class="menu-text">Bulk SMS</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('sms_campaigns.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('sms_campaigns.php'); ?>" href="<?php echo admin_url_for('sms_campaigns.php'); ?>" title="SMS Campaigns">
                    <span class="nav-icon"><?php echo get_svg_icon('campaign', 18); ?></span>
                    <span class="menu-text">SMS Campaigns</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('sms_templates.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('sms_templates.php'); ?>" href="<?php echo admin_url_for('sms_templates.php'); ?>" title="SMS Templates">
                    <span class="nav-icon"><?php echo get_svg_icon('template', 18); ?></span>
                    <span class="menu-text">SMS Templates</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('sms_analytics.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('sms_analytics.php'); ?>" href="<?php echo admin_url_for('sms_analytics.php'); ?>" title="SMS Analytics">
                    <span class="nav-icon"><?php echo get_svg_icon('analytics', 18); ?></span>
                    <span class="menu-text">SMS Analytics</span>
                </a>
            </li>
            <?php endif; ?>

            <!-- Integrations Section -->
            <li class="nav-item nav-section-header">
                <span class="nav-section-title">
                    <span class="nav-icon"><?php echo get_svg_icon('integration', 18); ?></span>
                    <span class="menu-text">Integrations</span>
                </span>
            </li>
            <?php if (canAccessPage('calendar_integration.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('calendar_integration.php'); ?>" href="<?php echo admin_url_for('calendar_integration.php'); ?>" title="Calendar Integration">
                    <span class="nav-icon"><?php echo get_svg_icon('calendar', 18); ?></span>
                    <span class="menu-text">Calendar Integration</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('social_media_integration.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('social_media_integration.php'); ?>" href="<?php echo admin_url_for('social_media_integration.php'); ?>" title="Social Media">
                    <span class="nav-icon"><?php echo get_svg_icon('social', 18); ?></span>
                    <span class="menu-text">Social Media</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('sms_integration.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('sms_integration.php'); ?>" href="<?php echo admin_url_for('sms_integration.php'); ?>" title="SMS Integration">
                    <span class="nav-icon"><?php echo get_svg_icon('sms', 18); ?></span>
                    <span class="menu-text">SMS Integration</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('payment_integration.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('payment_integration.php'); ?>" href="<?php echo admin_url_for('payment_integration.php'); ?>" title="Payment Integration">
                    <span class="nav-icon"><?php echo get_svg_icon('payment', 18); ?></span>
                    <span class="menu-text">Payment Integration</span>
                </a>
            </li>
            <?php endif; ?>

            <?php if (canAccessPage('donations.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('donations.php'); ?>" href="<?php echo admin_url_for('donations.php'); ?>" title="Donations">
                    <span class="nav-icon"><?php echo get_svg_icon('dollar', 18); ?></span>
                    <span class="menu-text">Donations</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('gift_management.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('gift_management.php'); ?>" href="<?php echo admin_url_for('gift_management.php'); ?>" title="Gift Management">
                    <span class="nav-icon"><?php echo get_svg_icon('gift', 18); ?></span>
                    <span class="menu-text">Gift Management</span>
                </a>
            </li>
            <?php endif; ?>



            <!-- Main Settings Section -->
            <li class="nav-item nav-section-header">
                <span class="nav-section-title">
                    <span class="nav-icon"><?php echo get_svg_icon('settings', 18); ?></span>
                    <span class="menu-text">Main Settings</span>
                </span>
            </li>

            <?php if (canAccessPage('custom_fields.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('custom_fields.php'); ?>" href="<?php echo admin_url_for('custom_fields.php'); ?>" title="Custom Fields">
                    <span class="nav-icon"><?php echo get_svg_icon('custom-field', 18); ?></span>
                    <span class="menu-text">Custom Fields</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('appearance_settings.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('appearance_settings.php'); ?>" href="<?php echo admin_url_for('appearance_settings.php'); ?>" title="Appearance & Branding">
                    <span class="nav-icon"><?php echo get_svg_icon('palette', 18); ?></span>
                    <span class="menu-text">Appearance & Branding</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('logo_management_consolidated.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('logo_management_consolidated.php'); ?>" href="<?php echo admin_url_for('logo_management_consolidated.php'); ?>" title="Logo Management">
                    <span class="nav-icon"><?php echo get_svg_icon('image', 18); ?></span>
                    <span class="menu-text">Logo Management</span>
                </a>
            </li>
            <?php endif; ?>

            <?php if (canAccessPage('security_audit.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('security_audit.php'); ?>" href="<?php echo admin_url_for('security_audit.php'); ?>" title="Security Audit">
                    <span class="nav-icon"><?php echo get_svg_icon('audit', 18); ?></span>
                    <span class="menu-text">Security Audit</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('security_settings.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('security_settings.php'); ?>" href="<?php echo admin_url_for('security_settings.php'); ?>" title="Security Settings">
                    <span class="nav-icon"><?php echo get_svg_icon('settings', 18); ?></span>
                    <span class="menu-text">Security Settings</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('backup_management.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('backup_management.php'); ?>" href="<?php echo admin_url_for('backup_management.php'); ?>" title="Database Backup">
                    <span class="nav-icon"><?php echo get_svg_icon('database', 18); ?></span>
                    <span class="menu-text">Database Backup</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('create_admin_users.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('create_admin_users.php'); ?>" href="<?php echo admin_url_for('create_admin_users.php'); ?>" title="Create Admin Users">
                    <span class="nav-icon"><?php echo get_svg_icon('user-plus', 18); ?></span>
                    <span class="menu-text">Create Admin Users</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('manage_user_permissions.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('manage_user_permissions.php'); ?>" href="<?php echo admin_url_for('manage_user_permissions.php'); ?>" title="User Permissions">
                    <span class="nav-icon"><?php echo get_svg_icon('person-gear', 18); ?></span>
                    <span class="menu-text">User Permissions</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('universal_organization_setup.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('universal_organization_setup.php'); ?>" href="<?php echo admin_url_for('universal_organization_setup.php'); ?>" title="Organization Setup">
                    <span class="nav-icon"><?php echo get_svg_icon('settings', 18); ?></span>
                    <span class="menu-text">Organization Setup</span>
                </a>
            </li>
            <?php endif; ?>


            <!-- RBAC Dashboards Section (Super Admin Only) -->
            <?php if ($primary_role === 'super_admin'): ?>
            <li class="nav-item nav-section-header">
                <span class="nav-section-title">
                    <span class="nav-icon"><?php echo get_svg_icon('shield', 18); ?></span>
                    <span class="menu-text">RBAC Dashboards</span>
                </span>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo is_active('setup_rbac_system.php'); ?>" href="<?php echo admin_url_for('setup_rbac_system.php'); ?>" title="RBAC Management">
                    <span class="nav-icon"><?php echo get_svg_icon('settings', 18); ?></span>
                    <span class="menu-text">RBAC Management</span>
                </a>
            </li>
            <?php if (canAccessPage('super_admin_navigation.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('super_admin_navigation.php'); ?>" href="<?php echo admin_url_for('super_admin_navigation.php'); ?>" title="Navigation Guide">
                    <span class="nav-icon"><?php echo get_svg_icon('info', 18); ?></span>
                    <span class="menu-text">Navigation Guide</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if (canAccessPage('setup_granular_permissions_system.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('setup_granular_permissions_system.php'); ?>" href="<?php echo admin_url_for('setup_granular_permissions_system.php'); ?>" title="Setup Granular System">
                    <span class="nav-icon"><?php echo get_svg_icon('gear-wide-connected', 18); ?></span>
                    <span class="menu-text">Setup Granular System</span>
                </a>
            </li>
            <?php endif; ?>
            <?php endif; ?>

            <!-- My Profile -->
            <?php if (canAccessPage('profile.php')): ?>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('profile.php'); ?>" href="<?php echo admin_url_for('profile.php'); ?>" title="My Profile">
                    <span class="nav-icon"><?php echo get_svg_icon('profile', 18); ?></span>
                    <span class="menu-text">My Profile</span>
                </a>
            </li>
            <?php endif; ?>

            <!-- Logout -->
            <li class="nav-item">
                <a class="nav-link" href="<?php echo admin_url_for('logout.php'); ?>" title="Logout">
                    <span class="nav-icon"><?php echo get_svg_icon('logout', 18); ?></span>
                    <span class="menu-text">Logout</span>
                </a>
            </li>
        </ul>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const desktopToggle = document.getElementById('sidebarToggleDesktop');
    const mainContent = document.querySelector('.main-content');
    const isMobile = window.innerWidth <= 768;

    // Mobile sidebar toggle
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            sidebar.classList.toggle('collapsed');
        });
    }

    // Desktop sidebar toggle
    if (!isMobile && desktopToggle) {
        // Check for user preference in localStorage
        if (localStorage.getItem('sidebarCollapsed') === 'true') {
            sidebar.classList.add('collapsed');
            if (mainContent) mainContent.classList.add('expanded');
            updateToggleIcon(true);
        }

        desktopToggle.addEventListener('click', function(e) {
            e.preventDefault();
            const isCollapsed = sidebar.classList.toggle('collapsed');

            if (mainContent) {
                mainContent.classList.toggle('expanded', isCollapsed);
            }

            updateToggleIcon(isCollapsed);
            localStorage.setItem('sidebarCollapsed', isCollapsed ? 'true' : 'false');
        });
    }

    function updateToggleIcon(isCollapsed) {
        if (desktopToggle) {
            const iconHtml = isCollapsed ?
                '<?php echo addslashes(get_svg_icon('chevron-right', 18)); ?>' :
                '<?php echo addslashes(get_svg_icon('chevron-left', 18)); ?>';
            desktopToggle.innerHTML = iconHtml;
        }
    }
});
</script>
