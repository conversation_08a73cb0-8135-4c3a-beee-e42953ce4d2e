<?php
// Complete system initialization
require_once '../config.php';

try {
    echo "Initializing granular permission system...\n\n";
    
    // First, let's check what we have
    $stmt = $pdo->query("SELECT COUNT(*) FROM permission_categories");
    $cat_count = $stmt->fetchColumn();
    echo "Permission categories: {$cat_count}\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM granular_permissions");
    $perm_count = $stmt->fetchColumn();
    echo "Granular permissions: {$perm_count}\n";
    
    // If no permissions exist, create them
    if ($perm_count == 0) {
        echo "\nCreating permissions...\n";
        
        // Get category IDs
        $stmt = $pdo->query("SELECT id, category_name FROM permission_categories");
        $categories = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        // Basic permissions for testing
        $basic_permissions = [
            ['dashboard_access', 'dashboard.view', 'View Dashboard', 'dashboard.php'],
            ['member_management', 'members.view', 'View Members', 'members.php'],
            ['member_management', 'members.add', 'Add Members', 'add_member.php'],
            ['event_management', 'events.view', 'View Events', 'events.php'],
            ['email_management', 'email.bulk_send', 'Bulk Email', 'bulk_email.php'],
            ['admin_management', 'admin.permission_management', 'Permission Management', 'manage_user_permissions.php'],
            ['admin_management', 'admin.granular_setup', 'Granular System Setup', 'setup_granular_permissions_system.php']
        ];
        
        foreach ($basic_permissions as $i => $perm) {
            $category_id = $categories[$perm[0]] ?? null;
            if ($category_id) {
                $stmt = $pdo->prepare("INSERT INTO granular_permissions (category_id, permission_key, permission_name, permission_description, page_file, sort_order) VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->execute([$category_id, $perm[1], $perm[2], $perm[3], $perm[4], $i + 1]);
                echo "✓ Created permission: {$perm[2]}\n";
            }
        }
    }
    
    // Get all admin users
    $stmt = $pdo->query("SELECT id, username FROM admins");
    $admin_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "\nFound " . count($admin_users) . " admin users\n";
    
    // Get all permissions
    $stmt = $pdo->query("SELECT id, permission_key, permission_name FROM granular_permissions WHERE is_active = 1");
    $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "Found " . count($permissions) . " permissions\n";
    
    // Assign all permissions to the first admin (super admin)
    if (!empty($admin_users) && !empty($permissions)) {
        $super_admin = $admin_users[0];
        echo "\nAssigning all permissions to super admin: {$super_admin['username']}\n";
        
        foreach ($permissions as $permission) {
            // Check if permission already assigned
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM user_individual_permissions WHERE user_id = ? AND permission_id = ?");
            $stmt->execute([$super_admin['id'], $permission['id']]);
            
            if ($stmt->fetchColumn() == 0) {
                // Assign permission
                $stmt = $pdo->prepare("INSERT INTO user_individual_permissions (user_id, permission_id, granted_by, is_active) VALUES (?, ?, ?, 1)");
                $stmt->execute([$super_admin['id'], $permission['id'], $super_admin['id']]);
                echo "✓ Assigned: {$permission['permission_name']}\n";
            } else {
                echo "- Already assigned: {$permission['permission_name']}\n";
            }
        }
    }
    
    // Final status check
    $stmt = $pdo->query("SELECT COUNT(*) FROM user_individual_permissions WHERE is_active = 1");
    $user_perm_count = $stmt->fetchColumn();
    
    echo "\n🎉 System initialization complete!\n";
    echo "- Permission categories: {$cat_count}\n";
    echo "- Granular permissions: " . count($permissions) . "\n";
    echo "- User permissions assigned: {$user_perm_count}\n";
    echo "\nThe granular permission system is now ready to use!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
?>
