<?php
// QR Code Email Sender using Database SMTP Configuration
require_once '../config.php';

echo "<h1>📧 QR Code Email Sender (Using Database SMTP)</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .step{background:#f5f5f5;padding:10px;margin:10px 0;border-left:4px solid #007bff;}</style>";

$target_email = 'bointa@<EMAIL>';

try {
    echo "<div class='step'>";
    echo "<h2>Step 1: Load SMTP Settings from Database</h2>";
    
    // Load email settings from database
    $emailSettings = [
        'smtp_host' => '',
        'smtp_auth' => true,
        'smtp_username' => '',
        'smtp_password' => '',
        'smtp_secure' => 'ssl',
        'smtp_port' => 465,
        'sender_email' => '',
        'sender_name' => 'Freedom Assembly Church',
        'admin_email' => '',
        'reply_to_email' => ''
    ];
    
    // Load settings from database
    $stmt = $pdo->prepare("SELECT * FROM email_settings");
    $stmt->execute();
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $key = $row['setting_key'];
        $emailSettings[$key] = $row['setting_value'];
    }
    
    echo "<span class='success'>✅ SMTP Settings loaded from database</span><br>";
    echo "<span class='info'>📧 SMTP Host: {$emailSettings['smtp_host']}</span><br>";
    echo "<span class='info'>📧 SMTP Port: {$emailSettings['smtp_port']}</span><br>";
    echo "<span class='info'>📧 SMTP Security: {$emailSettings['smtp_secure']}</span><br>";
    echo "<span class='info'>📧 Sender Email: {$emailSettings['sender_email']}</span><br>";
    echo "<span class='info'>📧 Sender Name: {$emailSettings['sender_name']}</span><br>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 2: Load PHPMailer</h2>";
    
    // Check if PHPMailer is available
    if (file_exists('../vendor/autoload.php')) {
        require_once '../vendor/autoload.php';
        echo "<span class='success'>✅ PHPMailer loaded via Composer</span><br>";
    } elseif (file_exists('../vendor/phpmailer/phpmailer/src/PHPMailer.php')) {
        require_once '../vendor/phpmailer/phpmailer/src/PHPMailer.php';
        require_once '../vendor/phpmailer/phpmailer/src/SMTP.php';
        require_once '../vendor/phpmailer/phpmailer/src/Exception.php';
        echo "<span class='success'>✅ PHPMailer loaded directly</span><br>";
    } else {
        throw new Exception("PHPMailer not found. Please install PHPMailer via Composer.");
    }
    
    use PHPMailer\PHPMailer\PHPMailer;
    use PHPMailer\PHPMailer\SMTP;
    use PHPMailer\PHPMailer\Exception;
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 3: Get Member and QR Data</h2>";
    
    // Get member data
    $stmt = $pdo->prepare("SELECT * FROM members WHERE email = ?");
    $stmt->execute([$target_email]);
    $member = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$member) {
        throw new Exception("Member with email {$target_email} not found.");
    }
    
    echo "<span class='success'>✅ Found member: {$member['full_name']}</span><br>";
    
    // Get QR code data
    $stmt = $pdo->prepare("
        SELECT mqr.*, e.title as event_title, e.event_date, e.location as event_location
        FROM member_qr_codes mqr
        JOIN events e ON mqr.event_id = e.id
        WHERE mqr.attendee_email = ?
        ORDER BY mqr.created_at DESC
        LIMIT 1
    ");
    $stmt->execute([$target_email]);
    $qr_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$qr_data) {
        throw new Exception("No QR code found for this member.");
    }
    
    echo "<span class='success'>✅ Found QR code: {$qr_data['qr_token']}</span><br>";
    echo "<span class='info'>🎪 Event: {$qr_data['event_title']}</span><br>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 4: Create and Send Email with PHPMailer</h2>";
    
    // Generate QR code URL
    $base_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
    $qr_url = $base_url . "/member_checkin.php?token=" . $qr_data['qr_token'];
    
    // Create PHPMailer instance
    $mail = new PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host       = $emailSettings['smtp_host'];
        $mail->SMTPAuth   = true;
        $mail->Username   = $emailSettings['smtp_username'];
        $mail->Password   = $emailSettings['smtp_password'];
        $mail->SMTPSecure = $emailSettings['smtp_secure'] === 'ssl' ? PHPMailer::ENCRYPTION_SMTPS : PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port       = $emailSettings['smtp_port'];
        
        // Recipients
        $mail->setFrom($emailSettings['sender_email'], $emailSettings['sender_name']);
        $mail->addAddress($target_email, $member['full_name']);
        $mail->addReplyTo($emailSettings['sender_email'], $emailSettings['sender_name']);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = 'Your QR Code for ' . $qr_data['event_title'];
        
        // Create beautiful HTML email
        $mail->Body = "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Event QR Code</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
                .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; }
                .header h1 { margin: 0; font-size: 24px; }
                .content { padding: 30px 20px; background: white; }
                .qr-section { text-align: center; margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 10px; }
                .qr-placeholder { border: 3px solid #007bff; padding: 40px 20px; background: white; border-radius: 10px; display: inline-block; margin: 20px 0; font-size: 16px; font-weight: bold; color: #007bff; max-width: 300px; word-break: break-all; }
                .instructions { background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3; }
                .instructions h3 { margin-top: 0; color: #1976d2; }
                .event-details { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
                .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px; }
                .btn { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
                .highlight { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>🎟️ Your Event QR Code</h1>
                    <p>Freedom Assembly Church</p>
                </div>
                
                <div class='content'>
                    <h2>Hello " . htmlspecialchars($member['full_name']) . "!</h2>
                    <p>Thank you for registering for <strong>" . htmlspecialchars($qr_data['event_title']) . "</strong>. Your personal QR code is ready!</p>
                    
                    <div class='event-details'>
                        <h3>📅 Event Details</h3>
                        <p><strong>Event:</strong> " . htmlspecialchars($qr_data['event_title']) . "</p>
                        <p><strong>Date:</strong> " . date('F j, Y g:i A', strtotime($qr_data['event_date'])) . "</p>
                        <p><strong>Location:</strong> " . htmlspecialchars($qr_data['event_location']) . "</p>
                    </div>
                    
                    <div class='qr-section'>
                        <h3>🎯 Your Personal QR Code</h3>
                        <div class='qr-placeholder'>
                            📱 SCAN THIS QR CODE 📱<br>
                            <small style='font-size: 12px; color: #666;'>" . htmlspecialchars($qr_data['qr_token']) . "</small>
                        </div>
                        <p><strong>QR Code ID:</strong> " . htmlspecialchars($qr_data['qr_token']) . "</p>
                        <a href='{$qr_url}' class='btn' style='color: white;'>🔗 Open Check-in Link</a>
                    </div>
                    
                    <div class='instructions'>
                        <h3>📱 How to Use Your QR Code</h3>
                        <ol>
                            <li><strong>Save this email</strong> or screenshot the QR code section</li>
                            <li><strong>Arrive at the event</strong> and look for check-in stations</li>
                            <li><strong>Show your QR code</strong> to staff at the entrance</li>
                            <li><strong>Get checked in instantly</strong> - no manual process needed!</li>
                        </ol>
                        
                        <p><strong>💡 Pro Tips:</strong></p>
                        <ul>
                            <li>You can show the QR code on your phone or print this email</li>
                            <li>If QR scanning doesn't work, use the check-in link above</li>
                            <li>Each QR code is unique and can only be used once</li>
                            <li>Arrive early to avoid queues at check-in</li>
                        </ul>
                    </div>
                    
                    <div class='highlight'>
                        <p><strong>🚀 New Feature:</strong> This QR code system makes check-in super fast! Just show your code and you're in. No more waiting in long lines!</p>
                    </div>
                    
                    <p>If you have any questions or need assistance, please contact our event team.</p>
                    <p>We look forward to seeing you at the event!</p>
                    
                    <p><strong>Blessings,</strong><br>
                    Freedom Assembly Church Event Team</p>
                </div>
                
                <div class='footer'>
                    <p>This is an automated message from Freedom Assembly Church</p>
                    <p>Event Management System | QR Code Check-in</p>
                    <p><strong>Direct Check-in URL:</strong><br>
                    <a href='{$qr_url}' style='color: #007bff; word-break: break-all;'>{$qr_url}</a></p>
                </div>
            </div>
        </body>
        </html>
        ";
        
        // Plain text version
        $mail->AltBody = "
Hello " . $member['full_name'] . "!

Thank you for registering for " . $qr_data['event_title'] . ". Your personal QR code is ready!

EVENT DETAILS:
Event: " . $qr_data['event_title'] . "
Date: " . date('F j, Y g:i A', strtotime($qr_data['event_date'])) . "
Location: " . $qr_data['event_location'] . "

YOUR QR CODE: " . $qr_data['qr_token'] . "

HOW TO USE:
1. Save this email or screenshot the QR code
2. Arrive at the event and look for check-in stations
3. Show your QR code to staff at the entrance
4. Get checked in instantly!

DIRECT CHECK-IN LINK:
{$qr_url}

If you have any questions, please contact our event team.

Blessings,
Freedom Assembly Church Event Team
        ";
        
        echo "<span class='info'>📧 Sending QR code email to: {$target_email}</span><br>";
        echo "<span class='info'>📋 Subject: {$mail->Subject}</span><br>";
        echo "<span class='info'>🔗 Check-in URL: <a href='{$qr_url}' target='_blank'>{$qr_url}</a></span><br>";
        
        // Send the email
        $mail->send();
        
        echo "<span class='success'>✅ QR code email sent successfully using PHPMailer!</span><br>";
        
        // Update database to mark email as sent
        $stmt = $pdo->prepare("
            UPDATE member_qr_codes 
            SET email_sent = 1, email_sent_at = NOW() 
            WHERE qr_token = ?
        ");
        $stmt->execute([$qr_data['qr_token']]);
        
        echo "<span class='success'>✅ Database updated - email marked as sent</span><br>";
        
    } catch (Exception $e) {
        echo "<span class='error'>❌ Email sending failed: {$mail->ErrorInfo}</span><br>";
        throw $e;
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 5: Test QR Code</h2>";
    echo "<span class='info'>🧪 Testing the QR code check-in process...</span><br>";
    echo "<span class='info'>🔗 QR Check-in URL: <a href='{$qr_url}' target='_blank'>{$qr_url}</a></span><br>";
    echo "<span class='info'>📱 Click the link above to test the check-in process</span><br>";
    echo "</div>";
    
    echo "<div class='step' style='background:#e8f5e8;border-color:#28a745;'>";
    echo "<h2>🎉 QR Code Email Sent Successfully!</h2>";
    echo "<span class='success'>✅ QR code email sent to {$target_email}</span><br>";
    echo "<span class='success'>✅ Used your configured SMTP server: {$emailSettings['smtp_host']}</span><br>";
    echo "<span class='success'>✅ Professional HTML email with QR code</span><br>";
    echo "<span class='success'>✅ Direct check-in link included</span><br>";
    echo "<span class='success'>✅ Database records updated</span><br>";
    echo "<br>";
    echo "<strong>📧 What the member received:</strong><br>";
    echo "<span class='info'>• Beautiful HTML email with church branding</span><br>";
    echo "<span class='info'>• Personal QR code for instant check-in</span><br>";
    echo "<span class='info'>• Event details and instructions</span><br>";
    echo "<span class='info'>• Direct check-in link as backup</span><br>";
    echo "<span class='info'>• Professional formatting and styling</span><br>";
    echo "<br>";
    echo "<strong>📱 Next Steps:</strong><br>";
    echo "<span class='info'>1. Check {$target_email} for the QR code email</span><br>";
    echo "<span class='info'>2. Test the QR code check-in process</span><br>";
    echo "<span class='info'>3. Verify the mobile interface works properly</span><br>";
    echo "<span class='info'>4. The system is ready for production use!</span><br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='step' style='background:#ffe8e8;border-color:#dc3545;'>";
    echo "<h2>❌ Process Failed</h2>";
    echo "<span class='error'>Error: " . $e->getMessage() . "</span><br>";
    echo "<span class='error'>File: " . $e->getFile() . " Line: " . $e->getLine() . "</span>";
    echo "</div>";
}
?>

<script>
console.log('QR Code email sending with database SMTP completed');
</script>
