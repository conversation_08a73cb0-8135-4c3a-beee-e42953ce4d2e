<?php
/**
 * Gift Management Dashboard
 * 
 * Admin interface for managing gifts, viewing delivery status, and handling gift coordination
 */

require_once '../config.php';
require_once '../includes/EnhancedDonationNotifier.php';

// Check if user is logged in as admin
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Get organization information
$stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('organization_name', 'organization_type')");
$stmt->execute();
$org_settings = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $org_settings[$row['setting_key']] = $row['setting_value'];
}

$organization_name = $org_settings['organization_name'] ?? 'Church Management System';

// Handle actions
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'resend_notification') {
        $donation_id = filter_var($_POST['donation_id'], FILTER_VALIDATE_INT);
        if ($donation_id) {
            try {
                $notifier = new EnhancedDonationNotifier($pdo);
                $result = $notifier->sendDonationNotifications($donation_id);

                if ($result['success']) {
                    $message = "Notification resent successfully!";
                } else {
                    $error = "Failed to resend notification: " . ($result['error'] ?? 'Unknown error');
                }
            } catch (Exception $e) {
                $error = "Error sending notification: " . $e->getMessage();
            }
        } else {
            $error = "Invalid donation ID.";
        }
    } elseif ($action === 'update_delivery_status') {
        $gift_id = filter_var($_POST['gift_id'], FILTER_VALIDATE_INT);
        $status = filter_var($_POST['status'], FILTER_SANITIZE_FULL_SPECIAL_CHARS);

        if ($gift_id && in_array($status, ['pending', 'delivered'])) {
            $is_delivered = ($status === 'delivered') ? 1 : 0;

            if ($status === 'delivered') {
                $stmt = $pdo->prepare("UPDATE member_gifts SET is_delivered = ?, delivered_at = NOW() WHERE id = ?");
                $result = $stmt->execute([$is_delivered, $gift_id]);
            } else {
                $stmt = $pdo->prepare("UPDATE member_gifts SET is_delivered = ?, delivered_at = NULL WHERE id = ?");
                $result = $stmt->execute([$is_delivered, $gift_id]);
            }

            if ($result) {
                $message = "Delivery status updated successfully!";
            } else {
                $error = "Failed to update delivery status.";
            }
        } else {
            $error = "Invalid gift ID or status.";
        }
    }
}

// Get filter parameters
$filter_type = $_GET['type'] ?? 'all';
$filter_status = $_GET['status'] ?? 'all';
$search = $_GET['search'] ?? '';

// Build query - simplified for member_gifts table
$where_conditions = [];
$params = [];

if ($filter_type !== 'all') {
    $where_conditions[] = "mg.gift_type = ?";
    $params[] = $filter_type;
}

if ($filter_status !== 'all') {
    if ($filter_status === 'delivered') {
        $where_conditions[] = "mg.is_delivered = 1";
    } else {
        $where_conditions[] = "mg.is_delivered = 0";
    }
}

if ($search) {
    $where_conditions[] = "(sender.full_name LIKE ? OR sender.email LIKE ? OR recipient.full_name LIKE ? OR recipient.email LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get gifts with member details
$stmt = $pdo->prepare("
    SELECT mg.*,
           recipient.full_name as recipient_name,
           recipient.email as recipient_email,
           CASE
               WHEN mg.sender_id IS NULL THEN 'Admin Gift'
               WHEN mg.is_anonymous = 1 THEN 'Anonymous Donor'
               ELSE COALESCE(sender.full_name, 'Unknown Donor')
           END as donor_name,
           CASE
               WHEN mg.sender_id IS NULL THEN '<EMAIL>'
               WHEN mg.is_anonymous = 1 THEN 'No email provided'
               ELSE COALESCE(sender.email, 'No email provided')
           END as donor_email,
           mg.gift_title as donation_type,
           mg.gift_message as description,
           mg.is_delivered as delivery_status,
           mg.is_anonymous as anonymous_gift,
           0 as amount,
           'USD' as currency,
           '' as sender_organization,
           '' as sender_department
    FROM member_gifts mg
    LEFT JOIN members recipient ON mg.recipient_id = recipient.id
    LEFT JOIN members sender ON mg.sender_id = sender.id
    $where_clause
    ORDER BY mg.created_at DESC
    LIMIT 50
");
$stmt->execute($params);
$donations = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get summary statistics from member_gifts table
$stats_stmt = $pdo->prepare("
    SELECT
        COUNT(*) as total_donations,
        SUM(CASE WHEN gift_type = 'birthday_gift' OR gift_title LIKE '%birthday%' THEN 1 ELSE 0 END) as birthday_gifts,
        SUM(CASE WHEN is_delivered = 1 THEN 1 ELSE 0 END) as delivered,
        SUM(CASE WHEN is_delivered = 0 THEN 1 ELSE 0 END) as pending,
        0 as total_amount
    FROM member_gifts
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
");
$stats_stmt->execute();
$stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

// Ensure all stats have default values to prevent null errors
$stats = array_merge([
    'total_donations' => 0,
    'birthday_gifts' => 0,
    'delivered' => 0,
    'pending' => 0,
    'total_amount' => 0
], $stats ?: []);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gift Management - <?php echo htmlspecialchars($organization_name); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .gift-type-icon {
            width: 20px;
            height: 20px;
            display: inline-block;
            text-align: center;
        }
        .stats-card {
            border-left: 4px solid;
        }
        .stats-card.primary { border-left-color: #007bff; }
        .stats-card.success { border-left-color: #28a745; }
        .stats-card.warning { border-left-color: #ffc107; }
        .stats-card.info { border-left-color: #17a2b8; }
    </style>
<?php
$page_title = "Gift Management Dashboard";
$page_header = "Gift Management Dashboard";
$page_description = "Manage and track gifts, donations, and financial contributions";
include 'includes/header.php';
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
                        <a href="enhanced_donate.php" class="btn btn-primary" target="_blank">
                            <i class="bi bi-plus-circle"></i> New Donation
                        </a>
                        <a href="dashboard.php" class="btn btn-secondary">
                            <i class="bi bi-house"></i> Dashboard
                        </a>
                    </div>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stats-card primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title text-muted">Total Donations (30d)</h6>
                                        <h3 class="mb-0"><?php echo number_format($stats['total_donations'] ?? 0); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-heart-fill text-primary" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title text-muted">Birthday Gifts</h6>
                                        <h3 class="mb-0"><?php echo number_format($stats['birthday_gifts'] ?? 0); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-gift-fill text-warning" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title text-muted">Delivered</h6>
                                        <h3 class="mb-0"><?php echo number_format($stats['delivered'] ?? 0); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-check-circle-fill text-success" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card info">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title text-muted">Total Amount</h6>
                                        <h3 class="mb-0">$<?php echo number_format($stats['total_amount'] ?? 0, 2); ?></h3>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-currency-dollar text-info" style="font-size: 2rem;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="type" class="form-label">Donation Type</label>
                                <select class="form-select" id="type" name="type">
                                    <option value="all" <?php echo $filter_type === 'all' ? 'selected' : ''; ?>>All Types</option>
                                    <option value="general" <?php echo $filter_type === 'general' ? 'selected' : ''; ?>>General Donation</option>
                                    <option value="birthday_gift" <?php echo $filter_type === 'birthday_gift' ? 'selected' : ''; ?>>Birthday Gift</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">Delivery Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="all" <?php echo $filter_status === 'all' ? 'selected' : ''; ?>>All Status</option>
                                    <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="scheduled" <?php echo $filter_status === 'scheduled' ? 'selected' : ''; ?>>Scheduled</option>
                                    <option value="delivered" <?php echo $filter_status === 'delivered' ? 'selected' : ''; ?>>Delivered</option>
                                    <option value="failed" <?php echo $filter_status === 'failed' ? 'selected' : ''; ?>>Failed</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="search" class="form-label">Search</label>
                                <input type="text" class="form-control" id="search" name="search" value="<?php echo htmlspecialchars($search); ?>" placeholder="Search donor, recipient...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-search"></i> Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Donations Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Recent Donations & Gifts</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($donations)): ?>
                            <div class="text-center py-4">
                                <i class="bi bi-inbox" style="font-size: 3rem; color: #ccc;"></i>
                                <p class="text-muted mt-2">No donations found matching your criteria.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Type</th>
                                            <th>Donor</th>
                                            <th>Recipient</th>
                                            <th>Amount</th>
                                            <th>Gift Type</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($donations as $donation): ?>
                                            <tr>
                                                <td>
                                                    <strong>#<?php echo $donation['id']; ?></strong>
                                                </td>
                                                <td>
                                                    <?php if ($donation['donation_type'] === 'birthday_gift'): ?>
                                                        <span class="badge bg-warning">
                                                            <i class="bi bi-gift"></i> Birthday Gift
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge bg-primary">
                                                            <i class="bi bi-heart"></i> General
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($donation['donor_name'] ?? 'Unknown Donor'); ?></strong>
                                                        <br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($donation['donor_email'] ?? 'No email provided'); ?></small>
                                                        <?php if ($donation['sender_organization']): ?>
                                                            <br>
                                                            <small class="text-info">
                                                                <i class="bi bi-building"></i>
                                                                <?php echo htmlspecialchars($donation['sender_organization'] ?? ''); ?>
                                                                <?php if ($donation['sender_department']): ?>
                                                                    (<?php echo htmlspecialchars($donation['sender_department'] ?? ''); ?>)
                                                                <?php endif; ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php if ($donation['recipient_name']): ?>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($donation['recipient_name'] ?? 'Unknown Recipient'); ?></strong>
                                                            <?php if ($donation['anonymous_gift']): ?>
                                                                <br><small class="text-warning"><i class="bi bi-eye-slash"></i> Anonymous</small>
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php else: ?>
                                                        <span class="text-muted">N/A</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong><?php echo $donation['currency']; ?> <?php echo number_format($donation['amount'] ?? 0, 2); ?></strong>
                                                </td>
                                                <td>
                                                    <?php
                                                    $gift_icons = [
                                                        'monetary' => 'bi-cash-coin text-success',
                                                        'digital_card' => 'bi-card-image text-primary',
                                                        'digital_message' => 'bi-chat-heart text-info',
                                                        'gift_card' => 'bi-gift text-warning',
                                                        'physical_coordination' => 'bi-box text-secondary'
                                                    ];
                                                    $icon = $gift_icons[$donation['gift_type']] ?? 'bi-question-circle';
                                                    ?>
                                                    <span class="gift-type-icon">
                                                        <i class="bi <?php echo $icon; ?>"></i>
                                                    </span>
                                                    <?php echo ucfirst(str_replace('_', ' ', $donation['gift_type'])); ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $delivery_status = $donation['is_delivered'] ? 'delivered' : 'pending';
                                                    $status_classes = [
                                                        'pending' => 'bg-warning',
                                                        'scheduled' => 'bg-info',
                                                        'delivered' => 'bg-success',
                                                        'failed' => 'bg-danger'
                                                    ];
                                                    $status_class = $status_classes[$delivery_status] ?? 'bg-secondary';
                                                    ?>
                                                    <span class="badge status-badge <?php echo $status_class; ?>">
                                                        <?php echo ucfirst($delivery_status); ?>
                                                    </span>
                                                    <?php if ($donation['delivery_date']): ?>
                                                        <br><small class="text-muted">
                                                            <i class="bi bi-calendar"></i>
                                                            <?php echo date('M j, Y', strtotime($donation['delivery_date'])); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <?php echo date('M j, Y g:i A', strtotime($donation['created_at'])); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <?php if ($donation['donation_type'] === 'birthday_gift'): ?>
                                                            <form method="POST" style="display: inline;">
                                                                <input type="hidden" name="action" value="resend_notification">
                                                                <input type="hidden" name="donation_id" value="<?php echo $donation['id']; ?>">
                                                                <button type="submit" class="btn btn-outline-success"
                                                                        title="Resend Notification"
                                                                        onclick="return confirm('Resend notification for this gift?')">
                                                                    <i class="bi bi-envelope"></i>
                                                                </button>
                                                            </form>
                                                        <?php endif; ?>

                                                        <div class="dropdown">
                                                            <button class="btn btn-outline-secondary dropdown-toggle"
                                                                    type="button"
                                                                    id="dropdownMenuButton<?php echo $donation['id']; ?>"
                                                                    data-bs-toggle="dropdown"
                                                                    aria-expanded="false">
                                                                <i class="bi bi-gear"></i>
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton<?php echo $donation['id']; ?>">
                                                                <li>
                                                                    <form method="POST" style="display: inline;">
                                                                        <input type="hidden" name="action" value="update_delivery_status">
                                                                        <input type="hidden" name="gift_id" value="<?php echo $donation['id']; ?>">
                                                                        <input type="hidden" name="status" value="delivered">
                                                                        <button type="submit" class="dropdown-item">
                                                                            <i class="bi bi-check-circle text-success"></i> Mark Delivered
                                                                        </button>
                                                                    </form>
                                                                </li>
                                                                <li>
                                                                    <form method="POST" style="display: inline;">
                                                                        <input type="hidden" name="action" value="update_delivery_status">
                                                                        <input type="hidden" name="gift_id" value="<?php echo $donation['id']; ?>">
                                                                        <input type="hidden" name="status" value="pending">
                                                                        <button type="submit" class="dropdown-item">
                                                                            <i class="bi bi-arrow-clockwise text-warning"></i> Mark Pending
                                                                        </button>
                                                                    </form>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

<style>
/* Fix dropdown positioning in tables */
.table td {
    position: relative;
}
.dropdown-menu {
    z-index: 1050 !important;
    position: absolute !important;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Debug: Check if Bootstrap is loaded
    console.log('Bootstrap version:', typeof bootstrap !== 'undefined' ? bootstrap.Tooltip.VERSION : 'Not loaded');

    // Initialize all dropdowns manually with proper configuration
    const dropdownElements = document.querySelectorAll('[data-bs-toggle="dropdown"]');
    console.log('Found dropdown elements:', dropdownElements.length);

    dropdownElements.forEach(function(element, index) {
        console.log('Initializing dropdown', index, element);
        try {
            // Create dropdown with specific options
            const dropdown = new bootstrap.Dropdown(element, {
                boundary: 'viewport',
                display: 'dynamic'
            });
            console.log('Successfully initialized dropdown', index);

            // Add manual click handler as backup
            element.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Manual dropdown toggle for', index);
                dropdown.toggle();
            });

        } catch (error) {
            console.error('Error initializing dropdown', index, error);

            // Fallback: manual toggle
            element.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const menu = element.nextElementSibling;
                if (menu && menu.classList.contains('dropdown-menu')) {
                    menu.classList.toggle('show');
                    element.setAttribute('aria-expanded', menu.classList.contains('show'));
                }
            });
        }
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                menu.classList.remove('show');
                const toggle = menu.previousElementSibling;
                if (toggle) {
                    toggle.setAttribute('aria-expanded', 'false');
                }
            });
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
