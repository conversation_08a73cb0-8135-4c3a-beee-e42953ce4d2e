<?php
session_start();
require_once '../config.php';
require_once 'includes/route_protection.php';

// Protect this page - Admins and Event Coordinators can access
protectAdminRoute();

$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    // Show event selection page instead of redirecting
    $page_title = 'Real-Time Dashboard - Select Event';
    $page_header = 'Real-Time Dashboard';
    $page_description = 'Select an event to monitor in real-time';
    include 'includes/header.php';

    echo '<div class="container-fluid">';
    echo '<div class="row">';
    echo '<div class="col-12">';
    echo '<div class="card">';
    echo '<div class="card-header">';
    echo '<h5><i class="bi bi-broadcast"></i> Select Event for Real-Time Monitoring</h5>';
    echo '</div>';
    echo '<div class="card-body">';

    // Get recent events
    $stmt = $pdo->query("SELECT id, title, event_date, status FROM events WHERE status = 'published' ORDER BY event_date DESC LIMIT 10");
    $events = $stmt->fetchAll();

    if (empty($events)) {
        echo '<div class="alert alert-info">';
        echo '<h6>No Events Available</h6>';
        echo '<p>There are no published events available for real-time monitoring.</p>';
        echo '<a href="' . admin_url_for('events.php') . '" class="btn btn-primary">Create New Event</a>';
        echo '</div>';
    } else {
        echo '<div class="row">';
        foreach ($events as $event) {
            echo '<div class="col-md-6 col-lg-4 mb-3">';
            echo '<div class="card h-100">';
            echo '<div class="card-body">';
            echo '<h6 class="card-title">' . htmlspecialchars($event['title']) . '</h6>';
            echo '<p class="card-text">';
            echo '<small class="text-muted">' . date('M j, Y g:i A', strtotime($event['event_date'])) . '</small><br>';
            echo '<span class="badge bg-success">' . ucfirst($event['status']) . '</span>';
            echo '</p>';
            echo '<a href="?event_id=' . $event['id'] . '" class="btn btn-primary btn-sm">';
            echo '<i class="bi bi-broadcast"></i> Monitor Event';
            echo '</a>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
        }
        echo '</div>';
    }

    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    echo '</div>';

    include 'includes/footer.php';
    exit();
}

// Get event details
$stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
$stmt->execute([$event_id]);
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    header('Location: ' . admin_url_for('events.php'));
    exit();
}

// Create real-time tracking tables if they don't exist
try {
    // First, check if the table exists and has the correct structure
    $stmt = $pdo->query("SHOW TABLES LIKE 'realtime_activity_log'");
    $table_exists = $stmt->rowCount() > 0;

    if ($table_exists) {
        // Check if session_id column exists
        $stmt = $pdo->query("SHOW COLUMNS FROM realtime_activity_log LIKE 'session_id'");
        $column_exists = $stmt->rowCount() > 0;

        if (!$column_exists) {
            // Add missing session_id column
            $pdo->exec("ALTER TABLE realtime_activity_log ADD COLUMN session_id INT(11) DEFAULT NULL AFTER event_id");
            $pdo->exec("ALTER TABLE realtime_activity_log ADD INDEX idx_session_id (session_id)");
        }

        // Check if staff_user_id column exists
        $stmt = $pdo->query("SHOW COLUMNS FROM realtime_activity_log LIKE 'staff_user_id'");
        $staff_column_exists = $stmt->rowCount() > 0;

        if (!$staff_column_exists) {
            // Add missing staff_user_id column
            $pdo->exec("ALTER TABLE realtime_activity_log ADD COLUMN staff_user_id INT(11) DEFAULT NULL");
        }

        // Check if activity_timestamp column exists
        $stmt = $pdo->query("SHOW COLUMNS FROM realtime_activity_log LIKE 'activity_timestamp'");
        $timestamp_column_exists = $stmt->rowCount() > 0;

        if (!$timestamp_column_exists) {
            // Add missing activity_timestamp column
            $pdo->exec("ALTER TABLE realtime_activity_log ADD COLUMN activity_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
            $pdo->exec("ALTER TABLE realtime_activity_log ADD INDEX idx_timestamp (activity_timestamp)");
        }

        // Check if activity_data column exists and is JSON type
        $stmt = $pdo->query("SHOW COLUMNS FROM realtime_activity_log LIKE 'activity_data'");
        $data_column = $stmt->fetch();

        if (!$data_column) {
            // Add missing activity_data column
            $pdo->exec("ALTER TABLE realtime_activity_log ADD COLUMN activity_data TEXT");
        } elseif (strpos(strtolower($data_column['Type']), 'json') === false && strpos(strtolower($data_column['Type']), 'text') === false) {
            // Convert to TEXT if it's not JSON or TEXT
            $pdo->exec("ALTER TABLE realtime_activity_log MODIFY COLUMN activity_data TEXT");
        }
    } else {
        // Create the table with proper structure
        $pdo->exec("
            CREATE TABLE realtime_activity_log (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                event_id INT(11) NOT NULL,
                session_id INT(11) DEFAULT NULL,
                activity_type ENUM('attendance_update', 'session_start', 'session_end', 'bulk_operation', 'staff_action') NOT NULL,
                activity_data JSON,
                staff_user_id INT(11) DEFAULT NULL,
                activity_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_event_id (event_id),
                INDEX idx_session_id (session_id),
                INDEX idx_timestamp (activity_timestamp)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ");
    }
    
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS realtime_dashboard_state (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            event_id INT(11) NOT NULL,
            state_key VARCHAR(255) NOT NULL,
            state_value JSON,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_event_state (event_id, state_key),
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
} catch (PDOException $e) {
    error_log("Error creating real-time tables: " . $e->getMessage());
}

// Function to log real-time activity
function logRealtimeActivity($pdo, $event_id, $activity_type, $activity_data, $session_id = null, $staff_user_id = null) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO realtime_activity_log (event_id, session_id, activity_type, activity_data, staff_user_id)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([$event_id, $session_id, $activity_type, json_encode($activity_data), $staff_user_id]);
        
        // Update dashboard state
        updateDashboardState($pdo, $event_id);
    } catch (Exception $e) {
        error_log("Error logging real-time activity: " . $e->getMessage());
    }
}

// Function to update dashboard state
function updateDashboardState($pdo, $event_id) {
    try {
        // Get current session statistics
        $stmt = $pdo->prepare("
            SELECT 
                es.id,
                es.session_title,
                es.start_datetime,
                es.end_datetime,
                COUNT(sa.id) as total_registered,
                COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as total_attended,
                COUNT(CASE WHEN sa.attendance_status = 'no_show' THEN 1 END) as total_no_show,
                ROUND((COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) / 
                       NULLIF(COUNT(sa.id), 0)) * 100, 1) as attendance_rate,
                CASE 
                    WHEN es.start_datetime > NOW() THEN 'upcoming'
                    WHEN es.start_datetime <= NOW() AND es.end_datetime >= NOW() THEN 'active'
                    ELSE 'completed'
                END as session_status
            FROM event_sessions es
            LEFT JOIN session_attendance sa ON es.id = sa.session_id
            WHERE es.event_id = ? AND es.status = 'active'
            GROUP BY es.id
            ORDER BY es.start_datetime
        ");
        $stmt->execute([$event_id]);
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get event-level statistics
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(DISTINCT CASE WHEN er.status = 'attending' THEN er.user_id END) as total_members_rsvp,
                COUNT(DISTINCT CASE WHEN erg.status = 'attending' THEN erg.id END) as total_guests_rsvp,
                COUNT(DISTINCT CASE WHEN er.actually_attended = 1 THEN er.user_id END) as total_members_attended,
                COUNT(DISTINCT CASE WHEN erg.actually_attended = 1 THEN erg.id END) as total_guests_attended
            FROM events e
            LEFT JOIN event_rsvps er ON e.id = er.event_id
            LEFT JOIN event_rsvps_guests erg ON e.id = erg.event_id
            WHERE e.id = ?
        ");
        $stmt->execute([$event_id]);
        $event_stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Calculate real-time metrics
        $total_rsvp = $event_stats['total_members_rsvp'] + $event_stats['total_guests_rsvp'];
        $total_attended = $event_stats['total_members_attended'] + $event_stats['total_guests_attended'];
        $attendance_rate = $total_rsvp > 0 ? round(($total_attended / $total_rsvp) * 100, 1) : 0;
        
        $active_sessions = array_filter($sessions, function($s) { return $s['session_status'] === 'active'; });
        $upcoming_sessions = array_filter($sessions, function($s) { return $s['session_status'] === 'upcoming'; });
        
        $dashboard_state = [
            'event_stats' => [
                'total_rsvp' => $total_rsvp,
                'total_attended' => $total_attended,
                'attendance_rate' => $attendance_rate,
                'active_sessions_count' => count($active_sessions),
                'upcoming_sessions_count' => count($upcoming_sessions)
            ],
            'sessions' => $sessions,
            'last_updated' => date('Y-m-d H:i:s'),
            'timestamp' => time()
        ];
        
        // Store the state
        $stmt = $pdo->prepare("
            INSERT INTO realtime_dashboard_state (event_id, state_key, state_value)
            VALUES (?, 'dashboard_stats', ?)
            ON DUPLICATE KEY UPDATE state_value = VALUES(state_value)
        ");
        $stmt->execute([$event_id, json_encode($dashboard_state)]);
        
    } catch (Exception $e) {
        error_log("Error updating dashboard state: " . $e->getMessage());
    }
}

// Handle AJAX requests for real-time updates
if (isset($_GET['ajax']) && $_GET['ajax'] === 'get_updates') {
    header('Content-Type: application/json');
    
    $last_timestamp = (int)($_GET['last_timestamp'] ?? 0);
    
    try {
        // Get latest dashboard state
        $stmt = $pdo->prepare("
            SELECT state_value, UNIX_TIMESTAMP(last_updated) as timestamp
            FROM realtime_dashboard_state 
            WHERE event_id = ? AND state_key = 'dashboard_stats'
        ");
        $stmt->execute([$event_id]);
        $state = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($state && $state['timestamp'] > $last_timestamp) {
            // Get recent activity
            $stmt = $pdo->prepare("
                SELECT 
                    ral.*,
                    es.session_title,
                    m.full_name as staff_name
                FROM realtime_activity_log ral
                LEFT JOIN event_sessions es ON ral.session_id = es.id
                LEFT JOIN members m ON ral.staff_user_id = m.id
                WHERE ral.event_id = ? AND UNIX_TIMESTAMP(ral.activity_timestamp) > ?
                ORDER BY ral.activity_timestamp DESC
                LIMIT 10
            ");
            $stmt->execute([$event_id, $last_timestamp]);
            $recent_activity = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'has_updates' => true,
                'dashboard_state' => json_decode($state['state_value'], true),
                'recent_activity' => $recent_activity,
                'timestamp' => $state['timestamp']
            ]);
        } else {
            echo json_encode([
                'success' => true,
                'has_updates' => false,
                'timestamp' => $last_timestamp
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    exit();
}

// Handle attendance updates via AJAX
if (isset($_POST['ajax']) && $_POST['ajax'] === 'update_attendance') {
    header('Content-Type: application/json');
    
    $session_id = (int)($_POST['session_id'] ?? 0);
    $attendee_id = $_POST['attendee_id'] ?? '';
    $new_status = $_POST['new_status'] ?? '';
    
    try {
        $pdo->beginTransaction();
        
        // Update attendance (reuse logic from existing system)
        // ... attendance update logic here ...
        
        // Log the activity
        logRealtimeActivity($pdo, $event_id, 'attendance_update', [
            'session_id' => $session_id,
            'attendee_id' => $attendee_id,
            'old_status' => $_POST['old_status'] ?? '',
            'new_status' => $new_status,
            'updated_by' => $_SESSION['admin_id'] ?? null
        ], $session_id, $_SESSION['admin_id'] ?? null);
        
        $pdo->commit();
        
        echo json_encode(['success' => true]);
    } catch (Exception $e) {
        $pdo->rollback();
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit();
}

// Initialize dashboard state
updateDashboardState($pdo, $event_id);

// Get current dashboard state
$stmt = $pdo->prepare("
    SELECT state_value, UNIX_TIMESTAMP(last_updated) as timestamp
    FROM realtime_dashboard_state 
    WHERE event_id = ? AND state_key = 'dashboard_stats'
");
$stmt->execute([$event_id]);
$dashboard_state = $stmt->fetch(PDO::FETCH_ASSOC);

$current_state = $dashboard_state ? json_decode($dashboard_state['state_value'], true) : null;
$last_timestamp = $dashboard_state['timestamp'] ?? time();

// Get recent activity for initial load
try {
    $stmt = $pdo->prepare("
        SELECT
            ral.*,
            es.session_title,
            m.full_name as staff_name
        FROM realtime_activity_log ral
        LEFT JOIN event_sessions es ON ral.session_id = es.id
        LEFT JOIN members m ON ral.staff_user_id = m.id
        WHERE ral.event_id = ?
        ORDER BY ral.activity_timestamp DESC
        LIMIT 20
    ");
} catch (PDOException $e) {
    // Fallback query if there are still column issues
    try {
        $stmt = $pdo->prepare("
            SELECT
                ral.*,
                NULL as session_title,
                NULL as staff_name
            FROM realtime_activity_log ral
            WHERE ral.event_id = ?
            ORDER BY ral.id DESC
            LIMIT 20
        ");
    } catch (PDOException $e2) {
        // Ultimate fallback - just get basic data
        $stmt = $pdo->prepare("
            SELECT
                ral.id,
                ral.event_id,
                ral.activity_type,
                NULL as session_title,
                NULL as staff_name,
                NOW() as activity_timestamp
            FROM realtime_activity_log ral
            WHERE ral.event_id = ?
            ORDER BY ral.id DESC
            LIMIT 20
        ");
    }
}
$stmt->execute([$event_id]);
$recent_activity = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'Real-Time Event Dashboard';
include 'includes/header.php';
?>

<style>
.realtime-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
}

.live-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    align-items: center;
    background: rgba(255,255,255,0.2);
    padding: 0.5rem 1rem;
    border-radius: 25px;
}

.live-dot {
    width: 10px;
    height: 10px;
    background: #28a745;
    border-radius: 50%;
    margin-right: 0.5rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.metric-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #28a745);
}

.metric-card.updating {
    transform: scale(1.02);
    box-shadow: 0 6px 20px rgba(0,123,255,0.3);
}

.session-status-card {
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.2s;
    border-left: 4px solid;
}

.session-status-card.active {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
}

.session-status-card.upcoming {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
}

.session-status-card.completed {
    border-left-color: #6c757d;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.activity-feed {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    padding: 0.75rem;
    border-bottom: 1px solid #eee;
    transition: all 0.2s;
}

.activity-item:hover {
    background-color: #f8f9fa;
}

.activity-item.new {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    animation: slideIn 0.5s ease;
}

@keyframes slideIn {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.progress-ring {
    width: 80px;
    height: 80px;
    margin: 0 auto;
}

.progress-ring-circle {
    stroke: #e9ecef;
    stroke-width: 8;
    fill: transparent;
    r: 30;
    cx: 40;
    cy: 40;
}

.progress-ring-progress {
    stroke: #007bff;
    stroke-width: 8;
    stroke-linecap: round;
    fill: transparent;
    r: 30;
    cx: 40;
    cy: 40;
    stroke-dasharray: 188.5;
    stroke-dashoffset: 188.5;
    transition: stroke-dashoffset 0.5s ease;
}

.connection-status {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 500;
}

.connection-status.connected {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.connection-status.disconnected {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.auto-refresh-controls {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 2rem;
}
</style>

<div class="container-fluid">
    <!-- Connection Status -->
    <div id="connectionStatus" class="connection-status connected">
        <i class="bi bi-wifi"></i> Connected - Live Updates
    </div>

    <div class="row">
        <div class="col-12">
            <div class="realtime-header">
                <div class="live-indicator">
                    <div class="live-dot"></div>
                    <span>LIVE</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="bi bi-broadcast"></i> Real-Time Event Dashboard</h1>
                        <p class="mb-0">
                            <strong><?php echo htmlspecialchars($event['title']); ?></strong> •
                            <?php echo date('M j, Y', strtotime($event['event_date'])); ?>
                        </p>
                    </div>
                    <div>
                        <span id="lastUpdated" class="badge bg-light text-dark">
                            Last updated: <span id="updateTime">Just now</span>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Auto-Refresh Controls -->
            <div class="auto-refresh-controls">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                            <label class="form-check-label" for="autoRefresh">
                                <strong>Auto-Refresh</strong> (Updates every 5 seconds)
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <button class="btn btn-outline-primary btn-sm" onclick="forceRefresh()">
                            <i class="bi bi-arrow-clockwise"></i> Refresh Now
                        </button>
                        <a href="multi_session_dashboard.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-arrow-left"></i> Standard Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <!-- Real-Time Metrics -->
            <div class="row mb-4" id="metricsContainer">
                <div class="col-md-3">
                    <div class="metric-card" id="totalRsvpCard">
                        <div class="progress-ring">
                            <svg class="progress-ring">
                                <circle class="progress-ring-circle"></circle>
                                <circle class="progress-ring-progress" id="attendanceProgress"></circle>
                            </svg>
                        </div>
                        <h3 id="totalRsvp" class="text-primary mt-2">
                            <?php echo $current_state['event_stats']['total_rsvp'] ?? 0; ?>
                        </h3>
                        <p class="mb-0">Total RSVP</p>
                        <small class="text-muted">
                            <span id="totalAttended"><?php echo $current_state['event_stats']['total_attended'] ?? 0; ?></span> attended
                            (<span id="attendanceRate"><?php echo $current_state['event_stats']['attendance_rate'] ?? 0; ?></span>%)
                        </small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card" id="activeSessionsCard">
                        <h3 id="activeSessions" class="text-success">
                            <?php echo $current_state['event_stats']['active_sessions_count'] ?? 0; ?>
                        </h3>
                        <p class="mb-0">Active Sessions</p>
                        <small class="text-muted">Currently running</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card" id="upcomingSessionsCard">
                        <h3 id="upcomingSessions" class="text-warning">
                            <?php echo $current_state['event_stats']['upcoming_sessions_count'] ?? 0; ?>
                        </h3>
                        <p class="mb-0">Upcoming Sessions</p>
                        <small class="text-muted">Starting soon</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card" id="totalSessionsCard">
                        <h3 id="totalSessions" class="text-info">
                            <?php echo count($current_state['sessions'] ?? []); ?>
                        </h3>
                        <p class="mb-0">Total Sessions</p>
                        <small class="text-muted">For this event</small>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Live Session Status -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-calendar-event"></i> Live Session Status
                                <span class="badge bg-primary ms-2" id="sessionUpdateBadge">Real-time</span>
                            </h5>
                        </div>
                        <div class="card-body" id="sessionStatusContainer">
                            <?php if ($current_state && !empty($current_state['sessions'])): ?>
                                <?php foreach ($current_state['sessions'] as $session): ?>
                                    <div class="session-status-card <?php echo $session['session_status']; ?>"
                                         data-session-id="<?php echo $session['id']; ?>">
                                        <div class="row align-items-center">
                                            <div class="col-md-6">
                                                <h6 class="mb-1"><?php echo htmlspecialchars($session['session_title']); ?></h6>
                                                <small class="text-muted">
                                                    <?php echo date('M j, g:i A', strtotime($session['start_datetime'])); ?>
                                                    <?php if ($session['session_status'] === 'active'): ?>
                                                        <span class="badge bg-success ms-2">LIVE NOW</span>
                                                    <?php elseif ($session['session_status'] === 'upcoming'): ?>
                                                        <span class="badge bg-warning ms-2">UPCOMING</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary ms-2">COMPLETED</span>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                            <div class="col-md-3 text-center">
                                                <div class="attendance-stats">
                                                    <h5 class="mb-0">
                                                        <span class="session-attended"><?php echo $session['total_attended']; ?></span>/<span class="session-registered"><?php echo $session['total_registered']; ?></span>
                                                    </h5>
                                                    <small class="text-muted">Attended/Registered</small>
                                                </div>
                                            </div>
                                            <div class="col-md-3 text-end">
                                                <div class="progress mb-1" style="height: 8px;">
                                                    <div class="progress-bar session-progress"
                                                         style="width: <?php echo $session['attendance_rate']; ?>%"
                                                         data-rate="<?php echo $session['attendance_rate']; ?>"></div>
                                                </div>
                                                <small class="text-muted session-rate"><?php echo $session['attendance_rate']; ?>% attendance</small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-calendar-x" style="font-size: 3rem;"></i>
                                    <h6 class="mt-2">No sessions found</h6>
                                    <p>Sessions will appear here when they are created</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Live Activity Feed -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-activity"></i> Live Activity Feed
                                <span class="badge bg-success ms-2" id="activityBadge">0 new</span>
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="activity-feed" id="activityFeed">
                                <?php foreach ($recent_activity as $activity): ?>
                                    <div class="activity-item" data-timestamp="<?php echo strtotime($activity['activity_timestamp']); ?>">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <small class="text-muted">
                                                    <?php echo date('g:i A', strtotime($activity['activity_timestamp'])); ?>
                                                </small>
                                                <p class="mb-1">
                                                    <?php
                                                    $data = json_decode($activity['activity_data'], true);
                                                    switch ($activity['activity_type']) {
                                                        case 'attendance_update':
                                                            echo "<strong>Attendance Updated</strong>";
                                                            if ($activity['session_title']) {
                                                                echo "<br><small class='text-muted'>{$activity['session_title']}</small>";
                                                            }
                                                            break;
                                                        case 'bulk_operation':
                                                            echo "<strong>Bulk Operation</strong>";
                                                            echo "<br><small class='text-muted'>Multiple records updated</small>";
                                                            break;
                                                        case 'session_start':
                                                            echo "<strong>Session Started</strong>";
                                                            if ($activity['session_title']) {
                                                                echo "<br><small class='text-muted'>{$activity['session_title']}</small>";
                                                            }
                                                            break;
                                                        default:
                                                            echo "<strong>" . ucfirst(str_replace('_', ' ', $activity['activity_type'])) . "</strong>";
                                                    }
                                                    ?>
                                                </p>
                                                <?php if ($activity['staff_name']): ?>
                                                    <small class="text-muted">by <?php echo htmlspecialchars($activity['staff_name']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <?php
                                                $icon = '';
                                                switch ($activity['activity_type']) {
                                                    case 'attendance_update':
                                                        $icon = 'bi-person-check';
                                                        break;
                                                    case 'bulk_operation':
                                                        $icon = 'bi-check2-all';
                                                        break;
                                                    case 'session_start':
                                                        $icon = 'bi-play-circle';
                                                        break;
                                                    default:
                                                        $icon = 'bi-activity';
                                                }
                                                ?>
                                                <i class="bi <?php echo $icon; ?> text-muted"></i>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
class RealtimeDashboard {
    constructor(eventId) {
        this.eventId = eventId;
        this.lastTimestamp = <?php echo $last_timestamp; ?>;
        this.autoRefreshEnabled = true;
        this.refreshInterval = null;
        this.connectionStatus = 'connected';
        this.newActivityCount = 0;

        this.init();
    }

    init() {
        this.startAutoRefresh();
        this.bindEvents();
        this.updateProgressRing();
    }

    bindEvents() {
        document.getElementById('autoRefresh').addEventListener('change', (e) => {
            this.autoRefreshEnabled = e.target.checked;
            if (this.autoRefreshEnabled) {
                this.startAutoRefresh();
            } else {
                this.stopAutoRefresh();
            }
        });

        // Visibility API to pause updates when tab is not active
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopAutoRefresh();
            } else if (this.autoRefreshEnabled) {
                this.startAutoRefresh();
                this.fetchUpdates(); // Immediate update when tab becomes active
            }
        });
    }

    startAutoRefresh() {
        this.stopAutoRefresh(); // Clear any existing interval
        this.refreshInterval = setInterval(() => {
            this.fetchUpdates();
        }, 5000); // Update every 5 seconds
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    async fetchUpdates() {
        try {
            const response = await fetch(`?event_id=${this.eventId}&ajax=get_updates&last_timestamp=${this.lastTimestamp}`);
            const data = await response.json();

            if (data.success) {
                this.updateConnectionStatus('connected');

                if (data.has_updates) {
                    this.updateDashboard(data.dashboard_state);
                    this.updateActivityFeed(data.recent_activity);
                    this.lastTimestamp = data.timestamp;
                    this.updateLastUpdatedTime();
                }
            } else {
                console.error('Update fetch failed:', data.error);
                this.updateConnectionStatus('disconnected');
            }
        } catch (error) {
            console.error('Network error:', error);
            this.updateConnectionStatus('disconnected');
        }
    }

    updateDashboard(state) {
        if (!state) return;

        // Update metrics with animation
        this.animateMetricUpdate('totalRsvp', state.event_stats.total_rsvp);
        this.animateMetricUpdate('totalAttended', state.event_stats.total_attended);
        this.animateMetricUpdate('attendanceRate', state.event_stats.attendance_rate + '%');
        this.animateMetricUpdate('activeSessions', state.event_stats.active_sessions_count);
        this.animateMetricUpdate('upcomingSessions', state.event_stats.upcoming_sessions_count);
        this.animateMetricUpdate('totalSessions', state.sessions.length);

        // Update progress ring
        this.updateProgressRing(state.event_stats.attendance_rate);

        // Update session status cards
        this.updateSessionStatus(state.sessions);
    }

    animateMetricUpdate(elementId, newValue) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const currentValue = element.textContent;
        if (currentValue !== newValue.toString()) {
            // Add update animation
            const card = element.closest('.metric-card');
            if (card) {
                card.classList.add('updating');
                setTimeout(() => card.classList.remove('updating'), 300);
            }

            element.textContent = newValue;
        }
    }

    updateProgressRing(percentage = 0) {
        const progressElement = document.getElementById('attendanceProgress');
        if (!progressElement) return;

        const circumference = 2 * Math.PI * 30; // radius = 30
        const offset = circumference - (percentage / 100) * circumference;

        progressElement.style.strokeDashoffset = offset;

        // Update color based on percentage
        if (percentage >= 80) {
            progressElement.style.stroke = '#28a745'; // Green
        } else if (percentage >= 60) {
            progressElement.style.stroke = '#ffc107'; // Yellow
        } else {
            progressElement.style.stroke = '#dc3545'; // Red
        }
    }

    updateSessionStatus(sessions) {
        sessions.forEach(session => {
            const card = document.querySelector(`[data-session-id="${session.id}"]`);
            if (!card) return;

            // Update attendance numbers
            const attendedElement = card.querySelector('.session-attended');
            const registeredElement = card.querySelector('.session-registered');
            const rateElement = card.querySelector('.session-rate');
            const progressBar = card.querySelector('.session-progress');

            if (attendedElement) attendedElement.textContent = session.total_attended;
            if (registeredElement) registeredElement.textContent = session.total_registered;
            if (rateElement) rateElement.textContent = session.attendance_rate + '% attendance';
            if (progressBar) {
                progressBar.style.width = session.attendance_rate + '%';
                progressBar.setAttribute('data-rate', session.attendance_rate);
            }

            // Update session status class
            card.className = `session-status-card ${session.session_status}`;
        });
    }

    updateActivityFeed(activities) {
        if (!activities || activities.length === 0) return;

        const feed = document.getElementById('activityFeed');
        const existingTimestamps = new Set();

        // Get existing activity timestamps
        feed.querySelectorAll('.activity-item').forEach(item => {
            existingTimestamps.add(parseInt(item.dataset.timestamp));
        });

        let newCount = 0;

        // Add new activities
        activities.forEach(activity => {
            const timestamp = Math.floor(new Date(activity.activity_timestamp).getTime() / 1000);

            if (!existingTimestamps.has(timestamp)) {
                const activityElement = this.createActivityElement(activity, timestamp);
                activityElement.classList.add('new');
                feed.insertBefore(activityElement, feed.firstChild);
                newCount++;

                // Remove 'new' class after animation
                setTimeout(() => activityElement.classList.remove('new'), 500);
            }
        });

        // Update new activity badge
        if (newCount > 0) {
            this.newActivityCount += newCount;
            document.getElementById('activityBadge').textContent = `${this.newActivityCount} new`;

            // Reset counter after 10 seconds
            setTimeout(() => {
                this.newActivityCount = 0;
                document.getElementById('activityBadge').textContent = '0 new';
            }, 10000);
        }

        // Keep only last 20 activities
        const items = feed.querySelectorAll('.activity-item');
        if (items.length > 20) {
            for (let i = 20; i < items.length; i++) {
                items[i].remove();
            }
        }
    }

    createActivityElement(activity, timestamp) {
        const div = document.createElement('div');
        div.className = 'activity-item';
        div.dataset.timestamp = timestamp;

        const data = JSON.parse(activity.activity_data || '{}');
        let activityText = '';
        let icon = 'bi-activity';

        switch (activity.activity_type) {
            case 'attendance_update':
                activityText = '<strong>Attendance Updated</strong>';
                icon = 'bi-person-check';
                break;
            case 'bulk_operation':
                activityText = '<strong>Bulk Operation</strong>';
                icon = 'bi-check2-all';
                break;
            case 'session_start':
                activityText = '<strong>Session Started</strong>';
                icon = 'bi-play-circle';
                break;
            default:
                activityText = '<strong>' + activity.activity_type.replace('_', ' ') + '</strong>';
        }

        if (activity.session_title) {
            activityText += `<br><small class='text-muted'>${activity.session_title}</small>`;
        }

        div.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <small class="text-muted">${new Date(activity.activity_timestamp).toLocaleTimeString()}</small>
                    <p class="mb-1">${activityText}</p>
                    ${activity.staff_name ? `<small class="text-muted">by ${activity.staff_name}</small>` : ''}
                </div>
                <div>
                    <i class="bi ${icon} text-muted"></i>
                </div>
            </div>
        `;

        return div;
    }

    updateConnectionStatus(status) {
        if (this.connectionStatus === status) return;

        this.connectionStatus = status;
        const statusElement = document.getElementById('connectionStatus');

        if (status === 'connected') {
            statusElement.className = 'connection-status connected';
            statusElement.innerHTML = '<i class="bi bi-wifi"></i> Connected - Live Updates';
        } else {
            statusElement.className = 'connection-status disconnected';
            statusElement.innerHTML = '<i class="bi bi-wifi-off"></i> Disconnected - Retrying...';
        }
    }

    updateLastUpdatedTime() {
        document.getElementById('updateTime').textContent = new Date().toLocaleTimeString();
    }

    forceRefresh() {
        this.fetchUpdates();
    }
}

// Initialize dashboard
const dashboard = new RealtimeDashboard(<?php echo $event_id; ?>);

// Global function for force refresh button
function forceRefresh() {
    dashboard.forceRefresh();
}

// Handle page unload
window.addEventListener('beforeunload', () => {
    dashboard.stopAutoRefresh();
});
</script>

<!-- WebSocket Client -->
<script src="js/realtime-websocket-client.js"></script>
<script>
// Enhanced Real-Time Dashboard with WebSocket Support
class EnhancedRealtimeDashboard extends RealtimeDashboard {
    constructor(eventId) {
        super(eventId);
        this.wsClient = null;
        this.isWebSocketEnabled = true;
        this.initializeWebSocket();
    }

    initializeWebSocket() {
        if (!this.isWebSocketEnabled || typeof ChurchEventWebSocketClient === 'undefined') {
            console.log('WebSocket disabled or not available, using polling');
            return;
        }

        try {
            this.wsClient = new ChurchEventWebSocketClient({
                serverUrl: 'ws://localhost:8080',
                debug: true,
                autoReconnect: true
            });

            // Connection events
            this.wsClient.on('connected', () => {
                console.log('WebSocket connected');
                this.updateConnectionStatus('connected');
                this.stopAutoRefresh(); // Stop polling when WebSocket is active

                // Authenticate with current user
                const userId = <?php echo $_SESSION['admin_id'] ?? 'null'; ?>;
                const authToken = 'session_token'; // In production, use proper token

                if (userId) {
                    this.wsClient.authenticate(userId, authToken)
                        .then(() => {
                            console.log('Authenticated successfully');
                            return this.wsClient.joinEvent(<?php echo $event_id; ?>);
                        })
                        .then(() => {
                            console.log('Joined event successfully');
                            this.updateConnectionStatus('websocket_active');
                        })
                        .catch(error => {
                            console.error('Authentication failed:', error);
                            this.fallbackToPolling();
                        });
                }
            });

            this.wsClient.on('disconnected', () => {
                console.log('WebSocket disconnected');
                this.updateConnectionStatus('disconnected');
                this.fallbackToPolling();
            });

            this.wsClient.on('error', (error) => {
                console.error('WebSocket error:', error);
                this.updateConnectionStatus('error');
                this.fallbackToPolling();
            });

            // Real-time event handlers
            this.wsClient.on('attendance_updated', (data) => {
                this.handleRealtimeAttendanceUpdate(data);
            });

            this.wsClient.on('session_status_updated', (data) => {
                this.handleRealtimeSessionUpdate(data);
            });

            this.wsClient.on('staff_joined', (data) => {
                this.handleStaffJoined(data);
            });

            this.wsClient.on('staff_disconnected', (data) => {
                this.handleStaffDisconnected(data);
            });

            this.wsClient.on('event_status', (data) => {
                this.handleRealtimeEventStatus(data);
            });

            // Connect to WebSocket
            this.wsClient.connect().catch(error => {
                console.error('Failed to connect to WebSocket:', error);
                this.fallbackToPolling();
            });

        } catch (error) {
            console.error('WebSocket initialization failed:', error);
            this.fallbackToPolling();
        }
    }

    fallbackToPolling() {
        this.isWebSocketEnabled = false;
        this.startAutoRefresh();
        console.log('Falling back to polling mode');
    }

    handleRealtimeAttendanceUpdate(data) {
        console.log('Real-time attendance update:', data);

        // Immediately update metrics
        this.fetchUpdates();

        // Add to activity feed with real-time styling
        this.addRealtimeActivity({
            type: 'attendance',
            message: `${data.attendee_name} ${data.action} ${data.session_title}`,
            user: data.updated_by,
            timestamp: data.timestamp,
            icon: data.action === 'checked_in' ? 'bi-check-circle' : 'bi-x-circle',
            class: data.action === 'checked_in' ? 'text-success' : 'text-warning'
        });

        // Show real-time notification
        this.showRealtimeNotification(`${data.attendee_name} ${data.action} ${data.session_title}`, 'info');
    }

    handleRealtimeSessionUpdate(data) {
        console.log('Real-time session status update:', data);
        this.fetchUpdates();

        if (data.status_type === 'capacity_alert') {
            this.showRealtimeNotification(`Capacity Alert: ${data.message}`, 'warning');
        }
    }

    handleStaffJoined(data) {
        console.log('Staff joined:', data);
        this.addRealtimeActivity({
            type: 'staff',
            message: `${data.user_name} joined the event`,
            user: 'System',
            timestamp: data.timestamp,
            icon: 'bi-person-plus',
            class: 'text-info'
        });

        this.showRealtimeNotification(`${data.user_name} joined the event`, 'info');
    }

    handleStaffDisconnected(data) {
        console.log('Staff disconnected:', data);
        this.addRealtimeActivity({
            type: 'staff',
            message: `${data.user_name} disconnected`,
            user: 'System',
            timestamp: data.timestamp,
            icon: 'bi-person-dash',
            class: 'text-muted'
        });
    }

    handleRealtimeEventStatus(data) {
        console.log('Real-time event status update:', data);
        if (data.stats) {
            this.updateDashboard({ event_stats: data.stats });
        }
    }

    addRealtimeActivity(item) {
        const feedContainer = document.getElementById('activityFeed');
        if (!feedContainer) return;

        const feedItem = document.createElement('div');
        feedItem.className = 'activity-item new realtime-update';
        feedItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <i class="bi ${item.icon} ${item.class}"></i>
                    <span class="ms-2">${item.message}</span>
                    <br>
                    <small class="text-muted">by ${item.user}</small>
                    <span class="badge bg-success ms-2" style="font-size: 0.6rem;">LIVE</span>
                </div>
                <small class="text-muted">${this.formatTimestamp(item.timestamp)}</small>
            </div>
        `;

        // Add to top of feed with animation
        feedContainer.insertBefore(feedItem, feedContainer.firstChild);

        // Remove 'new' class after animation
        setTimeout(() => feedItem.classList.remove('new'), 500);

        // Remove old items (keep last 20)
        const items = feedContainer.querySelectorAll('.activity-item');
        if (items.length > 20) {
            for (let i = 20; i < items.length; i++) {
                items[i].remove();
            }
        }

        // Update activity counter
        this.newActivityCount++;
        document.getElementById('activityBadge').textContent = `${this.newActivityCount} new`;
    }

    showRealtimeNotification(message, type = 'info') {
        // Create enhanced notification with real-time indicator
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed realtime-notification`;
        notification.style.cssText = 'top: 80px; right: 20px; z-index: 9999; min-width: 350px; border-left: 4px solid #28a745;';
        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="bi bi-broadcast text-success me-2"></i>
                <div class="flex-grow-1">
                    <strong>Real-Time Update</strong><br>
                    ${message}
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    updateConnectionStatus(status) {
        super.updateConnectionStatus(status);

        const statusElement = document.getElementById('connectionStatus');

        if (status === 'websocket_active') {
            statusElement.className = 'connection-status connected';
            statusElement.innerHTML = '<i class="bi bi-broadcast"></i> WebSocket Active - Real-Time Updates';
            statusElement.style.background = '#d4edda';
            statusElement.style.borderColor = '#c3e6cb';
        }
    }

    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString();
    }
}

// Replace the original dashboard with enhanced version
const enhancedDashboard = new EnhancedRealtimeDashboard(<?php echo $event_id; ?>);

// Override global function
function forceRefresh() {
    enhancedDashboard.forceRefresh();
}

// Add CSS for real-time elements
const style = document.createElement('style');
style.textContent = `
    .realtime-update {
        border-left: 3px solid #28a745 !important;
        background: linear-gradient(135deg, #e8f5e8, #d4edda) !important;
    }

    .realtime-notification {
        animation: slideInRight 0.3s ease;
    }

    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    .connection-status {
        transition: all 0.3s ease;
    }
`;
document.head.appendChild(style);
</script>

<?php include 'includes/footer.php'; ?>
