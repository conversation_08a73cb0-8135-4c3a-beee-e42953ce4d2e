<?php
/**
 * Universal AI Attendance Prediction Engine
 * Machine learning system that works for any organization type
 * Supports: Corporate, Educational, Sports, Entertainment, Healthcare, Government, Non-Profit
 */

require_once '../config.php';

class UniversalAttendancePredictionEngine {
    private $pdo;
    private $organization_type;
    private $prediction_models;
    private $feature_weights;
    
    // Organization-specific configurations
    private $org_configs = [
        'corporate' => [
            'name' => 'Corporate Events',
            'primary_factors' => ['time_of_day', 'day_of_week', 'season', 'meeting_type', 'department'],
            'terminology' => ['attendee' => 'Employee', 'session' => 'Meeting', 'event' => 'Conference'],
            'peak_times' => ['09:00-11:00', '14:00-16:00'],
            'seasonal_patterns' => ['low' => ['december', 'july'], 'high' => ['september', 'january']]
        ],
        'educational' => [
            'name' => 'Educational Institutions',
            'primary_factors' => ['academic_calendar', 'course_type', 'time_of_day', 'weather', 'exam_period'],
            'terminology' => ['attendee' => 'Student', 'session' => 'Class', 'event' => 'Course'],
            'peak_times' => ['10:00-12:00', '14:00-16:00'],
            'seasonal_patterns' => ['low' => ['summer', 'winter_break'], 'high' => ['fall', 'spring']]
        ],
        'sports' => [
            'name' => 'Sports Organizations',
            'primary_factors' => ['weather', 'team_performance', 'opponent', 'time_of_day', 'ticket_price'],
            'terminology' => ['attendee' => 'Fan', 'session' => 'Game', 'event' => 'Tournament'],
            'peak_times' => ['19:00-21:00', '15:00-17:00'],
            'seasonal_patterns' => ['varies_by_sport' => true]
        ],
        'entertainment' => [
            'name' => 'Entertainment Venues',
            'primary_factors' => ['artist_popularity', 'ticket_price', 'day_of_week', 'weather', 'competing_events'],
            'terminology' => ['attendee' => 'Guest', 'session' => 'Show', 'event' => 'Festival'],
            'peak_times' => ['20:00-22:00', '15:00-17:00'],
            'seasonal_patterns' => ['high' => ['summer', 'holidays'], 'low' => ['january', 'february']]
        ],
        'healthcare' => [
            'name' => 'Healthcare Organizations',
            'primary_factors' => ['appointment_type', 'urgency', 'time_of_day', 'day_of_week', 'season'],
            'terminology' => ['attendee' => 'Patient', 'session' => 'Appointment', 'event' => 'Clinic'],
            'peak_times' => ['09:00-11:00', '14:00-16:00'],
            'seasonal_patterns' => ['high' => ['flu_season'], 'low' => ['summer']]
        ],
        'government' => [
            'name' => 'Government Organizations',
            'primary_factors' => ['meeting_importance', 'public_interest', 'time_of_day', 'advance_notice'],
            'terminology' => ['attendee' => 'Citizen', 'session' => 'Meeting', 'event' => 'Forum'],
            'peak_times' => ['18:00-20:00', '10:00-12:00'],
            'seasonal_patterns' => ['high' => ['election_periods'], 'low' => ['holidays']]
        ],
        'nonprofit' => [
            'name' => 'Non-Profit Organizations',
            'primary_factors' => ['cause_relevance', 'volunteer_availability', 'weather', 'competing_events'],
            'terminology' => ['attendee' => 'Volunteer', 'session' => 'Activity', 'event' => 'Campaign'],
            'peak_times' => ['18:00-20:00', '10:00-12:00'],
            'seasonal_patterns' => ['high' => ['giving_season'], 'low' => ['summer']]
        ]
    ];
    
    public function __construct($pdo, $organization_type = 'corporate') {
        $this->pdo = $pdo;
        $this->organization_type = $organization_type;
        $this->initializePredictionModels();
        $this->setupDatabase();
    }
    
    private function setupDatabase() {
        // Create prediction tables if they don't exist
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS ai_prediction_models (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                organization_type VARCHAR(50) NOT NULL,
                model_name VARCHAR(100) NOT NULL,
                model_data JSON NOT NULL,
                accuracy_score DECIMAL(5,4) DEFAULT 0.0000,
                training_data_count INT(11) DEFAULT 0,
                last_trained TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE,
                INDEX idx_org_type (organization_type),
                INDEX idx_model_name (model_name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS ai_prediction_history (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                event_id INT(11) NOT NULL,
                session_id INT(11) DEFAULT NULL,
                organization_type VARCHAR(50) NOT NULL,
                predicted_attendance INT(11) NOT NULL,
                actual_attendance INT(11) DEFAULT NULL,
                prediction_factors JSON NOT NULL,
                accuracy_percentage DECIMAL(5,2) DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_event_id (event_id),
                INDEX idx_session_id (session_id),
                INDEX idx_org_type (organization_type),
                FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS ai_feature_importance (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                organization_type VARCHAR(50) NOT NULL,
                feature_name VARCHAR(100) NOT NULL,
                importance_score DECIMAL(5,4) NOT NULL,
                sample_size INT(11) NOT NULL,
                last_calculated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY unique_org_feature (organization_type, feature_name),
                INDEX idx_org_type (organization_type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
    }
    
    private function initializePredictionModels() {
        $this->prediction_models = [
            'linear_regression' => new LinearRegressionModel(),
            'decision_tree' => new DecisionTreeModel(),
            'neural_network' => new SimpleNeuralNetwork(),
            'ensemble' => new EnsembleModel()
        ];
        
        // Initialize feature weights based on organization type
        $config = $this->org_configs[$this->organization_type];
        $this->feature_weights = $this->calculateFeatureWeights($config['primary_factors']);
    }
    
    public function predictAttendance($event_id, $session_id = null, $additional_factors = []) {
        try {
            // Extract features for prediction
            $features = $this->extractFeatures($event_id, $session_id, $additional_factors);
            
            // Get organization-specific model
            $model = $this->getOrganizationModel();
            
            // Make prediction using ensemble of models
            $predictions = [];
            foreach ($this->prediction_models as $model_name => $model_instance) {
                $predictions[$model_name] = $model_instance->predict($features);
            }
            
            // Calculate weighted average based on model accuracy
            $final_prediction = $this->calculateEnsemblePrediction($predictions);
            
            // Apply organization-specific adjustments
            $adjusted_prediction = $this->applyOrganizationAdjustments($final_prediction, $features);
            
            // Store prediction for future accuracy tracking
            $this->storePrediction($event_id, $session_id, $adjusted_prediction, $features);
            
            return [
                'predicted_attendance' => round($adjusted_prediction),
                'confidence_score' => $this->calculateConfidenceScore($predictions),
                'factors_analyzed' => array_keys($features),
                'organization_type' => $this->organization_type,
                'model_accuracy' => $this->getModelAccuracy(),
                'recommendations' => $this->generateRecommendations($adjusted_prediction, $features)
            ];
            
        } catch (Exception $e) {
            error_log("Prediction error: " . $e->getMessage());
            return $this->getFallbackPrediction($event_id, $session_id);
        }
    }
    
    private function extractFeatures($event_id, $session_id, $additional_factors) {
        $features = [];
        
        // Get event data
        $stmt = $this->pdo->prepare("SELECT * FROM events WHERE id = ?");
        $stmt->execute([$event_id]);
        $event = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$event) {
            throw new Exception("Event not found");
        }
        
        // Basic temporal features
        $event_date = new DateTime($event['event_date']);
        $features['day_of_week'] = $event_date->format('N'); // 1-7
        $features['hour_of_day'] = $event_date->format('H');
        $features['month'] = $event_date->format('n');
        $features['is_weekend'] = $event_date->format('N') >= 6 ? 1 : 0;
        
        // Historical attendance features
        $features['avg_historical_attendance'] = $this->getHistoricalAverage($event_id);
        $features['recent_trend'] = $this->getRecentTrend();
        
        // Event-specific features
        $features['max_capacity'] = $event['max_attendees'] ?? 100;
        $features['advance_notice_days'] = $this->calculateAdvanceNotice($event_date);
        
        // Session-specific features (if applicable)
        if ($session_id) {
            $session_features = $this->getSessionFeatures($session_id);
            $features = array_merge($features, $session_features);
        }
        
        // Organization-specific features
        $org_features = $this->getOrganizationSpecificFeatures($event, $additional_factors);
        $features = array_merge($features, $org_features);
        
        // External factors
        $features['weather_score'] = $this->getWeatherScore($event_date);
        $features['competing_events'] = $this->getCompetingEventsCount($event_date);
        
        return $features;
    }
    
    private function getOrganizationSpecificFeatures($event, $additional_factors) {
        $features = [];
        $config = $this->org_configs[$this->organization_type];
        
        switch ($this->organization_type) {
            case 'corporate':
                $features['is_mandatory'] = $additional_factors['is_mandatory'] ?? 0;
                $features['department_size'] = $additional_factors['department_size'] ?? 50;
                $features['meeting_importance'] = $additional_factors['importance_level'] ?? 3;
                break;
                
            case 'educational':
                $features['is_exam_period'] = $this->isExamPeriod($event['event_date']) ? 1 : 0;
                $features['course_popularity'] = $additional_factors['course_rating'] ?? 3;
                $features['is_required_course'] = $additional_factors['is_required'] ?? 0;
                break;
                
            case 'sports':
                $features['team_performance'] = $additional_factors['team_ranking'] ?? 50;
                $features['opponent_popularity'] = $additional_factors['opponent_ranking'] ?? 50;
                $features['ticket_price'] = $additional_factors['ticket_price'] ?? 25;
                break;
                
            case 'entertainment':
                $features['artist_popularity'] = $additional_factors['artist_rating'] ?? 50;
                $features['ticket_price'] = $additional_factors['ticket_price'] ?? 50;
                $features['venue_capacity'] = $additional_factors['venue_size'] ?? 500;
                break;
                
            case 'healthcare':
                $features['appointment_urgency'] = $additional_factors['urgency_level'] ?? 3;
                $features['is_flu_season'] = $this->isFluSeason($event['event_date']) ? 1 : 0;
                $features['provider_popularity'] = $additional_factors['provider_rating'] ?? 4;
                break;
                
            case 'government':
                $features['public_interest_level'] = $additional_factors['interest_level'] ?? 3;
                $features['media_coverage'] = $additional_factors['media_attention'] ?? 2;
                $features['is_election_period'] = $this->isElectionPeriod($event['event_date']) ? 1 : 0;
                break;
                
            case 'nonprofit':
                $features['cause_relevance'] = $additional_factors['cause_popularity'] ?? 3;
                $features['volunteer_base_size'] = $additional_factors['volunteer_count'] ?? 100;
                $features['is_giving_season'] = $this->isGivingSeason($event['event_date']) ? 1 : 0;
                break;
        }
        
        return $features;
    }
    
    private function applyOrganizationAdjustments($prediction, $features) {
        $config = $this->org_configs[$this->organization_type];
        
        // Apply seasonal adjustments
        $seasonal_multiplier = $this->getSeasonalMultiplier($features['month']);
        $prediction *= $seasonal_multiplier;
        
        // Apply time-of-day adjustments
        $time_multiplier = $this->getTimeMultiplier($features['hour_of_day']);
        $prediction *= $time_multiplier;
        
        // Apply organization-specific business rules
        switch ($this->organization_type) {
            case 'corporate':
                if ($features['is_mandatory'] ?? 0) {
                    $prediction *= 1.3; // Mandatory meetings have higher attendance
                }
                break;
                
            case 'sports':
                if ($features['weather_score'] < 3 && isset($features['is_outdoor_event'])) {
                    $prediction *= 0.7; // Bad weather affects outdoor sports
                }
                break;
                
            case 'educational':
                if ($features['is_exam_period'] ?? 0) {
                    $prediction *= 0.8; // Lower attendance during exams
                }
                break;
        }
        
        // Ensure prediction doesn't exceed capacity
        $max_capacity = $features['max_capacity'] ?? 100;
        return min($prediction, $max_capacity);
    }
    
    public function trainModel($historical_data = null) {
        if (!$historical_data) {
            $historical_data = $this->getHistoricalData();
        }
        
        if (count($historical_data) < 10) {
            throw new Exception("Insufficient historical data for training (minimum 10 events required)");
        }
        
        $training_features = [];
        $training_targets = [];
        
        foreach ($historical_data as $record) {
            $features = $this->extractFeatures($record['event_id'], $record['session_id'] ?? null, $record['additional_factors'] ?? []);
            $training_features[] = $features;
            $training_targets[] = $record['actual_attendance'];
        }
        
        // Train each model
        $model_accuracies = [];
        foreach ($this->prediction_models as $model_name => $model) {
            $accuracy = $model->train($training_features, $training_targets);
            $model_accuracies[$model_name] = $accuracy;
        }
        
        // Store trained models
        $this->storeTrainedModels($model_accuracies);
        
        return [
            'models_trained' => count($this->prediction_models),
            'training_samples' => count($historical_data),
            'model_accuracies' => $model_accuracies,
            'overall_accuracy' => array_sum($model_accuracies) / count($model_accuracies)
        ];
    }
    
    public function getOrganizationConfig($org_type = null) {
        $org_type = $org_type ?? $this->organization_type;
        return $this->org_configs[$org_type] ?? $this->org_configs['corporate'];
    }
    
    public function getSupportedOrganizationTypes() {
        return array_keys($this->org_configs);
    }
    
    public function generateInsights($event_id, $prediction_result) {
        $insights = [];
        $config = $this->org_configs[$this->organization_type];
        
        // Attendance level insights
        $predicted = $prediction_result['predicted_attendance'];
        $capacity = $prediction_result['factors_analyzed']['max_capacity'] ?? 100;
        $utilization = ($predicted / $capacity) * 100;
        
        if ($utilization > 90) {
            $insights[] = [
                'type' => 'warning',
                'title' => 'High Demand Expected',
                'message' => "Predicted attendance ({$predicted}) is near capacity. Consider overflow planning.",
                'recommendation' => 'Set up waitlist or consider larger venue'
            ];
        } elseif ($utilization < 30) {
            $insights[] = [
                'type' => 'info',
                'title' => 'Low Attendance Predicted',
                'message' => "Predicted attendance ({$predicted}) is below 30% capacity.",
                'recommendation' => 'Consider marketing boost or venue downsizing'
            ];
        }
        
        // Organization-specific insights
        $org_insights = $this->getOrganizationSpecificInsights($prediction_result);
        $insights = array_merge($insights, $org_insights);
        
        return $insights;
    }
    
    // Placeholder methods for external data (to be implemented)
    private function getWeatherScore($date) { return 3; } // 1-5 scale
    private function getCompetingEventsCount($date) { return 2; }
    private function isExamPeriod($date) { return false; }
    private function isFluSeason($date) { return false; }
    private function isElectionPeriod($date) { return false; }
    private function isGivingSeason($date) { return false; }
    private function getHistoricalAverage($event_id) { return 75; }
    private function getRecentTrend() { return 1.1; }
    private function calculateAdvanceNotice($date) { return 14; }
    private function getSessionFeatures($session_id) { return []; }
    private function getSeasonalMultiplier($month) { return 1.0; }
    private function getTimeMultiplier($hour) { return 1.0; }
    private function calculateFeatureWeights($factors) { return []; }
    private function getOrganizationModel() { return null; }
    private function calculateEnsemblePrediction($predictions) { return array_sum($predictions) / count($predictions); }
    private function calculateConfidenceScore($predictions) { return 0.85; }
    private function getModelAccuracy() { return 0.82; }
    private function generateRecommendations($prediction, $features) { return []; }
    private function storePrediction($event_id, $session_id, $prediction, $features) { }
    private function getFallbackPrediction($event_id, $session_id) { return ['predicted_attendance' => 50]; }
    private function getHistoricalData() { return []; }
    private function storeTrainedModels($accuracies) { }
    private function getOrganizationSpecificInsights($result) { return []; }
}

// Placeholder ML model classes (to be implemented with actual algorithms)
class LinearRegressionModel { public function predict($features) { return 75; } public function train($features, $targets) { return 0.8; } }
class DecisionTreeModel { public function predict($features) { return 80; } public function train($features, $targets) { return 0.85; } }
class SimpleNeuralNetwork { public function predict($features) { return 78; } public function train($features, $targets) { return 0.82; } }
class EnsembleModel { public function predict($features) { return 77; } public function train($features, $targets) { return 0.87; } }

/**
 * Universal Organization Configuration Manager
 * Handles organization-specific settings and terminology
 */
class UniversalOrganizationManager {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->setupOrganizationTables();
    }

    private function setupOrganizationTables() {
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS organization_configs (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                organization_id VARCHAR(100) NOT NULL UNIQUE,
                organization_name VARCHAR(255) NOT NULL,
                organization_type VARCHAR(50) NOT NULL,
                custom_terminology JSON,
                branding_config JSON,
                prediction_settings JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_org_type (organization_type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
    }

    public function createOrganization($org_id, $org_name, $org_type, $custom_config = []) {
        $default_terminology = $this->getDefaultTerminology($org_type);
        $default_branding = $this->getDefaultBranding($org_type);
        $default_prediction = $this->getDefaultPredictionSettings($org_type);

        $stmt = $this->pdo->prepare("
            INSERT INTO organization_configs
            (organization_id, organization_name, organization_type, custom_terminology, branding_config, prediction_settings)
            VALUES (?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            organization_name = VALUES(organization_name),
            organization_type = VALUES(organization_type),
            updated_at = NOW()
        ");

        return $stmt->execute([
            $org_id,
            $org_name,
            $org_type,
            json_encode(array_merge($default_terminology, $custom_config['terminology'] ?? [])),
            json_encode(array_merge($default_branding, $custom_config['branding'] ?? [])),
            json_encode(array_merge($default_prediction, $custom_config['prediction'] ?? []))
        ]);
    }

    private function getDefaultTerminology($org_type) {
        $terminologies = [
            'corporate' => [
                'attendee' => 'Employee',
                'attendees' => 'Employees',
                'session' => 'Meeting',
                'sessions' => 'Meetings',
                'event' => 'Conference',
                'events' => 'Conferences',
                'organizer' => 'Manager',
                'check_in' => 'Sign In',
                'venue' => 'Meeting Room'
            ],
            'educational' => [
                'attendee' => 'Student',
                'attendees' => 'Students',
                'session' => 'Class',
                'sessions' => 'Classes',
                'event' => 'Course',
                'events' => 'Courses',
                'organizer' => 'Instructor',
                'check_in' => 'Attendance',
                'venue' => 'Classroom'
            ],
            'sports' => [
                'attendee' => 'Fan',
                'attendees' => 'Fans',
                'session' => 'Game',
                'sessions' => 'Games',
                'event' => 'Tournament',
                'events' => 'Tournaments',
                'organizer' => 'Coach',
                'check_in' => 'Entry',
                'venue' => 'Stadium'
            ],
            'entertainment' => [
                'attendee' => 'Guest',
                'attendees' => 'Guests',
                'session' => 'Show',
                'sessions' => 'Shows',
                'event' => 'Festival',
                'events' => 'Festivals',
                'organizer' => 'Producer',
                'check_in' => 'Admission',
                'venue' => 'Theater'
            ],
            'healthcare' => [
                'attendee' => 'Patient',
                'attendees' => 'Patients',
                'session' => 'Appointment',
                'sessions' => 'Appointments',
                'event' => 'Clinic',
                'events' => 'Clinics',
                'organizer' => 'Doctor',
                'check_in' => 'Check-In',
                'venue' => 'Medical Center'
            ],
            'government' => [
                'attendee' => 'Citizen',
                'attendees' => 'Citizens',
                'session' => 'Meeting',
                'sessions' => 'Meetings',
                'event' => 'Forum',
                'events' => 'Forums',
                'organizer' => 'Official',
                'check_in' => 'Registration',
                'venue' => 'City Hall'
            ],
            'nonprofit' => [
                'attendee' => 'Volunteer',
                'attendees' => 'Volunteers',
                'session' => 'Activity',
                'sessions' => 'Activities',
                'event' => 'Campaign',
                'events' => 'Campaigns',
                'organizer' => 'Coordinator',
                'check_in' => 'Sign Up',
                'venue' => 'Community Center'
            ]
        ];

        return $terminologies[$org_type] ?? $terminologies['corporate'];
    }

    private function getDefaultBranding($org_type) {
        $brandings = [
            'corporate' => [
                'primary_color' => '#0066cc',
                'secondary_color' => '#f8f9fa',
                'accent_color' => '#28a745',
                'logo_style' => 'professional',
                'theme' => 'business'
            ],
            'educational' => [
                'primary_color' => '#6f42c1',
                'secondary_color' => '#e9ecef',
                'accent_color' => '#fd7e14',
                'logo_style' => 'academic',
                'theme' => 'education'
            ],
            'sports' => [
                'primary_color' => '#dc3545',
                'secondary_color' => '#f8f9fa',
                'accent_color' => '#ffc107',
                'logo_style' => 'dynamic',
                'theme' => 'sports'
            ],
            'entertainment' => [
                'primary_color' => '#e83e8c',
                'secondary_color' => '#212529',
                'accent_color' => '#20c997',
                'logo_style' => 'creative',
                'theme' => 'entertainment'
            ],
            'healthcare' => [
                'primary_color' => '#20c997',
                'secondary_color' => '#f8f9fa',
                'accent_color' => '#0dcaf0',
                'logo_style' => 'medical',
                'theme' => 'healthcare'
            ],
            'government' => [
                'primary_color' => '#6c757d',
                'secondary_color' => '#e9ecef',
                'accent_color' => '#0d6efd',
                'logo_style' => 'official',
                'theme' => 'government'
            ],
            'nonprofit' => [
                'primary_color' => '#198754',
                'secondary_color' => '#f8f9fa',
                'accent_color' => '#fd7e14',
                'logo_style' => 'community',
                'theme' => 'nonprofit'
            ]
        ];

        return $brandings[$org_type] ?? $brandings['corporate'];
    }

    private function getDefaultPredictionSettings($org_type) {
        return [
            'enable_weather_factor' => in_array($org_type, ['sports', 'entertainment', 'nonprofit']),
            'enable_seasonal_adjustment' => true,
            'enable_historical_weighting' => true,
            'prediction_horizon_days' => 30,
            'min_confidence_threshold' => 0.7,
            'auto_retrain_frequency' => 'weekly'
        ];
    }

    public function getOrganizationConfig($org_id) {
        $stmt = $this->pdo->prepare("SELECT * FROM organization_configs WHERE organization_id = ?");
        $stmt->execute([$org_id]);
        $config = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$config) {
            return null;
        }

        return [
            'id' => $config['organization_id'],
            'name' => $config['organization_name'],
            'type' => $config['organization_type'],
            'terminology' => json_decode($config['custom_terminology'], true),
            'branding' => json_decode($config['branding_config'], true),
            'prediction_settings' => json_decode($config['prediction_settings'], true)
        ];
    }
}
?>
