<?php

/**
 * Database Backup Manager
 * Handles automated database backups with multiple formats and scheduling
 */
class DatabaseBackupManager {
    private $pdo;
    private $backupDir;
    private $dbConfig;
    
    public function __construct($pdo, $dbConfig = null) {
        $this->pdo = $pdo;
        $this->backupDir = __DIR__ . '/../backups/';
        
        // Ensure backup directory exists
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
        
        // Database configuration for mysqldump
        if ($dbConfig) {
            $this->dbConfig = $dbConfig;
        } else {
            // Get database configuration from global variables
            global $host, $dbname, $username, $password;
            $this->dbConfig = [
                'host' => $host ?? 'localhost',
                'database' => $dbname ?? 'campaign',
                'username' => $username ?? 'root',
                'password' => $password ?? ''
            ];
        }
    }
    
    /**
     * Create a backup based on configuration
     */
    public function createBackup($configId = null, $manualTrigger = false, $adminId = null) {
        try {
            // Get backup configuration
            if ($configId) {
                $config = $this->getBackupConfiguration($configId);
                if (!$config) {
                    throw new Exception("Backup configuration not found");
                }
            } else {
                // Default configuration for manual backups
                $config = [
                    'id' => null,
                    'name' => 'Manual Backup',
                    'backup_type' => 'full',
                    'format' => 'sql',
                    'email_notifications' => false
                ];
            }
            
            // Generate backup filename
            $timestamp = date('Y-m-d_H-i-s');
            $filename = $this->generateBackupFilename($config['name'], $config['format'], $timestamp);
            $filepath = $this->backupDir . $filename;
            
            // Create backup history record
            $historyId = $this->createBackupHistoryRecord($configId, $filename, $filepath, $config, $adminId);

            // Update status to running
            $this->updateBackupStatus($historyId, 'running');
            
            // Perform backup based on format
            $success = false;
            $errorMessage = null;
            
            try {
                if ($config['format'] === 'sql') {
                    $success = $this->createSQLBackup($filepath, $config['backup_type']);
                } else {
                    $success = $this->createJSONBackup($filepath, $config['backup_type']);
                }
                
                if ($success) {
                    // Get file size
                    $fileSize = file_exists($filepath) ? filesize($filepath) : 0;

                    // Update backup record
                    $this->updateBackupStatus($historyId, 'completed', null, $fileSize);

                    // Send notifications if enabled
                    if ($config['email_notifications'] && !empty($config['notification_emails'])) {
                        $this->sendBackupNotification($config, $filename, true);
                    }

                    // Cleanup old backups
                    $this->cleanupOldBackups($config);

                    return [
                        'success' => true,
                        'message' => 'Backup created successfully',
                        'filename' => $filename,
                        'filepath' => $filepath,
                        'size' => $fileSize
                    ];
                } else {
                    throw new Exception("Backup creation failed");
                }
                
            } catch (Exception $e) {
                $errorMessage = $e->getMessage();
                throw $e;
            }
            
        } catch (Exception $e) {
            // Update backup record with error
            if (isset($historyId)) {
                $this->updateBackupStatus($historyId, 'failed', $e->getMessage());
            }
            
            // Send error notification
            if (isset($config) && $config['email_notifications'] && !empty($config['notification_emails'])) {
                $this->sendBackupNotification($config, $filename ?? 'Unknown', false, $e->getMessage());
            }
            
            error_log("Backup creation error: " . $e->getMessage());
            
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Create SQL backup using mysqldump
     */
    private function createSQLBackup($filepath, $backupType) {
        $host = $this->dbConfig['host'];
        $database = $this->dbConfig['database'];
        $username = $this->dbConfig['username'];
        $password = $this->dbConfig['password'];
        
        // Build mysqldump command
        $command = "mysqldump";
        $command .= " --host=" . escapeshellarg($host);
        $command .= " --user=" . escapeshellarg($username);
        $command .= " --password=" . escapeshellarg($password);
        
        // Add options based on backup type
        switch ($backupType) {
            case 'structure_only':
                $command .= " --no-data";
                break;
            case 'data_only':
                $command .= " --no-create-info";
                break;
            case 'full':
            default:
                $command .= " --routines --triggers";
                break;
        }
        
        $command .= " " . escapeshellarg($database);
        $command .= " > " . escapeshellarg($filepath);
        
        // Execute command
        $output = [];
        $returnCode = 0;
        exec($command . " 2>&1", $output, $returnCode);
        
        if ($returnCode !== 0) {
            $errorOutput = implode("\n", $output);
            error_log("mysqldump error: " . $errorOutput);
            
            // Fallback to PHP-based backup
            return $this->createPHPSQLBackup($filepath, $backupType);
        }
        
        return file_exists($filepath) && filesize($filepath) > 0;
    }
    
    /**
     * Create SQL backup using PHP (fallback method)
     */
    private function createPHPSQLBackup($filepath, $backupType) {
        $sql = "-- Church Management System Database Backup\n";
        $sql .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n";
        $sql .= "-- Backup Type: " . $backupType . "\n\n";
        
        $sql .= "SET FOREIGN_KEY_CHECKS=0;\n\n";
        
        // Get all tables
        $stmt = $this->pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($tables as $table) {
            if ($backupType !== 'data_only') {
                // Get table structure
                $stmt = $this->pdo->query("SHOW CREATE TABLE `$table`");
                $createTable = $stmt->fetch(PDO::FETCH_ASSOC);
                $sql .= "DROP TABLE IF EXISTS `$table`;\n";
                $sql .= $createTable['Create Table'] . ";\n\n";
            }
            
            if ($backupType !== 'structure_only') {
                // Get table data
                $stmt = $this->pdo->query("SELECT * FROM `$table`");
                $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($rows)) {
                    $sql .= "INSERT INTO `$table` VALUES\n";
                    $values = [];
                    
                    foreach ($rows as $row) {
                        $rowValues = [];
                        foreach ($row as $value) {
                            if ($value === null) {
                                $rowValues[] = 'NULL';
                            } else {
                                $rowValues[] = $this->pdo->quote($value);
                            }
                        }
                        $values[] = '(' . implode(',', $rowValues) . ')';
                    }
                    
                    $sql .= implode(",\n", $values) . ";\n\n";
                }
            }
        }
        
        $sql .= "SET FOREIGN_KEY_CHECKS=1;\n";
        
        return file_put_contents($filepath, $sql) !== false;
    }
    
    /**
     * Create JSON backup
     */
    private function createJSONBackup($filepath, $backupType) {
        $backup = [
            'metadata' => [
                'created_at' => date('Y-m-d H:i:s'),
                'backup_type' => $backupType,
                'format' => 'json',
                'version' => '1.0'
            ],
            'data' => []
        ];
        
        // Get all tables
        $stmt = $this->pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($tables as $table) {
            $tableData = ['name' => $table];
            
            if ($backupType !== 'data_only') {
                // Get table structure
                $stmt = $this->pdo->query("DESCRIBE `$table`");
                $tableData['structure'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
            
            if ($backupType !== 'structure_only') {
                // Get table data
                $stmt = $this->pdo->query("SELECT * FROM `$table`");
                $tableData['data'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
            
            $backup['data'][] = $tableData;
        }
        
        $jsonData = json_encode($backup, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return file_put_contents($filepath, $jsonData) !== false;
    }
    
    /**
     * Generate backup filename
     */
    private function generateBackupFilename($configName, $format, $timestamp) {
        $safeName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $configName);
        $extension = $format === 'sql' ? 'sql' : 'json';
        return "backup_{$safeName}_{$timestamp}.{$extension}";
    }
    
    /**
     * Create backup history record
     */
    private function createBackupHistoryRecord($configId, $filename, $filepath, $config, $adminId) {
        $stmt = $this->pdo->prepare("
            INSERT INTO backup_history
            (configuration_id, backup_name, file_path, backup_type, format, status, started_at, created_by)
            VALUES (?, ?, ?, ?, ?, 'pending', NOW(), ?)
        ");

        $stmt->execute([
            $configId,
            $filename,
            $filepath,
            $config['backup_type'],
            $config['format'],
            $adminId
        ]);

        return $this->pdo->lastInsertId();
    }
    
    /**
     * Update backup status
     */
    private function updateBackupStatus($historyId, $status, $errorMessage = null, $fileSize = null) {
        $sql = "UPDATE backup_history SET status = ?";
        $params = [$status];
        
        if ($status === 'completed') {
            $sql .= ", completed_at = NOW()";
            if ($fileSize !== null) {
                $sql .= ", file_size = ?";
                $params[] = $fileSize;
            }
        }
        
        if ($errorMessage) {
            $sql .= ", error_message = ?";
            $params[] = $errorMessage;
        }
        
        $sql .= " WHERE id = ?";
        $params[] = $historyId;
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
    }
    
    /**
     * Get backup configuration
     */
    public function getBackupConfiguration($configId) {
        $stmt = $this->pdo->prepare("
            SELECT * FROM backup_configurations 
            WHERE id = ? AND is_active = 1
        ");
        $stmt->execute([$configId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get all backup configurations
     */
    public function getAllBackupConfigurations() {
        $stmt = $this->pdo->query("
            SELECT bc.*, a.username as created_by_name
            FROM backup_configurations bc
            LEFT JOIN admins a ON bc.created_by = a.id
            ORDER BY bc.created_at DESC
        ");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get backup history
     */
    public function getBackupHistory($limit = 50) {
        $stmt = $this->pdo->prepare("
            SELECT bh.*, bc.name as config_name, a.username as created_by_name
            FROM backup_history bh
            LEFT JOIN backup_configurations bc ON bh.configuration_id = bc.id
            LEFT JOIN admins a ON bh.created_by = a.id
            ORDER BY bh.started_at DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get backup history with pagination
     */
    public function getBackupHistoryPaginated($limit = 20, $offset = 0) {
        // Get total count
        $countStmt = $this->pdo->query("SELECT COUNT(*) as total FROM backup_history");
        $totalRecords = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Get paginated records
        $stmt = $this->pdo->prepare("
            SELECT bh.*, bc.name as config_name, a.username as created_by_name
            FROM backup_history bh
            LEFT JOIN backup_configurations bc ON bh.configuration_id = bc.id
            LEFT JOIN admins a ON bh.created_by = a.id
            ORDER BY bh.started_at DESC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$limit, $offset]);
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);

        return [
            'records' => $records,
            'total' => $totalRecords
        ];
    }
    
    /**
     * Send backup notification
     */
    private function sendBackupNotification($config, $filename, $success, $errorMessage = null) {
        // Implementation would depend on existing email system
        // This is a placeholder for notification logic
        try {
            $subject = $success ? 
                "Backup Completed: " . $config['name'] : 
                "Backup Failed: " . $config['name'];
            
            $message = $success ?
                "Backup '{$filename}' completed successfully." :
                "Backup failed with error: {$errorMessage}";
            
            // Send email notification (implementation would go here)
            error_log("Backup notification: $subject - $message");
            
        } catch (Exception $e) {
            error_log("Error sending backup notification: " . $e->getMessage());
        }
    }
    
    /**
     * Cleanup old backups based on retention policy
     */
    private function cleanupOldBackups($config) {
        try {
            $retentionDays = $config['retention_days'] ?? 30;
            $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$retentionDays} days"));

            // Get old backup files
            $stmt = $this->pdo->prepare("
                SELECT id, file_path
                FROM backup_history
                WHERE configuration_id = ? AND started_at < ? AND status = 'completed'
            ");
            $stmt->execute([$config['id'], $cutoffDate]);
            $oldBackups = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($oldBackups as $backup) {
                // Delete file
                if (file_exists($backup['file_path'])) {
                    unlink($backup['file_path']);
                }

                // Delete record
                $stmt = $this->pdo->prepare("DELETE FROM backup_history WHERE id = ?");
                $stmt->execute([$backup['id']]);
            }

        } catch (Exception $e) {
            error_log("Error cleaning up old backups: " . $e->getMessage());
        }
    }

    /**
     * Create backup configuration
     */
    public function createBackupConfiguration($data, $adminId) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO backup_configurations
                (name, description, backup_type, format, schedule_type, schedule_time,
                 schedule_day_of_week, schedule_day_of_month, retention_days, is_active,
                 email_notifications, notification_emails, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $data['name'],
                $data['description'] ?? '',
                $data['backup_type'] ?? 'full',
                $data['format'] ?? 'sql',
                $data['schedule_type'] ?? 'manual',
                $data['schedule_time'] ?? '02:00:00',
                $data['schedule_day_of_week'] ?? 0,
                $data['schedule_day_of_month'] ?? 1,
                $data['retention_days'] ?? 30,
                $data['is_active'] ?? 1,
                $data['email_notifications'] ?? 0,
                $data['notification_emails'] ?? '',
                $adminId
            ]);

            return [
                'success' => true,
                'message' => 'Backup configuration created successfully',
                'id' => $this->pdo->lastInsertId()
            ];

        } catch (Exception $e) {
            error_log("Error creating backup configuration: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Update backup configuration
     */
    public function updateBackupConfiguration($configId, $data) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE backup_configurations
                SET name = ?, description = ?, backup_type = ?, format = ?,
                    schedule_type = ?, schedule_time = ?, schedule_day_of_week = ?,
                    schedule_day_of_month = ?, retention_days = ?, is_active = ?,
                    email_notifications = ?, notification_emails = ?, updated_at = NOW()
                WHERE id = ?
            ");

            $stmt->execute([
                $data['name'],
                $data['description'] ?? '',
                $data['backup_type'] ?? 'full',
                $data['format'] ?? 'sql',
                $data['schedule_type'] ?? 'manual',
                $data['schedule_time'] ?? '02:00:00',
                $data['schedule_day_of_week'] ?? 0,
                $data['schedule_day_of_month'] ?? 1,
                $data['retention_days'] ?? 30,
                $data['is_active'] ?? 1,
                $data['email_notifications'] ?? 0,
                $data['notification_emails'] ?? '',
                $configId
            ]);

            return [
                'success' => true,
                'message' => 'Backup configuration updated successfully'
            ];

        } catch (Exception $e) {
            error_log("Error updating backup configuration: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete backup configuration
     */
    public function deleteBackupConfiguration($configId) {
        try {
            $this->pdo->beginTransaction();

            // Delete associated backup history
            $stmt = $this->pdo->prepare("DELETE FROM backup_history WHERE configuration_id = ?");
            $stmt->execute([$configId]);

            // Delete configuration
            $stmt = $this->pdo->prepare("DELETE FROM backup_configurations WHERE id = ?");
            $stmt->execute([$configId]);

            $this->pdo->commit();

            return [
                'success' => true,
                'message' => 'Backup configuration deleted successfully'
            ];

        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error deleting backup configuration: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete a single backup
     */
    public function deleteBackup($backupId) {
        try {
            // Get backup file path first
            $stmt = $this->pdo->prepare("SELECT file_path, backup_name FROM backup_history WHERE id = ?");
            $stmt->execute([$backupId]);
            $backup = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$backup) {
                return [
                    'success' => false,
                    'message' => 'Backup not found'
                ];
            }

            // Delete the physical file
            if (file_exists($backup['file_path'])) {
                unlink($backup['file_path']);
            }

            // Delete the database record
            $stmt = $this->pdo->prepare("DELETE FROM backup_history WHERE id = ?");
            $stmt->execute([$backupId]);

            return [
                'success' => true,
                'message' => 'Backup deleted successfully'
            ];

        } catch (Exception $e) {
            error_log("Error deleting backup: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Delete multiple backups
     */
    public function deleteMultipleBackups($backupIds) {
        try {
            if (empty($backupIds)) {
                return [
                    'success' => false,
                    'message' => 'No backups selected for deletion'
                ];
            }

            $this->pdo->beginTransaction();

            $deletedCount = 0;
            $errors = [];

            foreach ($backupIds as $backupId) {
                // Get backup file path
                $stmt = $this->pdo->prepare("SELECT file_path, backup_name FROM backup_history WHERE id = ?");
                $stmt->execute([$backupId]);
                $backup = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($backup) {
                    // Delete the physical file
                    if (file_exists($backup['file_path'])) {
                        if (unlink($backup['file_path'])) {
                            // Delete the database record
                            $stmt = $this->pdo->prepare("DELETE FROM backup_history WHERE id = ?");
                            $stmt->execute([$backupId]);
                            $deletedCount++;
                        } else {
                            $errors[] = "Failed to delete file for backup: " . $backup['backup_name'];
                        }
                    } else {
                        // File doesn't exist, just delete the record
                        $stmt = $this->pdo->prepare("DELETE FROM backup_history WHERE id = ?");
                        $stmt->execute([$backupId]);
                        $deletedCount++;
                    }
                }
            }

            $this->pdo->commit();

            if ($deletedCount > 0) {
                $message = "Successfully deleted {$deletedCount} backup(s)";
                if (!empty($errors)) {
                    $message .= ". Some errors occurred: " . implode(', ', $errors);
                }
                return [
                    'success' => true,
                    'message' => $message
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'No backups were deleted. ' . implode(', ', $errors)
                ];
            }

        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error deleting multiple backups: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get scheduled backups that need to run
     */
    public function getScheduledBackups() {
        $now = new DateTime();
        $currentTime = $now->format('H:i:s');
        $currentDayOfWeek = $now->format('w'); // 0 = Sunday
        $currentDayOfMonth = $now->format('j');

        $stmt = $this->pdo->prepare("
            SELECT * FROM backup_configurations
            WHERE is_active = 1
            AND schedule_type != 'manual'
            AND (
                (schedule_type = 'daily' AND schedule_time <= ?) OR
                (schedule_type = 'weekly' AND schedule_day_of_week = ? AND schedule_time <= ?) OR
                (schedule_type = 'monthly' AND schedule_day_of_month = ? AND schedule_time <= ?)
            )
        ");

        $stmt->execute([$currentTime, $currentDayOfWeek, $currentTime, $currentDayOfMonth, $currentTime]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Download backup file
     */
    public function downloadBackup($historyId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT backup_name, file_path, file_size
                FROM backup_history
                WHERE id = ? AND status = 'completed'
            ");
            $stmt->execute([$historyId]);
            $backup = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$backup || !file_exists($backup['file_path'])) {
                return [
                    'success' => false,
                    'message' => 'Backup file not found'
                ];
            }

            return [
                'success' => true,
                'filename' => $backup['backup_name'],
                'filepath' => $backup['file_path'],
                'size' => $backup['file_size']
            ];

        } catch (Exception $e) {
            error_log("Error downloading backup: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}
