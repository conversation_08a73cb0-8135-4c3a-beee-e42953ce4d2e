-- Role-Based Access Control System for Multi-Tier Dashboard Architecture
-- This creates the database schema for hierarchical dashboard roles and permissions

-- User Roles Table
CREATE TABLE IF NOT EXISTS user_roles (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    role_display_name VARCHAR(100) NOT NULL,
    role_description TEXT,
    hierarchy_level INT(11) NOT NULL DEFAULT 0,
    dashboard_route VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_hierarchy_level (hierarchy_level),
    INDEX idx_role_name (role_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default roles with hierarchy levels
INSERT INTO user_roles (role_name, role_display_name, role_description, hierarchy_level, dashboard_route) VALUES
('super_admin', 'Super Administrator', 'Complete oversight of all events, sessions, and user management', 1, 'super_admin_dashboard.php'),
('event_coordinator', 'Event Coordinator', 'Multi-session oversight for assigned events', 2, 'event_coordinator_dashboard.php'),
('organizer', 'Event Organizer', 'Event planning and setup tools', 3, 'organizer_dashboard.php'),
('session_moderator', 'Session Moderator', 'Individual session management for assigned sessions only', 4, 'session_moderator_dashboard.php'),
('staff', 'Check-in Staff', 'Limited access for check-in and basic attendance marking', 5, 'staff_dashboard.php');

-- Permissions Table
CREATE TABLE IF NOT EXISTS permissions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    permission_name VARCHAR(100) NOT NULL UNIQUE,
    permission_display_name VARCHAR(150) NOT NULL,
    permission_description TEXT,
    resource_type ENUM('event', 'session', 'user', 'system', 'report') NOT NULL,
    action_type ENUM('create', 'read', 'update', 'delete', 'manage', 'export') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_resource_type (resource_type),
    INDEX idx_action_type (action_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default permissions
INSERT INTO permissions (permission_name, permission_display_name, permission_description, resource_type, action_type) VALUES
-- System permissions
('system.manage_all', 'Manage All System Functions', 'Complete system administration access', 'system', 'manage'),
('system.view_analytics', 'View System Analytics', 'Access to system-wide analytics and reports', 'system', 'read'),
('system.export_data', 'Export System Data', 'Export comprehensive system data', 'system', 'export'),

-- User management permissions
('user.manage_all', 'Manage All Users', 'Create, update, delete all user accounts', 'user', 'manage'),
('user.view_all', 'View All Users', 'View all user accounts and roles', 'user', 'read'),
('user.assign_roles', 'Assign User Roles', 'Assign and modify user roles', 'user', 'update'),

-- Event permissions
('event.manage_all', 'Manage All Events', 'Create, update, delete all events', 'event', 'manage'),
('event.manage_assigned', 'Manage Assigned Events', 'Manage only assigned events', 'event', 'update'),
('event.view_all', 'View All Events', 'View all events in the system', 'event', 'read'),
('event.view_assigned', 'View Assigned Events', 'View only assigned events', 'event', 'read'),
('event.create', 'Create Events', 'Create new events', 'event', 'create'),
('event.export_data', 'Export Event Data', 'Export event attendance and analytics', 'event', 'export'),

-- Session permissions
('session.manage_all', 'Manage All Sessions', 'Create, update, delete all sessions', 'session', 'manage'),
('session.manage_assigned', 'Manage Assigned Sessions', 'Manage only assigned sessions', 'session', 'update'),
('session.view_all', 'View All Sessions', 'View all sessions in the system', 'session', 'read'),
('session.view_assigned', 'View Assigned Sessions', 'View only assigned sessions', 'session', 'read'),
('session.mark_attendance', 'Mark Session Attendance', 'Mark attendance for session participants', 'session', 'update'),
('session.export_data', 'Export Session Data', 'Export session attendance data', 'session', 'export'),

-- Report permissions
('report.view_all', 'View All Reports', 'Access to all system reports', 'report', 'read'),
('report.view_assigned', 'View Assigned Reports', 'View reports for assigned events/sessions', 'report', 'read'),
('report.export', 'Export Reports', 'Export report data', 'report', 'export');

-- Role Permissions Junction Table
CREATE TABLE IF NOT EXISTS role_permissions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    role_id INT(11) NOT NULL,
    permission_id INT(11) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_role_permission (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    INDEX idx_role_id (role_id),
    INDEX idx_permission_id (permission_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- User Role Assignments Table
CREATE TABLE IF NOT EXISTS user_role_assignments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) NOT NULL,
    role_id INT(11) NOT NULL,
    assigned_by INT(11) NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    is_active TINYINT(1) DEFAULT 1,
    UNIQUE KEY unique_user_role (user_id, role_id),
    FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES admins(id) ON DELETE RESTRICT,
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Event Assignments Table (for coordinators and organizers)
CREATE TABLE IF NOT EXISTS event_assignments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) NOT NULL,
    event_id INT(11) NOT NULL,
    role_type ENUM('coordinator', 'organizer') NOT NULL,
    assigned_by INT(11) NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active TINYINT(1) DEFAULT 1,
    UNIQUE KEY unique_user_event_role (user_id, event_id, role_type),
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES admins(id) ON DELETE RESTRICT,
    INDEX idx_user_id (user_id),
    INDEX idx_event_id (event_id),
    INDEX idx_role_type (role_type),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Session Assignments Table (for session moderators)
CREATE TABLE IF NOT EXISTS session_assignments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) NOT NULL,
    session_id INT(11) NOT NULL,
    assigned_by INT(11) NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active TINYINT(1) DEFAULT 1,
    UNIQUE KEY unique_user_session (user_id, session_id),
    FOREIGN KEY (session_id) REFERENCES event_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES admins(id) ON DELETE RESTRICT,
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Dashboard Access Log Table (for audit and analytics)
CREATE TABLE IF NOT EXISTS dashboard_access_log (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) NOT NULL,
    role_name VARCHAR(50) NOT NULL,
    dashboard_accessed VARCHAR(100) NOT NULL,
    access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_duration INT(11) DEFAULT 0,
    actions_performed INT(11) DEFAULT 0,
    INDEX idx_user_id (user_id),
    INDEX idx_role_name (role_name),
    INDEX idx_access_time (access_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Assign default permissions to roles
-- Super Admin gets all permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT ur.id, p.id
FROM user_roles ur, permissions p
WHERE ur.role_name = 'super_admin';

-- Event Coordinator permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT ur.id, p.id
FROM user_roles ur, permissions p
WHERE ur.role_name = 'event_coordinator'
AND p.permission_name IN (
    'event.view_assigned', 'event.manage_assigned', 'event.export_data',
    'session.view_all', 'session.manage_all', 'session.mark_attendance', 'session.export_data',
    'report.view_assigned', 'report.export'
);

-- Organizer permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT ur.id, p.id
FROM user_roles ur, permissions p
WHERE ur.role_name = 'organizer'
AND p.permission_name IN (
    'event.view_assigned', 'event.manage_assigned', 'event.create',
    'session.view_all', 'session.manage_all',
    'report.view_assigned'
);

-- Session Moderator permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT ur.id, p.id
FROM user_roles ur, permissions p
WHERE ur.role_name = 'session_moderator'
AND p.permission_name IN (
    'event.view_assigned',
    'session.view_assigned', 'session.manage_assigned', 'session.mark_attendance',
    'report.view_assigned'
);

-- Staff permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT ur.id, p.id
FROM user_roles ur, permissions p
WHERE ur.role_name = 'staff'
AND p.permission_name IN (
    'session.view_assigned', 'session.mark_attendance'
);

-- Add admin_roles column to existing admins table if it doesn't exist
ALTER TABLE admins ADD COLUMN default_role VARCHAR(50) DEFAULT 'staff';

-- Update existing admin to be super_admin (assuming first admin is super admin)
UPDATE admins SET default_role = 'super_admin' WHERE id = 1 LIMIT 1;
