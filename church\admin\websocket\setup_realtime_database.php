<?php
/**
 * Database Setup for Real-Time WebSocket System
 * Creates tables for activity logging and connection management
 */

require_once '../config.php';

echo "🚀 Setting up Real-Time WebSocket Database\n";
echo "==========================================\n\n";

$setup_results = [];
$total_steps = 0;
$completed_steps = 0;

function setupStep($description, $callback) {
    global $setup_results, $total_steps, $completed_steps, $pdo;
    
    $total_steps++;
    echo "⏳ $description... ";
    
    try {
        $result = $callback($pdo);
        $completed_steps++;
        echo "✅ DONE\n";
        $setup_results[] = ['step' => $description, 'status' => 'SUCCESS', 'details' => $result];
        return true;
    } catch (Exception $e) {
        echo "❌ FAILED\n";
        echo "   Error: " . $e->getMessage() . "\n";
        $setup_results[] = ['step' => $description, 'status' => 'FAILED', 'details' => $e->getMessage()];
        return false;
    }
}

// Step 1: Create real-time activity log table
setupStep("Creating realtime_activity_log table", function($pdo) {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS realtime_activity_log (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            event_id INT(11) NOT NULL,
            user_id INT(11) NOT NULL,
            action_type VARCHAR(100) NOT NULL,
            action_data JSON NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_event_id (event_id),
            INDEX idx_user_id (user_id),
            INDEX idx_action_type (action_type),
            INDEX idx_created_at (created_at),
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    return "Activity log table created with proper indexes";
});

// Step 2: Create WebSocket connections tracking table
setupStep("Creating websocket_connections table", function($pdo) {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS websocket_connections (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            connection_id VARCHAR(255) NOT NULL UNIQUE,
            user_id INT(11) NOT NULL,
            event_id INT(11) DEFAULT NULL,
            user_name VARCHAR(255) NOT NULL,
            user_type ENUM('admin', 'member', 'staff') NOT NULL,
            connected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_heartbeat TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE,
            user_agent TEXT,
            ip_address VARCHAR(45),
            INDEX idx_connection_id (connection_id),
            INDEX idx_user_id (user_id),
            INDEX idx_event_id (event_id),
            INDEX idx_is_active (is_active),
            INDEX idx_last_heartbeat (last_heartbeat),
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    return "WebSocket connections table created with tracking capabilities";
});

// Step 3: Create real-time session status table
setupStep("Creating realtime_session_status table", function($pdo) {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS realtime_session_status (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            session_id INT(11) NOT NULL,
            status_type ENUM('capacity_alert', 'status_update', 'staff_message', 'system_alert') NOT NULL,
            status_data JSON NOT NULL,
            created_by INT(11) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NULL,
            is_active BOOLEAN DEFAULT TRUE,
            priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
            INDEX idx_session_id (session_id),
            INDEX idx_status_type (status_type),
            INDEX idx_created_at (created_at),
            INDEX idx_is_active (is_active),
            INDEX idx_priority (priority),
            FOREIGN KEY (session_id) REFERENCES event_sessions(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    return "Real-time session status table created";
});

// Step 4: Create real-time notifications table
setupStep("Creating realtime_notifications table", function($pdo) {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS realtime_notifications (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            event_id INT(11) NOT NULL,
            notification_type ENUM('attendance_conflict', 'capacity_alert', 'staff_message', 'system_update', 'emergency') NOT NULL,
            title VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            notification_data JSON,
            created_by INT(11) NOT NULL,
            target_users JSON COMMENT 'Array of user IDs to notify, null for all event staff',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NULL,
            is_read BOOLEAN DEFAULT FALSE,
            priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
            INDEX idx_event_id (event_id),
            INDEX idx_notification_type (notification_type),
            INDEX idx_created_at (created_at),
            INDEX idx_is_read (is_read),
            INDEX idx_priority (priority),
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    return "Real-time notifications table created";
});

// Step 5: Create WebSocket server configuration table
setupStep("Creating websocket_server_config table", function($pdo) {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS websocket_server_config (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            config_key VARCHAR(100) NOT NULL UNIQUE,
            config_value TEXT NOT NULL,
            description TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_config_key (config_key)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    
    // Insert default configuration
    $default_configs = [
        ['server_port', '8080', 'WebSocket server port'],
        ['max_connections', '1000', 'Maximum concurrent connections'],
        ['heartbeat_interval', '30', 'Heartbeat interval in seconds'],
        ['connection_timeout', '300', 'Connection timeout in seconds'],
        ['enable_logging', '1', 'Enable detailed logging'],
        ['log_retention_days', '30', 'Days to retain activity logs']
    ];
    
    foreach ($default_configs as $config) {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO websocket_server_config (config_key, config_value, description)
            VALUES (?, ?, ?)
        ");
        $stmt->execute($config);
    }
    
    return "WebSocket server configuration table created with defaults";
});

// Step 6: Create indexes for performance optimization
setupStep("Creating performance optimization indexes", function($pdo) {
    // Composite indexes for common queries
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_activity_event_time ON realtime_activity_log (event_id, created_at DESC)",
        "CREATE INDEX IF NOT EXISTS idx_activity_user_time ON realtime_activity_log (user_id, created_at DESC)",
        "CREATE INDEX IF NOT EXISTS idx_connections_event_active ON websocket_connections (event_id, is_active)",
        "CREATE INDEX IF NOT EXISTS idx_notifications_event_unread ON realtime_notifications (event_id, is_read, priority)",
        "CREATE INDEX IF NOT EXISTS idx_session_status_active ON realtime_session_status (session_id, is_active, priority)"
    ];
    
    foreach ($indexes as $index_sql) {
        $pdo->exec($index_sql);
    }
    
    return "Performance optimization indexes created";
});

// Step 7: Create cleanup procedures
setupStep("Creating cleanup procedures", function($pdo) {
    // Procedure to clean up old connections
    $pdo->exec("
        CREATE PROCEDURE IF NOT EXISTS CleanupOldConnections()
        BEGIN
            DELETE FROM websocket_connections 
            WHERE last_heartbeat < DATE_SUB(NOW(), INTERVAL 10 MINUTE);
            
            DELETE FROM realtime_activity_log 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
            
            DELETE FROM realtime_notifications 
            WHERE expires_at IS NOT NULL AND expires_at < NOW();
            
            DELETE FROM realtime_session_status 
            WHERE expires_at IS NOT NULL AND expires_at < NOW();
        END
    ");
    
    return "Cleanup procedures created for maintenance";
});

// Step 8: Create sample real-time data
setupStep("Creating sample real-time data", function($pdo) {
    // Get test event
    $stmt = $pdo->prepare("SELECT id FROM events WHERE title = 'Test Event - Inheritance System' LIMIT 1");
    $stmt->execute();
    $test_event_id = $stmt->fetchColumn();
    
    if (!$test_event_id) {
        return "No test event found, skipping sample data";
    }
    
    // Create sample notifications
    $sample_notifications = [
        [
            'event_id' => $test_event_id,
            'notification_type' => 'capacity_alert',
            'title' => 'Workshop A Near Capacity',
            'message' => 'Workshop A: Leadership is at 90% capacity (45/50 attendees)',
            'priority' => 'medium',
            'created_by' => 1
        ],
        [
            'event_id' => $test_event_id,
            'notification_type' => 'staff_message',
            'title' => 'Setup Complete',
            'message' => 'All morning sessions are ready for attendees',
            'priority' => 'low',
            'created_by' => 1
        ]
    ];
    
    $created_notifications = 0;
    foreach ($sample_notifications as $notification) {
        $stmt = $pdo->prepare("
            INSERT INTO realtime_notifications 
            (event_id, notification_type, title, message, priority, created_by)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $notification['event_id'],
            $notification['notification_type'],
            $notification['title'],
            $notification['message'],
            $notification['priority'],
            $notification['created_by']
        ]);
        $created_notifications++;
    }
    
    return "Created $created_notifications sample notifications";
});

echo "\n";
echo "📋 Setup Summary\n";
echo "================\n";
echo "Total Steps: $total_steps\n";
echo "Completed: $completed_steps\n";
echo "Failed: " . ($total_steps - $completed_steps) . "\n";
echo "Success Rate: " . round(($completed_steps / $total_steps) * 100, 1) . "%\n\n";

if ($completed_steps === $total_steps) {
    echo "🎉 REAL-TIME DATABASE SETUP COMPLETED SUCCESSFULLY! 🎉\n\n";
    
    echo "✅ What was created:\n";
    echo "   • realtime_activity_log - Track all real-time actions\n";
    echo "   • websocket_connections - Manage active connections\n";
    echo "   • realtime_session_status - Live session status updates\n";
    echo "   • realtime_notifications - Staff notifications system\n";
    echo "   • websocket_server_config - Server configuration\n";
    echo "   • Performance indexes for fast queries\n";
    echo "   • Cleanup procedures for maintenance\n";
    echo "   • Sample notifications for testing\n\n";
    
    echo "🚀 Next Steps:\n";
    echo "   1. Install Ratchet WebSocket library: composer require ratchet/pawl\n";
    echo "   2. Start WebSocket server: php websocket/realtime_server.php\n";
    echo "   3. Test real-time connections with client integration\n\n";
    
    echo "📝 Server Details:\n";
    echo "   • Port: 8080 (configurable)\n";
    echo "   • Max Connections: 1000 (configurable)\n";
    echo "   • Heartbeat: 30 seconds\n";
    echo "   • Auto-cleanup: Old connections and logs\n\n";
    
} else {
    echo "⚠️  SETUP INCOMPLETE\n";
    echo "Some steps failed. Please review the errors above.\n\n";
    
    echo "❌ Failed Steps:\n";
    foreach ($setup_results as $result) {
        if ($result['status'] === 'FAILED') {
            echo "   • " . $result['step'] . ": " . $result['details'] . "\n";
        }
    }
    echo "\n";
}

echo "Setup completed at: " . date('Y-m-d H:i:s') . "\n";
?>
