-- Add the missing "Admin" (limited_admin) role to the RBAC system
-- This script adds the limited_admin role and its permissions

-- Insert the limited_admin role if it doesn't exist
INSERT IGNORE INTO user_roles (role_name, role_display_name, role_description, hierarchy_level, dashboard_route) VALUES
('limited_admin', 'Admin', 'Basic administrative functions with restricted access to system administration', 2, 'dashboard.php');

-- Update hierarchy levels for existing roles to accommodate the new admin role
UPDATE user_roles SET hierarchy_level = 1 WHERE role_name = 'super_admin';
UPDATE user_roles SET hierarchy_level = 2 WHERE role_name = 'limited_admin';
UPDATE user_roles SET hierarchy_level = 3 WHERE role_name = 'event_coordinator';
UPDATE user_roles SET hierarchy_level = 4 WHERE role_name = 'organizer';
UPDATE user_roles SET hierarchy_level = 5 WHERE role_name = 'session_moderator';
UPDATE user_roles SET hierarchy_level = 6 WHERE role_name = 'staff';

-- Add permissions for limited_admin role
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT ur.id, p.id
FROM user_roles ur, permissions p
WHERE ur.role_name = 'limited_admin'
AND p.permission_name IN (
    'member.view_all', 'member.manage_all', 'member.export_data',
    'event.view_all', 'event.manage_all', 'event.export_data',
    'session.view_all', 'session.manage_all', 'session.mark_attendance', 'session.export_data',
    'report.view_all', 'report.export'
);

-- Update super_admin dashboard route to use the original dashboard
UPDATE user_roles SET dashboard_route = 'dashboard.php' WHERE role_name = 'super_admin';

-- Show the results
SELECT role_name, role_display_name, hierarchy_level, dashboard_route FROM user_roles ORDER BY hierarchy_level;
