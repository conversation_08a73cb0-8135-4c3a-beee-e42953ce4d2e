<?php
/**
 * Comprehensive Validation Script for Enhanced Attendance Inheritance System
 * This script validates all components and ensures the system is working correctly
 */

require_once '../config.php';

echo "🚀 Enhanced Attendance Inheritance System - Validation Script\n";
echo "============================================================\n\n";

$validation_results = [];
$total_checks = 0;
$passed_checks = 0;

function validateCheck($description, $condition, $details = '') {
    global $validation_results, $total_checks, $passed_checks;
    
    $total_checks++;
    $status = $condition ? 'PASS' : 'FAIL';
    
    if ($condition) {
        $passed_checks++;
        echo "✅ $description\n";
    } else {
        echo "❌ $description\n";
        if ($details) {
            echo "   Details: $details\n";
        }
    }
    
    $validation_results[] = [
        'description' => $description,
        'status' => $status,
        'details' => $details
    ];
}

// 1. Database Schema Validation
echo "📊 Validating Database Schema...\n";
echo "--------------------------------\n";

try {
    // Check if inheritance tables exist
    $stmt = $pdo->query("SHOW TABLES LIKE 'attendance_inheritance_rules'");
    validateCheck("Attendance inheritance rules table exists", $stmt->rowCount() > 0);
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'attendance_inheritance_log'");
    validateCheck("Attendance inheritance log table exists", $stmt->rowCount() > 0);
    
    // Check table structure
    $stmt = $pdo->query("DESCRIBE attendance_inheritance_rules");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    validateCheck("Rules table has required columns", 
        in_array('rule_type', $columns) && 
        in_array('source_criteria', $columns) && 
        in_array('target_criteria', $columns) && 
        in_array('inheritance_logic', $columns)
    );
    
    $stmt = $pdo->query("DESCRIBE attendance_inheritance_log");
    $log_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    validateCheck("Log table has required columns",
        in_array('inheritance_type', $log_columns) &&
        in_array('old_status', $log_columns) &&
        in_array('new_status', $log_columns)
    );
    
} catch (Exception $e) {
    validateCheck("Database schema validation", false, $e->getMessage());
}

echo "\n";

// 2. File Structure Validation
echo "📁 Validating File Structure...\n";
echo "-------------------------------\n";

$required_files = [
    'attendance_inheritance_engine.php' => 'Main inheritance engine',
    'test_inheritance_system.php' => 'Test suite',
    'validate_inheritance_system.php' => 'This validation script'
];

foreach ($required_files as $file => $description) {
    validateCheck("$description exists", file_exists($file));
}

// Check if files are properly integrated
$dashboard_content = file_get_contents('multi_session_dashboard.php');
validateCheck("Dashboard links to inheritance engine", 
    strpos($dashboard_content, 'attendance_inheritance_engine.php') !== false
);

$bulk_content = file_get_contents('advanced_bulk_attendance.php');
validateCheck("Bulk operations links to inheritance engine", 
    strpos($bulk_content, 'attendance_inheritance_engine.php') !== false
);

echo "\n";

// 3. Class and Function Validation
echo "🔧 Validating Code Structure...\n";
echo "-------------------------------\n";

// Check if AttendanceInheritanceEngine class exists
$engine_content = file_get_contents('attendance_inheritance_engine.php');
validateCheck("AttendanceInheritanceEngine class exists", 
    strpos($engine_content, 'class AttendanceInheritanceEngine') !== false
);

validateCheck("applyAllRules method exists", 
    strpos($engine_content, 'function applyAllRules') !== false
);

validateCheck("applyBottomUpRule method exists", 
    strpos($engine_content, 'function applyBottomUpRule') !== false
);

validateCheck("applyTopDownRule method exists", 
    strpos($engine_content, 'function applyTopDownRule') !== false
);

validateCheck("applyPeerToPeerRule method exists", 
    strpos($engine_content, 'function applyPeerToPeerRule') !== false
);

validateCheck("applyConditionalRule method exists", 
    strpos($engine_content, 'function applyConditionalRule') !== false
);

validateCheck("createDefaultInheritanceRules function exists", 
    strpos($engine_content, 'function createDefaultInheritanceRules') !== false
);

echo "\n";

// 4. Integration Validation
echo "🔗 Validating System Integration...\n";
echo "-----------------------------------\n";

// Check if inheritance engine is accessible from other components
$session_manager_content = file_get_contents('session_group_manager.php');
validateCheck("Session group manager exists", !empty($session_manager_content));

$realtime_content = file_get_contents('realtime_dashboard.php');
validateCheck("Real-time dashboard exists", !empty($realtime_content));

$analytics_content = file_get_contents('advanced_analytics.php');
validateCheck("Advanced analytics exists", !empty($analytics_content));

$mobile_content = file_get_contents('mobile_session_manager.php');
validateCheck("Mobile session manager exists", !empty($mobile_content));

$capacity_content = file_get_contents('capacity_resource_manager.php');
validateCheck("Capacity resource manager exists", !empty($capacity_content));

echo "\n";

// 5. Functional Validation (Basic)
echo "⚙️  Validating Basic Functionality...\n";
echo "------------------------------------\n";

try {
    // Test database connection
    $stmt = $pdo->query("SELECT 1");
    validateCheck("Database connection works", $stmt !== false);
    
    // Test if we can create a test rule
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM events");
    $stmt->execute();
    $event_count = $stmt->fetchColumn();

    $stmt = $pdo->prepare("SELECT id FROM events WHERE title = 'Test Event - Inheritance System' LIMIT 1");
    $stmt->execute();
    $test_event_id = $stmt->fetchColumn();
    $event_exists = $test_event_id !== false;
    
    if ($event_exists) {
        // Try to create a test rule
        $stmt = $pdo->prepare("
            INSERT INTO attendance_inheritance_rules 
            (event_id, rule_name, rule_type, source_criteria, target_criteria, inheritance_logic, is_active, priority_order)
            VALUES (?, ?, ?, ?, ?, ?, FALSE, 999)
        ");
        $result = $stmt->execute([
            $test_event_id,
            'VALIDATION_TEST_RULE',
            'bottom_up',
            json_encode(['session_types' => []]),
            json_encode(['target' => 'event']),
            json_encode(['min_sessions' => 1, 'min_percentage' => 50])
        ]);
        
        validateCheck("Can create inheritance rules", $result);
        
        if ($result) {
            // Clean up test rule
            $stmt = $pdo->prepare("DELETE FROM attendance_inheritance_rules WHERE rule_name = 'VALIDATION_TEST_RULE'");
            $stmt->execute();
        }
    } else {
        validateCheck("Test event exists for validation", false, "No test event found (total events: $event_count)");
    }
    
} catch (Exception $e) {
    validateCheck("Basic functionality test", false, $e->getMessage());
}

echo "\n";

// 6. Security Validation
echo "🔒 Validating Security...\n";
echo "-------------------------\n";

// Check for auth_check includes
validateCheck("Inheritance engine has auth check", 
    strpos($engine_content, "require_once 'includes/auth_check.php'") !== false
);

validateCheck("Test suite has auth check", 
    strpos(file_get_contents('test_inheritance_system.php'), "require_once 'includes/auth_check.php'") !== false
);

// Check for SQL injection protection
validateCheck("Uses prepared statements", 
    strpos($engine_content, '$pdo->prepare(') !== false &&
    strpos($engine_content, '$stmt->execute(') !== false
);

validateCheck("No direct SQL concatenation", 
    strpos($engine_content, '$pdo->query("SELECT * FROM') === false
);

echo "\n";

// 7. Performance Validation
echo "⚡ Validating Performance Considerations...\n";
echo "------------------------------------------\n";

// Check for proper indexing hints in table creation
validateCheck("Tables have proper indexes", 
    strpos($engine_content, 'INDEX idx_event_id') !== false &&
    strpos($engine_content, 'INDEX idx_rule_id') !== false
);

// Check for transaction usage
validateCheck("Uses database transactions", 
    strpos($engine_content, 'beginTransaction()') !== false &&
    strpos($engine_content, 'commit()') !== false &&
    strpos($engine_content, 'rollback()') !== false
);

// Check for batch processing
validateCheck("Implements batch processing", 
    strpos($engine_content, 'foreach') !== false
);

echo "\n";

// Final Summary
echo "📋 Validation Summary\n";
echo "====================\n";
echo "Total Checks: $total_checks\n";
echo "Passed: $passed_checks\n";
echo "Failed: " . ($total_checks - $passed_checks) . "\n";
echo "Success Rate: " . round(($passed_checks / $total_checks) * 100, 1) . "%\n\n";

if ($passed_checks === $total_checks) {
    echo "🎉 ALL VALIDATIONS PASSED! 🎉\n";
    echo "The Enhanced Attendance Inheritance System is ready for production use.\n\n";
    
    echo "✅ Key Features Validated:\n";
    echo "   • Database schema is properly configured\n";
    echo "   • All required files are present and integrated\n";
    echo "   • Core functionality is working\n";
    echo "   • Security measures are in place\n";
    echo "   • Performance optimizations are implemented\n\n";
    
    echo "🚀 Next Steps:\n";
    echo "   1. Run the test suite: test_inheritance_system.php\n";
    echo "   2. Create default inheritance rules for your events\n";
    echo "   3. Train staff on the new inheritance features\n";
    echo "   4. Monitor performance with real event data\n\n";
    
} else {
    echo "⚠️  SOME VALIDATIONS FAILED\n";
    echo "Please review the failed checks above and fix any issues before proceeding.\n\n";
    
    echo "❌ Failed Checks:\n";
    foreach ($validation_results as $result) {
        if ($result['status'] === 'FAIL') {
            echo "   • " . $result['description'];
            if ($result['details']) {
                echo " (" . $result['details'] . ")";
            }
            echo "\n";
        }
    }
    echo "\n";
}

echo "📖 Documentation:\n";
echo "   • Inheritance Engine: attendance_inheritance_engine.php\n";
echo "   • Test Suite: test_inheritance_system.php\n";
echo "   • Integration: Check multi_session_dashboard.php\n\n";

echo "🔧 Troubleshooting:\n";
echo "   • If database errors: Check config.php connection\n";
echo "   • If file errors: Verify file permissions\n";
echo "   • If integration errors: Check include paths\n\n";

echo "Validation completed at: " . date('Y-m-d H:i:s') . "\n";
?>
