<?php
require_once '../config.php';
require_once 'includes/auth_check.php';
require_once 'attendance_inheritance_engine.php';

$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    header('Location: events.php');
    exit();
}

// Get event details
$stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
$stmt->execute([$event_id]);
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    header('Location: events.php');
    exit();
}

$test_results = [];
$test_passed = 0;
$test_failed = 0;

/**
 * Comprehensive Test Suite for Attendance Inheritance System
 */
function runInheritanceTests($pdo, $event_id) {
    global $test_results, $test_passed, $test_failed;
    
    // Test 1: Create test data
    $test_results[] = createTestData($pdo, $event_id);
    
    // Test 2: Test bottom-up inheritance (session → event)
    $test_results[] = testBottomUpInheritance($pdo, $event_id);
    
    // Test 3: Test top-down inheritance (event → session)
    $test_results[] = testTopDownInheritance($pdo, $event_id);
    
    // Test 4: Test peer-to-peer inheritance (session A → session B)
    $test_results[] = testPeerToPeerInheritance($pdo, $event_id);
    
    // Test 5: Test conditional inheritance
    $test_results[] = testConditionalInheritance($pdo, $event_id);
    
    // Test 6: Test complex scenarios
    $test_results[] = testComplexScenarios($pdo, $event_id);
    
    // Count results
    foreach ($test_results as $result) {
        if ($result['status'] === 'PASS') {
            $test_passed++;
        } else {
            $test_failed++;
        }
    }
}

function createTestData($pdo, $event_id) {
    try {
        // Create test sessions
        $test_sessions = [
            ['title' => 'Morning Worship', 'type' => 'worship', 'start' => '2024-01-15 09:00:00'],
            ['title' => 'Workshop A', 'type' => 'workshop', 'start' => '2024-01-15 10:30:00'],
            ['title' => 'Workshop B', 'type' => 'workshop', 'start' => '2024-01-15 11:00:00'],
            ['title' => 'Lunch', 'type' => 'meal', 'start' => '2024-01-15 12:00:00'],
            ['title' => 'Afternoon Session', 'type' => 'main_session', 'start' => '2024-01-15 14:00:00']
        ];
        
        $session_ids = [];
        foreach ($test_sessions as $session) {
            $stmt = $pdo->prepare("
                INSERT INTO event_sessions (event_id, session_title, session_type, start_datetime, end_datetime, max_attendees, status)
                VALUES (?, ?, ?, ?, DATE_ADD(?, INTERVAL 1 HOUR), 100, 'active')
            ");
            $stmt->execute([$event_id, $session['title'], $session['type'], $session['start'], $session['start']]);
            $session_ids[] = $pdo->lastInsertId();
        }
        
        // Create test members if they don't exist
        $test_members = [
            ['name' => 'Test Member 1', 'email' => '<EMAIL>'],
            ['name' => 'Test Member 2', 'email' => '<EMAIL>'],
            ['name' => 'Test Member 3', 'email' => '<EMAIL>']
        ];
        
        $member_ids = [];
        foreach ($test_members as $member) {
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO members (full_name, email, status)
                VALUES (?, ?, 'active')
            ");
            $stmt->execute([$member['name'], $member['email']]);
            
            $stmt = $pdo->prepare("SELECT id FROM members WHERE email = ?");
            $stmt->execute([$member['email']]);
            $member_ids[] = $stmt->fetchColumn();
        }
        
        // Create event RSVPs
        foreach ($member_ids as $member_id) {
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO event_rsvps (event_id, user_id, status, rsvp_date)
                VALUES (?, ?, 'attending', NOW())
            ");
            $stmt->execute([$event_id, $member_id]);
        }
        
        // Create some session attendance records
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO session_attendance (session_id, member_id, attendance_status, attendance_date)
            VALUES (?, ?, 'attended', NOW())
        ");
        
        // Member 1 attends morning worship and workshop A
        $stmt->execute([$session_ids[0], $member_ids[0]]);
        $stmt->execute([$session_ids[1], $member_ids[0]]);
        
        // Member 2 attends workshop A and workshop B
        $stmt->execute([$session_ids[1], $member_ids[1]]);
        $stmt->execute([$session_ids[2], $member_ids[1]]);
        
        return [
            'test' => 'Create Test Data',
            'status' => 'PASS',
            'message' => 'Created test sessions, members, and attendance records',
            'details' => [
                'sessions_created' => count($session_ids),
                'members_created' => count($member_ids),
                'session_ids' => $session_ids,
                'member_ids' => $member_ids
            ]
        ];
        
    } catch (Exception $e) {
        return [
            'test' => 'Create Test Data',
            'status' => 'FAIL',
            'message' => 'Failed to create test data: ' . $e->getMessage(),
            'details' => []
        ];
    }
}

function testBottomUpInheritance($pdo, $event_id) {
    try {
        // Create bottom-up rule
        $stmt = $pdo->prepare("
            INSERT INTO attendance_inheritance_rules 
            (event_id, rule_name, rule_type, source_criteria, target_criteria, inheritance_logic, is_active, priority_order)
            VALUES (?, ?, ?, ?, ?, ?, TRUE, 1)
        ");
        $stmt->execute([
            $event_id,
            'Test Bottom-Up Rule',
            'bottom_up',
            json_encode(['session_types' => []]),
            json_encode(['target' => 'event']),
            json_encode(['min_sessions' => 2, 'min_percentage' => 0])
        ]);
        $rule_id = $pdo->lastInsertId();
        
        // Apply the rule
        $engine = new AttendanceInheritanceEngine($pdo, $event_id, 1);
        $rule_stmt = $pdo->prepare("SELECT * FROM attendance_inheritance_rules WHERE id = ?");
        $rule_stmt->execute([$rule_id]);
        $rule = $rule_stmt->fetch(PDO::FETCH_ASSOC);
        
        $result = $engine->applyRule($rule);
        
        // Check if event attendance was updated
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM event_rsvps 
            WHERE event_id = ? AND actually_attended = 1
        ");
        $stmt->execute([$event_id]);
        $event_attendees = $stmt->fetchColumn();
        
        return [
            'test' => 'Bottom-Up Inheritance (Session → Event)',
            'status' => $event_attendees > 0 ? 'PASS' : 'FAIL',
            'message' => "Applied bottom-up rule, {$result['applied_count']} records updated, {$event_attendees} event attendees marked",
            'details' => $result
        ];
        
    } catch (Exception $e) {
        return [
            'test' => 'Bottom-Up Inheritance',
            'status' => 'FAIL',
            'message' => 'Error: ' . $e->getMessage(),
            'details' => []
        ];
    }
}

function testTopDownInheritance($pdo, $event_id) {
    try {
        // Mark someone as event attendee first
        $stmt = $pdo->prepare("
            UPDATE event_rsvps 
            SET actually_attended = 1 
            WHERE event_id = ? 
            LIMIT 1
        ");
        $stmt->execute([$event_id]);
        
        // Create top-down rule
        $stmt = $pdo->prepare("
            INSERT INTO attendance_inheritance_rules 
            (event_id, rule_name, rule_type, source_criteria, target_criteria, inheritance_logic, is_active, priority_order)
            VALUES (?, ?, ?, ?, ?, ?, TRUE, 2)
        ");
        $stmt->execute([
            $event_id,
            'Test Top-Down Rule',
            'top_down',
            json_encode(['target' => 'event']),
            json_encode(['session_types' => ['worship']]),
            json_encode(['target_status' => 'attended'])
        ]);
        $rule_id = $pdo->lastInsertId();
        
        // Apply the rule
        $engine = new AttendanceInheritanceEngine($pdo, $event_id, 1);
        $rule_stmt = $pdo->prepare("SELECT * FROM attendance_inheritance_rules WHERE id = ?");
        $rule_stmt->execute([$rule_id]);
        $rule = $rule_stmt->fetch(PDO::FETCH_ASSOC);
        
        $result = $engine->applyRule($rule);
        
        return [
            'test' => 'Top-Down Inheritance (Event → Session)',
            'status' => $result['applied_count'] > 0 ? 'PASS' : 'FAIL',
            'message' => "Applied top-down rule, {$result['applied_count']} records updated",
            'details' => $result
        ];
        
    } catch (Exception $e) {
        return [
            'test' => 'Top-Down Inheritance',
            'status' => 'FAIL',
            'message' => 'Error: ' . $e->getMessage(),
            'details' => []
        ];
    }
}

function testPeerToPeerInheritance($pdo, $event_id) {
    try {
        // Get workshop session IDs
        $stmt = $pdo->prepare("
            SELECT id FROM event_sessions 
            WHERE event_id = ? AND session_type = 'workshop'
            ORDER BY id
            LIMIT 2
        ");
        $stmt->execute([$event_id]);
        $workshop_sessions = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (count($workshop_sessions) < 2) {
            return [
                'test' => 'Peer-to-Peer Inheritance',
                'status' => 'SKIP',
                'message' => 'Not enough workshop sessions for peer-to-peer test',
                'details' => []
            ];
        }
        
        // Create peer-to-peer rule
        $stmt = $pdo->prepare("
            INSERT INTO attendance_inheritance_rules 
            (event_id, rule_name, rule_type, source_criteria, target_criteria, inheritance_logic, is_active, priority_order)
            VALUES (?, ?, ?, ?, ?, ?, TRUE, 3)
        ");
        $stmt->execute([
            $event_id,
            'Test Peer-to-Peer Rule',
            'peer_to_peer',
            json_encode(['session_ids' => [$workshop_sessions[0]]]),
            json_encode(['session_ids' => [$workshop_sessions[1]]]),
            json_encode(['copy_status' => 'attended'])
        ]);
        $rule_id = $pdo->lastInsertId();
        
        // Apply the rule
        $engine = new AttendanceInheritanceEngine($pdo, $event_id, 1);
        $rule_stmt = $pdo->prepare("SELECT * FROM attendance_inheritance_rules WHERE id = ?");
        $rule_stmt->execute([$rule_id]);
        $rule = $rule_stmt->fetch(PDO::FETCH_ASSOC);
        
        $result = $engine->applyRule($rule);
        
        return [
            'test' => 'Peer-to-Peer Inheritance (Workshop A → Workshop B)',
            'status' => $result['applied_count'] >= 0 ? 'PASS' : 'FAIL',
            'message' => "Applied peer-to-peer rule, {$result['applied_count']} records updated",
            'details' => $result
        ];
        
    } catch (Exception $e) {
        return [
            'test' => 'Peer-to-Peer Inheritance',
            'status' => 'FAIL',
            'message' => 'Error: ' . $e->getMessage(),
            'details' => []
        ];
    }
}

function testConditionalInheritance($pdo, $event_id) {
    try {
        // Create conditional rule (time-based)
        $stmt = $pdo->prepare("
            INSERT INTO attendance_inheritance_rules 
            (event_id, rule_name, rule_type, source_criteria, target_criteria, inheritance_logic, is_active, priority_order)
            VALUES (?, ?, ?, ?, ?, ?, TRUE, 4)
        ");
        $stmt->execute([
            $event_id,
            'Test Conditional Rule',
            'conditional',
            json_encode(['session_types' => ['workshop']]),
            json_encode(['session_types' => ['workshop']]),
            json_encode(['condition_type' => 'time_based', 'time_window_hours' => 2, 'copy_status' => 'attended'])
        ]);
        $rule_id = $pdo->lastInsertId();
        
        // Apply the rule
        $engine = new AttendanceInheritanceEngine($pdo, $event_id, 1);
        $rule_stmt = $pdo->prepare("SELECT * FROM attendance_inheritance_rules WHERE id = ?");
        $rule_stmt->execute([$rule_id]);
        $rule = $rule_stmt->fetch(PDO::FETCH_ASSOC);
        
        $result = $engine->applyRule($rule);
        
        return [
            'test' => 'Conditional Inheritance (Time-based)',
            'status' => $result['applied_count'] >= 0 ? 'PASS' : 'FAIL',
            'message' => "Applied conditional rule, {$result['applied_count']} records updated",
            'details' => $result
        ];
        
    } catch (Exception $e) {
        return [
            'test' => 'Conditional Inheritance',
            'status' => 'FAIL',
            'message' => 'Error: ' . $e->getMessage(),
            'details' => []
        ];
    }
}

function testComplexScenarios($pdo, $event_id) {
    try {
        // Apply all rules together
        $engine = new AttendanceInheritanceEngine($pdo, $event_id, 1);
        $results = $engine->applyAllRules();
        
        // Check final state
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(DISTINCT er.user_id) as event_attendees,
                COUNT(DISTINCT sa.session_id) as sessions_with_attendance,
                COUNT(sa.id) as total_session_attendance
            FROM event_rsvps er
            LEFT JOIN session_attendance sa ON er.user_id = sa.member_id
            WHERE er.event_id = ? AND er.actually_attended = 1
        ");
        $stmt->execute([$event_id]);
        $final_stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return [
            'test' => 'Complex Scenarios (All Rules Applied)',
            'status' => $results['total_applied'] > 0 ? 'PASS' : 'FAIL',
            'message' => "Applied {$results['rules_executed']} rules, updated {$results['total_applied']} records",
            'details' => array_merge($results, $final_stats)
        ];
        
    } catch (Exception $e) {
        return [
            'test' => 'Complex Scenarios',
            'status' => 'FAIL',
            'message' => 'Error: ' . $e->getMessage(),
            'details' => []
        ];
    }
}

// Run tests if requested
if (isset($_GET['run_tests']) && $_GET['run_tests'] === '1') {
    runInheritanceTests($pdo, $event_id);
}

$page_title = 'Inheritance System Test Suite';
include 'includes/header.php';
?>

<style>
.test-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.test-result {
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.test-result.PASS {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
}

.test-result.FAIL {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
}

.test-result.SKIP {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
}

.test-summary {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.test-details {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
    font-family: monospace;
    font-size: 0.875rem;
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="test-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="bi bi-check2-all"></i> Inheritance System Test Suite</h1>
                        <p class="mb-0">
                            <strong><?php echo htmlspecialchars($event['title']); ?></strong> •
                            Comprehensive testing of attendance inheritance functionality
                        </p>
                    </div>
                    <div>
                        <a href="attendance_inheritance_engine.php?event_id=<?php echo $event_id; ?>" class="btn btn-light me-2">
                            <i class="bi bi-diagram-3"></i> Inheritance Engine
                        </a>
                        <a href="multi_session_dashboard.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-light">
                            <i class="bi bi-arrow-left"></i> Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <?php if (empty($test_results)): ?>
                <!-- Test Controls -->
                <div class="test-summary">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5><i class="bi bi-play-circle"></i> Run Comprehensive Test Suite</h5>
                            <p class="text-muted mb-0">
                                This will test all inheritance scenarios including bottom-up, top-down, peer-to-peer, and conditional rules.
                                <br><strong>Note:</strong> This will create test data and may modify existing attendance records.
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="?event_id=<?php echo $event_id; ?>&run_tests=1" class="btn btn-success btn-lg">
                                <i class="bi bi-play-circle"></i> Run All Tests
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Test Overview -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-list-check"></i> Test Coverage</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check text-success"></i> Test Data Creation</li>
                                    <li><i class="bi bi-arrow-up text-success"></i> Bottom-Up Inheritance (Session → Event)</li>
                                    <li><i class="bi bi-arrow-down text-primary"></i> Top-Down Inheritance (Event → Session)</li>
                                    <li><i class="bi bi-arrow-left-right text-warning"></i> Peer-to-Peer Inheritance (Session A → Session B)</li>
                                    <li><i class="bi bi-gear text-info"></i> Conditional Inheritance (Time-based)</li>
                                    <li><i class="bi bi-diagram-3 text-secondary"></i> Complex Multi-Rule Scenarios</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-info-circle"></i> What Gets Tested</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><strong>Data Integrity:</strong> Proper record creation and updates</li>
                                    <li><strong>Rule Logic:</strong> Each inheritance type works correctly</li>
                                    <li><strong>Edge Cases:</strong> Missing data, conflicts, duplicates</li>
                                    <li><strong>Performance:</strong> Bulk operations complete successfully</li>
                                    <li><strong>Logging:</strong> All changes are properly tracked</li>
                                    <li><strong>Complex Scenarios:</strong> Multiple rules working together</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

            <?php else: ?>
                <!-- Test Results -->
                <div class="test-summary">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <h3 class="text-success"><?php echo $test_passed; ?></h3>
                            <p class="mb-0">Tests Passed</p>
                        </div>
                        <div class="col-md-4">
                            <h3 class="text-danger"><?php echo $test_failed; ?></h3>
                            <p class="mb-0">Tests Failed</p>
                        </div>
                        <div class="col-md-4">
                            <h3 class="text-primary"><?php echo count($test_results); ?></h3>
                            <p class="mb-0">Total Tests</p>
                        </div>
                    </div>

                    <div class="text-center mt-3">
                        <?php if ($test_failed === 0): ?>
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle"></i> <strong>All Tests Passed!</strong>
                                The inheritance system is working correctly.
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i> <strong>Some Tests Failed</strong>
                                Please review the results below and check system configuration.
                            </div>
                        <?php endif; ?>

                        <a href="?event_id=<?php echo $event_id; ?>" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-clockwise"></i> Run Tests Again
                        </a>
                    </div>
                </div>

                <!-- Detailed Results -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-clipboard-data"></i> Detailed Test Results</h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($test_results as $result): ?>
                            <div class="test-result <?php echo $result['status']; ?>">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">
                                            <?php
                                            $icon = $result['status'] === 'PASS' ? 'bi-check-circle' :
                                                   ($result['status'] === 'FAIL' ? 'bi-x-circle' : 'bi-dash-circle');
                                            ?>
                                            <i class="bi <?php echo $icon; ?>"></i>
                                            <?php echo htmlspecialchars($result['test']); ?>
                                        </h6>
                                        <p class="mb-0"><?php echo htmlspecialchars($result['message']); ?></p>

                                        <?php if (!empty($result['details'])): ?>
                                            <div class="test-details">
                                                <strong>Details:</strong><br>
                                                <?php echo htmlspecialchars(json_encode($result['details'], JSON_PRETTY_PRINT)); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <span class="badge bg-<?php echo $result['status'] === 'PASS' ? 'success' : ($result['status'] === 'FAIL' ? 'danger' : 'warning'); ?>">
                                            <?php echo $result['status']; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Auto-scroll to results if tests were run
<?php if (!empty($test_results)): ?>
document.addEventListener('DOMContentLoaded', function() {
    document.querySelector('.test-summary').scrollIntoView({ behavior: 'smooth' });
});
<?php endif; ?>
</script>

<?php include 'includes/footer.php'; ?>
