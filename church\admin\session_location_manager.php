<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

$event_id = $_GET['event_id'] ?? '';
if (empty($event_id)) {
    header("Location: events.php");
    exit();
}

$message = '';
$error = '';

// Get event details
try {
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$event) {
        header("Location: events.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading event: " . $e->getMessage();
}

// Create session_locations table if it doesn't exist
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS session_locations (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            session_id INT(11) NOT NULL,
            location_name VARCHAR(255) NOT NULL,
            location_description TEXT,
            capacity INT(11) DEFAULT NULL,
            equipment_needed TEXT,
            setup_instructions TEXT,
            assigned_staff JSON,
            device_assignments JSON,
            status ENUM('setup', 'active', 'cleanup', 'completed') DEFAULT 'setup',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_session_id (session_id),
            INDEX idx_status (status),
            FOREIGN KEY (session_id) REFERENCES event_sessions(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS session_devices (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            device_name VARCHAR(255) NOT NULL,
            device_type ENUM('tablet', 'phone', 'laptop', 'scanner') DEFAULT 'tablet',
            device_id VARCHAR(255) UNIQUE,
            assigned_session INT(11) DEFAULT NULL,
            assigned_staff INT(11) DEFAULT NULL,
            last_sync TIMESTAMP NULL,
            battery_level INT(11) DEFAULT NULL,
            status ENUM('available', 'assigned', 'in_use', 'offline') DEFAULT 'available',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_device_id (device_id),
            INDEX idx_assigned_session (assigned_session),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
} catch (PDOException $e) {
    error_log("Error creating session location tables: " . $e->getMessage());
}

// Handle location management actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'assign_location') {
            $session_id = $_POST['session_id'] ?? '';
            $location_name = $_POST['location_name'] ?? '';
            $location_description = $_POST['location_description'] ?? '';
            $capacity = $_POST['capacity'] ?? null;
            $equipment_needed = $_POST['equipment_needed'] ?? '';
            $setup_instructions = $_POST['setup_instructions'] ?? '';
            $assigned_staff = $_POST['assigned_staff'] ?? [];
            
            if (empty($session_id) || empty($location_name)) {
                throw new Exception("Session and location name are required.");
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO session_locations 
                (session_id, location_name, location_description, capacity, equipment_needed, setup_instructions, assigned_staff)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                location_name = VALUES(location_name),
                location_description = VALUES(location_description),
                capacity = VALUES(capacity),
                equipment_needed = VALUES(equipment_needed),
                setup_instructions = VALUES(setup_instructions),
                assigned_staff = VALUES(assigned_staff),
                updated_at = NOW()
            ");
            $stmt->execute([
                $session_id, 
                $location_name, 
                $location_description, 
                $capacity, 
                $equipment_needed, 
                $setup_instructions,
                json_encode($assigned_staff)
            ]);
            
            $message = "Location assigned successfully!";
            
        } elseif ($_POST['action'] === 'assign_device') {
            $device_id = $_POST['device_id'] ?? '';
            $session_id = $_POST['session_id'] ?? '';
            $staff_id = $_POST['staff_id'] ?? '';
            
            if (empty($device_id) || empty($session_id)) {
                throw new Exception("Device and session are required.");
            }
            
            $stmt = $pdo->prepare("
                UPDATE session_devices 
                SET assigned_session = ?, assigned_staff = ?, status = 'assigned', updated_at = NOW()
                WHERE device_id = ?
            ");
            $stmt->execute([$session_id, $staff_id ?: null, $device_id]);
            
            $message = "Device assigned successfully!";
            
        } elseif ($_POST['action'] === 'update_location_status') {
            $location_id = $_POST['location_id'] ?? '';
            $status = $_POST['status'] ?? '';
            
            if (empty($location_id) || empty($status)) {
                throw new Exception("Location and status are required.");
            }
            
            $stmt = $pdo->prepare("
                UPDATE session_locations 
                SET status = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$status, $location_id]);
            
            $message = "Location status updated successfully!";
            
        } elseif ($_POST['action'] === 'register_device') {
            $device_name = $_POST['device_name'] ?? '';
            $device_type = $_POST['device_type'] ?? 'tablet';
            $device_id = $_POST['device_id'] ?? '';
            
            if (empty($device_name) || empty($device_id)) {
                throw new Exception("Device name and ID are required.");
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO session_devices (device_name, device_type, device_id)
                VALUES (?, ?, ?)
                ON DUPLICATE KEY UPDATE
                device_name = VALUES(device_name),
                device_type = VALUES(device_type),
                updated_at = NOW()
            ");
            $stmt->execute([$device_name, $device_type, $device_id]);
            
            $message = "Device registered successfully!";
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get sessions with location assignments
$stmt = $pdo->prepare("
    SELECT 
        s.*,
        sl.id as location_id,
        sl.location_name,
        sl.location_description,
        sl.capacity,
        sl.equipment_needed,
        sl.setup_instructions,
        sl.assigned_staff,
        sl.device_assignments,
        sl.status as location_status,
        COUNT(sa.id) as registered_count,
        COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as attended_count
    FROM event_sessions s
    LEFT JOIN session_locations sl ON s.id = sl.session_id
    LEFT JOIN session_attendance sa ON s.id = sa.session_id
    WHERE s.event_id = ? AND s.status = 'active'
    GROUP BY s.id
    ORDER BY s.start_datetime
");
$stmt->execute([$event_id]);
$sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get available devices
$stmt = $pdo->prepare("
    SELECT 
        sd.*,
        s.session_title,
        m.full_name as staff_name
    FROM session_devices sd
    LEFT JOIN event_sessions s ON sd.assigned_session = s.id
    LEFT JOIN members m ON sd.assigned_staff = m.id
    ORDER BY sd.status, sd.device_name
");
$stmt->execute();
$devices = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get staff members for assignment
$stmt = $pdo->prepare("SELECT id, full_name, email FROM members WHERE is_active = 1 ORDER BY full_name");
$stmt->execute();
$staff_members = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Page title and header info
$page_title = 'Session Location Manager';
$page_header = 'Session Location Manager';
$page_description = 'Manage session locations, staff assignments, and device coordination';

// Include header
include 'includes/header.php';
?>

<style>
.location-card {
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
    border-radius: 10px;
}
.location-card.assigned {
    border-color: #28a745;
    background-color: #f8fff9;
}
.location-card.active {
    border-color: #007bff;
    background-color: #f8f9ff;
}
.device-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 12px;
    margin: 8px 0;
    transition: all 0.3s ease;
}
.device-item.available {
    border-color: #28a745;
    background-color: #f8fff9;
}
.device-item.assigned {
    border-color: #ffc107;
    background-color: #fffbf0;
}
.device-item.in_use {
    border-color: #007bff;
    background-color: #f8f9ff;
}
.device-item.offline {
    border-color: #dc3545;
    background-color: #fff5f5;
}
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}
.status-setup { background-color: #6c757d; }
.status-active { background-color: #28a745; }
.status-cleanup { background-color: #ffc107; }
.status-completed { background-color: #007bff; }
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <p class="text-muted mb-0">Event: <strong><?php echo htmlspecialchars($event['title']); ?></strong></p>
                <small class="text-muted"><?php echo date('F j, Y g:i A', strtotime($event['event_date'])); ?> • <?php echo htmlspecialchars($event['location']); ?></small>
            </div>
            <div>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#deviceModal">
                    <i class="bi bi-plus-circle"></i> Register Device
                </button>
                <a href="event_attendance_detail.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left"></i> Back to Event
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Device Status Overview -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-devices"></i> Device Status Overview</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php 
                    $device_stats = [
                        'available' => 0,
                        'assigned' => 0,
                        'in_use' => 0,
                        'offline' => 0
                    ];
                    foreach ($devices as $device) {
                        $device_stats[$device['status']]++;
                    }
                    ?>
                    <div class="col-md-3 text-center">
                        <h4 class="text-success"><?php echo $device_stats['available']; ?></h4>
                        <small class="text-muted">Available</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-warning"><?php echo $device_stats['assigned']; ?></h4>
                        <small class="text-muted">Assigned</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-primary"><?php echo $device_stats['in_use']; ?></h4>
                        <small class="text-muted">In Use</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-danger"><?php echo $device_stats['offline']; ?></h4>
                        <small class="text-muted">Offline</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sessions and Locations -->
<div class="row">
    <div class="col-md-8">
        <h5><i class="bi bi-calendar-event"></i> Sessions & Locations</h5>
        
        <?php foreach ($sessions as $session): ?>
            <div class="card location-card <?php echo $session['location_id'] ? ($session['location_status'] === 'active' ? 'active' : 'assigned') : ''; ?> mb-3">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0"><?php echo htmlspecialchars($session['session_title']); ?></h6>
                            <small class="text-muted">
                                <?php echo date('M j, Y g:i A', strtotime($session['start_datetime'])); ?>
                                <?php if ($session['location_name']): ?>
                                    • <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($session['location_name']); ?>
                                <?php endif; ?>
                            </small>
                        </div>
                        <div class="text-end">
                            <?php if ($session['location_id']): ?>
                                <span class="status-indicator status-<?php echo $session['location_status']; ?>"></span>
                                <span class="badge bg-<?php 
                                    echo $session['location_status'] === 'active' ? 'success' : 
                                        ($session['location_status'] === 'setup' ? 'secondary' : 
                                        ($session['location_status'] === 'cleanup' ? 'warning' : 'primary')); 
                                ?>">
                                    <?php echo ucfirst($session['location_status']); ?>
                                </span>
                            <?php else: ?>
                                <span class="badge bg-secondary">No Location</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <?php if ($session['location_id']): ?>
                        <!-- Location Details -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>Capacity:</strong> <?php echo $session['capacity'] ?: 'Unlimited'; ?><br>
                                <strong>Registered:</strong> <?php echo $session['registered_count']; ?><br>
                                <strong>Attended:</strong> <?php echo $session['attended_count']; ?>
                            </div>
                            <div class="col-md-6">
                                <?php if ($session['assigned_staff']): ?>
                                    <strong>Assigned Staff:</strong><br>
                                    <?php 
                                    $staff_ids = json_decode($session['assigned_staff'], true);
                                    foreach ($staff_ids as $staff_id) {
                                        $staff = array_filter($staff_members, function($s) use ($staff_id) {
                                            return $s['id'] == $staff_id;
                                        });
                                        if (!empty($staff)) {
                                            $staff = array_values($staff)[0];
                                            echo '<span class="badge bg-primary me-1">' . htmlspecialchars($staff['full_name']) . '</span>';
                                        }
                                    }
                                    ?>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <?php if ($session['equipment_needed']): ?>
                            <div class="mb-2">
                                <strong>Equipment:</strong> <?php echo htmlspecialchars($session['equipment_needed']); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($session['setup_instructions']): ?>
                            <div class="mb-3">
                                <strong>Setup Instructions:</strong><br>
                                <small class="text-muted"><?php echo nl2br(htmlspecialchars($session['setup_instructions'])); ?></small>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Status Update -->
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="action" value="update_location_status">
                            <input type="hidden" name="location_id" value="<?php echo $session['location_id']; ?>">
                            <div class="btn-group" role="group">
                                <button type="submit" name="status" value="setup" class="btn btn-sm btn-outline-secondary">Setup</button>
                                <button type="submit" name="status" value="active" class="btn btn-sm btn-outline-success">Active</button>
                                <button type="submit" name="status" value="cleanup" class="btn btn-sm btn-outline-warning">Cleanup</button>
                                <button type="submit" name="status" value="completed" class="btn btn-sm btn-outline-primary">Completed</button>
                            </div>
                        </form>
                        
                        <a href="mobile_checkin.php?event_id=<?php echo $event_id; ?>&session_id=<?php echo $session['id']; ?>" 
                           class="btn btn-sm btn-primary ms-2">
                            <i class="bi bi-phone"></i> Mobile Check-in
                        </a>
                        
                    <?php else: ?>
                        <!-- Assign Location Form -->
                        <button class="btn btn-success" onclick="showLocationForm(<?php echo $session['id']; ?>)">
                            <i class="bi bi-plus-circle"></i> Assign Location
                        </button>
                        
                        <div id="location-form-<?php echo $session['id']; ?>" style="display: none;" class="mt-3">
                            <form method="POST">
                                <input type="hidden" name="action" value="assign_location">
                                <input type="hidden" name="session_id" value="<?php echo $session['id']; ?>">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Location Name</label>
                                            <input type="text" class="form-control" name="location_name" required>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Capacity</label>
                                            <input type="number" class="form-control" name="capacity">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Description</label>
                                            <textarea class="form-control" name="location_description" rows="2"></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Assigned Staff</label>
                                            <select class="form-select" name="assigned_staff[]" multiple>
                                                <?php foreach ($staff_members as $staff): ?>
                                                    <option value="<?php echo $staff['id']; ?>">
                                                        <?php echo htmlspecialchars($staff['full_name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Equipment Needed</label>
                                    <input type="text" class="form-control" name="equipment_needed" 
                                           placeholder="e.g., Projector, Microphone, Tables">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Setup Instructions</label>
                                    <textarea class="form-control" name="setup_instructions" rows="3"></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-success">
                                    <i class="bi bi-check-circle"></i> Assign Location
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="hideLocationForm(<?php echo $session['id']; ?>)">
                                    Cancel
                                </button>
                            </form>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    
    <!-- Device Management -->
    <div class="col-md-4">
        <h5><i class="bi bi-devices"></i> Device Management</h5>
        
        <div class="card">
            <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                <?php foreach ($devices as $device): ?>
                    <div class="device-item <?php echo $device['status']; ?>">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong><?php echo htmlspecialchars($device['device_name']); ?></strong>
                                <br>
                                <small class="text-muted">
                                    <i class="bi bi-<?php 
                                        echo $device['device_type'] === 'tablet' ? 'tablet' : 
                                            ($device['device_type'] === 'phone' ? 'phone' : 
                                            ($device['device_type'] === 'laptop' ? 'laptop' : 'upc-scan')); 
                                    ?>"></i>
                                    <?php echo ucfirst($device['device_type']); ?>
                                    • ID: <?php echo htmlspecialchars($device['device_id']); ?>
                                </small>
                                
                                <?php if ($device['session_title']): ?>
                                    <br>
                                    <small class="text-primary">
                                        <i class="bi bi-calendar"></i> <?php echo htmlspecialchars($device['session_title']); ?>
                                    </small>
                                <?php endif; ?>
                                
                                <?php if ($device['staff_name']): ?>
                                    <br>
                                    <small class="text-success">
                                        <i class="bi bi-person"></i> <?php echo htmlspecialchars($device['staff_name']); ?>
                                    </small>
                                <?php endif; ?>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-<?php 
                                    echo $device['status'] === 'available' ? 'success' : 
                                        ($device['status'] === 'assigned' ? 'warning' : 
                                        ($device['status'] === 'in_use' ? 'primary' : 'danger')); 
                                ?>">
                                    <?php echo ucfirst(str_replace('_', ' ', $device['status'])); ?>
                                </span>
                                
                                <?php if ($device['battery_level']): ?>
                                    <br>
                                    <small class="text-muted">
                                        <i class="bi bi-battery-<?php 
                                            echo $device['battery_level'] > 75 ? 'full' : 
                                                ($device['battery_level'] > 50 ? 'three-quarters' : 
                                                ($device['battery_level'] > 25 ? 'half' : 'quarter')); 
                                        ?>"></i>
                                        <?php echo $device['battery_level']; ?>%
                                    </small>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <?php if ($device['status'] === 'available'): ?>
                            <div class="mt-2">
                                <button class="btn btn-sm btn-primary" onclick="showDeviceAssignment('<?php echo $device['device_id']; ?>')">
                                    <i class="bi bi-plus"></i> Assign
                                </button>
                            </div>
                            
                            <div id="device-assignment-<?php echo $device['device_id']; ?>" style="display: none;" class="mt-2">
                                <form method="POST">
                                    <input type="hidden" name="action" value="assign_device">
                                    <input type="hidden" name="device_id" value="<?php echo $device['device_id']; ?>">
                                    
                                    <div class="mb-2">
                                        <select class="form-select form-select-sm" name="session_id" required>
                                            <option value="">Select Session</option>
                                            <?php foreach ($sessions as $session): ?>
                                                <option value="<?php echo $session['id']; ?>">
                                                    <?php echo htmlspecialchars($session['session_title']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-2">
                                        <select class="form-select form-select-sm" name="staff_id">
                                            <option value="">Select Staff (Optional)</option>
                                            <?php foreach ($staff_members as $staff): ?>
                                                <option value="<?php echo $staff['id']; ?>">
                                                    <?php echo htmlspecialchars($staff['full_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-sm btn-success">Assign</button>
                                    <button type="button" class="btn btn-sm btn-secondary" onclick="hideDeviceAssignment('<?php echo $device['device_id']; ?>')">Cancel</button>
                                </form>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<!-- Device Registration Modal -->
<div class="modal fade" id="deviceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Register New Device</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="register_device">
                    
                    <div class="mb-3">
                        <label class="form-label">Device Name</label>
                        <input type="text" class="form-control" name="device_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Device Type</label>
                        <select class="form-select" name="device_type" required>
                            <option value="tablet">Tablet</option>
                            <option value="phone">Phone</option>
                            <option value="laptop">Laptop</option>
                            <option value="scanner">Scanner</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Device ID</label>
                        <input type="text" class="form-control" name="device_id" required 
                               placeholder="Unique identifier for this device">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Register Device</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
function showLocationForm(sessionId) {
    document.getElementById('location-form-' + sessionId).style.display = 'block';
}

function hideLocationForm(sessionId) {
    document.getElementById('location-form-' + sessionId).style.display = 'none';
}

function showDeviceAssignment(deviceId) {
    document.getElementById('device-assignment-' + deviceId).style.display = 'block';
}

function hideDeviceAssignment(deviceId) {
    document.getElementById('device-assignment-' + deviceId).style.display = 'none';
}

// Auto-refresh device status every 30 seconds
setInterval(function() {
    // This would make an AJAX call to update device status
    console.log('Refreshing device status...');
}, 30000);
</script>

<?php include 'includes/footer.php'; ?>
