<?php
require_once '../config.php';
require_once 'includes/auth_check.php';

$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    header('Location: events.php');
    exit();
}

// Get event details
$stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
$stmt->execute([$event_id]);
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    header('Location: events.php');
    exit();
}

// Create capacity management tables if they don't exist
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS capacity_alerts (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            event_id INT(11) NOT NULL,
            session_id INT(11) NOT NULL,
            alert_type ENUM('capacity_warning', 'capacity_full', 'low_utilization', 'resource_conflict') NOT NULL,
            alert_message TEXT NOT NULL,
            alert_data JSON,
            is_resolved BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            resolved_at TIMESTAMP NULL,
            INDEX idx_event_id (event_id),
            INDEX idx_session_id (session_id),
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
            FOREIGN KEY (session_id) REFERENCES event_sessions(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS waitlist_management (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            session_id INT(11) NOT NULL,
            member_id INT(11) DEFAULT NULL,
            guest_name VARCHAR(255) DEFAULT NULL,
            guest_email VARCHAR(255) DEFAULT NULL,
            waitlist_position INT(11) NOT NULL,
            added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notified_at TIMESTAMP NULL,
            converted_at TIMESTAMP NULL,
            status ENUM('waiting', 'notified', 'converted', 'expired') DEFAULT 'waiting',
            INDEX idx_session_id (session_id),
            INDEX idx_member_id (member_id),
            FOREIGN KEY (session_id) REFERENCES event_sessions(id) ON DELETE CASCADE,
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS resource_allocations (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            event_id INT(11) NOT NULL,
            resource_name VARCHAR(255) NOT NULL,
            resource_type ENUM('room', 'equipment', 'staff', 'catering') NOT NULL,
            total_capacity INT(11) DEFAULT NULL,
            allocated_sessions JSON,
            allocation_conflicts JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_event_id (event_id),
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
} catch (PDOException $e) {
    error_log("Error creating capacity management tables: " . $e->getMessage());
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'auto_optimize_capacity') {
            $optimization_results = performCapacityOptimization($pdo, $event_id);
            $message = "Capacity optimization completed! " . $optimization_results['summary'];
            
        } elseif ($action === 'resolve_alert') {
            $alert_id = (int)$_POST['alert_id'];
            $stmt = $pdo->prepare("
                UPDATE capacity_alerts 
                SET is_resolved = TRUE, resolved_at = NOW() 
                WHERE id = ? AND event_id = ?
            ");
            $stmt->execute([$alert_id, $event_id]);
            $message = "Alert resolved successfully.";
            
        } elseif ($action === 'add_to_waitlist') {
            $session_id = (int)$_POST['session_id'];
            $member_id = $_POST['member_id'] ? (int)$_POST['member_id'] : null;
            $guest_name = $_POST['guest_name'] ?? null;
            $guest_email = $_POST['guest_email'] ?? null;
            
            // Get next waitlist position
            $stmt = $pdo->prepare("
                SELECT COALESCE(MAX(waitlist_position), 0) + 1 as next_position
                FROM waitlist_management 
                WHERE session_id = ?
            ");
            $stmt->execute([$session_id]);
            $next_position = $stmt->fetchColumn();
            
            $stmt = $pdo->prepare("
                INSERT INTO waitlist_management (session_id, member_id, guest_name, guest_email, waitlist_position)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$session_id, $member_id, $guest_name, $guest_email, $next_position]);
            
            $message = "Added to waitlist at position {$next_position}.";
            
        } elseif ($action === 'process_waitlist') {
            $session_id = (int)$_POST['session_id'];
            $processed_count = processWaitlist($pdo, $session_id);
            $message = "Processed waitlist: {$processed_count} people notified.";
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Capacity optimization function
function performCapacityOptimization($pdo, $event_id) {
    $results = ['summary' => '', 'actions' => []];
    
    // Get sessions with capacity issues
    $stmt = $pdo->prepare("
        SELECT 
            es.*,
            COUNT(sa.id) as current_registrations,
            ROUND((COUNT(sa.id) / NULLIF(es.max_attendees, 0)) * 100, 1) as utilization_rate
        FROM event_sessions es
        LEFT JOIN session_attendance sa ON es.id = sa.session_id
        WHERE es.event_id = ? AND es.status = 'active' AND es.max_attendees > 0
        GROUP BY es.id
        HAVING utilization_rate > 90 OR utilization_rate < 30
        ORDER BY utilization_rate DESC
    ");
    $stmt->execute([$event_id]);
    $sessions_needing_optimization = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $actions_taken = 0;
    
    foreach ($sessions_needing_optimization as $session) {
        if ($session['utilization_rate'] > 90) {
            // Over-capacity: create alert and suggest solutions
            $alert_data = [
                'current_registrations' => $session['current_registrations'],
                'max_capacity' => $session['max_attendees'],
                'utilization_rate' => $session['utilization_rate'],
                'suggested_actions' => [
                    'Increase room capacity',
                    'Add additional session',
                    'Move to larger venue'
                ]
            ];
            
            $stmt = $pdo->prepare("
                INSERT INTO capacity_alerts (event_id, session_id, alert_type, alert_message, alert_data)
                VALUES (?, ?, 'capacity_warning', ?, ?)
            ");
            $stmt->execute([
                $event_id,
                $session['id'],
                "Session '{$session['session_title']}' is at {$session['utilization_rate']}% capacity",
                json_encode($alert_data)
            ]);
            $actions_taken++;
            
        } elseif ($session['utilization_rate'] < 30) {
            // Under-utilized: suggest consolidation
            $alert_data = [
                'current_registrations' => $session['current_registrations'],
                'max_capacity' => $session['max_attendees'],
                'utilization_rate' => $session['utilization_rate'],
                'suggested_actions' => [
                    'Reduce room size',
                    'Combine with similar session',
                    'Cancel if very low attendance'
                ]
            ];
            
            $stmt = $pdo->prepare("
                INSERT INTO capacity_alerts (event_id, session_id, alert_type, alert_message, alert_data)
                VALUES (?, ?, 'low_utilization', ?, ?)
            ");
            $stmt->execute([
                $event_id,
                $session['id'],
                "Session '{$session['session_title']}' has low utilization at {$session['utilization_rate']}%",
                json_encode($alert_data)
            ]);
            $actions_taken++;
        }
    }
    
    $results['summary'] = "Generated {$actions_taken} optimization alerts.";
    return $results;
}

// Waitlist processing function
function processWaitlist($pdo, $session_id) {
    // Check current capacity
    $stmt = $pdo->prepare("
        SELECT 
            es.max_attendees,
            COUNT(sa.id) as current_registrations
        FROM event_sessions es
        LEFT JOIN session_attendance sa ON es.id = sa.session_id
        WHERE es.id = ?
        GROUP BY es.id
    ");
    $stmt->execute([$session_id]);
    $capacity_info = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$capacity_info || !$capacity_info['max_attendees']) {
        return 0;
    }
    
    $available_spots = $capacity_info['max_attendees'] - $capacity_info['current_registrations'];
    
    if ($available_spots <= 0) {
        return 0;
    }
    
    // Get waitlist entries to process
    $stmt = $pdo->prepare("
        SELECT * FROM waitlist_management 
        WHERE session_id = ? AND status = 'waiting'
        ORDER BY waitlist_position
        LIMIT ?
    ");
    $stmt->execute([$session_id, $available_spots]);
    $waitlist_entries = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $processed_count = 0;
    
    foreach ($waitlist_entries as $entry) {
        // Add to session attendance
        if ($entry['member_id']) {
            $stmt = $pdo->prepare("
                INSERT INTO session_attendance (session_id, member_id, attendance_status)
                VALUES (?, ?, 'registered')
            ");
            $stmt->execute([$session_id, $entry['member_id']]);
        } else {
            $stmt = $pdo->prepare("
                INSERT INTO session_attendance (session_id, guest_name, guest_email, attendance_status)
                VALUES (?, ?, ?, 'registered')
            ");
            $stmt->execute([$session_id, $entry['guest_name'], $entry['guest_email']]);
        }
        
        // Update waitlist status
        $stmt = $pdo->prepare("
            UPDATE waitlist_management 
            SET status = 'converted', converted_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$entry['id']]);
        
        $processed_count++;
    }
    
    return $processed_count;
}

// Get current capacity alerts
$stmt = $pdo->prepare("
    SELECT ca.*, es.session_title
    FROM capacity_alerts ca
    JOIN event_sessions es ON ca.session_id = es.id
    WHERE ca.event_id = ? AND ca.is_resolved = FALSE
    ORDER BY ca.created_at DESC
");
$stmt->execute([$event_id]);
$active_alerts = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get capacity analysis
$stmt = $pdo->prepare("
    SELECT 
        es.*,
        COUNT(sa.id) as current_registrations,
        COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as current_attended,
        ROUND((COUNT(sa.id) / NULLIF(es.max_attendees, 0)) * 100, 1) as utilization_rate,
        CASE 
            WHEN es.max_attendees IS NULL THEN 'No Limit'
            WHEN COUNT(sa.id) >= es.max_attendees THEN 'Full'
            WHEN COUNT(sa.id) >= es.max_attendees * 0.9 THEN 'Near Full'
            WHEN COUNT(sa.id) >= es.max_attendees * 0.7 THEN 'Good'
            WHEN COUNT(sa.id) >= es.max_attendees * 0.3 THEN 'Low'
            ELSE 'Very Low'
        END as capacity_status
    FROM event_sessions es
    LEFT JOIN session_attendance sa ON es.id = sa.session_id
    WHERE es.event_id = ? AND es.status = 'active'
    GROUP BY es.id
    ORDER BY utilization_rate DESC
");
$stmt->execute([$event_id]);
$capacity_analysis = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get waitlist summary
$stmt = $pdo->prepare("
    SELECT 
        es.session_title,
        COUNT(wm.id) as waitlist_count,
        COUNT(CASE WHEN wm.status = 'waiting' THEN 1 END) as active_waitlist
    FROM event_sessions es
    LEFT JOIN waitlist_management wm ON es.id = wm.session_id
    WHERE es.event_id = ?
    GROUP BY es.id
    HAVING waitlist_count > 0
    ORDER BY active_waitlist DESC
");
$stmt->execute([$event_id]);
$waitlist_summary = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'Capacity & Resource Manager';
include 'includes/header.php';
?>

<style>
.capacity-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.alert-card {
    border-radius: 10px;
    margin-bottom: 1rem;
    border-left: 4px solid;
    transition: all 0.2s;
}

.alert-card.capacity_warning {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
}

.alert-card.capacity_full {
    border-left-color: #721c24;
    background: linear-gradient(135deg, #f8d7da, #f1aeb5);
}

.alert-card.low_utilization {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
}

.capacity-meter {
    height: 25px;
    background: #e9ecef;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

.capacity-fill {
    height: 100%;
    transition: width 0.5s ease;
    border-radius: 12px;
}

.capacity-fill.full {
    background: linear-gradient(90deg, #dc3545, #c82333);
}

.capacity-fill.near-full {
    background: linear-gradient(90deg, #fd7e14, #e55a00);
}

.capacity-fill.good {
    background: linear-gradient(90deg, #28a745, #1e7e34);
}

.capacity-fill.low {
    background: linear-gradient(90deg, #ffc107, #e0a800);
}

.capacity-fill.very-low {
    background: linear-gradient(90deg, #6c757d, #545b62);
}

.optimization-panel {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.waitlist-badge {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 500;
}

.resource-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.2s;
}

.resource-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="capacity-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="bi bi-speedometer2"></i> Capacity & Resource Manager</h1>
                        <p class="mb-0">
                            <strong><?php echo htmlspecialchars($event['title']); ?></strong> •
                            <?php echo date('M j, Y', strtotime($event['event_date'])); ?>
                        </p>
                    </div>
                    <div>
                        <a href="advanced_analytics.php?event_id=<?php echo $event_id; ?>" class="btn btn-light me-2">
                            <i class="bi bi-graph-up"></i> Analytics
                        </a>
                        <a href="multi_session_dashboard.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-light">
                            <i class="bi bi-arrow-left"></i> Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Quick Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h4 class="text-danger"><?php echo count(array_filter($capacity_analysis, function($s) { return $s['capacity_status'] === 'Full'; })); ?></h4>
                    <small>Sessions at Capacity</small>
                </div>
                <div class="stat-card">
                    <h4 class="text-warning"><?php echo count(array_filter($capacity_analysis, function($s) { return $s['capacity_status'] === 'Near Full'; })); ?></h4>
                    <small>Near Capacity</small>
                </div>
                <div class="stat-card">
                    <h4 class="text-info"><?php echo array_sum(array_column($waitlist_summary, 'active_waitlist')); ?></h4>
                    <small>People on Waitlists</small>
                </div>
                <div class="stat-card">
                    <h4 class="text-secondary"><?php echo count($active_alerts); ?></h4>
                    <small>Active Alerts</small>
                </div>
            </div>

            <!-- Auto-Optimization Panel -->
            <div class="optimization-panel">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5><i class="bi bi-magic"></i> Automated Capacity Optimization</h5>
                        <p class="text-muted mb-0">
                            AI-powered system to automatically detect capacity issues and suggest optimizations
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="action" value="auto_optimize_capacity">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-magic"></i> Run Optimization
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Capacity Alerts -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-exclamation-triangle"></i> Capacity Alerts
                                <span class="badge bg-danger ms-2"><?php echo count($active_alerts); ?></span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($active_alerts)): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-check-circle" style="font-size: 3rem; color: #28a745;"></i>
                                    <h6 class="mt-2 text-success">All Good!</h6>
                                    <p>No capacity alerts at this time</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($active_alerts as $alert): ?>
                                    <div class="alert-card <?php echo $alert['alert_type']; ?>">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <?php
                                                        $icon = $alert['alert_type'] === 'capacity_warning' ? 'bi-exclamation-triangle' :
                                                               ($alert['alert_type'] === 'low_utilization' ? 'bi-info-circle' : 'bi-x-circle');
                                                        ?>
                                                        <i class="bi <?php echo $icon; ?>"></i>
                                                        <?php echo htmlspecialchars($alert['session_title']); ?>
                                                    </h6>
                                                    <p class="mb-2"><?php echo htmlspecialchars($alert['alert_message']); ?></p>
                                                    <small class="text-muted">
                                                        <?php echo date('M j, g:i A', strtotime($alert['created_at'])); ?>
                                                    </small>

                                                    <?php if ($alert['alert_data']): ?>
                                                        <?php $data = json_decode($alert['alert_data'], true); ?>
                                                        <?php if (isset($data['suggested_actions'])): ?>
                                                            <div class="mt-2">
                                                                <small class="fw-bold">Suggested Actions:</small>
                                                                <ul class="small mb-0">
                                                                    <?php foreach ($data['suggested_actions'] as $action): ?>
                                                                        <li><?php echo htmlspecialchars($action); ?></li>
                                                                    <?php endforeach; ?>
                                                                </ul>
                                                            </div>
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </div>
                                                <div>
                                                    <form method="POST" class="d-inline">
                                                        <input type="hidden" name="action" value="resolve_alert">
                                                        <input type="hidden" name="alert_id" value="<?php echo $alert['id']; ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-success">
                                                            <i class="bi bi-check"></i> Resolve
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Waitlist Management -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-list-ol"></i> Waitlist Management
                                <span class="badge bg-info ms-2"><?php echo count($waitlist_summary); ?></span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($waitlist_summary)): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-list-ol" style="font-size: 3rem;"></i>
                                    <h6 class="mt-2">No Waitlists</h6>
                                    <p>No sessions have active waitlists</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($waitlist_summary as $waitlist): ?>
                                    <div class="resource-card">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1"><?php echo htmlspecialchars($waitlist['session_title']); ?></h6>
                                                <div class="waitlist-badge">
                                                    <?php echo $waitlist['active_waitlist']; ?> waiting
                                                </div>
                                            </div>
                                            <div>
                                                <form method="POST" class="d-inline">
                                                    <input type="hidden" name="action" value="process_waitlist">
                                                    <input type="hidden" name="session_id" value="<?php echo $waitlist['session_id']; ?>">
                                                    <button type="submit" class="btn btn-primary btn-sm">
                                                        <i class="bi bi-arrow-up"></i> Process
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

            <!-- Capacity Analysis -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-speedometer2"></i> Session Capacity Analysis</h5>
                            <small class="text-muted">Real-time capacity utilization and optimization opportunities</small>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Session</th>
                                            <th>Capacity</th>
                                            <th>Registered</th>
                                            <th>Attended</th>
                                            <th>Utilization</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($capacity_analysis as $session): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($session['session_title']); ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <?php echo date('M j, g:i A', strtotime($session['start_datetime'])); ?>
                                                        <?php if ($session['location']): ?>
                                                            • <?php echo htmlspecialchars($session['location']); ?>
                                                        <?php endif; ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <?php if ($session['max_attendees']): ?>
                                                        <?php echo number_format($session['max_attendees']); ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">No limit</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo number_format($session['current_registrations']); ?></td>
                                                <td><?php echo number_format($session['current_attended']); ?></td>
                                                <td>
                                                    <?php if ($session['max_attendees']): ?>
                                                        <div class="capacity-meter">
                                                            <div class="capacity-fill <?php echo strtolower(str_replace(' ', '-', $session['capacity_status'])); ?>"
                                                                 style="width: <?php echo min($session['utilization_rate'], 100); ?>%"></div>
                                                        </div>
                                                        <small class="text-muted"><?php echo $session['utilization_rate']; ?>%</small>
                                                    <?php else: ?>
                                                        <span class="text-muted">N/A</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php
                                                        echo $session['capacity_status'] === 'Full' ? 'danger' :
                                                            ($session['capacity_status'] === 'Near Full' ? 'warning' :
                                                            ($session['capacity_status'] === 'Good' ? 'success' :
                                                            ($session['capacity_status'] === 'Low' ? 'info' : 'secondary')));
                                                    ?>">
                                                        <?php echo $session['capacity_status']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <?php if ($session['capacity_status'] === 'Full'): ?>
                                                            <button class="btn btn-sm btn-outline-primary"
                                                                    onclick="showWaitlistModal(<?php echo $session['id']; ?>, '<?php echo addslashes($session['session_title']); ?>')">
                                                                <i class="bi bi-plus"></i> Waitlist
                                                            </button>
                                                        <?php endif; ?>

                                                        <button class="btn btn-sm btn-outline-info"
                                                                onclick="showCapacityDetails(<?php echo $session['id']; ?>)">
                                                            <i class="bi bi-info-circle"></i> Details
                                                        </button>

                                                        <?php if ($session['capacity_status'] === 'Very Low' || $session['capacity_status'] === 'Low'): ?>
                                                            <button class="btn btn-sm btn-outline-warning"
                                                                    onclick="suggestOptimization(<?php echo $session['id']; ?>)">
                                                                <i class="bi bi-lightbulb"></i> Optimize
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Resource Allocation Overview -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-diagram-3"></i> Resource Allocation Overview</h5>
                            <small class="text-muted">Track and optimize resource usage across sessions</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="resource-card text-center">
                                        <i class="bi bi-building" style="font-size: 2rem; color: #007bff;"></i>
                                        <h6 class="mt-2">Rooms</h6>
                                        <p class="text-muted">
                                            <?php
                                            $unique_locations = array_unique(array_filter(array_column($capacity_analysis, 'location')));
                                            echo count($unique_locations);
                                            ?> locations
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="resource-card text-center">
                                        <i class="bi bi-people" style="font-size: 2rem; color: #28a745;"></i>
                                        <h6 class="mt-2">Capacity</h6>
                                        <p class="text-muted">
                                            <?php echo number_format(array_sum(array_filter(array_column($capacity_analysis, 'max_attendees')))); ?> total seats
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="resource-card text-center">
                                        <i class="bi bi-graph-up" style="font-size: 2rem; color: #ffc107;"></i>
                                        <h6 class="mt-2">Utilization</h6>
                                        <p class="text-muted">
                                            <?php
                                            $avg_utilization = count($capacity_analysis) > 0 ?
                                                round(array_sum(array_filter(array_column($capacity_analysis, 'utilization_rate'))) / count(array_filter(array_column($capacity_analysis, 'utilization_rate'))), 1) : 0;
                                            echo $avg_utilization;
                                            ?>% average
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="resource-card text-center">
                                        <i class="bi bi-exclamation-triangle" style="font-size: 2rem; color: #dc3545;"></i>
                                        <h6 class="mt-2">Conflicts</h6>
                                        <p class="text-muted">
                                            <?php
                                            $conflicts = count(array_filter($capacity_analysis, function($s) {
                                                return $s['capacity_status'] === 'Full';
                                            }));
                                            echo $conflicts;
                                            ?> issues
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Waitlist Modal -->
<div class="modal fade" id="waitlistModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add to Waitlist</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="waitlistForm">
                <input type="hidden" name="action" value="add_to_waitlist">
                <input type="hidden" name="session_id" id="waitlistSessionId">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Session</label>
                        <input type="text" class="form-control" id="waitlistSessionTitle" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Add Person</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="person_type" value="member" id="memberType" checked>
                            <label class="form-check-label" for="memberType">
                                Existing Member
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="person_type" value="guest" id="guestType">
                            <label class="form-check-label" for="guestType">
                                Guest
                            </label>
                        </div>
                    </div>

                    <div id="memberSection">
                        <div class="mb-3">
                            <label class="form-label">Select Member</label>
                            <select name="member_id" class="form-select">
                                <option value="">Choose member...</option>
                                <!-- Members will be loaded via AJAX -->
                            </select>
                        </div>
                    </div>

                    <div id="guestSection" style="display: none;">
                        <div class="mb-3">
                            <label class="form-label">Guest Name</label>
                            <input type="text" name="guest_name" class="form-control">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Guest Email</label>
                            <input type="email" name="guest_email" class="form-control">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add to Waitlist</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Capacity Details Modal -->
<div class="modal fade" id="capacityDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Capacity Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="capacityDetailsContent">
                <!-- Content will be loaded via AJAX -->
            </div>
        </div>
    </div>
</div>

<script>
function showWaitlistModal(sessionId, sessionTitle) {
    document.getElementById('waitlistSessionId').value = sessionId;
    document.getElementById('waitlistSessionTitle').value = sessionTitle;

    const modal = new bootstrap.Modal(document.getElementById('waitlistModal'));
    modal.show();
}

function showCapacityDetails(sessionId) {
    const modal = new bootstrap.Modal(document.getElementById('capacityDetailsModal'));

    // Load capacity details via AJAX
    document.getElementById('capacityDetailsContent').innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading capacity details...</p>
        </div>
    `;

    modal.show();

    // Simulate loading capacity details
    setTimeout(() => {
        document.getElementById('capacityDetailsContent').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Registration Timeline</h6>
                    <div class="capacity-meter mb-2">
                        <div class="capacity-fill good" style="width: 75%"></div>
                    </div>
                    <small class="text-muted">75% registered in last 7 days</small>
                </div>
                <div class="col-md-6">
                    <h6>Attendance Prediction</h6>
                    <div class="capacity-meter mb-2">
                        <div class="capacity-fill near-full" style="width: 85%"></div>
                    </div>
                    <small class="text-muted">85% expected attendance rate</small>
                </div>
            </div>
            <hr>
            <h6>Optimization Suggestions</h6>
            <ul class="list-unstyled">
                <li><i class="bi bi-lightbulb text-warning"></i> Consider opening additional session</li>
                <li><i class="bi bi-people text-info"></i> Enable waitlist for overflow</li>
                <li><i class="bi bi-building text-primary"></i> Move to larger venue if available</li>
            </ul>
        `;
    }, 1000);
}

function suggestOptimization(sessionId) {
    const suggestions = [
        'Combine with similar low-attendance session',
        'Reduce room size to create more intimate setting',
        'Adjust timing to peak attendance hours',
        'Enhance marketing for this session',
        'Consider canceling if attendance remains very low'
    ];

    const suggestionList = suggestions.map(s => `<li>${s}</li>`).join('');

    const alertHtml = `
        <div class="alert alert-info alert-dismissible fade show">
            <h6><i class="bi bi-lightbulb"></i> Optimization Suggestions</h6>
            <ul class="mb-0">${suggestionList}</ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Insert at top of page
    document.querySelector('.container-fluid').insertAdjacentHTML('afterbegin', alertHtml);
}

// Person type toggle
document.addEventListener('change', function(e) {
    if (e.target.name === 'person_type') {
        const memberSection = document.getElementById('memberSection');
        const guestSection = document.getElementById('guestSection');

        if (e.target.value === 'member') {
            memberSection.style.display = 'block';
            guestSection.style.display = 'none';
        } else {
            memberSection.style.display = 'none';
            guestSection.style.display = 'block';
        }
    }
});

// Auto-refresh capacity data every 2 minutes
setInterval(function() {
    // Refresh capacity meters
    document.querySelectorAll('.capacity-fill').forEach(fill => {
        const currentWidth = parseFloat(fill.style.width);
        // Simulate small changes
        const newWidth = Math.max(0, Math.min(100, currentWidth + (Math.random() - 0.5) * 2));
        fill.style.width = newWidth + '%';
    });
}, 120000);

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
                </div>
            </div>
