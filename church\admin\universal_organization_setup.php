<?php
/**
 * Universal Organization Setup
 * Configure platform for any organization type with adaptive terminology and branding
 */

require_once '../config.php';
require_once '../includes/settings_helper.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'Organization Setup';
$page_header = 'Universal Organization Setup';
$page_description = 'Configure your platform for any organization type';

include 'includes/header.php';

$message = '';
$error = '';



// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo->beginTransaction();

        $allSettings = [];

        // Organization Settings - Process if organization_settings button clicked OR if organization data is present
        if (isset($_POST['organization_settings']) || isset($_POST['organization_name']) || isset($_POST['organization_type'])) {



            $allSettings = array_merge($allSettings, [
                'organization_type' => $_POST['organization_type'] ?? 'church',
                'organization_name' => $_POST['organization_name'] ?? '',
                'site_title' => $_POST['site_title'] ?? '',
                'admin_title' => $_POST['admin_title'] ?? '',
                'organization_mission' => $_POST['organization_mission'] ?? '',
                'organization_vision' => $_POST['organization_vision'] ?? '',
                'organization_values' => $_POST['organization_values'] ?? '',
                'member_term' => $_POST['member_term'] ?? 'Member',
                'leader_term' => $_POST['leader_term'] ?? 'Pastor',
                'group_term' => $_POST['group_term'] ?? 'Ministry',
                'event_term' => $_POST['event_term'] ?? 'Service',
                'donation_term' => $_POST['donation_term'] ?? 'Offering',
                'custom_attendee_term' => $_POST['custom_attendee_term'] ?? '',
                'custom_session_term' => $_POST['custom_session_term'] ?? '',
                'custom_event_term' => $_POST['custom_event_term'] ?? '',
                'footer_text' => $_POST['footer_text'] ?? ''
            ]);
        }

        // Contact Information - Process if contact_settings button clicked OR if contact data is present
        if (isset($_POST['contact_settings']) || isset($_POST['contact_phone']) || isset($_POST['contact_email'])) {
            $allSettings = array_merge($allSettings, [
                'contact_phone' => $_POST['contact_phone'] ?? '',
                'contact_email' => $_POST['contact_email'] ?? '',
                'contact_address' => $_POST['contact_address'] ?? '',
                'contact_city' => $_POST['contact_city'] ?? '',
                'contact_state' => $_POST['contact_state'] ?? '',
                'contact_zip' => $_POST['contact_zip'] ?? '',
                'contact_country' => $_POST['contact_country'] ?? '',
                'office_hours' => $_POST['office_hours'] ?? '',
                'emergency_contact' => $_POST['emergency_contact'] ?? '',
                'website_url' => $_POST['website_url'] ?? ''
            ]);
        }



        // Social Media Settings - Process if social_settings button clicked OR if social data is present
        if (isset($_POST['social_settings']) || isset($_POST['facebook_url']) || isset($_POST['twitter_url'])) {
            $allSettings = array_merge($allSettings, [
                'facebook_url' => $_POST['facebook_url'] ?? '',
                'twitter_url' => $_POST['twitter_url'] ?? '',
                'instagram_url' => $_POST['instagram_url'] ?? '',
                'youtube_url' => $_POST['youtube_url'] ?? '',
                'linkedin_url' => $_POST['linkedin_url'] ?? '',
                'tiktok_url' => $_POST['tiktok_url'] ?? '',
                'blog_url' => $_POST['blog_url'] ?? ''
            ]);
        }

        // Email Configuration - Process if email_settings button clicked OR if email data is present
        if (isset($_POST['email_settings']) || isset($_POST['smtp_host']) || isset($_POST['smtp_username'])) {
            $allSettings = array_merge($allSettings, [
                'smtp_host' => $_POST['smtp_host'] ?? '',
                'smtp_port' => $_POST['smtp_port'] ?? '587',
                'smtp_username' => $_POST['smtp_username'] ?? '',
                'smtp_password' => $_POST['smtp_password'] ?? '',
                'smtp_secure' => $_POST['smtp_encryption'] ?? 'tls', // Map smtp_encryption to smtp_secure
                'smtp_encryption' => $_POST['smtp_encryption'] ?? 'tls', // Keep for compatibility
                'smtp_auth' => '1', // Always enable SMTP auth
                'sender_email' => $_POST['from_email'] ?? '', // Map from_email to sender_email
                'sender_name' => $_POST['from_name'] ?? '', // Map from_name to sender_name
                'from_email' => $_POST['from_email'] ?? '', // Keep for compatibility
                'from_name' => $_POST['from_name'] ?? '', // Keep for compatibility
                'reply_to_email' => $_POST['reply_to_email'] ?? '',
                'email_signature' => $_POST['email_signature'] ?? '',
                'enable_email_queue' => isset($_POST['enable_email_queue']) ? '1' : '0'
            ]);
        }

        // System Settings - Process if system_settings button clicked OR if system data is present
        if (isset($_POST['system_settings']) || isset($_POST['timezone']) || isset($_POST['language'])) {
            $allSettings = array_merge($allSettings, [
                'timezone' => $_POST['timezone'] ?? 'America/New_York',
                'date_format' => $_POST['date_format'] ?? 'Y-m-d',
                'time_format' => $_POST['time_format'] ?? 'H:i',
                'currency_symbol' => $_POST['currency_symbol'] ?? '$',
                'currency_code' => $_POST['currency_code'] ?? 'USD',
                'language' => $_POST['language'] ?? 'en',
                'items_per_page' => $_POST['items_per_page'] ?? '25',
                'session_timeout' => $_POST['session_timeout'] ?? '3600',
                'max_upload_size' => $_POST['max_upload_size'] ?? '10',
                'backup_retention_days' => $_POST['backup_retention_days'] ?? '30'
            ]);
        }

        // Save all settings
        foreach ($allSettings as $key => $value) {
            $stmt = $pdo->prepare("
                INSERT INTO site_settings (setting_name, setting_value, created_at, updated_at)
                VALUES (?, ?, NOW(), NOW())
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
            ");
            $stmt->execute([$key, $value]);
        }

        $pdo->commit();
        $message = "Organization settings updated successfully! Your platform is now configured for " . ucfirst($allSettings['organization_type'] ?? 'your organization') . ".";

    } catch (PDOException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        $error = "Error updating settings: " . $e->getMessage();
    }
}

// Get current settings
$current_settings = [];
try {
    $setting_keys = [
        // Organization
        'organization_type', 'organization_name', 'site_title', 'admin_title',
        'organization_mission', 'organization_vision', 'organization_values',
        'member_term', 'leader_term', 'group_term', 'event_term', 'donation_term',
        'custom_attendee_term', 'custom_session_term', 'custom_event_term', 'footer_text',
        // Contact
        'contact_phone', 'contact_email', 'contact_address', 'contact_city',
        'contact_state', 'contact_zip', 'contact_country', 'office_hours',
        'emergency_contact', 'website_url',

        // Social Media
        'facebook_url', 'twitter_url', 'instagram_url', 'youtube_url',
        'linkedin_url', 'tiktok_url', 'blog_url',
        // Email
        'smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption', 'smtp_secure', 'smtp_auth',
        'from_email', 'from_name', 'sender_email', 'sender_name', 'reply_to_email', 'email_signature', 'enable_email_queue',
        'email_batch_size', 'email_sending_delay_seconds', 'contact_email',
        // System
        'timezone', 'date_format', 'time_format', 'currency_symbol', 'currency_code',
        'language', 'items_per_page', 'session_timeout', 'max_upload_size', 'backup_retention_days'
    ];

    $placeholders = str_repeat('?,', count($setting_keys) - 1) . '?';
    $stmt = $pdo->prepare("SELECT setting_name, setting_value FROM site_settings WHERE setting_name IN ($placeholders)");
    $stmt->execute($setting_keys);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($results as $row) {
        $current_settings[$row['setting_name']] = $row['setting_value'];
    }
} catch (PDOException $e) {
    // Use defaults if error
    error_log("Error fetching settings: " . $e->getMessage());
}

// Set default values for all settings
$defaults = [
    // Organization
    'organization_type' => 'church',
    'organization_name' => '',
    'site_title' => 'Organization Management System',
    'admin_title' => 'Admin Panel',
    'organization_mission' => '',
    'organization_vision' => '',
    'organization_values' => '',
    'member_term' => 'Member',
    'leader_term' => 'Pastor',
    'group_term' => 'Ministry',
    'event_term' => 'Service',
    'donation_term' => 'Offering',
    'custom_attendee_term' => '',
    'custom_session_term' => '',
    'custom_event_term' => '',
    'footer_text' => '',
    // Contact
    'contact_phone' => '',
    'contact_email' => '',
    'contact_address' => '',
    'contact_city' => '',
    'contact_state' => '',
    'contact_zip' => '',
    'contact_country' => '',
    'office_hours' => '',
    'emergency_contact' => '',
    'website_url' => '',

    // Social Media
    'facebook_url' => '',
    'twitter_url' => '',
    'instagram_url' => '',
    'youtube_url' => '',
    'linkedin_url' => '',
    'tiktok_url' => '',
    'blog_url' => '',
    // Email
    'smtp_host' => '',
    'smtp_port' => '587',
    'smtp_username' => '',
    'smtp_password' => '',
    'smtp_encryption' => 'tls',
    'smtp_secure' => 'tls',
    'smtp_auth' => '1',
    'from_email' => '',
    'from_name' => '',
    'sender_email' => '',
    'sender_name' => '',
    'reply_to_email' => '',
    'email_signature' => '',
    'enable_email_queue' => '0',
    'email_batch_size' => '25',
    'email_sending_delay_seconds' => '6',
    // System
    'timezone' => 'America/New_York',
    'date_format' => 'Y-m-d',
    'time_format' => 'H:i',
    'currency_symbol' => '$',
    'currency_code' => 'USD',
    'language' => 'en',
    'items_per_page' => '25',
    'session_timeout' => '3600',
    'max_upload_size' => '10',
    'backup_retention_days' => '30'
];

// Apply defaults for missing settings
foreach ($defaults as $key => $default_value) {
    if (!isset($current_settings[$key])) {
        $current_settings[$key] = $default_value;
    }
}

// Organization type configurations
$org_configs = [
    'church' => [
        'name' => 'Religious Organization',
        'attendee' => 'Member',
        'session' => 'Service',
        'event' => 'Event',
        'icon' => 'bi bi-house-heart',
        'color' => '#6f42c1',
        'description' => 'Churches, mosques, temples, and other religious organizations'
    ],
    'corporate' => [
        'name' => 'Corporate Organization',
        'attendee' => 'Employee',
        'session' => 'Meeting',
        'event' => 'Conference',
        'icon' => 'bi bi-building',
        'color' => '#0d6efd',
        'description' => 'Companies, corporations, and business organizations'
    ],
    'educational' => [
        'name' => 'Educational Institution',
        'attendee' => 'Student',
        'session' => 'Class',
        'event' => 'Course',
        'icon' => 'bi bi-mortarboard',
        'color' => '#198754',
        'description' => 'Schools, universities, training centers, and educational institutions'
    ],
    'sports' => [
        'name' => 'Sports Organization',
        'attendee' => 'Fan',
        'session' => 'Game',
        'event' => 'Tournament',
        'icon' => 'bi bi-trophy',
        'color' => '#fd7e14',
        'description' => 'Sports teams, stadiums, fitness centers, and athletic organizations'
    ],
    'entertainment' => [
        'name' => 'Entertainment Venue',
        'attendee' => 'Guest',
        'session' => 'Show',
        'event' => 'Festival',
        'icon' => 'bi bi-music-note-beamed',
        'color' => '#e91e63',
        'description' => 'Concert halls, theaters, entertainment venues, and event organizers'
    ],
    'healthcare' => [
        'name' => 'Healthcare Organization',
        'attendee' => 'Patient',
        'session' => 'Appointment',
        'event' => 'Clinic',
        'icon' => 'bi bi-heart-pulse',
        'color' => '#dc3545',
        'description' => 'Hospitals, clinics, medical centers, and healthcare providers'
    ],
    'government' => [
        'name' => 'Government Organization',
        'attendee' => 'Citizen',
        'session' => 'Meeting',
        'event' => 'Forum',
        'icon' => 'bi bi-bank',
        'color' => '#6610f2',
        'description' => 'Government agencies, city halls, and public organizations'
    ],
    'nonprofit' => [
        'name' => 'Non-Profit Organization',
        'attendee' => 'Volunteer',
        'session' => 'Activity',
        'event' => 'Campaign',
        'icon' => 'bi bi-heart',
        'color' => '#20c997',
        'description' => 'Charities, community centers, and non-profit organizations'
    ]
];
?>

<style>
.setup-header {
    background: linear-gradient(135deg, #6610f2 0%, #6f42c1 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.setup-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    border-left: 4px solid #6610f2;
}

.org-type-card {
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.org-type-card:hover {
    border-color: #6610f2;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.org-type-card.selected {
    border-color: #6610f2;
    background: #f8f9ff;
}

.org-type-card input[type="radio"] {
    position: absolute;
    opacity: 0;
}

.color-preview {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    display: inline-block;
    margin-right: 1rem;
}

.terminology-preview {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.feature-highlight {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    border-left: 4px solid #28a745;
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="setup-header">
                <div class="text-center">
                    <h1><i class="bi bi-gear-wide-connected"></i> Universal Organization Setup</h1>
                    <p class="mb-0">Configure your platform for any organization type with adaptive terminology and branding</p>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Settings Navigation Tabs -->
            <div class="setup-card">
                <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="organization-tab" data-bs-toggle="tab" data-bs-target="#organization" type="button" role="tab">
                            <i class="bi bi-building"></i> Organization
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab">
                            <i class="bi bi-telephone"></i> Contact
                        </button>
                    </li>

                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="social-tab" data-bs-toggle="tab" data-bs-target="#social" type="button" role="tab">
                            <i class="bi bi-share"></i> Social Media
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
                            <i class="bi bi-envelope"></i> Email
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                            <i class="bi bi-gear"></i> System
                        </button>
                    </li>
                </ul>
            </div>

            <form method="POST" id="organizationSetupForm">
                <div class="tab-content" id="settingsTabContent">
                    <!-- Organization Settings Tab -->
                    <div class="tab-pane fade show active" id="organization" role="tabpanel">
                        <div class="setup-card">
                            <h5><i class="bi bi-building"></i> Organization Type & Details</h5>
                            <p class="text-muted">Choose your organization type and configure basic information.</p>

                            <!-- Organization Type Selection -->
                            <div class="mb-4">
                                <label class="form-label">Organization Type</label>
                                <div class="row">
                                    <?php foreach ($org_configs as $type => $config): ?>
                                        <div class="col-md-6 col-lg-4">
                                            <div class="org-type-card <?php echo $current_settings['organization_type'] === $type ? 'selected' : ''; ?>" onclick="selectOrgType('<?php echo $type; ?>')">
                                                <input type="radio" name="organization_type" value="<?php echo $type; ?>" <?php echo $current_settings['organization_type'] === $type ? 'checked' : ''; ?>>
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="<?php echo $config['icon']; ?> text-primary me-3" style="font-size: 2rem; color: <?php echo $config['color']; ?>!important;"></i>
                                                    <div>
                                                        <h6 class="mb-0"><?php echo $config['name']; ?></h6>
                                                        <small class="text-muted"><?php echo $config['attendee']; ?>s • <?php echo $config['session']; ?>s • <?php echo $config['event']; ?>s</small>
                                                    </div>
                                                </div>
                                                <p class="text-muted small mb-0"><?php echo $config['description']; ?></p>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            <!-- Organization Details -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="organization_name" class="form-label">Organization Name</label>
                                        <input type="text" class="form-control" id="organization_name" name="organization_name"
                                               value="<?php echo htmlspecialchars($current_settings['organization_name']); ?>"
                                               placeholder="Enter your organization name">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="site_title" class="form-label">Site Title</label>
                                        <input type="text" class="form-control" id="site_title" name="site_title"
                                               value="<?php echo htmlspecialchars($current_settings['site_title']); ?>"
                                               placeholder="Website title">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="admin_title" class="form-label">Admin Panel Title</label>
                                        <input type="text" class="form-control" id="admin_title" name="admin_title"
                                               value="<?php echo htmlspecialchars($current_settings['admin_title']); ?>"
                                               placeholder="Admin panel title">
                                    </div>
                                </div>
                            </div>

                            <!-- Mission, Vision, Values -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="organization_mission" class="form-label">Mission Statement</label>
                                        <textarea class="form-control" id="organization_mission" name="organization_mission" rows="3"
                                                  placeholder="Your organization's mission statement"><?php echo htmlspecialchars($current_settings['organization_mission']); ?></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="organization_vision" class="form-label">Vision Statement</label>
                                        <textarea class="form-control" id="organization_vision" name="organization_vision" rows="3"
                                                  placeholder="Your organization's vision"><?php echo htmlspecialchars($current_settings['organization_vision']); ?></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="organization_values" class="form-label">Core Values</label>
                                        <textarea class="form-control" id="organization_values" name="organization_values" rows="3"
                                                  placeholder="Your organization's core values"><?php echo htmlspecialchars($current_settings['organization_values']); ?></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Custom Terminology -->
                            <h6><i class="bi bi-chat-text"></i> Custom Terminology</h6>
                            <p class="text-muted">Override default terms with your organization's preferred language.</p>

                            <div class="row">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="member_term" class="form-label">Member Term</label>
                                        <input type="text" class="form-control" id="member_term" name="member_term"
                                               value="<?php echo htmlspecialchars($current_settings['member_term']); ?>"
                                               placeholder="Member">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="leader_term" class="form-label">Leader Term</label>
                                        <input type="text" class="form-control" id="leader_term" name="leader_term"
                                               value="<?php echo htmlspecialchars($current_settings['leader_term']); ?>"
                                               placeholder="Pastor">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="event_term" class="form-label">Event Term</label>
                                        <input type="text" class="form-control" id="event_term" name="event_term"
                                               value="<?php echo htmlspecialchars($current_settings['event_term']); ?>"
                                               placeholder="Service">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label for="donation_term" class="form-label">Donation Term</label>
                                        <input type="text" class="form-control" id="donation_term" name="donation_term"
                                               value="<?php echo htmlspecialchars($current_settings['donation_term']); ?>"
                                               placeholder="Offering">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="footer_text" class="form-label">Footer Text</label>
                                        <textarea class="form-control" id="footer_text" name="footer_text" rows="2"
                                                  placeholder="Footer text for your website"><?php echo htmlspecialchars($current_settings['footer_text']); ?></textarea>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" name="organization_settings" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Save Organization Settings
                            </button>
                        </div>
                    </div>

                    <!-- Contact Information Tab -->
                    <div class="tab-pane fade" id="contact" role="tabpanel">
                        <div class="setup-card">
                            <h5><i class="bi bi-telephone"></i> Contact Information</h5>
                            <p class="text-muted">Configure your organization's contact details and location.</p>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="contact_phone" class="form-label">Phone Number</label>
                                        <input type="tel" class="form-control" id="contact_phone" name="contact_phone"
                                               value="<?php echo htmlspecialchars($current_settings['contact_phone']); ?>"
                                               placeholder="(*************">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="contact_email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="contact_email" name="contact_email"
                                               value="<?php echo htmlspecialchars($current_settings['contact_email']); ?>"
                                               placeholder="<EMAIL>">
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="contact_address" class="form-label">Street Address</label>
                                        <input type="text" class="form-control" id="contact_address" name="contact_address"
                                               value="<?php echo htmlspecialchars($current_settings['contact_address']); ?>"
                                               placeholder="123 Main Street">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="contact_city" class="form-label">City</label>
                                        <input type="text" class="form-control" id="contact_city" name="contact_city"
                                               value="<?php echo htmlspecialchars($current_settings['contact_city']); ?>"
                                               placeholder="City">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="contact_state" class="form-label">State/Province</label>
                                        <input type="text" class="form-control" id="contact_state" name="contact_state"
                                               value="<?php echo htmlspecialchars($current_settings['contact_state']); ?>"
                                               placeholder="State">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="contact_zip" class="form-label">ZIP/Postal Code</label>
                                        <input type="text" class="form-control" id="contact_zip" name="contact_zip"
                                               value="<?php echo htmlspecialchars($current_settings['contact_zip']); ?>"
                                               placeholder="12345">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="contact_country" class="form-label">Country</label>
                                        <input type="text" class="form-control" id="contact_country" name="contact_country"
                                               value="<?php echo htmlspecialchars($current_settings['contact_country']); ?>"
                                               placeholder="United States">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="website_url" class="form-label">Website URL</label>
                                        <input type="url" class="form-control" id="website_url" name="website_url"
                                               value="<?php echo htmlspecialchars($current_settings['website_url']); ?>"
                                               placeholder="https://www.yourorganization.com">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="office_hours" class="form-label">Office Hours</label>
                                        <input type="text" class="form-control" id="office_hours" name="office_hours"
                                               value="<?php echo htmlspecialchars($current_settings['office_hours']); ?>"
                                               placeholder="Mon-Fri 9AM-5PM">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="emergency_contact" class="form-label">Emergency Contact</label>
                                        <input type="text" class="form-control" id="emergency_contact" name="emergency_contact"
                                               value="<?php echo htmlspecialchars($current_settings['emergency_contact']); ?>"
                                               placeholder="Emergency contact information">
                                    </div>
                                </div>
                            </div>

                            <button type="submit" name="contact_settings" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Save Contact Information
                            </button>
                        </div>
                    </div>




                    <!-- Social Media Tab -->
                    <div class="tab-pane fade" id="social" role="tabpanel">
                        <div class="setup-card">
                            <h5><i class="bi bi-share"></i> Social Media Integration</h5>
                            <p class="text-muted">Connect your social media accounts and online presence.</p>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="facebook_url" class="form-label">
                                            <i class="bi bi-facebook text-primary"></i> Facebook URL
                                        </label>
                                        <input type="url" class="form-control" id="facebook_url" name="facebook_url"
                                               value="<?php echo htmlspecialchars($current_settings['facebook_url']); ?>"
                                               placeholder="https://facebook.com/yourorganization">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="twitter_url" class="form-label">
                                            <i class="bi bi-twitter text-info"></i> Twitter URL
                                        </label>
                                        <input type="url" class="form-control" id="twitter_url" name="twitter_url"
                                               value="<?php echo htmlspecialchars($current_settings['twitter_url']); ?>"
                                               placeholder="https://twitter.com/yourorganization">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="instagram_url" class="form-label">
                                            <i class="bi bi-instagram text-danger"></i> Instagram URL
                                        </label>
                                        <input type="url" class="form-control" id="instagram_url" name="instagram_url"
                                               value="<?php echo htmlspecialchars($current_settings['instagram_url']); ?>"
                                               placeholder="https://instagram.com/yourorganization">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="youtube_url" class="form-label">
                                            <i class="bi bi-youtube text-danger"></i> YouTube URL
                                        </label>
                                        <input type="url" class="form-control" id="youtube_url" name="youtube_url"
                                               value="<?php echo htmlspecialchars($current_settings['youtube_url']); ?>"
                                               placeholder="https://youtube.com/yourorganization">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="linkedin_url" class="form-label">
                                            <i class="bi bi-linkedin text-primary"></i> LinkedIn URL
                                        </label>
                                        <input type="url" class="form-control" id="linkedin_url" name="linkedin_url"
                                               value="<?php echo htmlspecialchars($current_settings['linkedin_url']); ?>"
                                               placeholder="https://linkedin.com/company/yourorganization">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="tiktok_url" class="form-label">
                                            <i class="bi bi-tiktok text-dark"></i> TikTok URL
                                        </label>
                                        <input type="url" class="form-control" id="tiktok_url" name="tiktok_url"
                                               value="<?php echo htmlspecialchars($current_settings['tiktok_url']); ?>"
                                               placeholder="https://tiktok.com/@yourorganization">
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="blog_url" class="form-label">
                                            <i class="bi bi-journal-text"></i> Blog URL
                                        </label>
                                        <input type="url" class="form-control" id="blog_url" name="blog_url"
                                               value="<?php echo htmlspecialchars($current_settings['blog_url']); ?>"
                                               placeholder="https://blog.yourorganization.com">
                                    </div>
                                </div>
                            </div>

                            <button type="submit" name="social_settings" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Save Social Media Settings
                            </button>
                        </div>
                    </div>
                    <!-- Email Configuration Tab -->
                    <div class="tab-pane fade" id="email" role="tabpanel">
                        <div class="setup-card">
                            <h5><i class="bi bi-envelope"></i> Email Configuration</h5>
                            <p class="text-muted">Configure SMTP settings for sending emails from your platform.</p>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="smtp_host" class="form-label">SMTP Host</label>
                                        <input type="text" class="form-control" id="smtp_host" name="smtp_host"
                                               value="<?php echo htmlspecialchars($current_settings['smtp_host']); ?>"
                                               placeholder="smtp.gmail.com">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="smtp_port" class="form-label">SMTP Port</label>
                                        <input type="number" class="form-control" id="smtp_port" name="smtp_port"
                                               value="<?php echo htmlspecialchars($current_settings['smtp_port']); ?>"
                                               placeholder="587">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="smtp_username" class="form-label">SMTP Username</label>
                                        <input type="text" class="form-control" id="smtp_username" name="smtp_username"
                                               value="<?php echo htmlspecialchars($current_settings['smtp_username']); ?>"
                                               placeholder="<EMAIL>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="smtp_password" class="form-label">SMTP Password</label>
                                        <input type="password" class="form-control" id="smtp_password" name="smtp_password"
                                               value="<?php echo htmlspecialchars($current_settings['smtp_password']); ?>"
                                               placeholder="Your SMTP password">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="smtp_encryption" class="form-label">Encryption</label>
                                        <select class="form-select" id="smtp_encryption" name="smtp_encryption">
                                            <option value="tls" <?php echo $current_settings['smtp_encryption'] === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                            <option value="ssl" <?php echo $current_settings['smtp_encryption'] === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                            <option value="none" <?php echo $current_settings['smtp_encryption'] === 'none' ? 'selected' : ''; ?>>None</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="from_email" class="form-label">From Email</label>
                                        <input type="email" class="form-control" id="from_email" name="from_email"
                                               value="<?php echo htmlspecialchars($current_settings['from_email']); ?>"
                                               placeholder="<EMAIL>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="from_name" class="form-label">From Name</label>
                                        <input type="text" class="form-control" id="from_name" name="from_name"
                                               value="<?php echo htmlspecialchars($current_settings['from_name']); ?>"
                                               placeholder="Your Organization">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="reply_to_email" class="form-label">Reply-To Email</label>
                                        <input type="email" class="form-control" id="reply_to_email" name="reply_to_email"
                                               value="<?php echo htmlspecialchars($current_settings['reply_to_email']); ?>"
                                               placeholder="<EMAIL>">
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="email_signature" class="form-label">Email Signature</label>
                                        <textarea class="form-control" id="email_signature" name="email_signature" rows="3"
                                                  placeholder="Best regards,&#10;Your Organization Team"><?php echo htmlspecialchars($current_settings['email_signature']); ?></textarea>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enable_email_queue" name="enable_email_queue"
                                               <?php echo $current_settings['enable_email_queue'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="enable_email_queue">
                                            Enable Email Queue (Recommended for bulk emails)
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" name="email_settings" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Save Email Settings
                            </button>
                        </div>
                    </div>

                    <!-- System Settings Tab -->
                    <div class="tab-pane fade" id="system" role="tabpanel">
                        <div class="setup-card">
                            <h5><i class="bi bi-gear"></i> System Settings</h5>
                            <p class="text-muted">Configure system preferences and operational settings.</p>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="timezone" class="form-label">Timezone</label>
                                        <select class="form-select" id="timezone" name="timezone">
                                            <option value="America/New_York" <?php echo $current_settings['timezone'] === 'America/New_York' ? 'selected' : ''; ?>>Eastern Time</option>
                                            <option value="America/Chicago" <?php echo $current_settings['timezone'] === 'America/Chicago' ? 'selected' : ''; ?>>Central Time</option>
                                            <option value="America/Denver" <?php echo $current_settings['timezone'] === 'America/Denver' ? 'selected' : ''; ?>>Mountain Time</option>
                                            <option value="America/Los_Angeles" <?php echo $current_settings['timezone'] === 'America/Los_Angeles' ? 'selected' : ''; ?>>Pacific Time</option>
                                            <option value="UTC" <?php echo $current_settings['timezone'] === 'UTC' ? 'selected' : ''; ?>>UTC</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="language" class="form-label">Language</label>
                                        <select class="form-select" id="language" name="language">
                                            <option value="en" <?php echo $current_settings['language'] === 'en' ? 'selected' : ''; ?>>English</option>
                                            <option value="es" <?php echo $current_settings['language'] === 'es' ? 'selected' : ''; ?>>Spanish</option>
                                            <option value="fr" <?php echo $current_settings['language'] === 'fr' ? 'selected' : ''; ?>>French</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="date_format" class="form-label">Date Format</label>
                                        <select class="form-select" id="date_format" name="date_format">
                                            <option value="Y-m-d" <?php echo $current_settings['date_format'] === 'Y-m-d' ? 'selected' : ''; ?>>YYYY-MM-DD</option>
                                            <option value="m/d/Y" <?php echo $current_settings['date_format'] === 'm/d/Y' ? 'selected' : ''; ?>>MM/DD/YYYY</option>
                                            <option value="d/m/Y" <?php echo $current_settings['date_format'] === 'd/m/Y' ? 'selected' : ''; ?>>DD/MM/YYYY</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="time_format" class="form-label">Time Format</label>
                                        <select class="form-select" id="time_format" name="time_format">
                                            <option value="H:i" <?php echo $current_settings['time_format'] === 'H:i' ? 'selected' : ''; ?>>24 Hour (HH:MM)</option>
                                            <option value="g:i A" <?php echo $current_settings['time_format'] === 'g:i A' ? 'selected' : ''; ?>>12 Hour (H:MM AM/PM)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="currency_symbol" class="form-label">Currency Symbol</label>
                                        <input type="text" class="form-control" id="currency_symbol" name="currency_symbol"
                                               value="<?php echo htmlspecialchars($current_settings['currency_symbol']); ?>"
                                               placeholder="$">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="currency_code" class="form-label">Currency Code</label>
                                        <input type="text" class="form-control" id="currency_code" name="currency_code"
                                               value="<?php echo htmlspecialchars($current_settings['currency_code']); ?>"
                                               placeholder="USD">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="items_per_page" class="form-label">Items Per Page</label>
                                        <select class="form-select" id="items_per_page" name="items_per_page">
                                            <option value="10" <?php echo $current_settings['items_per_page'] === '10' ? 'selected' : ''; ?>>10</option>
                                            <option value="25" <?php echo $current_settings['items_per_page'] === '25' ? 'selected' : ''; ?>>25</option>
                                            <option value="50" <?php echo $current_settings['items_per_page'] === '50' ? 'selected' : ''; ?>>50</option>
                                            <option value="100" <?php echo $current_settings['items_per_page'] === '100' ? 'selected' : ''; ?>>100</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="session_timeout" class="form-label">Session Timeout (seconds)</label>
                                        <input type="number" class="form-control" id="session_timeout" name="session_timeout"
                                               value="<?php echo htmlspecialchars($current_settings['session_timeout']); ?>"
                                               placeholder="3600">
                                    </div>
                                </div>
                            </div>

                            <button type="submit" name="system_settings" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Save System Settings
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
const orgConfigs = <?php echo json_encode($org_configs); ?>;

function selectOrgType(type) {
    // Remove selected class from all cards
    document.querySelectorAll('.org-type-card').forEach(card => {
        card.classList.remove('selected');
    });

    // Add selected class to clicked card
    event.currentTarget.classList.add('selected');

    // Check the radio button
    document.querySelector(`input[value="${type}"]`).checked = true;
}

// Update color text displays
document.addEventListener('DOMContentLoaded', function() {
    // Primary color
    const primaryColorInput = document.getElementById('primary_color');
    const primaryColorText = document.getElementById('primary_color_text');
    if (primaryColorInput && primaryColorText) {
        primaryColorInput.addEventListener('input', function() {
            primaryColorText.textContent = this.value;
        });
    }

    // Secondary color
    const secondaryColorInput = document.getElementById('secondary_color');
    const secondaryColorText = document.getElementById('secondary_color_text');
    if (secondaryColorInput && secondaryColorText) {
        secondaryColorInput.addEventListener('input', function() {
            secondaryColorText.textContent = this.value;
        });
    }

    // Initialize Bootstrap tabs
    const triggerTabList = [].slice.call(document.querySelectorAll('#settingsTabs button'));
    triggerTabList.forEach(function (triggerEl) {
        const tabTrigger = new bootstrap.Tab(triggerEl);

        triggerEl.addEventListener('click', function (event) {
            event.preventDefault();
            tabTrigger.show();
        });
    });

    // Form validation and submission feedback
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Saving...';
                submitBtn.disabled = true;
            }
        });
    });

    // Auto-save indication
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            // Add visual indication that changes need to be saved
            const submitBtns = document.querySelectorAll('button[type="submit"]');
            submitBtns.forEach(btn => {
                if (!btn.classList.contains('btn-warning')) {
                    btn.classList.remove('btn-primary');
                    btn.classList.add('btn-warning');
                    btn.innerHTML = btn.innerHTML.replace('Save', 'Save Changes');
                }
            });
        });
    });
});

// Tab switching with unsaved changes warning
function switchTab(tabId) {
    const hasUnsavedChanges = document.querySelectorAll('.btn-warning').length > 0;

    if (hasUnsavedChanges) {
        if (!confirm('You have unsaved changes. Are you sure you want to switch tabs?')) {
            return false;
        }
    }

    const tab = new bootstrap.Tab(document.querySelector(`#${tabId}-tab`));
    tab.show();
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
            case 's':
                e.preventDefault();
                // Find the active tab's submit button and click it
                const activeTab = document.querySelector('.tab-pane.active');
                if (activeTab) {
                    const submitBtn = activeTab.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.click();
                    }
                }
                break;
        }
    }
});
</script>

<?php include 'includes/footer.php'; ?>
