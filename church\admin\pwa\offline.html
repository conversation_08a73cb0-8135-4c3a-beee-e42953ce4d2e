<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Universal Event Management</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        .offline-container {
            max-width: 400px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .offline-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        
        h1 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        p {
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 2rem;
        }
        
        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }
        
        .features {
            margin-top: 2rem;
            text-align: left;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            opacity: 0.8;
        }
        
        .feature-icon {
            margin-right: 0.75rem;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📱</div>
        <h1>You're Offline</h1>
        <p>No internet connection detected. Don't worry - this app works offline too!</p>
        
        <button class="retry-btn" onclick="window.location.reload()">
            🔄 Try Again
        </button>
        
        <div class="features">
            <div class="feature">
                <span class="feature-icon">✅</span>
                <span>View cached events and data</span>
            </div>
            <div class="feature">
                <span class="feature-icon">📊</span>
                <span>Access offline analytics</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🔄</span>
                <span>Auto-sync when back online</span>
            </div>
        </div>
    </div>
    
    <script>
        // Check for connection and auto-reload when back online
        window.addEventListener('online', () => {
            window.location.reload();
        });
        
        // Show connection status
        if (navigator.onLine) {
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }
    </script>
</body>
</html>
