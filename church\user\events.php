<?php
/**
 * User Events Interface
 * 
 * Allows authenticated users to view and manage their event participation
 */

session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];

// Helper function to fix file paths
function fixFilePath($filePath) {
    if (empty($filePath)) {
        return '';
    }

    // If path already starts with ../uploads/, it's correct for user directory
    if (strpos($filePath, '../uploads/') === 0) {
        return $filePath;
    }

    // If path already starts with uploads/, it's likely correct
    if (strpos($filePath, 'uploads/') === 0) {
        return $filePath;
    }

    // If it's an absolute path, extract just the filename and put in uploads/
    if (strpos($filePath, '/') !== false || strpos($filePath, '\\') !== false) {
        return 'uploads/' . basename($filePath);
    }

    // If it's just a filename, put it in uploads/
    return 'uploads/' . $filePath;
}

// Get user data from members table
$stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
$stmt->execute([$userId]);
$userData = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$userData) {
    session_destroy();
    header("Location: login.php");
    exit();
}

// Check if user must change password (redirect to change password page)
if ($userData['must_change_password']) {
    header("Location: change_password.php");
    exit();
}

// Get site settings for branding
$sitename = get_organization_name() . ' - Events';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}

// Get upcoming events
$upcomingEvents = [];
$myEvents = [];
$userRsvps = [];

try {
    // Get filter parameters
    $filter = $_GET['filter'] ?? 'upcoming';
    $month = $_GET['month'] ?? '';
    $year = $_GET['year'] ?? '';

    // Pagination parameters
    $page = max(1, intval($_GET['page'] ?? 1));
    $eventsPerPage = 9; // 3x3 grid
    $offset = ($page - 1) * $eventsPerPage;

    // Build WHERE clause based on filters
    // Use the new status system to only show published events
    $activeCondition = "1=1"; // Default fallback
    try {
        // First try the new status column (preferred)
        $testStmt = $pdo->query("SELECT status FROM events LIMIT 1");
        $activeCondition = "e.status = 'published'";
    } catch (PDOException $e) {
        try {
            // Fallback to is_active column if status doesn't exist
            $testStmt = $pdo->query("SELECT is_active FROM events LIMIT 1");
            $activeCondition = "e.is_active = 1";
        } catch (PDOException $e2) {
            // Neither column exists, don't filter by active status
            $activeCondition = "1=1";
        }
    }

    $whereConditions = [$activeCondition];
    $params = [];

    if ($filter === 'upcoming') {
        $whereConditions[] = "e.event_date >= NOW()";
    } elseif ($filter === 'past') {
        $whereConditions[] = "e.event_date < NOW()";
    }

    // Add month/year filtering if specified
    if (!empty($month) && !empty($year)) {
        $whereConditions[] = "YEAR(e.event_date) = ? AND MONTH(e.event_date) = ?";
        $params[] = $year;
        $params[] = $month;
    } elseif (!empty($year)) {
        $whereConditions[] = "YEAR(e.event_date) = ?";
        $params[] = $year;
    }

    $whereClause = implode(' AND ', $whereConditions);
    $orderBy = ($filter === 'past') ? 'ORDER BY e.event_date DESC' : 'ORDER BY e.event_date ASC';

    // Get total count for pagination
    $countStmt = $pdo->prepare("SELECT COUNT(*) FROM events e WHERE $whereClause");
    $countStmt->execute($params);
    $totalEvents = $countStmt->fetchColumn();
    $totalPages = ceil($totalEvents / $eventsPerPage);

    // Get events based on filter
    $stmt = $pdo->prepare("
        SELECT e.*,
               (
                   (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'attending') +
                   (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = e.id AND erg.status = 'attending')
               ) as attending_count,
               (
                   (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'maybe') +
                   (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = e.id AND erg.status = 'maybe')
               ) as maybe_count
        FROM events e
        WHERE $whereClause
        $orderBy
        LIMIT $eventsPerPage OFFSET $offset
    ");
    $stmt->execute($params);
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get banner information for each event separately
    if (!empty($events)) {
        $eventIds = array_column($events, 'id');
        $placeholders = str_repeat('?,', count($eventIds) - 1) . '?';

        $bannerStmt = $pdo->prepare("
            SELECT DISTINCT event_id, file_path, file_type, file_name, alt_text
            FROM event_files
            WHERE event_id IN ($placeholders) AND is_header_banner = 1
            GROUP BY event_id
        ");
        $bannerStmt->execute($eventIds);
        $banners = $bannerStmt->fetchAll(PDO::FETCH_ASSOC);

        // Create a lookup array for banners
        $bannerLookup = [];
        foreach ($banners as $banner) {
            $bannerLookup[$banner['event_id']] = $banner;
        }

        // Add banner information to events
        foreach ($events as &$event) {
            if (isset($bannerLookup[$event['id']])) {
                $banner = $bannerLookup[$event['id']];
                $event['header_banner_path'] = $banner['file_path'];
                $event['header_banner_type'] = $banner['file_type'];
                $event['header_banner_name'] = $banner['file_name'];
                $event['header_banner_alt'] = $banner['alt_text'];
            } else {
                $event['header_banner_path'] = null;
                $event['header_banner_type'] = null;
                $event['header_banner_name'] = null;
                $event['header_banner_alt'] = null;
            }
        }
        unset($event); // Important: Break the reference to avoid issues with subsequent loops
    }

    // Get upcoming events count for stats
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM events e
        WHERE $activeCondition AND e.event_date >= NOW()
    ");
    $stmt->execute();
    $upcomingCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

    // Get user's RSVPs (all time for stats)
    $stmt = $pdo->prepare("
        SELECT er.*, e.title, e.event_date, e.location
        FROM event_rsvps er
        JOIN events e ON er.event_id = e.id
        WHERE er.user_id = ? AND e.event_date >= NOW()
        ORDER BY e.event_date ASC
    ");
    $stmt->execute([$userId]);
    $userRsvps = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get events user has actually attended (marked by admin)
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as attended_count
        FROM event_rsvps er
        JOIN events e ON er.event_id = e.id
        WHERE er.user_id = ? AND er.actually_attended = 1
    ");
    $stmt->execute([$userId]);
    $attendedCount = $stmt->fetch(PDO::FETCH_ASSOC)['attended_count'];

} catch (PDOException $e) {
    error_log("Error loading events: " . $e->getMessage());
    $upcomingEvents = [];
    $userRsvps = [];
    $attendedCount = 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Events - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo url_for('css/promotional-materials.css'); ?>">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        /* Responsive logo adjustments */
        @media (max-width: 768px) {
            .navbar-logo {
                max-height: 32px;
                max-width: 120px;
            }

            .navbar-brand {
                font-size: 1rem;
            }
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
        }

        .navbar-nav .nav-link.active {
            color: white !important;
            font-weight: 600;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            transition: background-color 0.15s ease-in-out;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .dropdown-item i {
            width: 16px;
            margin-right: 8px;
        }


        
        .events-container {
            margin-top: 2rem;
        }
        
        .events-card {
            background: var(--bs-body-bg, white);
            border-radius: var(--bs-border-radius, 15px);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
        }

        .events-header {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            color: white;
            border-radius: var(--bs-border-radius, 15px);
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .coming-soon {
            text-align: center;
            padding: 3rem 2rem;
            color: var(--bs-secondary, #6c757d);
        }

        .coming-soon i {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: var(--bs-primary, #667eea);
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list i {
            color: var(--bs-primary, #667eea);
            margin-right: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            border: none;
            border-radius: var(--bs-border-radius, 10px);
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        /* Enhanced Button Styles */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-outline-info {
            border: 2px solid #17a2b8;
            color: #17a2b8;
            background: transparent;
        }

        .btn-outline-info:hover {
            background: #17a2b8;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
        }

        .btn-outline-primary {
            border: 2px solid var(--bs-primary, #667eea);
            color: var(--bs-primary, #667eea);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--bs-primary, #667eea);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
            color: white;
            border: none;
        }

        .btn-warning:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
        }

        /* Enhanced Card Styles */
        .card {
            border: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .card-img-top-container {
            position: relative;
            overflow: hidden;
        }

        .card-img-top {
            transition: transform 0.3s ease;
        }

        .card:hover .card-img-top {
            transform: scale(1.05);
        }

        /* Badge Enhancements */
        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
            border-radius: 20px;
            font-weight: 500;
        }

        .badge.bg-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
        }

        .badge.bg-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
            color: white !important;
        }

        .badge.bg-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
        }

        /* Quick Stats Cards */
        .events-card.text-center {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .events-card.text-center:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .events-card.text-center .h4 {
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        /* Pagination Enhancements */
        .pagination .page-link {
            border: none;
            color: var(--bs-primary, #667eea);
            background: transparent;
            margin: 0 2px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .pagination .page-link:hover {
            background: var(--bs-primary, #667eea);
            color: white;
            transform: translateY(-1px);
        }

        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            border: none;
            color: white;
        }

        /* Filter Section Enhancement */
        .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-select:focus {
            border-color: var(--bs-primary, #667eea);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        }
        
        .btn-secondary {
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }

        .event-date-badge .badge {
            font-size: 0.8rem;
            line-height: 1.2;
        }

        .card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container events-container">
        <!-- Events Header -->
        <div class="events-header">
            <h1><i class="bi bi-calendar-event"></i> Events & Activities</h1>
            <p class="mb-0">Stay connected with upcoming events and activities</p>
        </div>

        <!-- Event Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="events-card">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0"><i class="bi bi-funnel"></i> Filter Events</h5>
                        </div>
                        <div class="col-md-6">
                            <form method="GET" class="d-flex gap-2">
                                <select name="filter" class="form-select form-select-sm">
                                    <option value="upcoming" <?= $filter === 'upcoming' ? 'selected' : '' ?>>Upcoming Events</option>
                                    <option value="past" <?= $filter === 'past' ? 'selected' : '' ?>>Past Events</option>
                                    <option value="all" <?= $filter === 'all' ? 'selected' : '' ?>>All Events</option>
                                </select>
                                <select name="month" class="form-select form-select-sm">
                                    <option value="">All Months</option>
                                    <?php for ($m = 1; $m <= 12; $m++): ?>
                                        <option value="<?= $m ?>" <?= $month == $m ? 'selected' : '' ?>>
                                            <?= date('F', mktime(0, 0, 0, $m, 1)) ?>
                                        </option>
                                    <?php endfor; ?>
                                </select>
                                <select name="year" class="form-select form-select-sm">
                                    <option value="">All Years</option>
                                    <?php
                                    $currentYear = date('Y');
                                    for ($y = $currentYear - 2; $y <= $currentYear + 2; $y++):
                                    ?>
                                        <option value="<?= $y ?>" <?= $year == $y ? 'selected' : '' ?>><?= $y ?></option>
                                    <?php endfor; ?>
                                </select>
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="bi bi-search"></i> Filter
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats Section -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="events-card text-center">
                    <div class="h4 text-primary mb-1"><?= $upcomingCount ?? 0 ?></div>
                    <small class="text-muted">Upcoming Events</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="events-card text-center">
                    <div class="h4 text-success mb-1"><?= count($userRsvps) ?></div>
                    <small class="text-muted">My RSVPs</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="events-card text-center">
                    <div class="h4 text-info mb-1"><?= $attendedCount ?? 0 ?></div>
                    <small class="text-muted">Events Attended</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="events-card text-center">
                    <?php
                    $pendingCount = 0;
                    foreach ($userRsvps as $rsvp) {
                        if ($rsvp['status'] === 'maybe') $pendingCount++;
                    }
                    ?>
                    <div class="h4 text-warning mb-1"><?= $pendingCount ?></div>
                    <small class="text-muted">Maybe Attending</small>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <!-- Events -->
                <div class="events-card">
                    <h4 class="mb-4">
                        <i class="bi bi-calendar-event"></i>
                        <?php
                        if ($filter === 'past') {
                            echo 'Past Events';
                        } elseif ($filter === 'all') {
                            echo 'All Events';
                        } else {
                            echo 'Upcoming Events';
                        }
                        ?>
                        <span class="badge bg-secondary ms-2"><?= $totalEvents ?></span>
                        <?php if ($totalPages > 1): ?>
                            <small class="text-muted ms-2">Page <?= $page ?> of <?= $totalPages ?></small>
                        <?php endif; ?>
                    </h4>

                    <?php if (empty($events)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-calendar-x display-1 text-muted"></i>
                            <h5 class="mt-3 text-muted">No Events Found</h5>
                            <p class="text-muted">
                                <?php if ($filter === 'past'): ?>
                                    No past events match your criteria.
                                <?php else: ?>
                                    There are no upcoming events at this time. Please check back later.
                                <?php endif; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($events as $event): ?>
                                <div class="col-md-4 mb-4">
                                    <div class="card h-100 border-0 shadow-sm">
                                        <!-- Header Banner -->
                                        <?php if (!empty($event['header_banner_path'])): ?>
                                            <div class="card-img-top-container" style="height: 200px; overflow: hidden;">
                                                <?php if (!empty($event['header_banner_type']) && strpos($event['header_banner_type'], 'image/') === 0): ?>
                                                    <?php $banner_path = fixFilePath($event['header_banner_path']); ?>
                                                    <img src="<?= htmlspecialchars(user_file_path($banner_path)) ?>"
                                                         alt="<?= htmlspecialchars(($event['header_banner_alt'] ?? '') ?: $event['title']) ?>"
                                                         class="card-img-top"
                                                         style="width: 100%; height: 100%; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="d-flex align-items-center justify-content-center h-100 bg-light">
                                                        <div class="text-center">
                                                            <i class="bi bi-file-pdf fs-1 text-danger mb-2"></i>
                                                            <div class="small text-muted">PDF Banner</div>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        <?php endif; ?>

                                        <div class="card-body">
                                            <!-- Event Date Badge -->
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <div class="event-date-badge">
                                                    <div class="badge bg-primary p-2">
                                                        <div class="fw-bold"><?= date('M j', strtotime($event['event_date'])) ?></div>
                                                        <div class="small"><?= date('g:i A', strtotime($event['event_date'])) ?></div>
                                                    </div>
                                                </div>
                                                <?php if ($event['max_attendees']): ?>
                                                    <small class="text-muted">
                                                        <i class="bi bi-people"></i> <?= $event['attending_count'] ?>/<?= $event['max_attendees'] ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>

                                            <h5 class="card-title"><?= htmlspecialchars($event['title']) ?></h5>

                                            <?php if ($event['location']): ?>
                                                <p class="text-muted small mb-2">
                                                    <i class="bi bi-geo-alt"></i> <?= htmlspecialchars($event['location']) ?>
                                                </p>
                                            <?php endif; ?>

                                            <p class="card-text">
                                                <?= htmlspecialchars(substr($event['description'], 0, 100)) ?>
                                                <?php if (strlen($event['description']) > 100): ?>...<?php endif; ?>
                                            </p>

                                            <!-- RSVP Status and Actions -->
                                            <?php
                                            $userRsvp = null;
                                            foreach ($userRsvps as $rsvp) {
                                                if ($rsvp['event_id'] == $event['id']) {
                                                    $userRsvp = $rsvp;
                                                    break;
                                                }
                                            }
                                            $is_past = strtotime($event['event_date']) < time();
                                            $is_full = $event['max_attendees'] && $event['attending_count'] >= $event['max_attendees'];
                                            ?>

                                            <div class="mt-auto">
                                                <div class="d-flex justify-content-between align-items-center mb-3">
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <button type="button" class="btn btn-outline-info px-3" onclick="showEventDetails(<?= $event['id'] ?>)">
                                                            <i class="bi bi-eye me-1"></i> Details
                                                        </button>
                                                        <a href="event_sessions.php?event_id=<?= $event['id'] ?>" class="btn btn-outline-primary px-3">
                                                            <i class="bi bi-collection me-1"></i> Sessions
                                                        </a>
                                                    </div>
                                                    <small class="text-muted">
                                                        <i class="bi bi-people"></i> <?= $event['attending_count'] ?><?= $event['max_attendees'] ? '/' . $event['max_attendees'] : '' ?>
                                                    </small>
                                                </div>

                                                <?php if ($is_past): ?>
                                                    <span class="badge bg-secondary">
                                                        <i class="bi bi-clock-history"></i> Past Event
                                                    </span>
                                                    <?php if ($userRsvp): ?>
                                                        <span class="badge bg-<?= $userRsvp['status'] === 'attending' ? 'success' : ($userRsvp['status'] === 'maybe' ? 'warning' : 'secondary') ?> ms-2">
                                                            <?= ucfirst(str_replace('_', ' ', $userRsvp['status'])) ?>
                                                        </span>
                                                    <?php endif; ?>
                                                <?php elseif ($userRsvp): ?>
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <span class="badge bg-<?= $userRsvp['status'] === 'attending' ? 'success' : ($userRsvp['status'] === 'maybe' ? 'warning' : 'secondary') ?> px-3 py-2">
                                                            <i class="bi bi-<?= $userRsvp['status'] === 'attending' ? 'check-circle' : ($userRsvp['status'] === 'maybe' ? 'question-circle' : 'x-circle') ?> me-1"></i>
                                                            <?= ucfirst(str_replace('_', ' ', $userRsvp['status'])) ?>
                                                        </span>
                                                        <a href="enhanced_rsvp.php?event_id=<?= $event['id'] ?>" class="btn btn-outline-primary btn-sm px-3">
                                                            <i class="bi bi-pencil me-1"></i> Update
                                                        </a>
                                                    </div>
                                                <?php elseif ($is_full): ?>
                                                    <button class="btn btn-warning btn-sm w-100 py-2" onclick="rsvpEvent(<?= $event['id'] ?>, 'maybe')">
                                                        <i class="bi bi-clock me-1"></i> Join Waitlist
                                                    </button>
                                                <?php else: ?>
                                                    <a href="enhanced_rsvp.php?event_id=<?= $event['id'] ?>" class="btn btn-primary btn-sm w-100 py-2">
                                                        <i class="bi bi-calendar-plus me-1"></i> RSVP + Guests
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="d-flex justify-content-center mt-4">
                            <nav aria-label="Events pagination">
                                <ul class="pagination">
                                    <!-- Previous Page -->
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>">
                                                <i class="bi bi-chevron-left"></i> Previous
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li class="page-item disabled">
                                            <span class="page-link"><i class="bi bi-chevron-left"></i> Previous</span>
                                        </li>
                                    <?php endif; ?>

                                    <!-- Page Numbers -->
                                    <?php
                                    $startPage = max(1, $page - 2);
                                    $endPage = min($totalPages, $page + 2);

                                    // Show first page if not in range
                                    if ($startPage > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => 1])) ?>">1</a>
                                        </li>
                                        <?php if ($startPage > 2): ?>
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <!-- Current page range -->
                                    <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"><?= $i ?></a>
                                        </li>
                                    <?php endfor; ?>

                                    <!-- Show last page if not in range -->
                                    <?php if ($endPage < $totalPages): ?>
                                        <?php if ($endPage < $totalPages - 1): ?>
                                            <li class="page-item disabled"><span class="page-link">...</span></li>
                                        <?php endif; ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $totalPages])) ?>"><?= $totalPages ?></a>
                                        </li>
                                    <?php endif; ?>

                                    <!-- Next Page -->
                                    <?php if ($page < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>">
                                                Next <i class="bi bi-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php else: ?>
                                        <li class="page-item disabled">
                                            <span class="page-link">Next <i class="bi bi-chevron-right"></i></span>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>

                        <!-- Pagination Info -->
                        <div class="text-center mt-2">
                            <small class="text-muted">
                                Showing <?= min($offset + 1, $totalEvents) ?>-<?= min($offset + $eventsPerPage, $totalEvents) ?> of <?= $totalEvents ?> events
                            </small>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- My RSVPs -->
                <?php if (!empty($userRsvps)): ?>
                <div class="events-card">
                    <h4 class="mb-4"><i class="bi bi-calendar-check"></i> My RSVPs</h4>
                    <div class="list-group list-group-flush">
                        <?php foreach ($userRsvps as $rsvp): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1"><?= htmlspecialchars($rsvp['title']) ?></h6>
                                    <small class="text-muted">
                                        <i class="bi bi-calendar"></i> <?= date('M j, Y g:i A', strtotime($rsvp['event_date'])) ?>
                                        <?php if ($rsvp['location']): ?>
                                            | <i class="bi bi-geo-alt"></i> <?= htmlspecialchars($rsvp['location']) ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                                <span class="badge bg-<?= $rsvp['status'] === 'attending' ? 'success' : ($rsvp['status'] === 'maybe' ? 'warning' : 'secondary') ?>">
                                    <?= ucfirst($rsvp['status']) ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Additional Information Section -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="events-card">
                    <h5 class="mb-3"><i class="bi bi-bell"></i> Event Notifications</h5>
                    <p class="small text-muted">Stay updated with event notifications. You can manage your notification preferences in your settings.</p>
                    <div class="d-grid">
                        <a href="settings.php" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-gear"></i> Notification Settings
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="events-card">
                    <h5 class="mb-3"><i class="bi bi-question-circle"></i> Need Help?</h5>
                    <p class="small text-muted">Have questions about events or need assistance with RSVPs? We're here to help!</p>
                    <div class="d-grid">
                        <a href="mailto:<EMAIL>" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-envelope"></i> Contact Events Team
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- RSVP Modal -->
    <div class="modal fade" id="rsvpModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">RSVP for Event</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="rsvpForm">
                        <input type="hidden" id="rsvp_event_id" name="event_id">
                        <input type="hidden" id="rsvp_status" name="status">

                        <div class="mb-3">
                            <label class="form-label">RSVP Status</label>
                            <div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="rsvp_response" id="attending" value="attending" checked>
                                    <label class="form-check-label" for="attending">
                                        <i class="bi bi-check-circle text-success"></i> I will attend
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="rsvp_response" id="maybe" value="maybe">
                                    <label class="form-check-label" for="maybe">
                                        <i class="bi bi-question-circle text-warning"></i> Maybe
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="rsvp_response" id="not_attending" value="not_attending">
                                    <label class="form-check-label" for="not_attending">
                                        <i class="bi bi-x-circle text-danger"></i> Cannot attend
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Notes (Optional)</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Any special requirements or comments..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitRSVP()">Submit RSVP</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Event Details Modal -->
    <div class="modal fade" id="eventDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="eventDetailsTitle">Event Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="eventDetailsBody">
                    <!-- Event details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <a href="#" class="btn btn-primary" id="eventRsvpBtn">RSVP</a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function rsvpEvent(eventId, defaultStatus = 'attending') {
            document.getElementById('rsvp_event_id').value = eventId;
            document.getElementById('rsvp_status').value = defaultStatus;

            // Set default radio button
            document.querySelector(`input[name="rsvp_response"][value="${defaultStatus}"]`).checked = true;

            new bootstrap.Modal(document.getElementById('rsvpModal')).show();
        }

        function updateRsvp(eventId) {
            rsvpEvent(eventId);
        }

        function submitRSVP() {
            const form = document.getElementById('rsvpForm');
            const formData = new FormData(form);

            // Get selected response
            const selectedResponse = document.querySelector('input[name="rsvp_response"]:checked');
            if (!selectedResponse) {
                alert('Please select an RSVP status.');
                return;
            }

            formData.set('status', selectedResponse.value);
            formData.append('action', 'rsvp');

            fetch('rsvp_handler.php', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('RSVP updated successfully!');
                    bootstrap.Modal.getInstance(document.getElementById('rsvpModal')).hide();
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Fetch error:', error);
                // Try to get more detailed error information
                fetch('rsvp_handler.php', {
                    method: 'POST',
                    body: formData,
                    credentials: 'same-origin'
                })
                .then(response => response.text())
                .then(text => {
                    console.error('Server response:', text);
                    alert('Network error occurred. Server response: ' + text.substring(0, 200));
                })
                .catch(err => {
                    console.error('Secondary error:', err);
                    alert('Network error occurred. Please try again.');
                });
            });
        }

        let currentEventId = null;

        function showEventDetails(eventId) {
            currentEventId = eventId;

            // Show loading state
            document.getElementById('eventDetailsBody').innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div></div>';

            // Show modal
            new bootstrap.Modal(document.getElementById('eventDetailsModal')).show();

            // Fetch event details
            fetch('get_event_details.php?id=' + eventId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('eventDetailsTitle').textContent = data.event.title;
                        document.getElementById('eventDetailsBody').innerHTML = data.html;

                        // Update RSVP button based on user's current RSVP status
                        const rsvpBtn = document.getElementById('eventRsvpBtn');
                        rsvpBtn.href = `enhanced_rsvp.php?event_id=${eventId}`;
                        if (data.event.user_rsvp_status) {
                            rsvpBtn.textContent = 'Update RSVP';
                        } else {
                            rsvpBtn.textContent = 'RSVP';
                        }
                    } else {
                        document.getElementById('eventDetailsBody').innerHTML = '<div class="alert alert-danger">Error loading event details: ' + data.message + '</div>';
                    }
                })
                .catch(error => {
                    document.getElementById('eventDetailsBody').innerHTML = '<div class="alert alert-danger">Error loading event details.</div>';
                });
        }

        function rsvpFromDetails() {
            if (currentEventId) {
                bootstrap.Modal.getInstance(document.getElementById('eventDetailsModal')).hide();
                rsvpEvent(currentEventId);
            }
        }
    </script>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
</body>
</html>
