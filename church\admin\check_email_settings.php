<?php
// Check email settings in database
require_once '../config.php';

echo "<h1>📧 Email Settings Database Check</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .step{background:#f5f5f5;padding:10px;margin:10px 0;border-left:4px solid #007bff;}</style>";

try {
    echo "<div class='step'>";
    echo "<h2>Step 1: Check Email Settings Table</h2>";
    
    // Check if email_settings table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'email_settings'");
    if ($stmt->rowCount() > 0) {
        echo "<span class='success'>✅ email_settings table exists</span><br>";
        
        // Get all email settings
        $stmt = $pdo->query("SELECT * FROM email_settings");
        $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($settings)) {
            echo "<span class='error'>❌ No email settings found in database</span><br>";
        } else {
            echo "<span class='success'>✅ Found " . count($settings) . " email settings</span><br>";
            echo "<table border='1' style='border-collapse:collapse; margin:10px 0;'>";
            echo "<tr><th>Setting Key</th><th>Setting Value</th></tr>";
            foreach ($settings as $setting) {
                $value = $setting['setting_value'];
                // Hide password for security
                if (strpos($setting['setting_key'], 'password') !== false) {
                    $value = str_repeat('*', strlen($value));
                }
                echo "<tr><td>{$setting['setting_key']}</td><td>{$value}</td></tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<span class='error'>❌ email_settings table does not exist</span><br>";
        
        // Create the table with default settings
        echo "<span class='info'>🔧 Creating email_settings table...</span><br>";
        
        $pdo->exec("CREATE TABLE IF NOT EXISTS email_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(50) NOT NULL UNIQUE,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // Insert default settings based on what I saw in email_settings.php
        $defaultSettings = [
            'smtp_host' => 'smtp.hostinger.com',
            'smtp_auth' => '1',
            'smtp_username' => '<EMAIL>',
            'smtp_password' => '!3wlI!dL',
            'smtp_secure' => 'ssl',
            'smtp_port' => '465',
            'sender_email' => '<EMAIL>',
            'sender_name' => 'Freedom Assembly Church'
        ];
        
        $stmt = $pdo->prepare("INSERT INTO email_settings (setting_key, setting_value) VALUES (?, ?)");
        
        foreach ($defaultSettings as $key => $value) {
            $stmt->execute([$key, $value]);
        }
        
        echo "<span class='success'>✅ email_settings table created with default settings</span><br>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 2: Check PHPMailer Availability</h2>";
    
    if (file_exists('../vendor/autoload.php')) {
        echo "<span class='success'>✅ Composer autoload found</span><br>";
        require_once '../vendor/autoload.php';
        
        if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
            echo "<span class='success'>✅ PHPMailer class available</span><br>";
        } else {
            echo "<span class='error'>❌ PHPMailer class not found via autoload</span><br>";
        }
    } elseif (file_exists('../vendor/phpmailer/phpmailer/src/PHPMailer.php')) {
        echo "<span class='success'>✅ PHPMailer files found directly</span><br>";
    } else {
        echo "<span class='error'>❌ PHPMailer not found</span><br>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 3: Test Email Settings</h2>";
    
    // Load email settings
    $emailSettings = [];
    $stmt = $pdo->query("SELECT * FROM email_settings");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $emailSettings[$row['setting_key']] = $row['setting_value'];
    }
    
    echo "<span class='info'>📧 Current SMTP Configuration:</span><br>";
    echo "<span class='info'>• Host: " . ($emailSettings['smtp_host'] ?? 'Not set') . "</span><br>";
    echo "<span class='info'>• Port: " . ($emailSettings['smtp_port'] ?? 'Not set') . "</span><br>";
    echo "<span class='info'>• Security: " . ($emailSettings['smtp_secure'] ?? 'Not set') . "</span><br>";
    echo "<span class='info'>• Username: " . ($emailSettings['smtp_username'] ?? 'Not set') . "</span><br>";
    echo "<span class='info'>• Sender Email: " . ($emailSettings['sender_email'] ?? 'Not set') . "</span><br>";
    echo "<span class='info'>• Sender Name: " . ($emailSettings['sender_name'] ?? 'Not set') . "</span><br>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 4: Check Member and QR Data</h2>";
    
    $target_email = 'bointa@<EMAIL>';
    
    // Check member
    $stmt = $pdo->prepare("SELECT * FROM members WHERE email = ?");
    $stmt->execute([$target_email]);
    $member = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($member) {
        echo "<span class='success'>✅ Member found: {$member['full_name']}</span><br>";
        
        // Check QR code
        $stmt = $pdo->prepare("
            SELECT mqr.*, e.title as event_title, e.event_date, e.location as event_location
            FROM member_qr_codes mqr
            JOIN events e ON mqr.event_id = e.id
            WHERE mqr.attendee_email = ?
            ORDER BY mqr.created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$target_email]);
        $qr_data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($qr_data) {
            echo "<span class='success'>✅ QR code found: {$qr_data['qr_token']}</span><br>";
            echo "<span class='info'>🎪 Event: {$qr_data['event_title']}</span><br>";
            echo "<span class='info'>📅 Date: " . date('F j, Y g:i A', strtotime($qr_data['event_date'])) . "</span><br>";
        } else {
            echo "<span class='error'>❌ No QR code found for this member</span><br>";
        }
    } else {
        echo "<span class='error'>❌ Member not found: {$target_email}</span><br>";
    }
    echo "</div>";
    
    echo "<div class='step' style='background:#e8f5e8;border-color:#28a745;'>";
    echo "<h2>🎯 Email System Status</h2>";
    
    $ready = true;
    $issues = [];
    
    if (empty($emailSettings['smtp_host'])) {
        $ready = false;
        $issues[] = "SMTP host not configured";
    }
    
    if (empty($emailSettings['smtp_username'])) {
        $ready = false;
        $issues[] = "SMTP username not configured";
    }
    
    if (empty($emailSettings['smtp_password'])) {
        $ready = false;
        $issues[] = "SMTP password not configured";
    }
    
    if (!$member) {
        $ready = false;
        $issues[] = "Test member not found";
    }
    
    if (!isset($qr_data)) {
        $ready = false;
        $issues[] = "QR code not found";
    }
    
    if (!class_exists('PHPMailer\PHPMailer\PHPMailer') && !file_exists('../vendor/phpmailer/phpmailer/src/PHPMailer.php')) {
        $ready = false;
        $issues[] = "PHPMailer not available";
    }
    
    if ($ready) {
        echo "<span class='success'>✅ Email system is ready to send QR code emails!</span><br>";
        echo "<span class='success'>✅ All components are properly configured</span><br>";
        echo "<br>";
        echo "<strong>📧 Ready to send test email to: {$target_email}</strong><br>";
    } else {
        echo "<span class='error'>❌ Email system has issues:</span><br>";
        foreach ($issues as $issue) {
            echo "<span class='error'>• {$issue}</span><br>";
        }
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='step' style='background:#ffe8e8;border-color:#dc3545;'>";
    echo "<h2>❌ Check Failed</h2>";
    echo "<span class='error'>Error: " . $e->getMessage() . "</span><br>";
    echo "<span class='error'>File: " . $e->getFile() . " Line: " . $e->getLine() . "</span>";
    echo "</div>";
}
?>

<script>
console.log('Email settings check completed');
</script>
