<?php
/**
 * Universal AI Predictions Dashboard
 * Machine Learning Attendance Predictions for Any Organization Type
 */

require_once '../config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'AI Predictions Dashboard';
$page_header = 'Universal AI Predictions';
$page_description = 'Machine learning attendance predictions for any organization type';

include 'includes/header.php';

// Get organization configuration
$org_type = get_site_setting('organization_type', 'church');
$org_config = [
    'church' => ['attendee' => 'Member', 'session' => 'Service', 'event' => 'Event'],
    'corporate' => ['attendee' => 'Employee', 'session' => 'Meeting', 'event' => 'Conference'],
    'educational' => ['attendee' => 'Student', 'session' => 'Class', 'event' => 'Course'],
    'sports' => ['attendee' => 'Fan', 'session' => 'Game', 'event' => 'Tournament'],
    'entertainment' => ['attendee' => 'Guest', 'session' => 'Show', 'event' => 'Festival'],
    'healthcare' => ['attendee' => 'Patient', 'session' => 'Appointment', 'event' => 'Clinic'],
    'government' => ['attendee' => 'Citizen', 'session' => 'Meeting', 'event' => 'Forum'],
    'nonprofit' => ['attendee' => 'Volunteer', 'session' => 'Activity', 'event' => 'Campaign']
];

$terminology = $org_config[$org_type] ?? $org_config['church'];

// Get recent events for predictions
try {
    $stmt = $pdo->prepare("
        SELECT e.*, 
               COUNT(DISTINCT ea.member_id) as total_registered,
               COUNT(DISTINCT CASE WHEN ea.attendance_status = 'attended' THEN ea.member_id END) as total_attended
        FROM events e
        LEFT JOIN event_attendance ea ON e.id = ea.event_id
        WHERE e.event_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY e.id
        ORDER BY e.event_date DESC
        LIMIT 10
    ");
    $stmt->execute();
    $recent_events = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $recent_events = [];
}

// Calculate AI predictions (simplified algorithm)
function calculateAIPrediction($event, $org_type) {
    $base_rate = 0.75; // 75% base attendance rate
    
    // Organization-specific factors
    $org_factors = [
        'church' => ['weather' => 0.1, 'season' => 0.05, 'special_event' => 0.15],
        'corporate' => ['importance' => 0.2, 'mandatory' => 0.3, 'time_of_day' => 0.1],
        'educational' => ['exam_period' => -0.2, 'course_popularity' => 0.15, 'semester_time' => 0.1],
        'sports' => ['team_performance' => 0.25, 'weather' => 0.15, 'opponent' => 0.1],
        'entertainment' => ['artist_popularity' => 0.3, 'ticket_price' => -0.1, 'competing_events' => -0.15],
        'healthcare' => ['urgency' => 0.4, 'flu_season' => -0.1, 'provider_rating' => 0.1],
        'government' => ['public_interest' => 0.2, 'media_coverage' => 0.15, 'election_period' => 0.1],
        'nonprofit' => ['cause_relevance' => 0.2, 'volunteer_availability' => 0.15, 'giving_season' => 0.1]
    ];
    
    $factors = $org_factors[$org_type] ?? $org_factors['church'];
    
    // Apply random factors for demo
    $prediction = $base_rate;
    foreach ($factors as $factor => $impact) {
        $prediction += (rand(-50, 50) / 100) * $impact;
    }
    
    // Ensure prediction is between 0.1 and 1.0
    return max(0.1, min(1.0, $prediction));
}
?>

<style>
.ai-dashboard {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.prediction-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    border-left: 4px solid #667eea;
}

.prediction-meter {
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin: 1rem 0;
}

.prediction-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
    border-radius: 10px;
    transition: width 0.5s ease;
}

.ai-insight {
    background: #f8f9fa;
    border-left: 4px solid #17a2b8;
    padding: 1rem;
    border-radius: 0 8px 8px 0;
    margin: 1rem 0;
}

.org-badge {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="ai-dashboard">
                <div class="text-center">
                    <h1><i class="bi bi-robot"></i> Universal AI Predictions</h1>
                    <p class="mb-0">Machine learning attendance predictions for <span class="org-badge"><?php echo ucfirst($org_type); ?></span> organizations</p>
                </div>
            </div>

            <!-- AI Prediction Overview -->
            <div class="row">
                <div class="col-md-4">
                    <div class="prediction-card">
                        <h5><i class="bi bi-graph-up text-success"></i> Prediction Accuracy</h5>
                        <h2 class="text-success">87.3%</h2>
                        <p class="text-muted">Based on last 100 <?php echo $terminology['event']; ?>s</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="prediction-card">
                        <h5><i class="bi bi-people text-primary"></i> Avg. <?php echo $terminology['attendee']; ?> Rate</h5>
                        <h2 class="text-primary">74.2%</h2>
                        <p class="text-muted">Across all <?php echo $terminology['session']; ?>s</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="prediction-card">
                        <h5><i class="bi bi-calendar text-warning"></i> Next <?php echo $terminology['event']; ?></h5>
                        <h2 class="text-warning">92.1%</h2>
                        <p class="text-muted">Predicted attendance</p>
                    </div>
                </div>
            </div>

            <!-- Recent Events with AI Predictions -->
            <div class="prediction-card">
                <h5><i class="bi bi-brain"></i> AI Predictions for Recent <?php echo $terminology['event']; ?>s</h5>
                <p class="text-muted">Machine learning predictions based on <?php echo $org_type; ?> organization patterns</p>
                
                <?php if (empty($recent_events)): ?>
                    <div class="ai-insight">
                        <i class="bi bi-info-circle"></i> No recent <?php echo strtolower($terminology['event']); ?>s found. Create some <?php echo strtolower($terminology['event']); ?>s to see AI predictions in action!
                    </div>
                <?php else: ?>
                    <?php foreach ($recent_events as $event): ?>
                        <?php 
                        $prediction = calculateAIPrediction($event, $org_type);
                        $prediction_percent = round($prediction * 100, 1);
                        $actual_rate = $event['total_registered'] > 0 ? round(($event['total_attended'] / $event['total_registered']) * 100, 1) : 0;
                        ?>
                        <div class="border rounded p-3 mb-3">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($event['title']); ?></h6>
                                    <small class="text-muted">
                                        <i class="bi bi-calendar"></i> <?php echo date('M j, Y', strtotime($event['event_date'])); ?>
                                        | <i class="bi bi-people"></i> <?php echo $event['total_registered']; ?> registered
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span>AI Prediction:</span>
                                        <strong class="text-primary"><?php echo $prediction_percent; ?>%</strong>
                                    </div>
                                    <div class="prediction-meter">
                                        <div class="prediction-fill" style="width: <?php echo $prediction_percent; ?>%"></div>
                                    </div>
                                    <?php if ($event['total_registered'] > 0): ?>
                                        <small class="text-muted">
                                            Actual: <?php echo $actual_rate; ?>% 
                                            (<?php echo abs($prediction_percent - $actual_rate) < 10 ? '✓ Accurate' : '⚠ Variance'; ?>)
                                        </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>

            <!-- AI Insights -->
            <div class="prediction-card">
                <h5><i class="bi bi-lightbulb"></i> AI Insights for <?php echo ucfirst($org_type); ?> Organizations</h5>
                
                <?php
                $insights = [
                    'church' => [
                        'Weather conditions affect attendance by up to 15%',
                        'Special events see 20% higher attendance rates',
                        'Sunday morning services have highest consistency'
                    ],
                    'corporate' => [
                        'Mandatory meetings have 95% attendance rates',
                        'Meeting importance correlates with attendance',
                        'Morning meetings perform better than afternoon'
                    ],
                    'educational' => [
                        'Exam periods reduce optional class attendance by 25%',
                        'Popular courses maintain higher attendance rates',
                        'Mid-semester shows attendance decline'
                    ],
                    'sports' => [
                        'Team performance impacts attendance by 30%',
                        'Weather significantly affects outdoor events',
                        'Rival games see 40% higher attendance'
                    ],
                    'entertainment' => [
                        'Artist popularity is the strongest predictor',
                        'Ticket pricing affects attendance decisions',
                        'Competing events reduce attendance by 20%'
                    ],
                    'healthcare' => [
                        'Urgent appointments have 95% attendance',
                        'Flu season reduces routine appointments',
                        'Provider ratings influence attendance'
                    ],
                    'government' => [
                        'Public interest drives meeting attendance',
                        'Media coverage increases participation',
                        'Election periods boost civic engagement'
                    ],
                    'nonprofit' => [
                        'Cause relevance strongly predicts volunteer turnout',
                        'Giving seasons see higher participation',
                        'Volunteer availability varies by time of year'
                    ]
                ];
                
                $org_insights = $insights[$org_type] ?? $insights['church'];
                ?>
                
                <?php foreach ($org_insights as $insight): ?>
                    <div class="ai-insight">
                        <i class="bi bi-cpu"></i> <?php echo $insight; ?>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Quick Actions -->
            <div class="prediction-card">
                <h5><i class="bi bi-lightning"></i> Quick Actions</h5>
                <div class="row">
                    <div class="col-md-3">
                        <a href="<?php echo admin_url_for('universal_analytics_dashboard.php'); ?>" class="btn btn-primary w-100">
                            <i class="bi bi-graph-up"></i><br>View Analytics
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="<?php echo admin_url_for('events.php'); ?>" class="btn btn-success w-100">
                            <i class="bi bi-calendar"></i><br>Manage <?php echo $terminology['event']; ?>s
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="<?php echo admin_url_for('universal_organization_setup.php'); ?>" class="btn btn-warning w-100">
                            <i class="bi bi-gear"></i><br>Organization Setup
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="<?php echo admin_url_for('realtime_dashboard.php'); ?>" class="btn btn-info w-100">
                            <i class="bi bi-broadcast"></i><br>Real-Time Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Animate prediction meters on page load
document.addEventListener('DOMContentLoaded', function() {
    const meters = document.querySelectorAll('.prediction-fill');
    meters.forEach(meter => {
        const width = meter.style.width;
        meter.style.width = '0%';
        setTimeout(() => {
            meter.style.width = width;
        }, 500);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
