<?php
session_start();

// Include the configuration file
require_once '../config.php';

$token = $_GET['token'] ?? '';
$event_id = $_GET['event_id'] ?? '';
$message = '';
$error = '';
$session_data = null;

// If token is provided, validate and get session data
if (!empty($token)) {
    try {
        // First try the new session-specific QR codes
        $stmt = $pdo->prepare("
            SELECT
                s.*,
                e.title as event_title,
                e.id as event_id,
                sqr.expires_at,
                sqr.attendee_name,
                sqr.attendee_email,
                sqr.attendee_type,
                sqr.member_id,
                sqr.guest_name,
                sqr.guest_email,
                sqr.is_used
            FROM session_attendance_qr_codes sqr
            JOIN event_sessions s ON sqr.session_id = s.id
            JOIN events e ON s.event_id = e.id
            WHERE sqr.qr_token = ? AND sqr.expires_at > NOW()
        ");
        $stmt->execute([$token]);
        $session_data = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$session_data) {
            // Fallback to old session QR codes for backward compatibility
            $stmt = $pdo->prepare("
                SELECT s.*, e.title as event_title, e.id as event_id, qr.expires_at, qr.is_active
                FROM session_qr_codes qr
                JOIN event_sessions s ON qr.session_id = s.id
                JOIN events e ON s.event_id = e.id
                WHERE qr.qr_token = ? AND qr.is_active = 1 AND qr.expires_at > NOW()
            ");
            $stmt->execute([$token]);
            $session_data = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($session_data) {
                $session_data['qr_type'] = 'legacy';
                // Update scan count for legacy QR codes
                $stmt = $pdo->prepare("UPDATE session_qr_codes SET scan_count = scan_count + 1 WHERE qr_token = ?");
                $stmt->execute([$token]);
            }
        } else {
            $session_data['qr_type'] = 'session_specific';
        }

        if (!$session_data) {
            $error = "Invalid or expired QR code. Please contact event staff.";
        }
    } catch (PDOException $e) {
        $error = "Database error: " . $e->getMessage();
    }
}

// Handle check-in submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'qr_checkin' && !empty($token)) {
            // Handle QR code check-in
            if (!$session_data) {
                throw new Exception("Invalid QR code session data.");
            }

            $session_id = $session_data['id'];
            $pdo->beginTransaction();

            if ($session_data['qr_type'] === 'session_specific') {
                // Session-specific QR code check-in
                $member_id = $session_data['member_id'];
                $guest_name = $session_data['guest_name'];
                $guest_email = $session_data['guest_email'];
                $attendee_type = $session_data['attendee_type'];

                if ($session_data['is_used']) {
                    throw new Exception("This QR code has already been used for check-in.");
                }

                // Mark QR code as used
                $stmt = $pdo->prepare("
                    UPDATE session_attendance_qr_codes
                    SET is_used = 1, used_at = NOW()
                    WHERE qr_token = ?
                ");
                $stmt->execute([$token]);

                // Update or create session attendance record
                if ($attendee_type === 'member' && $member_id) {
                    $stmt = $pdo->prepare("
                        INSERT INTO session_attendance
                        (session_id, member_id, attendance_status, registration_date, attendance_date)
                        VALUES (?, ?, 'attended', NOW(), NOW())
                        ON DUPLICATE KEY UPDATE
                        attendance_status = 'attended', attendance_date = NOW()
                    ");
                    $stmt->execute([$session_id, $member_id]);
                } else {
                    $stmt = $pdo->prepare("
                        INSERT INTO session_attendance
                        (session_id, guest_name, guest_email, attendance_status, registration_date, attendance_date)
                        VALUES (?, ?, ?, 'attended', NOW(), NOW())
                        ON DUPLICATE KEY UPDATE
                        attendance_status = 'attended', attendance_date = NOW()
                    ");
                    $stmt->execute([$session_id, $guest_name, $guest_email]);
                }

                $message = "✅ {$session_data['attendee_name']} checked in successfully via QR code!";

            } else {
                // Legacy session QR code - show attendee selection
                $message = "QR code scanned successfully. Please select the attendee to check in.";
            }

            $pdo->commit();

        } elseif ($_POST['action'] === 'checkin_attendee') {
            $session_id = $_POST['session_id'] ?? '';
            $attendee_type = $_POST['attendee_type'] ?? '';
            $member_id = $_POST['member_id'] ?? '';
            $guest_name = $_POST['guest_name'] ?? '';
            $guest_email = $_POST['guest_email'] ?? '';
            
            if (empty($session_id)) {
                throw new Exception("Session ID is required.");
            }
            
            $pdo->beginTransaction();
            
            if ($attendee_type === 'member' && !empty($member_id)) {
                // Check if member attendance record exists
                $stmt = $pdo->prepare("
                    SELECT id FROM session_attendance 
                    WHERE session_id = ? AND member_id = ?
                ");
                $stmt->execute([$session_id, $member_id]);
                $existing = $stmt->fetch();
                
                if ($existing) {
                    // Update existing record
                    $stmt = $pdo->prepare("
                        UPDATE session_attendance 
                        SET attendance_status = 'attended', attendance_date = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$existing['id']]);
                } else {
                    // Create new record
                    $stmt = $pdo->prepare("
                        INSERT INTO session_attendance 
                        (session_id, member_id, attendance_status, registration_date, attendance_date)
                        VALUES (?, ?, 'attended', NOW(), NOW())
                    ");
                    $stmt->execute([$session_id, $member_id]);
                }
                
                $message = "Member checked in successfully!";
                
            } elseif ($attendee_type === 'guest' && !empty($guest_name) && !empty($guest_email)) {
                // Check if guest attendance record exists
                $stmt = $pdo->prepare("
                    SELECT id FROM session_attendance 
                    WHERE session_id = ? AND guest_name = ? AND guest_email = ?
                ");
                $stmt->execute([$session_id, $guest_name, $guest_email]);
                $existing = $stmt->fetch();
                
                if ($existing) {
                    // Update existing record
                    $stmt = $pdo->prepare("
                        UPDATE session_attendance 
                        SET attendance_status = 'attended', attendance_date = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$existing['id']]);
                } else {
                    // Create new record
                    $stmt = $pdo->prepare("
                        INSERT INTO session_attendance 
                        (session_id, guest_name, guest_email, attendance_status, registration_date, attendance_date)
                        VALUES (?, ?, ?, 'attended', NOW(), NOW())
                    ");
                    $stmt->execute([$session_id, $guest_name, $guest_email]);
                }
                
                $message = "Guest checked in successfully!";
                
            } else {
                throw new Exception("Please provide valid attendee information.");
            }
            
            $pdo->commit();
            
        } elseif ($_POST['action'] === 'bulk_checkin') {
            $session_id = $_POST['session_id'] ?? '';
            $attendee_ids = $_POST['attendee_ids'] ?? [];
            
            if (empty($session_id) || empty($attendee_ids)) {
                throw new Exception("Session and attendees are required.");
            }
            
            $pdo->beginTransaction();
            $checked_in_count = 0;
            
            foreach ($attendee_ids as $attendee_id) {
                if (strpos($attendee_id, 'member_') === 0) {
                    $member_id = substr($attendee_id, 7);
                    $stmt = $pdo->prepare("
                        UPDATE session_attendance 
                        SET attendance_status = 'attended', attendance_date = NOW()
                        WHERE session_id = ? AND member_id = ?
                    ");
                    $stmt->execute([$session_id, $member_id]);
                    $checked_in_count += $stmt->rowCount();
                } else {
                    // Handle guest format: guest_name_email
                    $parts = explode('_', $attendee_id, 3);
                    if (count($parts) >= 3) {
                        $guest_name = $parts[1];
                        $guest_email = $parts[2];
                        $stmt = $pdo->prepare("
                            UPDATE session_attendance 
                            SET attendance_status = 'attended', attendance_date = NOW()
                            WHERE session_id = ? AND guest_name = ? AND guest_email = ?
                        ");
                        $stmt->execute([$session_id, $guest_name, $guest_email]);
                        $checked_in_count += $stmt->rowCount();
                    }
                }
            }
            
            $pdo->commit();
            $message = "Successfully checked in {$checked_in_count} attendees!";
        }
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $error = $e->getMessage();
    }
}

// Get session attendees if we have session data
$attendees = [];
if ($session_data) {
    $stmt = $pdo->prepare("
        SELECT 
            sa.*,
            CASE 
                WHEN sa.member_id IS NOT NULL THEN m.full_name
                ELSE sa.guest_name
            END as attendee_name,
            CASE 
                WHEN sa.member_id IS NOT NULL THEN m.email
                ELSE sa.guest_email
            END as attendee_email,
            CASE 
                WHEN sa.member_id IS NOT NULL THEN 'member'
                ELSE 'guest'
            END as attendee_type,
            CASE 
                WHEN sa.member_id IS NOT NULL THEN CONCAT('member_', sa.member_id)
                ELSE CONCAT('guest_', sa.guest_name, '_', sa.guest_email)
            END as attendee_id
        FROM session_attendance sa
        LEFT JOIN members m ON sa.member_id = m.id
        WHERE sa.session_id = ?
        ORDER BY sa.attendance_status ASC, attendee_name ASC
    ");
    $stmt->execute([$session_data['id']]);
    $attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Get all members for quick search
$all_members = [];
if ($session_data) {
    $stmt = $pdo->prepare("SELECT id, full_name, email FROM members WHERE is_active = 1 ORDER BY full_name");
    $stmt->execute();
    $all_members = $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Check-in</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-size: 1.1rem;
        }
        .mobile-container {
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
        }
        .checkin-card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border: none;
        }
        .session-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 20px;
        }
        .attendee-item {
            border: 2px solid #dee2e6;
            border-radius: 10px;
            margin: 8px 0;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .attendee-item:hover {
            border-color: #0d6efd;
            background-color: #f8f9ff;
        }
        .attendee-item.selected {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        .attendee-item.attended {
            border-color: #6c757d;
            background-color: #f8f9fa;
            opacity: 0.7;
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 4px 8px;
        }
        .quick-checkin-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            border-radius: 50px;
            padding: 15px 20px;
        }
        .search-box {
            font-size: 1.1rem;
            padding: 12px;
            border-radius: 10px;
        }
        @media (max-width: 576px) {
            .mobile-container {
                padding: 10px;
            }
            .attendee-item {
                padding: 12px;
            }
        }
    </style>
</head>
<body>

<div class="mobile-container">
    <?php if (empty($session_data)): ?>
        <!-- No Session Data / QR Scanner -->
        <div class="card checkin-card">
            <div class="card-body text-center py-5">
                <i class="bi bi-qr-code-scan text-primary" style="font-size: 4rem;"></i>
                <h3 class="mt-3">Mobile Check-in</h3>
                <p class="lead text-muted">Scan a session QR code to begin check-in</p>
                
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger mt-3">
                        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <div class="mt-4">
                    <button class="btn btn-primary btn-lg" onclick="startQRScanner()">
                        <i class="bi bi-camera"></i> Scan QR Code
                    </button>
                </div>
                
                <div class="mt-3">
                    <small class="text-muted">Or enter session code manually:</small>
                    <form method="GET" class="mt-2">
                        <div class="input-group">
                            <input type="text" class="form-control" name="token" placeholder="Enter session code">
                            <button type="submit" class="btn btn-outline-primary">Go</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
    <?php else: ?>
        <!-- Session Check-in Interface -->
        <div class="card checkin-card">
            <!-- Session Header -->
            <div class="session-header">
                <h4 class="mb-1">
                    <i class="bi bi-calendar-event"></i> <?php echo htmlspecialchars($session_data['session_title']); ?>
                </h4>
                <p class="mb-0 text-white-50"><?php echo htmlspecialchars($session_data['event_title']); ?></p>
                <small class="text-white-50">
                    <?php echo date('M j, Y g:i A', strtotime($session_data['start_datetime'])); ?>
                    <?php if ($session_data['location']): ?>
                        • <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($session_data['location']); ?>
                    <?php endif; ?>
                </small>
            </div>
            
            <div class="card-body">
                <!-- Session-Specific QR Code Check-in -->
                <?php if (isset($session_data['qr_type']) && $session_data['qr_type'] === 'session_specific' && !$message): ?>
                    <div class="alert alert-info border-0 mb-4">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-qr-code-scan fs-3 me-3 text-primary"></i>
                            <div>
                                <h6 class="mb-1">Session-Specific QR Code Detected</h6>
                                <p class="mb-2">
                                    <strong><?php echo htmlspecialchars($session_data['attendee_name']); ?></strong>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($session_data['attendee_email']); ?></small>
                                </p>
                                <?php if ($session_data['is_used']): ?>
                                    <div class="badge bg-warning">Already checked in</div>
                                <?php else: ?>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="qr_checkin">
                                        <input type="hidden" name="token" value="<?php echo htmlspecialchars($token); ?>">
                                        <button type="submit" class="btn btn-success btn-sm">
                                            <i class="bi bi-check-circle"></i> Check In Now
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Success/Error Messages -->
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Quick Stats -->
                <div class="row mb-3">
                    <div class="col-4 text-center">
                        <h5 class="text-primary mb-0"><?php echo count($attendees); ?></h5>
                        <small class="text-muted">Registered</small>
                    </div>
                    <div class="col-4 text-center">
                        <h5 class="text-success mb-0"><?php echo count(array_filter($attendees, function($a) { return $a['attendance_status'] === 'attended'; })); ?></h5>
                        <small class="text-muted">Checked In</small>
                    </div>
                    <div class="col-4 text-center">
                        <h5 class="text-warning mb-0"><?php echo count(array_filter($attendees, function($a) { return $a['attendance_status'] === 'registered'; })); ?></h5>
                        <small class="text-muted">Pending</small>
                    </div>
                </div>
                
                <!-- Search Box -->
                <div class="mb-3">
                    <input type="text" class="form-control search-box" id="attendee-search" 
                           placeholder="Search attendees..." onkeyup="filterAttendees()">
                </div>
                
                <!-- Quick Actions -->
                <div class="row mb-3">
                    <div class="col-6">
                        <button class="btn btn-outline-primary w-100" onclick="selectAllPending()">
                            <i class="bi bi-check-square"></i> Select Pending
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-outline-secondary w-100" onclick="selectNone()">
                            <i class="bi bi-square"></i> Clear Selection
                        </button>
                    </div>
                </div>
                
                <!-- Attendee List -->
                <div class="attendee-list" style="max-height: 400px; overflow-y: auto;">
                    <?php if (empty($attendees)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-person-x fs-3 d-block mb-2"></i>
                            <p>No attendees registered for this session.</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($attendees as $attendee): ?>
                            <div class="attendee-item <?php echo $attendee['attendance_status'] === 'attended' ? 'attended' : ''; ?>"
                                 data-attendee-id="<?php echo $attendee['attendee_id']; ?>"
                                 data-name="<?php echo strtolower($attendee['attendee_name']); ?>"
                                 onclick="toggleAttendeeSelection('<?php echo $attendee['attendee_id']; ?>')">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <input type="checkbox" class="form-check-input me-3 attendee-checkbox"
                                               value="<?php echo $attendee['attendee_id']; ?>"
                                               onclick="event.stopPropagation();"
                                               <?php echo $attendee['attendance_status'] === 'attended' ? 'disabled' : ''; ?>>
                                        <div>
                                            <strong><?php echo htmlspecialchars($attendee['attendee_name']); ?></strong>
                                            <?php if ($attendee['attendee_email']): ?>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="bi bi-envelope"></i> <?php echo htmlspecialchars($attendee['attendee_email']); ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge status-badge bg-<?php
                                            echo $attendee['attendance_status'] === 'attended' ? 'success' :
                                                ($attendee['attendance_status'] === 'no_show' ? 'danger' : 'secondary');
                                        ?>">
                                            <?php echo ucfirst($attendee['attendance_status']); ?>
                                        </span>
                                        <br>
                                        <small class="text-muted">
                                            <span class="badge bg-<?php echo $attendee['attendee_type'] === 'member' ? 'primary' : 'info'; ?>">
                                                <?php echo ucfirst($attendee['attendee_type']); ?>
                                            </span>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                
                <!-- Manual Check-in Form -->
                <div class="mt-4">
                    <h6><i class="bi bi-person-plus"></i> Manual Check-in</h6>
                    <form method="POST">
                        <input type="hidden" name="action" value="checkin_attendee">
                        <input type="hidden" name="session_id" value="<?php echo $session_data['id']; ?>">
                        
                        <div class="mb-3">
                            <select class="form-select" name="attendee_type" id="attendee-type" onchange="toggleAttendeeFields()">
                                <option value="">Select Attendee Type</option>
                                <option value="member">Church Member</option>
                                <option value="guest">Guest</option>
                            </select>
                        </div>
                        
                        <div id="member-fields" style="display: none;">
                            <div class="mb-3">
                                <select class="form-select" name="member_id" id="member-select">
                                    <option value="">Select Member</option>
                                    <?php foreach ($all_members as $member): ?>
                                        <option value="<?php echo $member['id']; ?>">
                                            <?php echo htmlspecialchars($member['full_name']); ?> 
                                            (<?php echo htmlspecialchars($member['email']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div id="guest-fields" style="display: none;">
                            <div class="mb-3">
                                <input type="text" class="form-control" name="guest_name" placeholder="Guest Name">
                            </div>
                            <div class="mb-3">
                                <input type="email" class="form-control" name="guest_email" placeholder="Guest Email">
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-success w-100">
                            <i class="bi bi-check-circle"></i> Check In
                        </button>
                    </form>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Quick Check-in Button -->
<?php if ($session_data && !empty($attendees)): ?>
    <button class="btn btn-success quick-checkin-btn" onclick="bulkCheckin()" id="quick-checkin-btn" style="display: none;">
        <i class="bi bi-check-all"></i> Check In Selected (<span id="selected-count">0</span>)
    </button>
<?php endif; ?>

<!-- Staff QR Scanner Button -->
<a href="staff_qr_scanner.php" class="btn btn-info" style="position: fixed; bottom: 20px; left: 20px; z-index: 1000; box-shadow: 0 4px 8px rgba(0,0,0,0.3); border-radius: 50px; padding: 15px 20px;">
    <i class="bi bi-camera-fill"></i> Staff Scanner
</a>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/realtime-sync.js"></script>
<script>
// Initialize real-time sync for this session
<?php if ($session_data): ?>
const realtimeSync = window.RealtimeSyncManager.create(<?php echo $session_data['id']; ?>, {
    onUpdate: function(updates) {
        console.log('Received updates:', updates);
        // Updates are automatically processed by the sync system
    },
    onConflict: function(conflictData) {
        showConflictAlert(conflictData);
    },
    onStatsUpdate: function(stats) {
        updateSessionStats(stats);
    },
    onError: function(error) {
        console.error('Sync error:', error);
    }
});

function showConflictAlert(conflictData) {
    const alert = document.createElement('div');
    alert.className = 'alert alert-warning alert-dismissible fade show';
    alert.innerHTML = `
        <i class="bi bi-exclamation-triangle"></i>
        <strong>Scheduling Conflict Detected!</strong><br>
        This member has conflicts with ${conflictData.conflicts.length} other session(s).
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.mobile-container').insertBefore(alert, document.querySelector('.card'));
}

function updateSessionStats(stats) {
    // Update the quick stats display
    const statsElements = document.querySelectorAll('.col-4 h5');
    if (statsElements.length >= 3) {
        statsElements[0].textContent = stats.total_registered || 0;
        statsElements[1].textContent = stats.total_attended || 0;
        statsElements[2].textContent = stats.total_pending || 0;
    }
}
<?php endif; ?>

// Mobile check-in functionality
function toggleAttendeeFields() {
    const attendeeType = document.getElementById('attendee-type').value;
    const memberFields = document.getElementById('member-fields');
    const guestFields = document.getElementById('guest-fields');
    
    if (attendeeType === 'member') {
        memberFields.style.display = 'block';
        guestFields.style.display = 'none';
    } else if (attendeeType === 'guest') {
        memberFields.style.display = 'none';
        guestFields.style.display = 'block';
    } else {
        memberFields.style.display = 'none';
        guestFields.style.display = 'none';
    }
}

function toggleAttendeeSelection(attendeeId) {
    const item = document.querySelector(`[data-attendee-id="${attendeeId}"]`);
    const checkbox = item.querySelector('.attendee-checkbox');
    
    if (!checkbox.disabled) {
        checkbox.checked = !checkbox.checked;
        
        if (checkbox.checked) {
            item.classList.add('selected');
        } else {
            item.classList.remove('selected');
        }
        
        updateSelectionCount();
    }
}

function selectAllPending() {
    document.querySelectorAll('.attendee-item:not(.attended)').forEach(item => {
        const checkbox = item.querySelector('.attendee-checkbox');
        if (!checkbox.disabled) {
            checkbox.checked = true;
            item.classList.add('selected');
        }
    });
    updateSelectionCount();
}

function selectNone() {
    document.querySelectorAll('.attendee-checkbox').forEach(checkbox => {
        checkbox.checked = false;
        checkbox.closest('.attendee-item').classList.remove('selected');
    });
    updateSelectionCount();
}

function updateSelectionCount() {
    const selectedCount = document.querySelectorAll('.attendee-checkbox:checked').length;
    const quickBtn = document.getElementById('quick-checkin-btn');
    const countSpan = document.getElementById('selected-count');
    
    if (selectedCount > 0) {
        quickBtn.style.display = 'block';
        countSpan.textContent = selectedCount;
    } else {
        quickBtn.style.display = 'none';
    }
}

function filterAttendees() {
    const searchTerm = document.getElementById('attendee-search').value.toLowerCase();
    
    document.querySelectorAll('.attendee-item').forEach(item => {
        const name = item.getAttribute('data-name');
        if (name.includes(searchTerm)) {
            item.style.display = '';
        } else {
            item.style.display = 'none';
        }
    });
}

function bulkCheckin() {
    const selectedCheckboxes = document.querySelectorAll('.attendee-checkbox:checked');
    const attendeeIds = Array.from(selectedCheckboxes).map(cb => cb.value);
    
    if (attendeeIds.length === 0) {
        alert('Please select attendees to check in.');
        return;
    }
    
    if (!confirm(`Check in ${attendeeIds.length} selected attendees?`)) {
        return;
    }
    
    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';
    
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'bulk_checkin';
    form.appendChild(actionInput);
    
    const sessionInput = document.createElement('input');
    sessionInput.type = 'hidden';
    sessionInput.name = 'session_id';
    sessionInput.value = '<?php echo $session_data['id'] ?? ''; ?>';
    form.appendChild(sessionInput);
    
    attendeeIds.forEach(id => {
        const attendeeInput = document.createElement('input');
        attendeeInput.type = 'hidden';
        attendeeInput.name = 'attendee_ids[]';
        attendeeInput.value = id;
        form.appendChild(attendeeInput);
    });
    
    document.body.appendChild(form);
    form.submit();
}

function startQRScanner() {
    // This would integrate with a QR scanner library
    alert('QR Scanner would be implemented here using camera API');
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateSelectionCount();
    
    // Add touch-friendly interactions
    document.querySelectorAll('.attendee-item').forEach(item => {
        item.style.minHeight = '70px';
    });
});
</script>

</body>
</html>
