<?php
/**
 * Test Super Admin Dashboard Access
 */

// Start session and simulate admin login
session_start();
$_SESSION['admin_id'] = 4; // Admin user ID
$_SESSION['admin_name'] = 'Administrator';
$_SESSION['admin_username'] = 'admin';

echo "<h1>Super Admin Dashboard Test</h1>";
echo "<p>Session admin_id: " . ($_SESSION['admin_id'] ?? 'Not set') . "</p>";

try {
    // Include required files
    require_once '../config.php';
    require_once 'includes/rbac_access_control.php';
    
    echo "<h2>RBAC Test</h2>";
    $rbac = new RBACAccessControl($pdo, $_SESSION['admin_id']);
    
    echo "<p><strong>Primary Role:</strong> " . ($rbac->getPrimaryRole() ?: 'None') . "</p>";
    echo "<p><strong>Has super_admin role:</strong> " . ($rbac->hasRole('super_admin') ? 'Yes' : 'No') . "</p>";
    echo "<p><strong>Default Dashboard:</strong> " . $rbac->getDefaultDashboard() . "</p>";
    
    echo "<h2>Sidebar Test</h2>";
    $sidebar_items = $rbac->getSidebarItems();
    echo "<p><strong>Sidebar items count:</strong> " . count($sidebar_items) . "</p>";
    
    if (!empty($sidebar_items)) {
        echo "<h3>Super Admin Sidebar Items:</h3>";
        echo "<ul>";
        foreach ($sidebar_items as $key => $item) {
            if (isset($item['items'])) {
                echo "<li><strong>" . htmlspecialchars($item['title']) . "</strong> (Section)";
                echo "<ul>";
                foreach ($item['items'] as $subitem) {
                    echo "<li>" . htmlspecialchars($subitem['title']) . " (" . htmlspecialchars($subitem['url']) . ")</li>";
                }
                echo "</ul>";
                echo "</li>";
            } else {
                echo "<li>" . htmlspecialchars($item['title']) . " (" . htmlspecialchars($item['url']) . ")</li>";
            }
        }
        echo "</ul>";
    }
    
    echo "<h2>Dashboard Access Test</h2>";
    echo "<p><a href='super_admin_dashboard.php' target='_blank'>Open Super Admin Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
