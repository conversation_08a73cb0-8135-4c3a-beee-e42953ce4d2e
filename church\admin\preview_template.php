<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Check if template ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: email_templates.php");
    exit();
}

$template_id = intval($_GET['id']);

// Check if this is an embedded preview request
$isEmbedded = isset($_GET['embed']) && $_GET['embed'] == 1;

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Get template
$stmt = $conn->prepare("SELECT * FROM email_templates WHERE id = ?");
$stmt->execute([$template_id]);
$template = $stmt->fetch();

// Replace lottery number placeholders with sample values for preview
function replaceLotteryPlaceholders($content) {
    // Check if this is a lottery template (contains lottery ball placeholders)
    if (strpos($content, '{BALL1}') !== false || 
        strpos($content, '{BALL2}') !== false ||
        strpos($content, '{BALL3}') !== false) {
        
        // Use fixed sample numbers for preview
        $ball1 = 14;
        $ball2 = 28;
        $ball3 = 53;
        $ball4 = 67;
        $ball5 = 92;
        $powerball = 9;
        
        // Replace placeholders with sample numbers
        $content = str_replace(
            ['{BALL1}', '{BALL2}', '{BALL3}', '{BALL4}', '{BALL5}', '{POWERBALL}'],
            [$ball1, $ball2, $ball3, $ball4, $ball5, $powerball],
            $content
        );
    }
    
    return $content;
}

// Apply replacement to template content
if ($template) {
    $template['content'] = replaceLotteryPlaceholders($template['content']);
}

if (!$template) {
    header("Location: email_templates.php");
    exit();
}

// Get a sample member for preview
$stmt = $conn->query("SELECT * FROM members ORDER BY id LIMIT 1");
$sample_member = [
    'full_name' => 'John Doe',
    'email' => '<EMAIL>',
    'phone_number' => '(*************',
    'image_path' => 'uploads/67c5912233dc8.jpg'
];

if ($row = $stmt->fetch()) {
    $sample_member = array_merge($sample_member, $row);
}

// Add church logo path for preview - using the correct local path
$churchLogo = '../church/assets/images/banner.jpg';

// If this is a birthday notification template, add a birthday member
if (strpos(strtolower($template['template_name']), 'notification') !== false ||
    (isset($template['is_birthday_template']) && $template['is_birthday_template']) ||
    strpos(strtolower($template['template_name']), 'birthday') !== false) {
    // Use the selected member if provided
    $birthday_member = null;
    if (isset($_GET['birthday_member_id']) && is_numeric($_GET['birthday_member_id'])) {
        $stmt = $conn->prepare("SELECT * FROM members WHERE id = ?");
        $stmt->execute([intval($_GET['birthday_member_id'])]);
        $birthday_member = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    // Fallback to previous logic if not found
    if (!$birthday_member) {
        $stmt = $conn->query("SELECT * FROM members WHERE id != {$sample_member['id']} AND image_path IS NOT NULL AND image_path != '' ORDER BY id LIMIT 1");
        $birthday_member = [
            'full_name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'phone_number' => '(*************',
            'birth_date' => date('Y-m-d', strtotime('1990-' . date('m-d', strtotime('+3 days')))),
            'image_path' => 'assets/img/default-avatar.png' // Use default as fallback
        ];
        if ($row = $stmt->fetch()) {
            $birthday_member = array_merge($birthday_member, $row);
        } else {
            // If no member with image found, try to get any member
            $stmt = $conn->query("SELECT * FROM members WHERE id != {$sample_member['id']} ORDER BY id LIMIT 1");
            if ($row = $stmt->fetch()) {
                $birthday_member = array_merge($birthday_member, $row);
                // Use default avatar if no image path
                if (empty($birthday_member['image_path'])) {
                    $birthday_member['image_path'] = 'assets/img/default-avatar.png';
                }
            }
        }
    }
    // Add birthday member placeholders with local paths for preview
    $birthdayMemberImagePath = '../' . ltrim($birthday_member['image_path'], '/');
    // Create proper HTML img tag for preview
    $birthdayMemberImageHtml = '<img src="' . $birthdayMemberImagePath . '" alt="' .
        htmlspecialchars($birthday_member['full_name']) .
        '" style="width: 140px; height: 140px; border-radius: 50%; object-fit: cover; border: 5px solid #ff758c; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">';
    $birthday_member_replacements = [
        '{birthday_member_name}' => explode(' ', $birthday_member['full_name'])[0],
        '{birthday_member_full_name}' => $birthday_member['full_name'],
        '{birthday_member_email}' => $birthday_member['email'] ?? '',
        '{birthday_member_phone}' => $birthday_member['phone_number'] ?? 'Not provided',
        '{birthday_member_image}' => $birthdayMemberImageHtml,
        '{birthday_member_photo_url}' => $birthdayMemberImagePath,
        '{member_image}' => $birthdayMemberImageHtml,
        '{member_image_url}' => $birthdayMemberImagePath,
        '{image_path}' => $birthdayMemberImagePath,
        '{profile_photo}' => $birthdayMemberImageHtml,
        '{church_logo}' => $churchLogo,
        '{birthday_member_birth_date}' => date('F j', strtotime($birthday_member['birth_date'] ?? 'now')),
        '{birthday_member_age}' => date('Y') - date('Y', strtotime($birthday_member['birth_date'] ?? 'now')),
        '{age}' => date('Y') - date('Y', strtotime($birthday_member['birth_date'] ?? 'now')),
        '{days_text}' => 'in 3 days',
        '{upcoming_birthday_formatted}' => date('l, F j, Y', strtotime('+3 days'))
    ];
}

// For birthday templates, we need to handle image placeholders specially
if (isset($birthday_member_replacements)) {
    // First apply birthday member replacements (including proper HTML img tags) to avoid URL generation
    $subject = str_replace(array_keys($birthday_member_replacements), array_values($birthday_member_replacements), $template['subject']);
    $content = str_replace(array_keys($birthday_member_replacements), array_values($birthday_member_replacements), $template['content']);

    // Then apply regular member placeholders for any remaining placeholders, but skip image processing to avoid URL conflicts
    $subject = replaceTemplatePlaceholders($subject, $sample_member, true); // Skip member image processing
    $content = replaceTemplatePlaceholders($content, $sample_member, true); // Skip member image processing
} else {
    // For non-birthday templates, first apply regular placeholders
    $subject = replaceTemplatePlaceholders($template['subject'], $sample_member, false);
    $content = replaceTemplatePlaceholders($template['content'], $sample_member, false);

    // Then create HTML img tag for member image and replace any remaining URL placeholders
    $memberImagePath = '../' . ltrim($sample_member['image_path'], '/');
    $memberImageHtml = '<img src="' . $memberImagePath . '" alt="' .
        htmlspecialchars($sample_member['full_name']) .
        '" style="width: 140px; height: 140px; border-radius: 50%; object-fit: cover; border: 5px solid #ff758c; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">';

    // Replace any full URLs that might have been generated with HTML img tags
    // Handle multiple possible URL formats that could be generated
    $possibleUrls = [
        'http://localhost/campaign/church/' . ltrim($sample_member['image_path'], '/'),
        'http://localhost/campaign/church/' . $sample_member['image_path'],
        'http://localhost/campaign/' . ltrim($sample_member['image_path'], '/'),
        'http://localhost/campaign/' . $sample_member['image_path'],
        '../' . ltrim($sample_member['image_path'], '/'),
        $sample_member['image_path']
    ];

    // Only replace if the URL hasn't already been replaced with an img tag
    foreach ($possibleUrls as $url) {
        if (strpos($content, $url) !== false && strpos($content, '<img') === false) {
            $content = str_replace($url, $memberImageHtml, $content);
            break; // Stop after first successful replacement
        }
    }
}

// Add church logo placeholder for all templates
$content = str_replace('{church_logo}', $churchLogo, $content);

// Create a complete HTML document for the iframe
$previewHtml = '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Email Preview</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px;
            line-height: 1.6;
        }
        img.church-logo {
            max-width: 200px;
            height: auto;
            margin-bottom: 20px;
        }
        img.member-image {
            max-width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            margin: 10px 0;
        }
        .preview-note {
            background: #f8f9fa;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
            font-size: 0.9em;
            color: #666;
        }
    </style>
</head>
<body>
' . $content . '
</body>
</html>';

// Close connection
$conn = null;

// If this is an embedded preview, just output the HTML content
if ($isEmbedded) {
    echo $previewHtml;
    exit;
}

// Set page variables
$page_title = 'Template Preview';
$page_header = 'Email Template Preview';
$page_description = 'Preview how your email templates will appear to recipients.';

// Include header
include 'includes/header.php';
?>

<div class="mb-4">
    <a href="email_templates.php" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left me-2"></i>Back to Templates
    </a>
</div>

<!-- Template Preview -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><?php echo htmlspecialchars($template['template_name']); ?></h5>
        <span class="badge <?php echo $template['is_birthday_template'] ? 'bg-warning' : 'bg-info'; ?>">
            <?php echo $template['is_birthday_template'] ? 'Birthday' : 'General'; ?>
        </span>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <h6>Subject:</h6>
            <p class="p-2 bg-light rounded"><?php echo htmlspecialchars($subject); ?></p>
        </div>
        <div class="mb-3">
            <h6>Template Preview:</h6>
            <div class="border" style="padding: 0;">
                <iframe srcdoc="<?php echo htmlspecialchars($previewHtml); ?>" style="width: 100%; height: 600px; border: none;"></iframe>
            </div>
        </div>
    </div>
</div>

<?php 
// Include footer if needed
// include 'includes/footer.php';
?> 