<?php
/**
 * Universal Organization Setup Wizard
 * Configures the platform for any organization type
 */

require_once '../config.php';
require_once '../includes/auth_check.php';
require_once '../ai/universal_prediction_engine.php';

// Initialize managers
$org_manager = new UniversalOrganizationManager($pdo);
$prediction_engine = new UniversalAttendancePredictionEngine($pdo);

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup_organization'])) {
    try {
        $org_id = $_POST['organization_id'];
        $org_name = $_POST['organization_name'];
        $org_type = $_POST['organization_type'];
        
        // Custom terminology
        $custom_terminology = [];
        if (isset($_POST['custom_terminology'])) {
            foreach ($_POST['custom_terminology'] as $key => $value) {
                if (!empty($value)) {
                    $custom_terminology[$key] = $value;
                }
            }
        }
        
        // Custom branding
        $custom_branding = [];
        if (isset($_POST['custom_branding'])) {
            foreach ($_POST['custom_branding'] as $key => $value) {
                if (!empty($value)) {
                    $custom_branding[$key] = $value;
                }
            }
        }
        
        // Prediction settings
        $prediction_settings = [];
        if (isset($_POST['prediction_settings'])) {
            foreach ($_POST['prediction_settings'] as $key => $value) {
                $prediction_settings[$key] = $value === 'on' ? true : $value;
            }
        }
        
        $custom_config = [
            'terminology' => $custom_terminology,
            'branding' => $custom_branding,
            'prediction' => $prediction_settings
        ];
        
        // Create organization
        $result = $org_manager->createOrganization($org_id, $org_name, $org_type, $custom_config);
        
        if ($result) {
            $_SESSION['organization_id'] = $org_id;
            $success_message = "Organization setup completed successfully!";
        } else {
            $error_message = "Failed to create organization configuration.";
        }
        
    } catch (Exception $e) {
        $error_message = "Setup error: " . $e->getMessage();
    }
}

// Get supported organization types
$supported_types = $prediction_engine->getSupportedOrganizationTypes();

$page_title = 'Universal Organization Setup';
include '../includes/header.php';
?>

<style>
.setup-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.setup-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.org-type-card {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.org-type-card:hover {
    border-color: #0066cc;
    background: #f8f9fa;
}

.org-type-card.selected {
    border-color: #0066cc;
    background: linear-gradient(135deg, #e8f4fd, #d1ecf1);
}

.terminology-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.color-picker-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.color-preview {
    width: 30px;
    height: 30px;
    border-radius: 5px;
    border: 1px solid #ddd;
}

.feature-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 5px;
    margin-bottom: 0.5rem;
}
</style>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="setup-header">
                <div class="text-center">
                    <h1><i class="bi bi-gear"></i> Universal Organization Setup</h1>
                    <p class="mb-0">Configure your platform for any organization type</p>
                    <small class="opacity-75">Corporate • Educational • Sports • Entertainment • Healthcare • Government • Non-Profit</small>
                </div>
            </div>

            <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="bi bi-check-circle"></i> <?php echo $success_message; ?>
                    <div class="mt-2">
                        <a href="../ai/universal_ai_dashboard.php" class="btn btn-success btn-sm">
                            <i class="bi bi-robot"></i> Open AI Dashboard
                        </a>
                        <a href="../multi_session_dashboard.php" class="btn btn-outline-success btn-sm">
                            <i class="bi bi-speedometer2"></i> Main Dashboard
                        </a>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <form method="POST" action="">
                <!-- Step 1: Basic Organization Info -->
                <div class="setup-card">
                    <h4><i class="bi bi-building"></i> Step 1: Organization Information</h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">Organization ID</label>
                            <input type="text" class="form-control" name="organization_id" 
                                   value="<?php echo $_SESSION['organization_id'] ?? 'org_' . uniqid(); ?>" required>
                            <small class="text-muted">Unique identifier for your organization</small>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Organization Name</label>
                            <input type="text" class="form-control" name="organization_name" 
                                   placeholder="e.g., Acme Corporation, Springfield University" required>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Organization Type Selection -->
                <div class="setup-card">
                    <h4><i class="bi bi-collection"></i> Step 2: Select Organization Type</h4>
                    <p class="text-muted">Choose the type that best describes your organization. This will configure appropriate terminology and features.</p>
                    
                    <div class="row">
                        <?php foreach ($supported_types as $type): ?>
                            <?php $config = $prediction_engine->getOrganizationConfig($type); ?>
                            <div class="col-md-6 col-lg-4">
                                <div class="org-type-card" onclick="selectOrgType('<?php echo $type; ?>')">
                                    <input type="radio" name="organization_type" value="<?php echo $type; ?>" 
                                           id="type_<?php echo $type; ?>" style="display: none;">
                                    <h6><?php echo $config['name']; ?></h6>
                                    <p class="text-muted mb-2">
                                        <?php echo ucfirst($config['terminology']['attendees']); ?> attend 
                                        <?php echo $config['terminology']['sessions']; ?> in 
                                        <?php echo $config['terminology']['events']; ?>
                                    </p>
                                    <small class="text-primary">
                                        Peak times: <?php echo implode(', ', $config['peak_times']); ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Step 3: Custom Terminology -->
                <div class="setup-card">
                    <h4><i class="bi bi-chat-text"></i> Step 3: Customize Terminology (Optional)</h4>
                    <p class="text-muted">Customize the terms used throughout the platform to match your organization's language.</p>
                    
                    <div class="terminology-grid" id="terminologyGrid">
                        <!-- Dynamic terminology inputs will be populated here -->
                    </div>
                </div>

                <!-- Step 4: Branding -->
                <div class="setup-card">
                    <h4><i class="bi bi-palette"></i> Step 4: Branding & Colors (Optional)</h4>
                    <p class="text-muted">Customize the visual appearance to match your organization's brand.</p>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">Primary Color</label>
                            <div class="color-picker-group">
                                <input type="color" class="form-control form-control-color" 
                                       name="custom_branding[primary_color]" value="#0066cc" id="primaryColor">
                                <div class="color-preview" id="primaryPreview"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Secondary Color</label>
                            <div class="color-picker-group">
                                <input type="color" class="form-control form-control-color" 
                                       name="custom_branding[secondary_color]" value="#f8f9fa" id="secondaryColor">
                                <div class="color-preview" id="secondaryPreview"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Accent Color</label>
                            <div class="color-picker-group">
                                <input type="color" class="form-control form-control-color" 
                                       name="custom_branding[accent_color]" value="#28a745" id="accentColor">
                                <div class="color-preview" id="accentPreview"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 5: AI Prediction Settings -->
                <div class="setup-card">
                    <h4><i class="bi bi-robot"></i> Step 5: AI Prediction Features</h4>
                    <p class="text-muted">Configure which AI features to enable for your organization type.</p>
                    
                    <div class="feature-toggle">
                        <div>
                            <strong>Weather Factor Analysis</strong>
                            <br><small class="text-muted">Include weather conditions in attendance predictions</small>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="prediction_settings[enable_weather_factor]" 
                                   id="weatherFactor" checked>
                        </div>
                    </div>
                    
                    <div class="feature-toggle">
                        <div>
                            <strong>Seasonal Adjustments</strong>
                            <br><small class="text-muted">Apply seasonal patterns to predictions</small>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="prediction_settings[enable_seasonal_adjustment]" 
                                   id="seasonalAdjustment" checked>
                        </div>
                    </div>
                    
                    <div class="feature-toggle">
                        <div>
                            <strong>Historical Weighting</strong>
                            <br><small class="text-muted">Give more weight to recent historical data</small>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="prediction_settings[enable_historical_weighting]" 
                                   id="historicalWeighting" checked>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">Prediction Horizon (Days)</label>
                            <input type="number" class="form-control" name="prediction_settings[prediction_horizon_days]" 
                                   value="30" min="1" max="365">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Minimum Confidence Threshold</label>
                            <input type="range" class="form-range" name="prediction_settings[min_confidence_threshold]" 
                                   value="0.7" min="0.5" max="0.95" step="0.05" id="confidenceThreshold">
                            <small class="text-muted">Value: <span id="confidenceValue">70%</span></small>
                        </div>
                    </div>
                </div>

                <!-- Submit -->
                <div class="setup-card text-center">
                    <h5>Ready to Configure Your Organization?</h5>
                    <p class="text-muted">This will set up your platform with the selected configuration.</p>
                    <button type="submit" name="setup_organization" class="btn btn-primary btn-lg">
                        <i class="bi bi-check-circle"></i> Complete Setup
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Organization type configurations
const orgConfigs = <?php echo json_encode(array_map(function($type) use ($prediction_engine) {
    return $prediction_engine->getOrganizationConfig($type);
}, $supported_types)); ?>;

function selectOrgType(type) {
    // Update radio button
    document.getElementById('type_' + type).checked = true;
    
    // Update visual selection
    document.querySelectorAll('.org-type-card').forEach(card => {
        card.classList.remove('selected');
    });
    event.currentTarget.classList.add('selected');
    
    // Update terminology inputs
    updateTerminologyInputs(type);
    
    // Update branding colors
    updateBrandingColors(type);
    
    // Update prediction settings
    updatePredictionSettings(type);
}

function updateTerminologyInputs(type) {
    const config = orgConfigs[type];
    const grid = document.getElementById('terminologyGrid');
    
    const terminologyKeys = ['attendee', 'attendees', 'session', 'sessions', 'event', 'events', 'organizer', 'check_in', 'venue'];
    
    grid.innerHTML = terminologyKeys.map(key => `
        <div>
            <label class="form-label">${key.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</label>
            <input type="text" class="form-control" name="custom_terminology[${key}]" 
                   placeholder="${config.terminology[key] || key}" 
                   value="${config.terminology[key] || ''}">
        </div>
    `).join('');
}

function updateBrandingColors(type) {
    const config = orgConfigs[type];
    
    document.getElementById('primaryColor').value = config.branding.primary_color;
    document.getElementById('secondaryColor').value = config.branding.secondary_color;
    document.getElementById('accentColor').value = config.branding.accent_color;
    
    updateColorPreviews();
}

function updatePredictionSettings(type) {
    const config = orgConfigs[type];
    
    // Update weather factor based on org type
    const weatherTypes = ['sports', 'entertainment', 'nonprofit'];
    document.getElementById('weatherFactor').checked = weatherTypes.includes(type);
}

function updateColorPreviews() {
    document.getElementById('primaryPreview').style.backgroundColor = document.getElementById('primaryColor').value;
    document.getElementById('secondaryPreview').style.backgroundColor = document.getElementById('secondaryColor').value;
    document.getElementById('accentPreview').style.backgroundColor = document.getElementById('accentColor').value;
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Set default organization type
    selectOrgType('corporate');
    
    // Color picker event listeners
    ['primaryColor', 'secondaryColor', 'accentColor'].forEach(id => {
        document.getElementById(id).addEventListener('change', updateColorPreviews);
    });
    
    // Confidence threshold slider
    document.getElementById('confidenceThreshold').addEventListener('input', function() {
        document.getElementById('confidenceValue').textContent = Math.round(this.value * 100) + '%';
    });
    
    updateColorPreviews();
});
</script>

<?php include '../includes/footer.php'; ?>
