<?php
/**
 * API endpoint to get public events for homepage
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Include configuration
    require_once '../config.php';
    
    // Get upcoming events (next 6 events) with combined member and guest RSVP counts
    // Only show published events (use new status system with fallback)
    $activeCondition = "e.is_active = 1"; // Default fallback
    try {
        // First try the new status column (preferred)
        $testStmt = $pdo->query("SELECT status FROM events LIMIT 1");
        $activeCondition = "e.status = 'published'";
    } catch (PDOException $e) {
        // Fallback to is_active column if status doesn't exist
        $activeCondition = "e.is_active = 1";
    }

    $stmt = $pdo->prepare("
        SELECT e.id, e.title, e.description, e.event_date, e.location, e.max_attendees, e.is_active,
               (
                   (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id) +
                   (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = e.id)
               ) as rsvp_count,
               (
                   (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'attending') +
                   (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = e.id AND erg.status = 'attending')
               ) as attending_count
        FROM events e
        WHERE $activeCondition
        AND e.event_date > NOW()
        ORDER BY e.event_date ASC
        LIMIT 6
    ");
    
    $stmt->execute();
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format events for frontend
    $formatted_events = [];
    foreach ($events as $event) {
        $formatted_events[] = [
            'id' => (int)$event['id'],
            'title' => $event['title'],
            'description' => $event['description'],
            'event_date' => $event['event_date'],
            'location' => $event['location'],
            'max_attendees' => $event['max_attendees'] ? (int)$event['max_attendees'] : null,
            'rsvp_count' => (int)$event['rsvp_count'],
            'attending_count' => (int)$event['attending_count'],
            'is_active' => (bool)$event['is_active']
        ];
    }
    
    $response = [
        'success' => true,
        'events' => $formatted_events,
        'total_count' => count($formatted_events)
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to load events',
        'message' => $e->getMessage()
    ]);
}
?>
