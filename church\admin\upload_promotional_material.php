<?php
/**
 * Promotional Material Upload Handler
 *
 * Handles AJAX uploads of promotional materials for events
 */

// Include AJAX session handler for proper session management
require_once 'includes/ajax-session-handler.php';

// Validate admin session
$sessionValidation = handleAjaxSessionValidation();

// Include configuration safely
$pdo = includeConfigForAjax();

// Debug logging
error_log("PROMOTIONAL UPLOAD - Session admin_id: " . ($_SESSION['admin_id'] ?? 'NOT SET'));
error_log("PROMOTIONAL UPLOAD - POST data: " . print_r($_POST, true));
error_log("PROMOTIONAL UPLOAD - FILES data: " . print_r($_FILES, true));

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    error_log("PROMOTIONAL UPLOAD ERROR - Invalid request method: " . $_SERVER['REQUEST_METHOD']);
    sendAjaxResponse(['success' => false, 'message' => 'Invalid request method']);
}

$event_id = filter_input(INPUT_POST, 'event_id', FILTER_VALIDATE_INT);
$is_header_banner = filter_input(INPUT_POST, 'is_header_banner', FILTER_VALIDATE_BOOLEAN);
$alt_text = filter_input(INPUT_POST, 'alt_text', FILTER_SANITIZE_STRING);

error_log("PROMOTIONAL UPLOAD - Event ID: " . ($event_id ?? 'NULL'));
error_log("PROMOTIONAL UPLOAD - Is header banner: " . ($is_header_banner ? 'true' : 'false'));

if (!$event_id) {
    error_log("PROMOTIONAL UPLOAD ERROR - Invalid event ID");
    sendAjaxResponse(['success' => false, 'message' => 'Invalid event ID']);
}

if (!isset($_FILES['promotional_file'])) {
    error_log("PROMOTIONAL UPLOAD ERROR - No promotional_file in FILES array");
    sendAjaxResponse(['success' => false, 'message' => 'No file uploaded - promotional_file not found']);
}

if ($_FILES['promotional_file']['error'] !== UPLOAD_ERR_OK) {
    $upload_error = $_FILES['promotional_file']['error'];
    error_log("PROMOTIONAL UPLOAD ERROR - Upload error code: $upload_error");
    sendAjaxResponse(['success' => false, 'message' => 'File upload error (code: ' . $upload_error . ')']);
}

try {
    $file = $_FILES['promotional_file'];
    $file_name = $file['name'];
    $file_tmp = $file['tmp_name'];
    $file_size = $file['size'];
    $file_type = $file['type'];
    $file_extension = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

    error_log("PROMOTIONAL UPLOAD - File details: name='$file_name', type='$file_type', extension='$file_extension', size=$file_size");

    // Validate file type
    $allowed_types = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf'
    ];
    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];

    if (!in_array($file_type, $allowed_types) || !in_array($file_extension, $allowed_extensions)) {
        error_log("PROMOTIONAL UPLOAD ERROR - Invalid file type: '$file_type' or extension: '$file_extension'");
        sendAjaxResponse(['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, GIF, and PDF files are allowed.']);
    }

    error_log("PROMOTIONAL UPLOAD - File type validation passed");

    // Validate file size (15MB max)
    $max_size = 15 * 1024 * 1024;
    if ($file_size > $max_size) {
        sendAjaxResponse(['success' => false, 'message' => 'File too large. Maximum size is 15MB.']);
    }

    // Create upload directories using dynamic paths
    $base_dir = admin_file_path('uploads/events/');
    $promotional_dir = $base_dir . 'promotional/';
    $thumbnails_dir = $base_dir . 'thumbnails/';

    error_log("PROMOTIONAL UPLOAD - Base dir: '$base_dir'");
    error_log("PROMOTIONAL UPLOAD - Promotional dir: '$promotional_dir'");
    error_log("PROMOTIONAL UPLOAD - Thumbnails dir: '$thumbnails_dir'");

    foreach ([$base_dir, $promotional_dir, $thumbnails_dir] as $dir) {
        if (!is_dir($dir)) {
            error_log("PROMOTIONAL UPLOAD - Creating directory: '$dir'");
            if (!mkdir($dir, 0755, true)) {
                error_log("PROMOTIONAL UPLOAD ERROR - Failed to create directory: '$dir'");
                throw new Exception("Failed to create upload directory: $dir");
            }
        } else {
            error_log("PROMOTIONAL UPLOAD - Directory exists: '$dir'");
        }
    }

    // Sanitize original filename
    $original_filename = pathinfo($file_name, PATHINFO_FILENAME);
    $safe_filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $original_filename);
    $safe_filename = preg_replace('/_+/', '_', $safe_filename); // Replace multiple underscores with single
    $safe_filename = trim($safe_filename, '_'); // Remove leading/trailing underscores

    // Ensure we have a valid filename
    if (empty($safe_filename)) {
        $safe_filename = 'file_' . time();
    }

    // Generate unique filename
    $unique_name = 'event_' . $event_id . '_' . time() . '_' . $safe_filename . '.' . $file_extension;

    // Log filename processing for debugging
    error_log("UPLOAD DEBUG - Original: '$file_name', Safe: '$safe_filename', Unique: '$unique_name'");
    
    // Determine file category and target directory
    $file_category = in_array($file_extension, ['jpg', 'jpeg', 'png', 'gif']) ? 'promotional' : 'document';
    $target_dir = ($file_category === 'promotional') ? $promotional_dir : $base_dir;
    $file_path = $target_dir . $unique_name;

    // Move uploaded file
    error_log("UPLOAD DEBUG - Attempting to move from '$file_tmp' to '$file_path'");

    // Check if source file exists
    if (!file_exists($file_tmp)) {
        error_log("UPLOAD ERROR - Temporary file not found: '$file_tmp'");
        sendAjaxResponse(['success' => false, 'message' => "Temporary file not found for $file_name"]);
    }

    // Check if target directory is writable
    if (!is_writable($target_dir)) {
        error_log("UPLOAD ERROR - Target directory not writable: '$target_dir'");
        sendAjaxResponse(['success' => false, 'message' => "Target directory is not writable: $target_dir"]);
    }

    if (!move_uploaded_file($file_tmp, $file_path)) {
        $error = error_get_last();
        $error_msg = $error ? $error['message'] : 'Unknown error';
        error_log("UPLOAD ERROR - Failed to move file: $error_msg");
        sendAjaxResponse(['success' => false, 'message' => "Failed to save $file_name: $error_msg"]);
    }

    error_log("UPLOAD SUCCESS - File moved to: '$file_path'");

    // Generate thumbnail for images
    $thumbnail_path = null;
    if ($file_category === 'promotional') {
        try {
            $thumbnail_path = generateThumbnail($file_path, $thumbnails_dir, $unique_name);
        } catch (Exception $e) {
            error_log("Thumbnail generation error: " . $e->getMessage());
            error_log("Error file: " . $e->getFile() . " line: " . $e->getLine());
            // Continue without thumbnail if there's an error
        }
    }

    // If this is set as header banner, unset any existing header banner for this event
    if ($is_header_banner) {
        $stmt = $pdo->prepare("UPDATE event_files SET is_header_banner = 0 WHERE event_id = ?");
        $stmt->execute([$event_id]);
    }

    // Save to database
    $stmt = $pdo->prepare("
        INSERT INTO event_files (event_id, file_name, file_path, file_type, file_size,
                               uploaded_by, file_category, is_header_banner, alt_text, display_order)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    // Get next display order
    $order_stmt = $pdo->prepare("SELECT COALESCE(MAX(display_order), 0) + 1 FROM event_files WHERE event_id = ?");
    $order_stmt->execute([$event_id]);
    $display_order = (int)$order_stmt->fetchColumn();
    
    $stmt->execute([
        $event_id,
        $file_name,
        $file_path,
        $file_type,
        $file_size,
        $_SESSION['admin_id'],
        $file_category,
        $is_header_banner ? 1 : 0,
        $alt_text,
        $display_order
    ]);

    $file_id = $pdo->lastInsertId();

    sendAjaxResponse([
        'success' => true,
        'message' => 'File uploaded successfully',
        'file' => [
            'id' => $file_id,
            'name' => $file_name,
            'category' => $file_category,
            'is_header_banner' => $is_header_banner,
            'thumbnail' => $thumbnail_path,
            'file_path' => $file_path
        ]
    ]);

} catch (Exception $e) {
    // Log the error for debugging with more details
    error_log("Promotional material upload error: " . $e->getMessage());
    error_log("Error file: " . $e->getFile() . " line: " . $e->getLine());
    error_log("Stack trace: " . $e->getTraceAsString());
    error_log("Upload file: " . ($file_name ?? 'unknown') . " | Event ID: " . ($event_id ?? 'unknown'));

    // Clean up uploaded file if there was an error
    if (isset($file_path) && file_exists($file_path)) {
        unlink($file_path);
    }

    sendAjaxResponse(['success' => false, 'message' => 'Upload failed: ' . $e->getMessage()]);
}

// Function to generate thumbnails for images
function generateThumbnail($source_path, $thumbnail_dir, $filename) {
    try {
        $thumbnail_path = $thumbnail_dir . 'thumb_' . $filename;

        // Check if source file exists
        if (!file_exists($source_path)) {
            error_log("Thumbnail error: Source file does not exist: $source_path");
            return null;
        }

        // Get image info
        $image_info = @getimagesize($source_path);
        if (!$image_info) {
            error_log("Thumbnail error: Could not get image size for: $source_path");
            return null;
        }

        $width = $image_info[0];
        $height = $image_info[1];
        $type = $image_info[2];
    
    // Calculate thumbnail dimensions (max 300x200)
    $thumb_width = 300;
    $thumb_height = 200;
    
    $ratio = min($thumb_width / $width, $thumb_height / $height);
    $new_width = (int)round($width * $ratio);
    $new_height = (int)round($height * $ratio);
    
    // Create source image
    switch ($type) {
        case IMAGETYPE_JPEG:
            $source = imagecreatefromjpeg($source_path);
            break;
        case IMAGETYPE_PNG:
            $source = imagecreatefrompng($source_path);
            break;
        case IMAGETYPE_GIF:
            $source = imagecreatefromgif($source_path);
            break;
        default:
            return null;
    }
    
    if (!$source) {
        return null;
    }
    
    // Create thumbnail
    $thumbnail = imagecreatetruecolor($new_width, $new_height);
    
    // Preserve transparency for PNG and GIF
    if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
        imagealphablending($thumbnail, false);
        imagesavealpha($thumbnail, true);
        $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
        imagefilledrectangle($thumbnail, 0, 0, $new_width, $new_height, $transparent);
    }
    
    imagecopyresampled($thumbnail, $source, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
    
    // Save thumbnail
    $success = false;
    switch ($type) {
        case IMAGETYPE_JPEG:
            $success = imagejpeg($thumbnail, $thumbnail_path, 85);
            break;
        case IMAGETYPE_PNG:
            $success = imagepng($thumbnail, $thumbnail_path);
            break;
        case IMAGETYPE_GIF:
            $success = imagegif($thumbnail, $thumbnail_path);
            break;
    }
    
    imagedestroy($source);
    imagedestroy($thumbnail);

    return $success ? $thumbnail_path : null;

    } catch (Exception $e) {
        error_log("Thumbnail generation exception: " . $e->getMessage());
        return null;
    }
}
?>
