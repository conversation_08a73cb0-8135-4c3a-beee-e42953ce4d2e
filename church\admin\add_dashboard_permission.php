<?php
// <PERSON><PERSON><PERSON> to add dashboard.view permission to admin67
require_once '../config.php';

echo "<h2>Add Dashboard Permission to Admin67</h2>";

try {
    // Get admin67 user ID
    $stmt = $pdo->prepare("SELECT id FROM admins WHERE username = 'admin67'");
    $stmt->execute();
    $admin67 = $stmt->fetch();
    
    if (!$admin67) {
        echo "<p style='color: red;'>Error: admin67 user not found!</p>";
        exit;
    }
    
    $user_id = $admin67['id'];
    echo "<p>Found admin67 with User ID: " . $user_id . "</p>";
    
    // Get dashboard.view permission ID
    $stmt = $pdo->prepare("SELECT id FROM granular_permissions WHERE permission_key = 'dashboard.view'");
    $stmt->execute();
    $permission = $stmt->fetch();
    
    if (!$permission) {
        echo "<p style='color: red;'>Error: dashboard.view permission not found!</p>";
        exit;
    }
    
    $permission_id = $permission['id'];
    echo "<p>Found dashboard.view permission with ID: " . $permission_id . "</p>";
    
    // Check if permission already exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM user_individual_permissions 
        WHERE user_id = ? AND permission_id = ?
    ");
    $stmt->execute([$user_id, $permission_id]);
    $exists = $stmt->fetchColumn();
    
    if ($exists > 0) {
        echo "<p style='color: orange;'>Permission already exists for admin67!</p>";
        
        // Update to make sure it's active
        $stmt = $pdo->prepare("
            UPDATE user_individual_permissions
            SET is_active = 1
            WHERE user_id = ? AND permission_id = ?
        ");
        $stmt->execute([$user_id, $permission_id]);
        echo "<p style='color: green;'>✓ Permission activated for admin67!</p>";
    } else {
        // Add the permission (using admin user ID 4 as granted_by)
        $stmt = $pdo->prepare("
            INSERT INTO user_individual_permissions (user_id, permission_id, is_active, granted_by)
            VALUES (?, ?, 1, 4)
        ");
        $stmt->execute([$user_id, $permission_id]);
        echo "<p style='color: green;'>✓ Dashboard.view permission added to admin67!</p>";
    }
    
    // Also add requests.view permission for testing
    $stmt = $pdo->prepare("SELECT id FROM granular_permissions WHERE permission_key = 'requests.view'");
    $stmt->execute();
    $requests_permission = $stmt->fetch();
    
    if ($requests_permission) {
        $requests_permission_id = $requests_permission['id'];
        
        // Check if requests permission already exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM user_individual_permissions 
            WHERE user_id = ? AND permission_id = ?
        ");
        $stmt->execute([$user_id, $requests_permission_id]);
        $requests_exists = $stmt->fetchColumn();
        
        if ($requests_exists == 0) {
            // Add the requests permission (using admin user ID 4 as granted_by)
            $stmt = $pdo->prepare("
                INSERT INTO user_individual_permissions (user_id, permission_id, is_active, granted_by)
                VALUES (?, ?, 1, 4)
            ");
            $stmt->execute([$user_id, $requests_permission_id]);
            echo "<p style='color: green;'>✓ Requests.view permission also added to admin67!</p>";
        } else {
            echo "<p style='color: orange;'>Requests.view permission already exists for admin67!</p>";
        }
    }
    
    // Show current permissions for admin67
    echo "<h3>Current Permissions for admin67:</h3>";
    $stmt = $pdo->prepare("
        SELECT gp.permission_key, gp.permission_name
        FROM user_individual_permissions uip
        JOIN granular_permissions gp ON uip.permission_id = gp.id
        WHERE uip.user_id = ? 
        AND uip.is_active = 1 
        AND gp.is_active = 1
        ORDER BY gp.permission_key
    ");
    $stmt->execute([$user_id]);
    $permissions = $stmt->fetchAll();
    
    echo "<p>Total permissions: " . count($permissions) . "</p>";
    echo "<ul>";
    foreach ($permissions as $perm) {
        echo "<li><strong>" . htmlspecialchars($perm['permission_key']) . "</strong> - " . htmlspecialchars($perm['permission_name']) . "</li>";
    }
    echo "</ul>";
    
    echo "<p><a href='debug_login.php'>← Back to Debug Page</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h2, h3 { color: #333; }
    p { margin: 10px 0; }
    ul { margin: 10px 0; }
    li { margin: 5px 0; }
</style>
