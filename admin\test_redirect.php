<?php
/**
 * Test Redirection Logic
 */

// Start session
session_start();

// Simulate admin login
$_SESSION['admin_id'] = 4; // Admin user ID from debug
$_SESSION['admin_name'] = 'Administrator';
$_SESSION['admin_username'] = 'admin';

echo "<h1>Testing Admin Redirection</h1>";
echo "<p>Session admin_id: " . ($_SESSION['admin_id'] ?? 'Not set') . "</p>";

try {
    // Include required files
    require_once '../church/config.php';
    require_once '../church/admin/includes/rbac_access_control.php';
    require_once '../church/admin/includes/route_protection.php';
    
    echo "<h2>RBAC Test</h2>";
    $rbac = new RBACAccessControl($pdo, $_SESSION['admin_id']);
    
    echo "<p><strong>Primary Role:</strong> " . ($rbac->getPrimaryRole() ?: 'None') . "</p>";
    echo "<p><strong>Has super_admin role:</strong> " . ($rbac->hasRole('super_admin') ? 'Yes' : 'No') . "</p>";
    echo "<p><strong>Default Dashboard:</strong> " . $rbac->getDefaultDashboard() . "</p>";
    
    echo "<h2>Route Protection Test</h2>";
    $dashboard = getUserDefaultDashboard();
    echo "<p><strong>getUserDefaultDashboard() returns:</strong> " . $dashboard . "</p>";
    
    // Check if dashboard file exists
    $dashboard_path = '../church/admin/' . $dashboard;
    echo "<p><strong>Dashboard path:</strong> " . $dashboard_path . "</p>";
    echo "<p><strong>File exists:</strong> " . (file_exists($dashboard_path) ? 'Yes' : 'No') . "</p>";
    
    echo "<h2>Manual Redirection Test</h2>";
    echo "<p><a href='../church/admin/" . $dashboard . "'>Click here to go to dashboard</a></p>";
    
    echo "<h2>Sidebar Test</h2>";
    $sidebar_items = $rbac->getSidebarItems();
    echo "<p><strong>Sidebar items count:</strong> " . count($sidebar_items) . "</p>";
    
    if (!empty($sidebar_items)) {
        echo "<h3>Sidebar Structure:</h3>";
        echo "<pre>" . print_r($sidebar_items, true) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
