<?php
/**
 * Debug Admin Roles - Check all admins and their role assignments
 * This script shows why some admins might not appear in session assignment dropdown
 */

// Include the configuration file
require_once '../config.php';

echo "<h2>Debug: Admin Users and Role Assignments</h2>\n";

try {
    // 1. Show ALL admin users in the database
    echo "<h3>1. All Admin Users in Database</h3>\n";
    $stmt = $pdo->query("SELECT id, username, email, created_at FROM admins ORDER BY id");
    $all_admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>\n";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th style='padding: 8px;'>ID</th>";
    echo "<th style='padding: 8px;'>Username</th>";
    echo "<th style='padding: 8px;'>Email</th>";
    echo "<th style='padding: 8px;'>Created</th>";
    echo "<th style='padding: 8px;'>Has Role?</th>";
    echo "</tr>\n";
    
    foreach ($all_admins as $admin) {
        // Check if this admin has any role assignments
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM user_role_assignments ura 
            WHERE ura.user_id = ? AND ura.is_active = 1
        ");
        $stmt->execute([$admin['id']]);
        $has_role = $stmt->fetchColumn() > 0;
        
        $role_status = $has_role ? '✅ Yes' : '❌ No';
        $row_color = $has_role ? '' : 'background-color: #ffe6e6;';
        
        echo "<tr style='$row_color'>";
        echo "<td style='padding: 8px;'>" . $admin['id'] . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($admin['username']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($admin['email']) . "</td>";
        echo "<td style='padding: 8px;'>" . $admin['created_at'] . "</td>";
        echo "<td style='padding: 8px;'>" . $role_status . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<p><strong>Total Admins:</strong> " . count($all_admins) . "</p>\n";
    
    // 2. Show current role assignments
    echo "<h3>2. Current Role Assignments</h3>\n";
    $stmt = $pdo->query("
        SELECT a.id, a.username, a.email, ur.role_name, ur.role_display_name, ur.hierarchy_level,
               ura.assigned_at, ura.expires_at, ura.is_active
        FROM admins a
        LEFT JOIN user_role_assignments ura ON a.id = ura.user_id
        LEFT JOIN user_roles ur ON ura.role_id = ur.id
        ORDER BY a.id, ur.hierarchy_level
    ");
    $assignments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>\n";
    echo "<tr style='background-color: #f0f0f0;'>";
    echo "<th style='padding: 8px;'>Admin ID</th>";
    echo "<th style='padding: 8px;'>Username</th>";
    echo "<th style='padding: 8px;'>Email</th>";
    echo "<th style='padding: 8px;'>Role</th>";
    echo "<th style='padding: 8px;'>Active</th>";
    echo "<th style='padding: 8px;'>Assigned</th>";
    echo "</tr>\n";
    
    foreach ($assignments as $assignment) {
        $role_display = $assignment['role_display_name'] ?? 'No Role Assigned';
        $is_active = $assignment['is_active'] ? '✅' : '❌';
        $assigned_date = $assignment['assigned_at'] ?? 'N/A';
        
        $row_color = $assignment['role_name'] ? '' : 'background-color: #ffe6e6;';
        
        echo "<tr style='$row_color'>";
        echo "<td style='padding: 8px;'>" . $assignment['id'] . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($assignment['username']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($assignment['email']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($role_display) . "</td>";
        echo "<td style='padding: 8px;'>" . $is_active . "</td>";
        echo "<td style='padding: 8px;'>" . $assigned_date . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // 3. Show the current session moderator query results
    echo "<h3>3. Current Session Moderator Query Results</h3>\n";
    echo "<p><em>This is what the dropdown currently shows:</em></p>\n";
    $stmt = $pdo->query("
        SELECT DISTINCT a.id, a.username, a.email, ur.role_display_name
        FROM admins a
        JOIN user_role_assignments ura ON a.id = ura.user_id
        JOIN user_roles ur ON ura.role_id = ur.id
        WHERE ur.role_name IN ('session_moderator', 'event_coordinator', 'limited_admin', 'super_admin')
        AND ura.is_active = 1
        ORDER BY ur.hierarchy_level ASC, a.username
    ");
    $current_moderators = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>\n";
    echo "<tr style='background-color: #e6f3ff;'>";
    echo "<th style='padding: 8px;'>ID</th>";
    echo "<th style='padding: 8px;'>Username</th>";
    echo "<th style='padding: 8px;'>Email</th>";
    echo "<th style='padding: 8px;'>Role</th>";
    echo "</tr>\n";
    
    foreach ($current_moderators as $moderator) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . $moderator['id'] . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($moderator['username']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($moderator['email']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($moderator['role_display_name']) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<p><strong>Moderators shown in dropdown:</strong> " . count($current_moderators) . "</p>\n";
    
    // 4. Identify missing admins
    echo "<h3>4. Missing Admins Analysis</h3>\n";
    $assigned_admin_ids = array_column($current_moderators, 'id');
    $all_admin_ids = array_column($all_admins, 'id');
    $missing_admin_ids = array_diff($all_admin_ids, $assigned_admin_ids);
    
    if (!empty($missing_admin_ids)) {
        echo "<p style='color: red;'><strong>Admins missing from dropdown:</strong></p>\n";
        echo "<ul>\n";
        foreach ($missing_admin_ids as $missing_id) {
            $missing_admin = array_filter($all_admins, function($admin) use ($missing_id) {
                return $admin['id'] == $missing_id;
            });
            $missing_admin = reset($missing_admin);
            echo "<li>ID: " . $missing_id . " - " . htmlspecialchars($missing_admin['username']) . " (" . htmlspecialchars($missing_admin['email']) . ")</li>\n";
        }
        echo "</ul>\n";
        
        echo "<p><strong>Reason:</strong> These admins don't have any role assigned in the RBAC system.</p>\n";
        echo "<p><strong>Solution:</strong> Assign roles to these admins using the 'Assign User Roles' section above.</p>\n";
    } else {
        echo "<p style='color: green;'>✅ All admins have roles assigned and appear in the dropdown.</p>\n";
    }
    
    // 5. Quick fix buttons
    echo "<h3>5. Quick Fix Actions</h3>\n";
    if (!empty($missing_admin_ids)) {
        echo "<p>You can quickly assign roles to missing admins:</p>\n";
        foreach ($missing_admin_ids as $missing_id) {
            $missing_admin = array_filter($all_admins, function($admin) use ($missing_id) {
                return $admin['id'] == $missing_id;
            });
            $missing_admin = reset($missing_admin);
            
            echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; background: #f9f9f9;'>\n";
            echo "<strong>" . htmlspecialchars($missing_admin['username']) . "</strong><br>\n";
            echo "<form method='POST' action='setup_rbac_system.php' style='display: inline-block; margin: 5px;'>\n";
            echo "<input type='hidden' name='action' value='assign_role'>\n";
            echo "<input type='hidden' name='user_id' value='" . $missing_id . "'>\n";
            echo "<select name='role_id' required style='margin-right: 5px;'>\n";
            echo "<option value=''>Choose role...</option>\n";
            
            // Get available roles
            $stmt = $pdo->query("SELECT id, role_display_name, hierarchy_level FROM user_roles ORDER BY hierarchy_level");
            $available_roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($available_roles as $role) {
                echo "<option value='" . $role['id'] . "'>" . htmlspecialchars($role['role_display_name']) . " (Level " . $role['hierarchy_level'] . ")</option>\n";
            }
            echo "</select>\n";
            echo "<button type='submit' style='background: #007cba; color: white; padding: 5px 10px; border: none; border-radius: 3px;'>Assign Role</button>\n";
            echo "</form>\n";
            echo "</div>\n";
        }
    }
    
    echo "<p><a href='setup_rbac_system.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Go to RBAC Management</a></p>\n";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</h3>\n";
}
?>
