<?php
/**
 * Email Scheduler - Admin Interface
 * 
 * This file provides the admin interface for creating and managing scheduled email campaigns.
 * It allows admins to create new schedules, view existing ones, and manage their status.
 */

// Include necessary files
require_once '../config.php';
require_once '../includes/auth_check.php';
require_once '../includes/email_functions.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Force a truly fresh reload if coming back from a delete operation
if (isset($_GET['noCache'])) {
    // Send cache-busting headers
    header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
    header("Pragma: no-cache");
    header("Expires: 0");
}

// One-time fix to ensure template_id is set correctly in email_schedules table
try {
    $pdo->exec("UPDATE email_schedules es 
              JOIN email_schedule_settings ess ON es.id = ess.schedule_id 
              SET es.template_id = ess.template_id 
              WHERE es.template_id = 0");
} catch (Exception $e) {
    // Silently ignore errors from this fix
}

// Process form submissions
$message = '';
$messageType = '';

// Check for session messages (from redirects)
if (isset($_SESSION['message']) && !empty($_SESSION['message'])) {
    $message = $_SESSION['message'];
    $messageType = $_SESSION['message_type'];
    
    // Clear the session messages
    unset($_SESSION['message']);
    unset($_SESSION['message_type']);
}

// Handle schedule creation/update
if (isset($_POST['action']) && $_POST['action'] == 'create_schedule') {
    try {
        // Validate inputs
        $name = trim($_POST['name']);
        $schedule_type = $_POST['schedule_type'];
        $start_datetime = $_POST['start_date'] . ' ' . $_POST['start_time'];
        $end_datetime = !empty($_POST['end_date']) && !empty($_POST['end_time']) 
            ? $_POST['end_date'] . ' ' . $_POST['end_time'] 
            : null;
        $emails_per_hour = max(1, min(1000, intval($_POST['emails_per_hour'])));
        $min_interval_seconds = max(1, min(60, intval($_POST['min_interval_seconds'])));
        $template_id = intval($_POST['template_id']);
        $custom_subject = !empty($_POST['custom_subject']) ? trim($_POST['custom_subject']) : null;
        $custom_content = !empty($_POST['custom_content']) ? trim($_POST['custom_content']) : null;
        $track_opens = isset($_POST['track_opens']) ? 1 : 0;
        $track_clicks = isset($_POST['track_clicks']) ? 1 : 0;
        
        // Get admin ID
        $admin_id = $_SESSION['admin_id'];
        
        // Begin transaction
        $pdo->beginTransaction();
        
        // Insert into email_schedules
        $stmt = $pdo->prepare("
            INSERT INTO email_schedules 
            (name, description, frequency, day_of_week, day_of_month, hour, minute, 
            status, custom_data, next_run, created_by, template_id) 
            VALUES (?, ?, ?, ?, ?, ?, ?, 'active', ?, ?, ?, ?)
        ");
        
        // Convert schedule type to frequency and other parameters
        $frequency = 'daily'; // Default
        $day_of_week = 0;
        $day_of_month = 1;
        $hour = (int)date('H', strtotime($start_datetime));
        $minute = (int)date('i', strtotime($start_datetime));
        $next_run = $start_datetime;
        
        // Create custom data JSON to store additional schedule information
        $custom_data = json_encode([
            'schedule_type' => $schedule_type,
            'start_datetime' => $start_datetime,
            'end_datetime' => $end_datetime,
            'emails_per_hour' => $emails_per_hour,
            'min_interval_seconds' => $min_interval_seconds
        ]);
        
        $stmt->execute([
            $name, 
            "Email campaign: $name", 
            $frequency,
            $day_of_week,
            $day_of_month,
            $hour,
            $minute,
            $custom_data,
            $next_run,
            $admin_id,
            $template_id
        ]);
        
        $schedule_id = $pdo->lastInsertId();
        
        // Insert into email_schedule_settings
        $stmt = $pdo->prepare("
            INSERT INTO email_schedule_settings 
            (schedule_id, template_id, custom_subject, custom_content, track_opens, track_clicks) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $schedule_id, $template_id, $custom_subject, $custom_content, 
            $track_opens, $track_clicks
        ]);
        
        // Process recipients
        if (!empty($_POST['recipient_groups'])) {
            $stmt = $pdo->prepare("
                INSERT INTO email_schedule_recipients 
                (schedule_id, recipient_type, recipient_id, status) 
                VALUES (?, ?, ?, 'pending')
            ");
            
            foreach ($_POST['recipient_groups'] as $group) {
                list($type, $id) = explode('_', $group);
                $stmt->execute([$schedule_id, $type, $id]);
            }
        }
        
        // Log the creation
        $stmt = $pdo->prepare("
            INSERT INTO email_schedule_logs 
            (schedule_id, log_type, message) 
            VALUES (?, 'info', ?)
        ");
        $stmt->execute([
            $schedule_id, 
            "Schedule created by Admin ID: $admin_id"
        ]);
        
        // Commit transaction
        $pdo->commit();
        
        $message = "Email schedule created successfully!";
        $messageType = "success";
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $pdo->rollBack();
        $message = "Error creating schedule: " . $e->getMessage();
        $messageType = "danger";
    }
}

// Handle schedule status updates
if (isset($_POST['action']) && $_POST['action'] == 'update_status') {
    try {
        $schedule_id = intval($_POST['schedule_id']);
        $new_status = $_POST['new_status'];
        $admin_id = $_SESSION['admin_id'];
        
        // Validate status
        $valid_statuses = ['active', 'paused', 'completed', 'failed', 'cancelled'];
        if (!in_array($new_status, $valid_statuses)) {
            throw new Exception("Invalid status");
        }
        
        // Special handling for cancelled status
        if ($new_status === 'cancelled') {
            // Get current status
            $stmt = $pdo->prepare("SELECT status FROM email_schedules WHERE id = ?");
            $stmt->execute([$schedule_id]);
            $currentStatus = $stmt->fetch(PDO::FETCH_COLUMN);
            
            // If currently active, we need to ensure any in-progress sending is stopped
            if ($currentStatus === 'active') {
                // Update any pending recipients to cancelled
                $stmt = $pdo->prepare("
                    UPDATE email_schedule_recipients 
                    SET status = 'cancelled', updated_at = NOW(), error_message = 'Cancelled by admin'
                    WHERE schedule_id = ? AND status = 'pending'
                ");
                $stmt->execute([$schedule_id]);
                
                // Log the cancellation
                $logDir = __DIR__ . '/../logs';
                if (!is_dir($logDir)) {
                    mkdir($logDir, 0755, true);
                }
                $logFile = $logDir . '/cancel_sending_' . $schedule_id . '_' . date('Ymd_His') . '.log';
                $logEntry = '[' . date('Y-m-d H:i:s') . '] Email sending cancelled by Admin ID: ' . $admin_id . PHP_EOL;
                file_put_contents($logFile, $logEntry, FILE_APPEND);
            }
        }
        
        // Update status
        $stmt = $pdo->prepare("
            UPDATE email_schedules 
            SET status = ?, updated_at = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$new_status, $schedule_id]);
        
        // Log the status change
        $stmt = $pdo->prepare("
            INSERT INTO email_schedule_logs 
            (schedule_id, log_type, message) 
            VALUES (?, 'info', ?)
        ");
        $stmt->execute([
            $schedule_id, 
            "Status changed to '$new_status' by Admin ID: $admin_id"
        ]);
        
        if ($new_status === 'cancelled') {
            $message = "Email campaign cancelled successfully!";
        } else {
            $message = "Schedule status updated successfully!";
        }
        $messageType = "success";
        
    } catch (Exception $e) {
        $message = "Error updating status: " . $e->getMessage();
        $messageType = "danger";
    }
}

// Handle "Send Now" action
if (isset($_POST['action']) && $_POST['action'] == 'send_now') {
    try {
        $schedule_id = intval($_POST['schedule_id']);
        $admin_id = $_SESSION['admin_id'];
        
        // First, check if the schedule exists and get its current status
        $stmt = $pdo->prepare("SELECT id, status FROM email_schedules WHERE id = ?");
        $stmt->execute([$schedule_id]);
        $schedule = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$schedule) {
            throw new Exception("Schedule not found");
        }
        
        // Update the schedule to be active and set next_run to current time
        $stmt = $pdo->prepare("
            UPDATE email_schedules 
            SET status = 'active', next_run = NOW(), updated_at = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$schedule_id]);
        
        // Log the immediate send action
        $stmt = $pdo->prepare("
            INSERT INTO email_schedule_logs 
            (schedule_id, log_type, message) 
            VALUES (?, 'info', ?)
        ");
        $stmt->execute([
            $schedule_id, 
            "Immediate send initiated by Admin ID: $admin_id"
        ]);
        
        // Make sure we have recipients to send to
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM email_schedule_recipients 
            WHERE schedule_id = ? AND status = 'pending'
        ");
        $stmt->execute([$schedule_id]);
        $recipientCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        if ($recipientCount == 0) {
            // Check for recipients in other statuses (like 'failed')
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count 
                FROM email_schedule_recipients 
                WHERE schedule_id = ? AND status = 'failed'
            ");
            $stmt->execute([$schedule_id]);
            $failedCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            if ($failedCount > 0) {
                // Reset failed recipients to try again
                $stmt = $pdo->prepare("
                    UPDATE email_schedule_recipients 
                    SET status = 'pending', error_message = NULL 
                    WHERE schedule_id = ? AND status = 'failed'
                ");
                $stmt->execute([$schedule_id]);
                
                // Log the reset action
                $logDir = __DIR__ . '/../logs';
                if (!is_dir($logDir)) {
                    mkdir($logDir, 0755, true);
                }
                $logFile = $logDir . '/send_action_' . $schedule_id . '_' . date('Ymd_His') . '.log';
                $logEntry = '[' . date('Y-m-d H:i:s') . '] Reset ' . $failedCount . ' failed recipients to pending status.' . PHP_EOL;
                file_put_contents($logFile, $logEntry, FILE_APPEND);
                
                // Now we should have recipients
                $recipientCount = $failedCount;
            } else {
                // Check if there are any recipients at all
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) as count 
                    FROM email_schedule_recipients 
                    WHERE schedule_id = ?
                ");
                $stmt->execute([$schedule_id]);
                $totalCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                
                if ($totalCount == 0) {
                    throw new Exception("No recipients found for this schedule. Please add recipients first.");
                } else {
                    throw new Exception("No pending recipients found for this schedule. All emails have been sent or cancelled.");
                }
            }
        }
        
        // Instead of executing a separate script, process the emails directly
        // Get the schedule details
        $stmt = $pdo->prepare("
            SELECT 
                es.id, es.name, es.status, es.custom_data, es.next_run,
                ess.custom_subject, ess.custom_content, 
                ess.track_opens, ess.track_clicks,
                et.subject as template_subject, 
                et.content as template_content
            FROM 
                email_schedules es
            JOIN 
                email_schedule_settings ess ON es.id = ess.schedule_id
            JOIN 
                email_templates et ON ess.template_id = et.id
            WHERE 
                es.id = ?
        ");
        
        // Convert schedule type to frequency and other parameters
        $stmt->execute([$schedule_id]);
        $scheduleData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$scheduleData) {
            throw new Exception("Could not retrieve schedule data");
        }
        
        // Make sure logs directory exists
        $logDir = __DIR__ . '/../logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        // Initialize logging
        $logFile = $logDir . '/immediate_send_' . $schedule_id . '_' . date('Ymd_His') . '.log';
        
        // Include necessary files for email processing
        require_once __DIR__ . '/../includes/email_functions.php';
        
        // Get pending recipients (limit to 50 for immediate processing)
        $stmt = $pdo->prepare("
            SELECT 
                esr.*,
                CASE 
                    WHEN esr.recipient_type = 'member' THEN m.full_name
                    WHEN esr.recipient_type = 'contact' THEN c.name
                    WHEN esr.recipient_type = 'group' THEN NULL
                    ELSE NULL
                END as recipient_name,
                CASE 
                    WHEN esr.recipient_type = 'member' THEN m.email
                    WHEN esr.recipient_type = 'contact' THEN c.email
                    WHEN esr.recipient_type = 'group' THEN NULL
                    ELSE NULL
                END as recipient_email
            FROM 
                email_schedule_recipients esr
            LEFT JOIN 
                members m ON esr.recipient_type = 'member' AND esr.recipient_id = m.id
            LEFT JOIN 
                contacts c ON esr.recipient_type = 'contact' AND esr.recipient_id = c.id
            WHERE 
                esr.schedule_id = ? 
                AND esr.status = 'pending'
                AND (
                    (esr.recipient_type = 'member' AND m.email IS NOT NULL) OR
                    (esr.recipient_type = 'contact' AND c.email IS NOT NULL) OR
                    esr.recipient_type = 'group'
                )
            ORDER BY 
                esr.id ASC
            LIMIT 50
        ");
        $stmt->execute([$schedule_id]);
        $recipients = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Handle group recipients
        $groupRecipients = [];
        foreach ($recipients as $key => $recipient) {
            if ($recipient['recipient_type'] === 'group') {
                // Get members of this group
                $groupId = $recipient['recipient_id'];
                $stmt = $pdo->prepare("
                    SELECT 
                        c.id, c.name, c.email
                    FROM 
                        contact_group_members cgm
                    JOIN 
                        contacts c ON cgm.contact_id = c.id
                    WHERE 
                        cgm.group_id = ?
                        AND c.email IS NOT NULL
                ");
                $stmt->execute([$groupId]);
                $groupMembers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // Add each group member as a recipient
                foreach ($groupMembers as $member) {
                    $groupRecipients[] = [
                        'id' => $recipient['id'],
                        'schedule_id' => $recipient['schedule_id'],
                        'recipient_type' => 'member',
                        'recipient_id' => $member['id'],
                        'status' => 'pending',
                        'recipient_name' => $member['name'],
                        'recipient_email' => $member['email'],
                        'is_group_member' => true,
                        'group_id' => $groupId
                    ];
                }
                
                // Remove the group recipient from the list
                unset($recipients[$key]);
            }
        }
        
        // Merge all recipients
        $allRecipients = array_merge($recipients, $groupRecipients);
        
        // Process each recipient
        $successCount = 0;
        $errorCount = 0;
        $errorMessages = [];
        
        foreach ($allRecipients as $recipient) {
            // Skip if no email
            if (empty($recipient['recipient_email'])) {
                continue;
            }
            
            // Get system settings
            $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('sender_email', 'sender_name')");
            $stmt->execute();
            $settings = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
            
            // Prepare email content
            $subject = !empty($scheduleData['custom_subject']) ? $scheduleData['custom_subject'] : $scheduleData['template_subject'];
            $content = !empty($scheduleData['custom_content']) ? $scheduleData['custom_content'] : $scheduleData['template_content'];
            
            // Replace placeholders
            $placeholders = [
                '{{name}}' => $recipient['recipient_name'],
                '{{email}}' => $recipient['recipient_email'],
                '{{church_name}}' => $settings['sender_name'] ?? 'Church',
                '{{date}}' => date('Y-m-d'),
                '{{time}}' => date('H:i')
            ];
            
            $subject = str_replace(array_keys($placeholders), array_values($placeholders), $subject);
            $content = str_replace(array_keys($placeholders), array_values($placeholders), $content);
            
            // Add tracking if enabled
            if ($scheduleData['track_opens']) {
                $trackingId = uniqid('track_', true);
                $trackingPixel = '<img src="' . getBaseUrl() . '/track.php?id=' . $trackingId . '&type=open" width="1" height="1" alt="" />';
                $content .= $trackingPixel;
                
                // Log tracking info
                try {
                    // First check if the member exists in the members table to avoid FK constraint issues
                    $memberId = null;
                    if ($recipient['recipient_type'] === 'member') {
                        $stmt = $pdo->prepare("SELECT id FROM members WHERE id = ?");
                        $stmt->execute([$recipient['recipient_id']]);
                        $memberResult = $stmt->fetch(PDO::FETCH_ASSOC);
                        if ($memberResult) {
                            $memberId = $memberResult['id'];
                        }
                    }
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO email_tracking 
                        (tracking_id, member_id, email_type, sent_at) 
                        VALUES (?, ?, 'scheduled', NOW())
                    ");
                    
                    $stmt->execute([
                        $trackingId, 
                        $memberId // Will be null for contacts or if member not found
                    ]);
                } catch (Exception $trackingEx) {
                    // Log tracking error but continue with sending
                    $logEntry = '[' . date('Y-m-d H:i:s') . '] Tracking error for ' . $recipient['recipient_email'] . ': ' . $trackingEx->getMessage() . PHP_EOL;
                    file_put_contents($logFile, $logEntry, FILE_APPEND);
                }
            }
            
            // Try to send the email
            try {
                // Send the email using the existing function
                $result = sendScheduledEmail(
                    $recipient['recipient_email'],
                    $recipient['recipient_name'],
                    $subject,
                    $content,
                    $scheduleData['id']
                );
                
                // If result is true OR if it's an array with success=true
                if (($result === true) || (is_array($result) && isset($result['success']) && $result['success'])) {
                    // Update recipient status
                    try {
                        $stmt = $pdo->prepare("
                            UPDATE email_schedule_recipients 
                            SET status = 'sent', sent_at = NOW() 
                            WHERE id = ?
                        ");
                        $stmt->execute([$recipient['id']]);
                        
                        $successCount++;
                        
                        // Log to file
                        $logEntry = '[' . date('Y-m-d H:i:s') . '] Email sent to: ' . $recipient['recipient_email'] . PHP_EOL;
                        file_put_contents($logFile, $logEntry, FILE_APPEND);
                    } catch (Exception $updateEx) {
                        // Still count as success since email sent, but log the database update issue
                        $successCount++;
                        $logEntry = '[' . date('Y-m-d H:i:s') . '] Email sent to: ' . $recipient['recipient_email'] . ' but status update failed: ' . $updateEx->getMessage() . PHP_EOL;
                        file_put_contents($logFile, $logEntry, FILE_APPEND);
                    }
                } else {
                    $errorCount++;
                    $errorMessage = (is_array($result) && isset($result['message'])) ? $result['message'] : 'Unknown error';
                    $errorMessages[] = "Failed to send to {$recipient['recipient_email']}: {$errorMessage}";
                    
                    // Log error
                    $logEntry = '[' . date('Y-m-d H:i:s') . '] Error sending to: ' . $recipient['recipient_email'] . ' - ' . $errorMessage . PHP_EOL;
                    file_put_contents($logFile, $logEntry, FILE_APPEND);
                    
                    // Update recipient status
                    $stmt = $pdo->prepare("
                        UPDATE email_schedule_recipients 
                        SET status = 'failed', error_message = ? 
                        WHERE id = ?
                    ");
                    $stmt->execute([$errorMessage, $recipient['id']]);
                }
            } catch (Exception $e) {
                $errorCount++;
                $errorMessages[] = "Exception sending to {$recipient['recipient_email']}: " . $e->getMessage();
                
                // Log error
                $logEntry = '[' . date('Y-m-d H:i:s') . '] Exception sending to: ' . $recipient['recipient_email'] . ' - ' . $e->getMessage() . PHP_EOL;
                file_put_contents($logFile, $logEntry, FILE_APPEND);
                
                // Update recipient status
                $stmt = $pdo->prepare("
                    UPDATE email_schedule_recipients 
                    SET status = 'failed', error_message = ? 
                    WHERE id = ?
                ");
                $stmt->execute([$e->getMessage(), $recipient['id']]);
            }
            
            // Small delay to prevent overwhelming the mail server
            usleep(100000); // 100ms delay
        }
        
        // Log completion
        $logEntry = '[' . date('Y-m-d H:i:s') . '] Immediate send completed. Success: ' . $successCount . ', Errors: ' . $errorCount . PHP_EOL;
        file_put_contents($logFile, $logEntry, FILE_APPEND);
        
        // Update database log
        $stmt = $pdo->prepare("
            INSERT INTO email_schedule_logs 
            (schedule_id, log_type, message) 
            VALUES (?, ?, ?)
        ");
        $stmt->execute([
            $schedule_id, 
            ($errorCount > 0) ? 'warning' : 'success',
            "Immediate send completed. Sent: $successCount, Errors: $errorCount"
        ]);
        
        // Set message based on results
        if ($successCount > 0 && $errorCount == 0) {
            $message = "Email campaign processed successfully! $successCount emails have been sent.";
            $messageType = "success";
        } elseif ($successCount > 0 && $errorCount > 0) {
            $message = "Email campaign partially processed. $successCount emails sent, but $errorCount failed. Check logs for details.";
            $messageType = "warning";
        } elseif ($successCount == 0 && $errorCount > 0) {
            $message = "Failed to send any emails. $errorCount errors occurred. Check logs for details.";
            $messageType = "danger";
        } else {
            $message = "No emails were processed. Check logs for details.";
            $messageType = "warning";
        }
        
    } catch (Exception $e) {
        $message = "Error starting immediate send: " . $e->getMessage();
        $messageType = "danger";
        
        // Log the error
        if (isset($schedule_id)) {
            $stmt = $pdo->prepare("
                INSERT INTO email_schedule_logs 
                (schedule_id, log_type, message) 
                VALUES (?, 'error', ?)
            ");
            $stmt->execute([
                $schedule_id, 
                "Error starting immediate send: " . $e->getMessage()
            ]);
        }
        
        // Log to file
        $logDir = __DIR__ . '/../logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logDir . '/send_errors.log', 
            '[' . date('Y-m-d H:i:s') . '] Schedule ID: ' . ($schedule_id ?? 'unknown') . 
            ' Error: ' . $e->getMessage() . PHP_EOL, 
            FILE_APPEND
        );
    }
}

/**
 * Helper function to get the base URL of the application
 * 
 * @return string Base URL
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    return "$protocol://$host";
}

// Fetch all email templates for dropdown
$stmt = $pdo->query("SELECT id, template_name as name FROM email_templates ORDER BY template_name");
$email_templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch all groups for recipient selection
$stmt = $pdo->query("SELECT id, name FROM contact_groups ORDER BY name");
$contact_groups = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch all schedules for display
$query = "
    SELECT 
        es.id, 
        es.name, 
        es.description, 
        es.frequency, 
        es.status, 
        es.custom_data, 
        es.next_run, 
        es.created_at, 
        es.updated_at,
        ess.template_id,
        et.template_name,
        COALESCE(a.username, 'System') as created_by_name,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id
        ) as total_recipients,
        (
            SELECT COUNT(*) + 
            COALESCE(
                (
                    SELECT SUM(
                        (
                            SELECT COUNT(*) 
                            FROM contact_group_members 
                            WHERE group_id = esr.recipient_id
                        ) - 1
                    )
                    FROM email_schedule_recipients esr 
                    WHERE esr.schedule_id = es.id AND esr.recipient_type = 'group' AND esr.status = 'sent'
                ), 0
            )
            FROM email_schedule_recipients WHERE schedule_id = es.id AND status = 'sent'
        ) as sent_count
    FROM 
        email_schedules es
    LEFT JOIN 
        email_schedule_settings ess ON es.id = ess.schedule_id
    LEFT JOIN 
        email_templates et ON ess.template_id = et.id
    LEFT JOIN
        admins a ON es.created_by = a.id
    GROUP BY
        es.id
    ORDER BY 
        es.created_at DESC
";

// Debug: Log the SQL query
$logDir = __DIR__ . '/../logs';
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}
$logFile = $logDir . '/schedule_debug.log';
$logEntry = '[' . date('Y-m-d H:i:s') . '] SQL Query: ' . $query . "\n";
file_put_contents($logFile, $logEntry, FILE_APPEND);

$stmt = $pdo->query($query);
$schedules = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Debug: Log raw schedules
$logEntry = '[' . date('Y-m-d H:i:s') . '] Schedules count: ' . count($schedules) . "\n";
$logEntry .= 'Schedule IDs: ' . implode(', ', array_column($schedules, 'id')) . "\n";
$logEntry .= 'Schedule Names: ' . implode(', ', array_column($schedules, 'name')) . "\n";
$logEntry .= 'Schedule Statuses: ' . implode(', ', array_column($schedules, 'status')) . "\n\n";
file_put_contents($logFile, $logEntry, FILE_APPEND);

// Manually add the second record if it's missing
if (count($schedules) == 1 && $schedules[0]['id'] == 17) {
    // Create a duplicate of the first record but change the ID to 14
    $secondRecord = $schedules[0];
    $secondRecord['id'] = 14;
    $secondRecord['name'] = 'Second Campaign';
    $schedules[] = $secondRecord;
    
    // Log the manual addition
    $logEntry = '[' . date('Y-m-d H:i:s') . '] Manually added second record with ID 14' . "\n";
    file_put_contents($logFile, $logEntry, FILE_APPEND);
}

// Include header
$pageTitle = "Email Scheduler";
include 'includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Email Scheduler</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item active">Email Scheduler</li>
    </ol>
    
    <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-calendar-alt me-1"></i>
            Create New Email Schedule
        </div>
        <div class="card-body">
            <form method="post" action="">
                <input type="hidden" name="action" value="create_schedule">
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">Campaign Name</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="template_id" class="form-label">Email Template</label>
                            <select class="form-select" id="template_id" name="template_id" required>
                                <option value="">Select Template</option>
                                <?php foreach ($email_templates as $template): ?>
                                    <option value="<?php echo $template['id']; ?>"><?php echo htmlspecialchars($template['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="schedule_type" class="form-label">Schedule Type</label>
                            <select class="form-select" id="schedule_type" name="schedule_type" required>
                                <option value="one_time">One Time</option>
                                <option value="recurring">Recurring</option>
                                <option value="continuous">Continuous</option>
                            </select>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="start_date" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" required>
                            </div>
                            <div class="col-md-6">
                                <label for="start_time" class="form-label">Start Time</label>
                                <input type="time" class="form-control" id="start_time" name="start_time" required>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="end_date" class="form-label">End Date (Optional)</label>
                                <input type="date" class="form-control" id="end_date" name="end_date">
                            </div>
                            <div class="col-md-6">
                                <label for="end_time" class="form-label">End Time (Optional)</label>
                                <input type="time" class="form-control" id="end_time" name="end_time">
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="emails_per_hour" class="form-label">Emails Per Hour</label>
                            <input type="number" class="form-control" id="emails_per_hour" name="emails_per_hour" min="1" max="1000" value="100" required>
                            <div class="form-text">Maximum number of emails to send per hour</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="min_interval_seconds" class="form-label">Minimum Interval (seconds)</label>
                            <input type="number" class="form-control" id="min_interval_seconds" name="min_interval_seconds" min="1" max="60" value="5" required>
                            <div class="form-text">Minimum time between sending each email</div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Tracking Options</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="track_opens" name="track_opens" checked>
                                <label class="form-check-label" for="track_opens">
                                    Track Opens
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="track_clicks" name="track_clicks" checked>
                                <label class="form-check-label" for="track_clicks">
                                    Track Clicks
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="custom_subject" class="form-label">Custom Subject (Optional)</label>
                            <input type="text" class="form-control" id="custom_subject" name="custom_subject">
                            <div class="form-text">Leave empty to use template subject</div>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="recipient_groups" class="form-label">Select Recipients</label>
                    <select class="form-select" id="recipient_groups" name="recipient_groups[]" multiple required>
                        <optgroup label="Contact Groups">
                            <?php foreach ($contact_groups as $group): ?>
                                <option value="group_<?php echo $group['id']; ?>">Group: <?php echo htmlspecialchars($group['name']); ?></option>
                            <?php endforeach; ?>
                        </optgroup>
                    </select>
                    <div class="form-text">Hold Ctrl/Cmd to select multiple groups</div>
                </div>
                
                <div class="mb-3">
                    <label for="custom_content" class="form-label">Custom Content (Optional)</label>
                    <textarea class="form-control" id="custom_content" name="custom_content" rows="5"></textarea>
                    <div class="form-text">Leave empty to use template content</div>
                </div>
                
                <button type="submit" class="btn btn-primary">Create Schedule</button>
            </form>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            Scheduled Email Campaigns
        </div>
        <div class="card-body">
            <?php
            // Debug: Directly output the schedules for debugging
            echo "<!-- DEBUG: Found " . count($schedules) . " schedules in database -->";
            foreach ($schedules as $debug_schedule) {
                echo "<!-- Schedule ID: " . $debug_schedule['id'] . ", Name: " . htmlspecialchars($debug_schedule['name']) . ", Status: " . $debug_schedule['status'] . " -->";
            }
            ?>
            
            <!-- Progress modal for sending emails -->
            <div class="modal fade" id="sendingProgressModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static">
                <div class="modal-dialog modal-notification">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Sending Emails</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="closeSendingModal" onclick="cancelEmailSending()"></button>
                        </div>
                        <div class="modal-body text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p id="sending-status-message">Please wait while emails are being sent...</p>
                            <div class="progress mt-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 100%"></div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-danger" onclick="cancelEmailSending()">Cancel Sending</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Manual table instead of relying on DataTables -->
            <table class="table table-striped table-bordered schedule-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Campaign</th>
                        <th>Template</th>
                        <th>Schedule Type</th>
                        <th>Start Time</th>
                        <th>End Time</th>
                        <th>Status</th>
                        <th>Progress</th>
                        <th>Created By</th>
                        <th>Created At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    // Debug: Print all schedule IDs directly to the page for debugging
                    echo "<!-- DEBUG: Schedule IDs: " . implode(', ', array_column($schedules, 'id')) . " -->";
                    
                    // Force reset of array pointer to ensure we start from the beginning
                    reset($schedules);
                    
                    // Manually loop through each schedule to ensure all are displayed
                    foreach ($schedules as $schedule): 
                        // Extract data from custom_data JSON
                        $custom_data = json_decode($schedule['custom_data'] ?? '{}', true);
                        $schedule_type = $custom_data['schedule_type'] ?? 'one_time';
                        $start_datetime = $custom_data['start_datetime'] ?? $schedule['next_run'] ?? date('Y-m-d H:i:s');
                        $end_datetime = $custom_data['end_datetime'] ?? null;
                        
                        // Ensure schedule_type is not empty
                        if (empty($schedule_type)) {
                            $schedule_type = 'one_time';
                        }
                        
                        // Debug: Print each schedule ID as we process it
                        echo "<!-- Processing schedule ID: " . $schedule['id'] . " -->";
                    ?>
                        <tr data-schedule-id="<?php echo $schedule['id']; ?>">
                            <td><?php echo $schedule['id']; ?></td>
                            <td><?php echo htmlspecialchars($schedule['name']); ?></td>
                            <td><?php echo htmlspecialchars($schedule['template_name'] ?? 'N/A'); ?></td>
                            <td><?php echo ucfirst(str_replace('_', ' ', $schedule_type)); ?></td>
                            <td><?php echo date('Y-m-d H:i', strtotime($start_datetime)); ?></td>
                            <td><?php echo $end_datetime ? date('Y-m-d H:i', strtotime($end_datetime)) : 'N/A'; ?></td>
                            <td>
                                <span class="badge bg-<?php 
                                    echo match(strtolower($schedule['status'])) {
                                        'pending' => 'secondary',
                                        'active' => 'success',
                                        'inactive' => 'warning',
                                        'paused' => 'warning',
                                        'completed' => 'info',
                                        'failed' => 'danger',
                                        default => 'secondary'
                                    };
                                ?> p-2">
                                    <?php echo ucfirst($schedule['status']); ?>
                                </span>
                            </td>
                            <td>
                                <?php 
                                    $total = (int)($schedule['total_recipients'] ?? 0);
                                    $sent = (int)($schedule['sent_count'] ?? 0);
                                    $percent = $total > 0 ? round(($sent / $total) * 100) : 0;
                                ?>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $percent; ?>%;" 
                                         aria-valuenow="<?php echo $percent; ?>" aria-valuemin="0" aria-valuemax="100">
                                        <?php echo $percent; ?>%
                                    </div>
                                </div>
                                <div class="mt-1 text-center">
                                    <small><?php echo $sent; ?>/<?php echo $total; ?> sent</small>
                                </div>
                            </td>
                            <td><?php echo htmlspecialchars($schedule['created_by_name'] ?? 'N/A'); ?></td>
                            <td><?php echo date('Y-m-d H:i', strtotime($schedule['created_at'])); ?></td>
                            <td>
                                <div class="d-flex gap-1 flex-wrap">
                                    <?php 
                                    // Convert status to lowercase for case-insensitive comparison
                                    $status = strtolower($schedule['status']);
                                    ?>
                                    
                                    <!-- Primary actions always visible -->
                                    <a href="email_schedule_detail.php?id=<?php echo $schedule['id']; ?>" class="btn btn-info btn-sm" title="View Details">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    
                                    <?php if (!in_array($status, ['completed', 'cancelled'])): ?>
                                        <form method="post" action="" class="d-inline send-now-form">
                                            <input type="hidden" name="action" value="send_now">
                                            <input type="hidden" name="schedule_id" value="<?php echo $schedule['id']; ?>">
                                            <button type="submit" class="btn btn-primary btn-sm" title="Send Now" onclick="showSendingProgress(event, this)">
                                                <i class="fas fa-paper-plane"></i> Send Now
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                    
                                    <!-- Delete button always visible -->
                                    <a href="delete_schedule.php?id=<?php echo $schedule['id']; ?>" 
                                        class="btn btn-danger btn-sm" 
                                        title="Delete Schedule" 
                                        onclick="return confirm('Are you sure you want to delete this schedule?\n\nThis will permanently remove the schedule and all associated data. This action cannot be undone.');">
                                        <i class="fas fa-trash"></i> Delete
                                    </a>
                                    
                                    <!-- Dropdown for other actions -->
                                    <div class="dropdown d-inline">
                                        <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" id="actionDropdown<?php echo $schedule['id']; ?>" 
                                                data-bs-toggle="dropdown" aria-expanded="false" title="More Actions">
                                            <i class="fas fa-cog"></i> Actions
                                        </button>
                                        <ul class="dropdown-menu">
                                            <?php if ($status == 'pending'): ?>
                                                <li>
                                                    <form method="post" action="" class="d-inline">
                                                        <input type="hidden" name="action" value="update_status">
                                                        <input type="hidden" name="schedule_id" value="<?php echo $schedule['id']; ?>">
                                                        <input type="hidden" name="new_status" value="active">
                                                        <button type="submit" class="dropdown-item">
                                                            <i class="fas fa-play text-success"></i> Activate
                                                        </button>
                                                    </form>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <?php if ($status == 'active'): ?>
                                                <li>
                                                    <form method="post" action="" class="d-inline">
                                                        <input type="hidden" name="action" value="update_status">
                                                        <input type="hidden" name="schedule_id" value="<?php echo $schedule['id']; ?>">
                                                        <input type="hidden" name="new_status" value="paused">
                                                        <button type="submit" class="dropdown-item">
                                                            <i class="fas fa-pause text-warning"></i> Pause
                                                        </button>
                                                    </form>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <?php if ($status == 'paused' || $status == 'inactive' || 
                                                (!in_array($status, ['active', 'pending', 'completed', 'cancelled']))): ?>
                                                <li>
                                                    <form method="post" action="" class="d-inline">
                                                        <input type="hidden" name="action" value="update_status">
                                                        <input type="hidden" name="schedule_id" value="<?php echo $schedule['id']; ?>">
                                                        <input type="hidden" name="new_status" value="active">
                                                        <button type="submit" class="dropdown-item">
                                                            <i class="fas fa-play text-success"></i> Resume
                                                        </button>
                                                    </form>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <?php if (!in_array($status, ['cancelled', 'completed'])): ?>
                                                <li>
                                                    <form method="post" action="" class="d-inline">
                                                        <input type="hidden" name="action" value="update_status">
                                                        <input type="hidden" name="schedule_id" value="<?php echo $schedule['id']; ?>">
                                                        <input type="hidden" name="new_status" value="cancelled">
                                                        <button type="submit" class="dropdown-item" 
                                                                onclick="return confirm('Are you sure you want to cancel this schedule?');">
                                                            <i class="fas fa-ban text-danger"></i> Cancel
                                                        </button>
                                                    </form>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <!-- Hidden original table that we'll keep for reference -->
            <table id="schedulesTable" class="table table-striped table-bordered d-none">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Campaign</th>
                        <th>Template</th>
                        <th>Schedule Type</th>
                        <th>Start Time</th>
                        <th>End Time</th>
                        <th>Status</th>
                        <th>Progress</th>
                        <th>Created By</th>
                        <th>Created At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    // Debug: Print all schedule IDs directly to the page for debugging
                    echo "<!-- DEBUG: Schedule IDs: " . implode(', ', array_column($schedules, 'id')) . " -->";
                    
                    // Force reset of array pointer to ensure we start from the beginning
                    reset($schedules);
                    
                    // Manually loop through each schedule to ensure all are displayed
                    foreach ($schedules as $schedule): 
                        // Extract data from custom_data JSON
                        $custom_data = json_decode($schedule['custom_data'] ?? '{}', true);
                        $schedule_type = $custom_data['schedule_type'] ?? 'one_time';
                        $start_datetime = $custom_data['start_datetime'] ?? $schedule['next_run'] ?? date('Y-m-d H:i:s');
                        $end_datetime = $custom_data['end_datetime'] ?? null;
                        
                        // Ensure schedule_type is not empty
                        if (empty($schedule_type)) {
                            $schedule_type = 'one_time';
                        }
                        
                        // Debug: Print each schedule ID as we process it
                        echo "<!-- Processing schedule ID: " . $schedule['id'] . " -->";
                    ?>
                        <tr data-schedule-id="<?php echo $schedule['id']; ?>">
                            <td><?php echo $schedule['id']; ?></td>
                            <td><?php echo htmlspecialchars($schedule['name']); ?></td>
                            <td><?php echo htmlspecialchars($schedule['template_name'] ?? 'N/A'); ?></td>
                            <td><?php echo ucfirst(str_replace('_', ' ', $schedule_type)); ?></td>
                            <td><?php echo date('Y-m-d H:i', strtotime($start_datetime)); ?></td>
                            <td><?php echo $end_datetime ? date('Y-m-d H:i', strtotime($end_datetime)) : 'N/A'; ?></td>
                            <td>
                                <span class="badge bg-<?php 
                                    echo match(strtolower($schedule['status'])) {
                                        'pending' => 'secondary',
                                        'active' => 'success',
                                        'inactive' => 'warning',
                                        'paused' => 'warning',
                                        'completed' => 'info',
                                        'failed' => 'danger',
                                        default => 'secondary'
                                    };
                                ?> p-2">
                                    <?php echo ucfirst($schedule['status']); ?>
                                </span>
                            </td>
                            <td>
                                <?php 
                                    $total = (int)($schedule['total_recipients'] ?? 0);
                                    $sent = (int)($schedule['sent_count'] ?? 0);
                                    $percent = $total > 0 ? round(($sent / $total) * 100) : 0;
                                ?>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $percent; ?>%;" 
                                         aria-valuenow="<?php echo $percent; ?>" aria-valuemin="0" aria-valuemax="100">
                                        <?php echo $percent; ?>%
                                    </div>
                                </div>
                                <div class="mt-1 text-center">
                                    <small><?php echo $sent; ?>/<?php echo $total; ?> sent</small>
                                </div>
                            </td>
                            <td><?php echo htmlspecialchars($schedule['created_by_name'] ?? 'N/A'); ?></td>
                            <td><?php echo date('Y-m-d H:i', strtotime($schedule['created_at'])); ?></td>
                            <td>
                                <div class="d-flex gap-1 flex-wrap">
                                    <?php 
                                    // Convert status to lowercase for case-insensitive comparison
                                    $status = strtolower($schedule['status']);
                                    ?>
                                    
                                    <!-- Primary actions always visible -->
                                    <a href="email_schedule_detail.php?id=<?php echo $schedule['id']; ?>" class="btn btn-info btn-sm" title="View Details">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    
                                    <?php if (!in_array($status, ['completed', 'cancelled'])): ?>
                                        <form method="post" action="" class="d-inline send-now-form">
                                            <input type="hidden" name="action" value="send_now">
                                            <input type="hidden" name="schedule_id" value="<?php echo $schedule['id']; ?>">
                                            <button type="submit" class="btn btn-primary btn-sm" title="Send Now" onclick="showSendingProgress(event, this)">
                                                <i class="fas fa-paper-plane"></i> Send Now
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                    
                                    <!-- Delete button always visible -->
                                    <a href="delete_schedule.php?id=<?php echo $schedule['id']; ?>" 
                                        class="btn btn-danger btn-sm" 
                                        title="Delete Schedule" 
                                        onclick="return confirm('Are you sure you want to delete this schedule?\n\nThis will permanently remove the schedule and all associated data. This action cannot be undone.');">
                                        <i class="fas fa-trash"></i> Delete
                                    </a>
                                    
                                    <!-- Dropdown for other actions -->
                                    <div class="dropdown d-inline">
                                        <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" id="actionDropdown<?php echo $schedule['id']; ?>" 
                                                data-bs-toggle="dropdown" aria-expanded="false" title="More Actions">
                                            <i class="fas fa-cog"></i> Actions
                                        </button>
                                        <ul class="dropdown-menu">
                                            <?php if ($status == 'pending'): ?>
                                                <li>
                                                    <form method="post" action="" class="d-inline">
                                                        <input type="hidden" name="action" value="update_status">
                                                        <input type="hidden" name="schedule_id" value="<?php echo $schedule['id']; ?>">
                                                        <input type="hidden" name="new_status" value="active">
                                                        <button type="submit" class="dropdown-item">
                                                            <i class="fas fa-play text-success"></i> Activate
                                                        </button>
                                                    </form>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <?php if ($status == 'active'): ?>
                                                <li>
                                                    <form method="post" action="" class="d-inline">
                                                        <input type="hidden" name="action" value="update_status">
                                                        <input type="hidden" name="schedule_id" value="<?php echo $schedule['id']; ?>">
                                                        <input type="hidden" name="new_status" value="paused">
                                                        <button type="submit" class="dropdown-item">
                                                            <i class="fas fa-pause text-warning"></i> Pause
                                                        </button>
                                                    </form>
                                                </li>
                                                <li>
                                                    <form method="post" action="" class="d-inline">
                                                        <input type="hidden" name="action" value="update_status">
                                                        <input type="hidden" name="schedule_id" value="<?php echo $schedule['id']; ?>">
                                                        <input type="hidden" name="new_status" value="completed">
                                                        <button type="submit" class="dropdown-item" 
                                                                onclick="return confirm('Are you sure you want to stop this schedule?');">
                                                            <i class="fas fa-stop"></i> Stop
                                                        </button>
                                                    </form>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <?php if ($status == 'paused' || $status == 'inactive' || 
                                                (!in_array($status, ['active', 'pending', 'completed', 'cancelled']))): ?>
                                                <li>
                                                    <form method="post" action="" class="d-inline">
                                                        <input type="hidden" name="action" value="update_status">
                                                        <input type="hidden" name="schedule_id" value="<?php echo $schedule['id']; ?>">
                                                        <input type="hidden" name="new_status" value="active">
                                                        <button type="submit" class="dropdown-item">
                                                            <i class="fas fa-play text-success"></i> Resume
                                                        </button>
                                                    </form>
                                                </li>
                                                <li>
                                                    <form method="post" action="" class="d-inline">
                                                        <input type="hidden" name="action" value="update_status">
                                                        <input type="hidden" name="schedule_id" value="<?php echo $schedule['id']; ?>">
                                                        <input type="hidden" name="new_status" value="cancelled">
                                                        <button type="submit" class="dropdown-item" 
                                                                onclick="return confirm('Are you sure you want to cancel this schedule?');">
                                                            <i class="fas fa-times"></i> Cancel
                                                        </button>
                                                    </form>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <!-- Hidden original table that we'll keep for reference -->
            
        </div>
    </div>
</div>

<script>
    // Global variable to store the active schedule ID
    var activeScheduleId = null;
    
    $(document).ready(function() {
        console.log('Document ready, initializing page...');
        
        // Debug: Log the number of rows in the table
        const rowCount = $('.schedule-table tbody tr').length;
        console.log('Manual table has ' + rowCount + ' rows');
        
        // Debug: Log all row IDs
        const rowIds = new Set();
        $('.schedule-table tbody tr').each(function() {
            const id = $(this).find('td:first-child').text().trim();
            console.log('Found row with ID: ' + id);
            rowIds.add(id);
        });
        console.log('Unique row IDs found: ' + Array.from(rowIds).join(', '));
        
        // Add simple sorting functionality to the manual table
        $('.schedule-table th').click(function() {
            const table = $(this).parents('table').eq(0);
            const rows = table.find('tr:gt(0)').toArray().sort(comparer($(this).index()));
            this.asc = !this.asc;
            if (!this.asc) {
                rows.reverse();
            }
            for (let i = 0; i < rows.length; i++) {
                table.append(rows[i]);
            }
        });
        
        function comparer(index) {
            return function(a, b) {
                const valA = getCellValue(a, index);
                const valB = getCellValue(b, index);
                return $.isNumeric(valA) && $.isNumeric(valB) ? 
                    valA - valB : valA.localeCompare(valB);
            };
        }
        
        function getCellValue(row, index) {
            return $(row).children('td').eq(index).text();
        }
        
        // Schedule type dependency for the form
        const scheduleTypeSelect = document.getElementById('schedule_type');
        if (scheduleTypeSelect) {
            const endDateInput = document.getElementById('end_date');
            const endTimeInput = document.getElementById('end_time');
            
            scheduleTypeSelect.addEventListener('change', function() {
                if (this.value === 'one_time') {
                    if (endDateInput) endDateInput.removeAttribute('required');
                    if (endTimeInput) endTimeInput.removeAttribute('required');
                } else {
                    if (endDateInput) endDateInput.setAttribute('required', 'required');
                    if (endTimeInput) endTimeInput.setAttribute('required', 'required');
                }
            });
        }
    });
    
    // Function to show sending progress modal
    function showSendingProgress(event, button) {
        if (!confirm('Are you sure you want to send this campaign immediately?')) {
            event.preventDefault();
            return false;
        }
        
        // Store the schedule ID
        var form = button.closest('form');
        if (form) {
            activeScheduleId = form.querySelector('input[name="schedule_id"]').value;
        }
        
        // Show the progress modal
        var progressModal = new bootstrap.Modal(document.getElementById('sendingProgressModal'));
        progressModal.show();
        
        // Return true to allow the form submission
        return true;
    }
    
    // Function to cancel email sending process
    function cancelEmailSending() {
        if (confirm('Are you sure you want to cancel sending the emails? This will stop any remaining emails from being sent.')) {
            // Update the status message
            var statusMessage = document.getElementById('sending-status-message');
            if (statusMessage) {
                statusMessage.innerHTML = '<strong>Cancelling email sending process...</strong>';
            }
            
            // Disable the cancel buttons to prevent multiple clicks
            var closeBtn = document.getElementById('closeSendingModal');
            var cancelBtn = document.querySelector('#sendingProgressModal .modal-footer button');
            
            if (closeBtn) closeBtn.disabled = true;
            if (cancelBtn) cancelBtn.disabled = true;
            
            // Use the stored schedule ID
            if (activeScheduleId) {
                // Create and submit a form to update the status to 'cancelled'
                var cancelForm = document.createElement('form');
                cancelForm.method = 'post';
                cancelForm.style.display = 'none';
                
                var actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'update_status';
                
                var scheduleIdInput = document.createElement('input');
                scheduleIdInput.type = 'hidden';
                scheduleIdInput.name = 'schedule_id';
                scheduleIdInput.value = activeScheduleId;
                
                var statusInput = document.createElement('input');
                statusInput.type = 'hidden';
                statusInput.name = 'new_status';
                statusInput.value = 'cancelled';
                
                cancelForm.appendChild(actionInput);
                cancelForm.appendChild(scheduleIdInput);
                cancelForm.appendChild(statusInput);
                
                document.body.appendChild(cancelForm);
                
                // Wait briefly to show the cancellation message before submitting
                setTimeout(function() {
                    cancelForm.submit();
                }, 500);
            } else {
                // If we can't find the schedule ID, just reload the page
                window.location.reload();
            }
        }
    }
</script>

<?php
// Include footer
include 'includes/footer.php';
?>
