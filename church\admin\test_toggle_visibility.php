<?php
// Test page for toggle button visibility
session_start();
$_SESSION['admin_id'] = 4;
$_SESSION['admin_username'] = 'admin';
$_SESSION['admin_role'] = 'super_admin';

require_once '../config.php';
require_once 'includes/rbac_access_control_granular.php';

// Include functions
if (!function_exists('get_organization_name')) {
    function get_organization_name() {
        return 'Freedom Assembly Church';
    }
}

if (!function_exists('get_site_setting')) {
    function get_site_setting($key, $default = '') {
        return $default;
    }
}

if (!function_exists('admin_url_for')) {
    function admin_url_for($page) {
        return $page;
    }
}

if (!function_exists('is_active')) {
    function is_active($page) {
        return '';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toggle Visibility Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { margin: 0; padding: 0; }
        .container-fluid { padding: 0; }
        .main-content { 
            margin-left: 250px; 
            padding: 20px; 
            transition: margin-left 0.3s ease;
        }
        .main-content.expanded { 
            margin-left: 60px; 
        }
        .sidebar {
            width: 250px;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: fixed;
            left: 0;
            top: 0;
            overflow-y: auto;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar_rbac.php'; ?>
            
            <div class="main-content">
                <h1>🔍 Toggle Button Visibility Test</h1>
                
                <div class="alert alert-info">
                    <h4>🎯 Test Instructions:</h4>
                    <ol>
                        <li><strong>Look for the toggle button</strong> in the top-right corner of the sidebar (chevron arrow)</li>
                        <li><strong>Click the toggle button</strong> to collapse the sidebar</li>
                        <li><strong>Verify the toggle button remains visible</strong> when sidebar is collapsed</li>
                        <li><strong>Click again</strong> to expand and verify it works both ways</li>
                    </ol>
                </div>
                
                <div class="alert alert-success">
                    <h4>✅ Expected Behavior:</h4>
                    <ul>
                        <li><strong>Expanded State:</strong> Toggle button visible in top-right corner of sidebar</li>
                        <li><strong>Collapsed State:</strong> Toggle button moves outside sidebar edge but remains visible</li>
                        <li><strong>Icon Changes:</strong> Left chevron ↔ Right chevron</li>
                        <li><strong>Smooth Animation:</strong> Sidebar width transitions smoothly</li>
                        <li><strong>Persistent:</strong> State saves in localStorage</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h4>🔧 Recent Fixes Applied:</h4>
                    <ul>
                        <li>✅ Added <code>display: flex !important</code> to ensure visibility</li>
                        <li>✅ Increased z-index to 1001 for proper layering</li>
                        <li>✅ Adjusted positioning: <code>right: -20px</code> when collapsed</li>
                        <li>✅ Added box-shadow for better visibility</li>
                        <li>✅ Increased button size to 36x36px when collapsed</li>
                        <li>✅ Added <code>overflow: visible</code> to sidebar header</li>
                    </ul>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>🎨 CSS Debug Info</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Normal State CSS:</strong></p>
                        <code>
                            .sidebar-toggle-btn {<br>
                            &nbsp;&nbsp;position: absolute;<br>
                            &nbsp;&nbsp;top: 15px;<br>
                            &nbsp;&nbsp;right: 15px;<br>
                            &nbsp;&nbsp;display: flex !important;<br>
                            &nbsp;&nbsp;z-index: 1000;<br>
                            }
                        </code>
                        
                        <p class="mt-3"><strong>Collapsed State CSS:</strong></p>
                        <code>
                            .sidebar.collapsed .sidebar-toggle-btn {<br>
                            &nbsp;&nbsp;position: absolute;<br>
                            &nbsp;&nbsp;right: -20px;<br>
                            &nbsp;&nbsp;top: 15px;<br>
                            &nbsp;&nbsp;display: flex !important;<br>
                            &nbsp;&nbsp;z-index: 1001;<br>
                            &nbsp;&nbsp;width: 36px;<br>
                            &nbsp;&nbsp;height: 36px;<br>
                            }
                        </code>
                    </div>
                </div>
                
                <div class="mt-4">
                    <button class="btn btn-primary" onclick="testCollapse()">🧪 Test Collapse Programmatically</button>
                    <button class="btn btn-secondary" onclick="checkToggleVisibility()">👁️ Check Toggle Visibility</button>
                </div>
                
                <div id="testResults" class="mt-3"></div>
            </div>
        </div>
    </div>
    
    <script>
        function testCollapse() {
            const sidebar = document.getElementById('sidebar');
            const isCollapsed = sidebar.classList.contains('collapsed');
            
            if (isCollapsed) {
                sidebar.classList.remove('collapsed');
                document.querySelector('.main-content').classList.remove('expanded');
            } else {
                sidebar.classList.add('collapsed');
                document.querySelector('.main-content').classList.add('expanded');
            }
            
            setTimeout(checkToggleVisibility, 500);
        }
        
        function checkToggleVisibility() {
            const toggle = document.getElementById('sidebarToggleDesktop');
            const sidebar = document.getElementById('sidebar');
            const results = document.getElementById('testResults');
            
            if (toggle) {
                const rect = toggle.getBoundingClientRect();
                const isVisible = rect.width > 0 && rect.height > 0;
                const isCollapsed = sidebar.classList.contains('collapsed');
                
                results.innerHTML = `
                    <div class="alert ${isVisible ? 'alert-success' : 'alert-danger'}">
                        <h6>Toggle Button Status:</h6>
                        <ul>
                            <li><strong>Visible:</strong> ${isVisible ? '✅ Yes' : '❌ No'}</li>
                            <li><strong>Sidebar State:</strong> ${isCollapsed ? 'Collapsed' : 'Expanded'}</li>
                            <li><strong>Button Position:</strong> ${rect.left.toFixed(0)}px, ${rect.top.toFixed(0)}px</li>
                            <li><strong>Button Size:</strong> ${rect.width.toFixed(0)}px × ${rect.height.toFixed(0)}px</li>
                        </ul>
                    </div>
                `;
            } else {
                results.innerHTML = '<div class="alert alert-danger">❌ Toggle button not found!</div>';
            }
        }
        
        // Check visibility on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkToggleVisibility, 1000);
        });
    </script>
</body>
</html>
