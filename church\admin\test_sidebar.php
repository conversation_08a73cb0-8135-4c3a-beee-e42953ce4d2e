<?php
// Simple test page to verify sidebar functionality
session_start();

// Set a fake session for testing
$_SESSION['admin_id'] = 4;
$_SESSION['admin_username'] = 'admin';
$_SESSION['admin_role'] = 'super_admin';

require_once '../config.php';
require_once 'includes/rbac_access_control_granular.php';

// Include functions
if (!function_exists('get_organization_name')) {
    function get_organization_name() {
        return 'Freedom Assembly Church';
    }
}

if (!function_exists('get_site_setting')) {
    function get_site_setting($key, $default = '') {
        return $default;
    }
}

if (!function_exists('admin_url_for')) {
    function admin_url_for($page) {
        return $page;
    }
}

if (!function_exists('is_active')) {
    function is_active($page) {
        return '';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sidebar Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { margin: 0; padding: 0; }
        .container-fluid { padding: 0; }
        .main-content { 
            margin-left: 250px; 
            padding: 20px; 
            transition: margin-left 0.3s ease;
        }
        .main-content.expanded { 
            margin-left: 60px; 
        }
        .sidebar {
            width: 250px;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: fixed;
            left: 0;
            top: 0;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar_rbac.php'; ?>
            
            <div class="main-content">
                <h1>Sidebar Collapse Test</h1>
                <p>This page is for testing the sidebar collapse functionality.</p>
                <p><strong>Instructions:</strong></p>
                <ol>
                    <li>Look for the collapse arrow icon in the top-right corner of the sidebar</li>
                    <li>Click the arrow to collapse/expand the sidebar</li>
                    <li>The sidebar should smoothly animate between expanded and collapsed states</li>
                    <li>The arrow should change direction when clicked</li>
                    <li>The main content should adjust its margin accordingly</li>
                </ol>
                
                <div class="alert alert-info">
                    <strong>Expected Behavior:</strong>
                    <ul>
                        <li>✅ Single collapse arrow icon (no duplicates)</li>
                        <li>✅ Smooth animation when collapsing/expanding</li>
                        <li>✅ Arrow changes from left-chevron to right-chevron</li>
                        <li>✅ Main content margin adjusts automatically</li>
                        <li>✅ State persists in localStorage</li>
                    </ul>
                </div>
                
                <div class="alert alert-success">
                    <strong>Fixed Issues:</strong>
                    <ul>
                        <li>✅ Removed duplicate toggle buttons</li>
                        <li>✅ Fixed JavaScript to work with single toggle</li>
                        <li>✅ Updated SVG icon switching</li>
                        <li>✅ Cleaned up conflicting IDs</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
