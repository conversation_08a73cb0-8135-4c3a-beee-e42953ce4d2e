<?php
// <PERSON><PERSON><PERSON> to reset admin67 password for testing
require_once '../config.php';

echo "<h2>Reset Admin67 Password</h2>";

try {
    // Set a simple password for testing
    $new_password = 'test123';
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    
    // Update admin67 password
    $stmt = $pdo->prepare("UPDATE admins SET password = ? WHERE username = 'admin67'");
    $stmt->execute([$hashed_password]);
    
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Password reset successfully!</p>";
        echo "<p><strong>Username:</strong> admin67</p>";
        echo "<p><strong>New Password:</strong> " . htmlspecialchars($new_password) . "</p>";
        echo "<p><a href='login.php'>→ Go to Login Page</a></p>";
        echo "<p><a href='debug_login.php'>→ Back to Debug Page</a></p>";
    } else {
        echo "<p style='color: red;'>Error: admin67 user not found or password not updated!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h2 { color: #333; }
    p { margin: 10px 0; }
</style>
