<?php
/**
 * Admin Gift Sending Interface
 * Allows administrators to send gifts to users
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config.php';

$message = '';
$error = '';

// Create/modify member_gifts table to allow NULL sender_id for admin gifts
try {
    // First, check if table exists and modify it to allow NULL sender_id
    $pdo->exec("CREATE TABLE IF NOT EXISTS member_gifts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        sender_id INT NULL,
        recipient_id INT NOT NULL,
        gift_type VARCHAR(50) NOT NULL,
        gift_title VARCHAR(255) NOT NULL,
        gift_message TEXT,
        gift_file_path VARCHAR(500),
        delivery_date DATE,
        is_anonymous BOOLEAN DEFAULT FALSE,
        is_delivered BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        delivered_at TIMESTAMP NULL,
        INDEX idx_sender (sender_id),
        INDEX idx_recipient (recipient_id),
        INDEX idx_delivery_date (delivery_date),
        FOREIGN KEY (recipient_id) REFERENCES members(id) ON DELETE CASCADE
    )");

    // Try to modify existing table to allow NULL sender_id
    try {
        $pdo->exec("ALTER TABLE member_gifts MODIFY COLUMN sender_id INT NULL");
        $pdo->exec("ALTER TABLE member_gifts DROP FOREIGN KEY member_gifts_ibfk_1");
    } catch (PDOException $e) {
        // Ignore errors if constraints don't exist or table is already correct
    }
} catch (PDOException $e) {
    // Table might already exist, continue
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_gift'])) {
    try {
        // Handle file upload
        $uploaded_file = null;
        if (isset($_FILES['gift_file']) && $_FILES['gift_file']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = admin_file_path('uploads/gifts/');
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = pathinfo($_FILES['gift_file']['name'], PATHINFO_EXTENSION);
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'];
            
            if (in_array(strtolower($file_extension), $allowed_extensions)) {
                $filename = uniqid() . '.' . $file_extension;
                $upload_path = $upload_dir . $filename;
                
                if (move_uploaded_file($_FILES['gift_file']['tmp_name'], $upload_path)) {
                    $uploaded_file = 'uploads/gifts/' . $filename;
                }
            } else {
                throw new Exception('Invalid file type. Please upload images, PDFs, or documents only.');
            }
        }
        
        // Insert gift record
        $stmt = $pdo->prepare("
            INSERT INTO member_gifts (
                sender_id, recipient_id, gift_type, gift_title, gift_message, 
                gift_file_path, delivery_date, is_anonymous, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $delivery_date = $_POST['delivery_date'] ?: date('Y-m-d');
        $sender_id = null; // Admin gifts have null sender_id to distinguish them
        
        $stmt->execute([
            $sender_id,
            $_POST['recipient_id'],
            $_POST['gift_type'],
            $_POST['gift_title'],
            $_POST['gift_message'],
            $uploaded_file,
            $delivery_date,
            isset($_POST['is_anonymous']) ? 1 : 0
        ]);
        
        $gift_id = $pdo->lastInsertId();
        
        // Send email notification if delivery is immediate
        if ($delivery_date <= date('Y-m-d')) {
            // Get recipient details
            $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
            $stmt->execute([$_POST['recipient_id']]);
            $recipient = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($recipient && $recipient['email']) {
                $sender_name = isset($_POST['is_anonymous']) && $_POST['is_anonymous'] ? 'Church Administration' : get_organization_name() . ' Administration';

                $subject = "You've received a gift from " . get_organization_name() . "!";
                $email_body = "
                <h2>You've received a gift!</h2>
                <p>Dear {$recipient['full_name']},</p>
                <p>You've received a special gift from {$sender_name}!</p>
                <p><strong>Gift:</strong> {$_POST['gift_title']}</p>
                <p><strong>Message:</strong> {$_POST['gift_message']}</p>
                ";

                if ($uploaded_file) {
                    $email_body .= "<p><strong>Attachment:</strong> Please check your member portal for the attached gift file.</p>";
                }

                $email_body .= "
                <p>Login to your member portal to view your gift!</p>
                <p>With warm regards,<br>" . get_organization_name() . " Administration</p>
                ";

                // Send email using existing email system
                try {
                    if (function_exists('sendEmail')) {
                        $email_sent = sendEmail(
                            $recipient['email'],
                            $recipient['full_name'],
                            $subject,
                            $email_body,
                            true
                        );

                        if (!$email_sent) {
                            error_log("Failed to send gift notification email to " . $recipient['email']);
                        }
                    }
                } catch (Exception $e) {
                    error_log("Error sending gift notification email: " . $e->getMessage());
                }
            }
        }
        
        $message = "Gift sent successfully! " . ($delivery_date > date('Y-m-d') ? "It will be delivered on " . date('F j, Y', strtotime($delivery_date)) : "The recipient has been notified.");
        
    } catch (Exception $e) {
        $error = "Error sending gift: " . $e->getMessage();
    }
}

// Get all active members for recipient selection
$stmt = $pdo->prepare("
    SELECT id, full_name, first_name, last_name, birth_date, email
    FROM members 
    WHERE status = 'active'
    ORDER BY full_name
");
$stmt->execute();
$members = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get upcoming birthdays (next 30 days)
$stmt = $pdo->prepare("
    SELECT id, full_name, birth_date,
           CASE 
               WHEN DATE_FORMAT(birth_date, '%m-%d') >= DATE_FORMAT(CURDATE(), '%m-%d')
               THEN DATEDIFF(DATE(CONCAT(YEAR(CURDATE()), '-', DATE_FORMAT(birth_date, '%m-%d'))), CURDATE())
               ELSE DATEDIFF(DATE(CONCAT(YEAR(CURDATE()) + 1, '-', DATE_FORMAT(birth_date, '%m-%d'))), CURDATE())
           END as days_until_birthday
    FROM members 
    WHERE status = 'active' AND birth_date IS NOT NULL
    HAVING days_until_birthday <= 30
    ORDER BY days_until_birthday
");
$stmt->execute();
$upcoming_birthdays = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Send Gift to Member";
$page_header = "Send Gift to Member";
include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-gift"></i> Send Gift to Member</h1>
    <a href="gift_management.php" class="btn btn-outline-primary">
        <i class="bi bi-list"></i> View All Gifts
    </a>
</div>

<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <!-- Upcoming Birthdays -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-calendar-heart"></i> Upcoming Birthdays</h5>
            </div>
            <div class="card-body">
                <?php if (empty($upcoming_birthdays)): ?>
                    <p class="text-muted">No upcoming birthdays in the next 30 days.</p>
                <?php else: ?>
                    <?php foreach ($upcoming_birthdays as $birthday): ?>
                        <div class="card mb-2" style="border-left: 4px solid #ffc107;">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong><?php echo htmlspecialchars($birthday['full_name']); ?></strong>
                                        <br><small class="text-muted">
                                            <?php echo date('M j', strtotime($birthday['birth_date'])); ?>
                                            (<?php echo $birthday['days_until_birthday']; ?> days)
                                        </small>
                                    </div>
                                    <button class="btn btn-sm btn-warning" onclick="selectRecipient(<?php echo $birthday['id']; ?>, '<?php echo htmlspecialchars($birthday['full_name']); ?>')">
                                        <i class="bi bi-gift"></i> Send
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Gift Sending Form -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-send"></i> Send Gift to Member</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="giftForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="recipient_id" class="form-label">Recipient *</label>
                                <select class="form-select" id="recipient_id" name="recipient_id" required>
                                    <option value="">Select a member...</option>
                                    <?php foreach ($members as $member): ?>
                                        <option value="<?php echo $member['id']; ?>">
                                            <?php echo htmlspecialchars($member['full_name']); ?>
                                            <?php if ($member['birth_date']): ?>
                                                (Birthday: <?php echo date('M j', strtotime($member['birth_date'])); ?>)
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="gift_type" class="form-label">Gift Type *</label>
                                <select class="form-select" id="gift_type" name="gift_type" required>
                                    <option value="">Select gift type...</option>
                                    <option value="digital_card">Digital Card</option>
                                    <option value="gift_card">Gift Card</option>
                                    <option value="certificate">Certificate</option>
                                    <option value="document">Document</option>
                                    <option value="appreciation">Appreciation Gift</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="gift_title" class="form-label">Gift Title *</label>
                        <input type="text" class="form-control" id="gift_title" name="gift_title"
                               placeholder="e.g., Birthday Blessing, Appreciation Certificate, etc." required>
                    </div>

                    <div class="mb-3">
                        <label for="gift_message" class="form-label">Message *</label>
                        <textarea class="form-control" id="gift_message" name="gift_message" rows="4"
                                  placeholder="Write a message to accompany the gift..." required></textarea>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Upload Gift File (Optional)</label>
                        <div class="file-upload-area" onclick="document.getElementById('gift_file').click()"
                             style="border: 2px dashed #007bff; border-radius: 8px; padding: 30px; text-align: center; cursor: pointer;">
                            <i class="bi bi-cloud-upload" style="font-size: 2rem; color: #007bff;"></i>
                            <p class="mb-0 mt-2">Click to upload or drag and drop</p>
                            <small class="text-muted">Images, PDFs, Documents (Max 10MB)</small>
                        </div>
                        <input type="file" class="form-control d-none" id="gift_file" name="gift_file"
                               accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx">
                        <div id="file-preview" class="mt-2"></div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="delivery_date" class="form-label">Delivery Date</label>
                                <input type="date" class="form-control" id="delivery_date" name="delivery_date"
                                       min="<?php echo date('Y-m-d'); ?>" value="<?php echo date('Y-m-d'); ?>">
                                <small class="text-muted">Leave as today for immediate delivery</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_anonymous" name="is_anonymous">
                                    <label class="form-check-label" for="is_anonymous">
                                        Send as "Church Administration" (anonymous)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" name="send_gift" class="btn btn-primary btn-lg">
                            <i class="bi bi-send"></i> Send Gift
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    function selectRecipient(memberId, memberName) {
        document.getElementById('recipient_id').value = memberId;
        
        // Auto-fill birthday-related fields
        document.getElementById('gift_type').value = 'digital_card';
        document.getElementById('gift_title').value = 'Happy Birthday!';
        document.getElementById('gift_message').value = `Dear ${memberName},\n\nWishing you a wonderful birthday filled with joy, blessings, and happiness!\n\nMay this new year of your life bring you countless reasons to smile and be grateful.\n\nWith warm birthday wishes,\n<?php echo get_organization_name(); ?> Team`;
    }

    // File upload handling
    const fileInput = document.getElementById('gift_file');
    const filePreview = document.getElementById('file-preview');

    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            filePreview.innerHTML = `
                <div class="alert alert-info">
                    <i class="bi bi-file-earmark"></i> Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)
                </div>
            `;
        }
    });
</script>

<?php include 'includes/footer.php'; ?>
