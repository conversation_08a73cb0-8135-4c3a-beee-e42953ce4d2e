<?php
// Setup Assignment Tables for Session and Event Assignments
require_once '../config.php';

echo "<h2>🔧 Setting Up Assignment Tables</h2>";

try {
    // 1. Create event_assignments table
    echo "<h3>📋 Step 1: Creating event_assignments Table</h3>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS event_assignments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        event_id INT NOT NULL,
        role_type ENU<PERSON>('coordinator', 'organizer') NOT NULL,
        assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active TINYINT(1) DEFAULT 1,
        notes TEXT,
        INDEX idx_user_id (user_id),
        INDEX idx_event_id (event_id),
        INDEX idx_active (is_active),
        UNIQUE KEY unique_user_event_role (user_id, event_id, role_type, is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($sql);
    echo "✅ event_assignments table created successfully<br>";
    
    // 2. Create session_assignments table (if not exists)
    echo "<h3>📋 Step 2: Creating session_assignments Table</h3>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS session_assignments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        session_id INT NOT NULL,
        assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_active TINYINT(1) DEFAULT 1,
        notes TEXT,
        INDEX idx_user_id (user_id),
        INDEX idx_session_id (session_id),
        INDEX idx_active (is_active),
        UNIQUE KEY unique_user_session (user_id, session_id, is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($sql);
    echo "✅ session_assignments table created successfully<br>";
    
    // 3. Check if events table exists, create if not
    echo "<h3>📋 Step 3: Ensuring events Table Exists</h3>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS events (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        event_date DATE NOT NULL,
        start_time TIME,
        end_time TIME,
        location VARCHAR(255),
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_event_date (event_date),
        INDEX idx_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($sql);
    echo "✅ events table created successfully<br>";
    
    // 4. Check if event_sessions table exists, create if not
    echo "<h3>📋 Step 4: Ensuring event_sessions Table Exists</h3>";
    
    $sql = "
    CREATE TABLE IF NOT EXISTS event_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        event_id INT NOT NULL,
        session_title VARCHAR(255) NOT NULL,
        description TEXT,
        start_datetime DATETIME NOT NULL,
        end_datetime DATETIME,
        location VARCHAR(255),
        max_participants INT,
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_event_id (event_id),
        INDEX idx_start_datetime (start_datetime),
        INDEX idx_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($sql);
    echo "✅ event_sessions table created successfully<br>";
    
    // 5. Add some sample data if tables are empty
    echo "<h3>📋 Step 5: Adding Sample Data (if needed)</h3>";
    
    // Check if events table is empty
    $stmt = $pdo->query("SELECT COUNT(*) FROM events");
    $event_count = $stmt->fetchColumn();
    
    if ($event_count == 0) {
        echo "Adding sample events...<br>";
        
        $sample_events = [
            [
                'title' => 'Sunday Service',
                'description' => 'Weekly Sunday worship service',
                'event_date' => date('Y-m-d', strtotime('next Sunday')),
                'start_time' => '10:00:00',
                'end_time' => '12:00:00',
                'location' => 'Main Sanctuary'
            ],
            [
                'title' => 'Bible Study',
                'description' => 'Weekly Bible study and discussion',
                'event_date' => date('Y-m-d', strtotime('next Wednesday')),
                'start_time' => '19:00:00',
                'end_time' => '20:30:00',
                'location' => 'Fellowship Hall'
            ],
            [
                'title' => 'Youth Meeting',
                'description' => 'Monthly youth gathering and activities',
                'event_date' => date('Y-m-d', strtotime('next Friday')),
                'start_time' => '18:00:00',
                'end_time' => '21:00:00',
                'location' => 'Youth Center'
            ]
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO events (title, description, event_date, start_time, end_time, location)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($sample_events as $event) {
            $stmt->execute([
                $event['title'],
                $event['description'],
                $event['event_date'],
                $event['start_time'],
                $event['end_time'],
                $event['location']
            ]);
        }
        
        echo "✅ Added " . count($sample_events) . " sample events<br>";
        
        // Add sample sessions for the first event
        $stmt = $pdo->prepare("
            INSERT INTO event_sessions (event_id, session_title, description, start_datetime, end_datetime, location)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $sample_sessions = [
            [
                'event_id' => 1,
                'session_title' => 'Opening Prayer',
                'description' => 'Service opening and welcome',
                'start_datetime' => date('Y-m-d 10:00:00', strtotime('next Sunday')),
                'end_datetime' => date('Y-m-d 10:15:00', strtotime('next Sunday')),
                'location' => 'Main Sanctuary'
            ],
            [
                'event_id' => 1,
                'session_title' => 'Worship & Music',
                'description' => 'Praise and worship time',
                'start_datetime' => date('Y-m-d 10:15:00', strtotime('next Sunday')),
                'end_datetime' => date('Y-m-d 10:45:00', strtotime('next Sunday')),
                'location' => 'Main Sanctuary'
            ],
            [
                'event_id' => 1,
                'session_title' => 'Main Message',
                'description' => 'Pastor\'s sermon and teaching',
                'start_datetime' => date('Y-m-d 10:45:00', strtotime('next Sunday')),
                'end_datetime' => date('Y-m-d 11:30:00', strtotime('next Sunday')),
                'location' => 'Main Sanctuary'
            ]
        ];
        
        foreach ($sample_sessions as $session) {
            $stmt->execute([
                $session['event_id'],
                $session['session_title'],
                $session['description'],
                $session['start_datetime'],
                $session['end_datetime'],
                $session['location']
            ]);
        }
        
        echo "✅ Added " . count($sample_sessions) . " sample sessions<br>";
    } else {
        echo "✅ Events table already has data (" . $event_count . " events)<br>";
    }
    
    // 6. Show table status
    echo "<h3>📋 Step 6: Table Status Summary</h3>";
    
    $tables = ['events', 'event_sessions', 'session_assignments', 'event_assignments'];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>Table</th><th>Exists</th><th>Record Count</th><th>Status</th></tr>";
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<tr>";
            echo "<td>$table</td>";
            echo "<td>✅ Yes</td>";
            echo "<td>$count</td>";
            echo "<td>✅ Ready</td>";
            echo "</tr>";
        } catch (PDOException $e) {
            echo "<tr>";
            echo "<td>$table</td>";
            echo "<td>❌ No</td>";
            echo "<td>-</td>";
            echo "<td>❌ Missing</td>";
            echo "</tr>";
        }
    }
    echo "</table>";
    
    echo "<h3>🎯 Summary</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>✅ Setup Complete!</strong></p>";
    echo "<ul>";
    echo "<li>✅ Created event_assignments table for event coordinators/organizers</li>";
    echo "<li>✅ Created session_assignments table for session moderators</li>";
    echo "<li>✅ Ensured events and event_sessions tables exist</li>";
    echo "<li>✅ Added sample data if tables were empty</li>";
    echo "<li>✅ Updated RBAC system to show all users in dropdowns</li>";
    echo "<li>✅ Created assignment_dashboard.php for users with assignments</li>";
    echo "<li>✅ Updated login redirection to check for assignments</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>📝 Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li><strong>Test RBAC System</strong> - Go to setup_rbac_system.php#assign-role</li>";
    echo "<li><strong>Assign Sessions/Events</strong> - All users now appear in dropdowns</li>";
    echo "<li><strong>Test User Login</strong> - Users with assignments will be redirected appropriately</li>";
    echo "<li><strong>Verify Assignment Dashboard</strong> - Check assignment_dashboard.php</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>❌ Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Stack Trace:</strong><br><pre>" . $e->getTraceAsString() . "</pre></p>";
    echo "</div>";
}

echo "<p>";
echo "<a href='setup_rbac_system.php#assign-role' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔧 Test RBAC System</a>";
echo "<a href='assignment_dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📊 Assignment Dashboard</a>";
echo "<a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 Main Dashboard</a>";
echo "</p>";
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h2, h3 { color: #333; }
    table { width: 100%; margin: 10px 0; }
    th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
    p { margin: 8px 0; }
    ul, ol { margin: 10px 0; padding-left: 20px; }
    li { margin: 5px 0; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>
