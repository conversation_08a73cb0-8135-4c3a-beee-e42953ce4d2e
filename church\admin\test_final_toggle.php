<?php
// Final test for toggle button visibility
session_start();
$_SESSION['admin_id'] = 4;
$_SESSION['admin_username'] = 'admin';
$_SESSION['admin_role'] = 'super_admin';

require_once '../config.php';
require_once 'includes/rbac_access_control_granular.php';

// Include functions
if (!function_exists('get_organization_name')) {
    function get_organization_name() {
        return 'Freedom Assembly Church';
    }
}

if (!function_exists('get_site_setting')) {
    function get_site_setting($key, $default = '') {
        return $default;
    }
}

if (!function_exists('admin_url_for')) {
    function admin_url_for($page) {
        return $page;
    }
}

if (!function_exists('is_active')) {
    function is_active($page) {
        return '';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Toggle Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/admin-style.css">
    <style>
        body { margin: 0; padding: 0; }
        .container-fluid { padding: 0; }
        .main-content { 
            margin-left: 250px; 
            padding: 20px; 
            transition: margin-left 0.3s ease;
        }
        .main-content.expanded { 
            margin-left: 60px; 
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar_rbac.php'; ?>
            
            <div class="main-content">
                <h1>🎯 Final Toggle Button Test</h1>
                
                <div class="alert alert-success">
                    <h4>✅ Fixed Issues:</h4>
                    <ul>
                        <li>✅ Removed conflicting CSS from sidebar_rbac.php</li>
                        <li>✅ Using external admin-style.css for toggle button styles</li>
                        <li>✅ Proper positioning: right: -15px when collapsed</li>
                        <li>✅ Correct transform: translateY(-50%) for vertical centering</li>
                    </ul>
                </div>
                
                <div class="alert alert-info">
                    <h4>🎨 Expected CSS Behavior:</h4>
                    <p><strong>Normal State:</strong></p>
                    <code>
                        .sidebar-toggle-btn {<br>
                        &nbsp;&nbsp;right: 10px;<br>
                        &nbsp;&nbsp;top: 50%;<br>
                        &nbsp;&nbsp;transform: translateY(-50%);<br>
                        }
                    </code>
                    
                    <p class="mt-3"><strong>Collapsed State:</strong></p>
                    <code>
                        .sidebar.collapsed .sidebar-toggle-btn {<br>
                        &nbsp;&nbsp;right: -15px;<br>
                        &nbsp;&nbsp;top: 50%;<br>
                        &nbsp;&nbsp;transform: translateY(-50%);<br>
                        }
                    </code>
                </div>
                
                <div class="alert alert-warning">
                    <h4>🧪 Test Instructions:</h4>
                    <ol>
                        <li><strong>Look for the toggle button</strong> in the sidebar header (chevron arrow)</li>
                        <li><strong>Click to collapse</strong> - button should move outside sidebar edge but remain visible</li>
                        <li><strong>Click to expand</strong> - button should return to normal position</li>
                        <li><strong>Check icon changes</strong> - left chevron ↔ right chevron</li>
                    </ol>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>🔧 Technical Summary</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Problem:</strong> Toggle button disappeared when sidebar collapsed</p>
                        <p><strong>Root Cause:</strong> Conflicting CSS positioning in sidebar_rbac.php</p>
                        <p><strong>Solution:</strong> Removed inline CSS, using external admin-style.css</p>
                        <p><strong>Result:</strong> Toggle button now properly positioned and always visible</p>
                    </div>
                </div>
                
                <div class="mt-4">
                    <button class="btn btn-primary" onclick="testToggle()">🧪 Test Toggle Function</button>
                    <button class="btn btn-info" onclick="checkButtonPosition()">📍 Check Button Position</button>
                </div>
                
                <div id="testOutput" class="mt-3"></div>
            </div>
        </div>
    </div>
    
    <script>
        function testToggle() {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.getElementById('sidebarToggleDesktop');
            const output = document.getElementById('testOutput');
            
            if (toggle) {
                toggle.click();
                setTimeout(() => {
                    const isCollapsed = sidebar.classList.contains('collapsed');
                    output.innerHTML = `
                        <div class="alert alert-success">
                            <h6>Toggle Test Result:</h6>
                            <p><strong>Sidebar State:</strong> ${isCollapsed ? 'Collapsed ✅' : 'Expanded ✅'}</p>
                            <p><strong>Toggle Function:</strong> Working ✅</p>
                        </div>
                    `;
                }, 300);
            } else {
                output.innerHTML = '<div class="alert alert-danger">❌ Toggle button not found!</div>';
            }
        }
        
        function checkButtonPosition() {
            const toggle = document.getElementById('sidebarToggleDesktop');
            const sidebar = document.getElementById('sidebar');
            const output = document.getElementById('testOutput');
            
            if (toggle) {
                const rect = toggle.getBoundingClientRect();
                const isCollapsed = sidebar.classList.contains('collapsed');
                const isVisible = rect.width > 0 && rect.height > 0 && rect.top >= 0;
                
                output.innerHTML = `
                    <div class="alert ${isVisible ? 'alert-success' : 'alert-danger'}">
                        <h6>Button Position Check:</h6>
                        <ul>
                            <li><strong>Visible:</strong> ${isVisible ? '✅ Yes' : '❌ No'}</li>
                            <li><strong>Sidebar State:</strong> ${isCollapsed ? 'Collapsed' : 'Expanded'}</li>
                            <li><strong>Position:</strong> ${rect.left.toFixed(0)}px, ${rect.top.toFixed(0)}px</li>
                            <li><strong>Size:</strong> ${rect.width.toFixed(0)}px × ${rect.height.toFixed(0)}px</li>
                        </ul>
                    </div>
                `;
            } else {
                output.innerHTML = '<div class="alert alert-danger">❌ Toggle button not found!</div>';
            }
        }
        
        // Auto-check on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkButtonPosition, 1000);
        });
    </script>
</body>
</html>
