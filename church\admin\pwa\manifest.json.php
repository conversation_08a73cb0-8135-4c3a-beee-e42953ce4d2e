<?php
/**
 * Dynamic PWA Manifest
 * Generates manifest.json with dynamic URLs and organization settings
 */

require_once '../../config.php';

// Get organization settings
$org_name = get_site_setting('organization_name', 'Universal Event Management Platform');
$org_type = get_site_setting('organization_type', 'church');
$primary_color = get_site_setting('primary_color', '#007bff');

// Set content type
header('Content-Type: application/json');

// Generate dynamic manifest
$manifest = [
    "name" => $org_name . " - Event Management",
    "short_name" => substr($org_name, 0, 12) ?: "EventManager",
    "description" => "Universal Event Management Platform for " . ucfirst($org_type) . " organizations",
    "start_url" => admin_url_for('pwa/'),
    "display" => "standalone",
    "background_color" => "#ffffff",
    "theme_color" => $primary_color,
    "orientation" => "portrait-primary",
    "scope" => admin_url_for(''),
    "lang" => "en",
    "dir" => "ltr",
    "categories" => ["productivity", "business", "utilities"],
    "screenshots" => [
        [
            "src" => admin_url_for('assets/images/screenshot-mobile.png'),
            "sizes" => "390x844",
            "type" => "image/png",
            "form_factor" => "narrow"
        ],
        [
            "src" => admin_url_for('assets/images/screenshot-desktop.png'),
            "sizes" => "1920x1080",
            "type" => "image/png",
            "form_factor" => "wide"
        ]
    ],
    "icons" => [
        [
            "src" => admin_url_for('assets/images/icon-72x72.png'),
            "sizes" => "72x72",
            "type" => "image/png",
            "purpose" => "maskable any"
        ],
        [
            "src" => admin_url_for('assets/images/icon-96x96.png'),
            "sizes" => "96x96",
            "type" => "image/png",
            "purpose" => "maskable any"
        ],
        [
            "src" => admin_url_for('assets/images/icon-128x128.png'),
            "sizes" => "128x128",
            "type" => "image/png",
            "purpose" => "maskable any"
        ],
        [
            "src" => admin_url_for('assets/images/icon-144x144.png'),
            "sizes" => "144x144",
            "type" => "image/png",
            "purpose" => "maskable any"
        ],
        [
            "src" => admin_url_for('assets/images/icon-152x152.png'),
            "sizes" => "152x152",
            "type" => "image/png",
            "purpose" => "maskable any"
        ],
        [
            "src" => admin_url_for('assets/images/icon-192x192.png'),
            "sizes" => "192x192",
            "type" => "image/png",
            "purpose" => "maskable any"
        ],
        [
            "src" => admin_url_for('assets/images/icon-384x384.png'),
            "sizes" => "384x384",
            "type" => "image/png",
            "purpose" => "maskable any"
        ],
        [
            "src" => admin_url_for('assets/images/icon-512x512.png'),
            "sizes" => "512x512",
            "type" => "image/png",
            "purpose" => "maskable any"
        ]
    ],
    "shortcuts" => [
        [
            "name" => "Events",
            "short_name" => "Events",
            "description" => "View and manage events",
            "url" => admin_url_for('events.php'),
            "icons" => [
                [
                    "src" => admin_url_for('assets/images/icon-192x192.png'),
                    "sizes" => "192x192"
                ]
            ]
        ],
        [
            "name" => "AI Predictions",
            "short_name" => "AI",
            "description" => "View AI attendance predictions",
            "url" => admin_url_for('ai/universal_ai_dashboard.php'),
            "icons" => [
                [
                    "src" => admin_url_for('assets/images/icon-192x192.png'),
                    "sizes" => "192x192"
                ]
            ]
        ],
        [
            "name" => "Analytics",
            "short_name" => "Analytics",
            "description" => "View comprehensive analytics",
            "url" => admin_url_for('universal_analytics_dashboard.php'),
            "icons" => [
                [
                    "src" => admin_url_for('assets/images/icon-192x192.png'),
                    "sizes" => "192x192"
                ]
            ]
        ],
        [
            "name" => "Real-Time Dashboard",
            "short_name" => "Live",
            "description" => "Real-time event coordination",
            "url" => admin_url_for('realtime_dashboard.php'),
            "icons" => [
                [
                    "src" => admin_url_for('assets/images/icon-192x192.png'),
                    "sizes" => "192x192"
                ]
            ]
        ]
    ],
    "related_applications" => [],
    "prefer_related_applications" => false,
    "edge_side_panel" => [
        "preferred_width" => 400
    ],
    "launch_handler" => [
        "client_mode" => "navigate-existing"
    ]
];

echo json_encode($manifest, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
?>
