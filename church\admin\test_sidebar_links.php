<?php
/**
 * Test Sidebar Links - Verify all sidebar links are working correctly
 * This script tests the sidebar link fixes
 */

// Include the configuration file
require_once '../config.php';
require_once 'includes/rbac_access_control.php';

echo "<h2>Testing Sidebar Link Fixes</h2>\n";

try {
    // Define the page permissions directly for testing
    $page_permissions = [
        'notifications.php' => ['super_admin', 'limited_admin', 'event_coordinator', 'session_moderator', 'staff'],
        'logo_management_consolidated.php' => ['super_admin'],
        'social_media_integration.php' => ['super_admin'],
        'settings.php' => ['super_admin', 'limited_admin'],
        'appearance_settings.php' => ['super_admin'],
        'branding_settings.php' => ['super_admin'],
        'security_audit.php' => ['super_admin'],
        'security_settings.php' => ['super_admin'],
        'backup_management.php' => ['super_admin'],
        'profile.php' => ['super_admin', 'limited_admin', 'event_coordinator', 'organizer', 'session_moderator', 'staff']
    ];

    // Test 1: Check if Logo Management link is correct
    echo "<h3>1. Logo Management Link Test</h3>\n";

    $logo_file_exists = file_exists(__DIR__ . '/logo_management_consolidated.php');
    $logo_permission_exists = array_key_exists('logo_management_consolidated.php', $page_permissions);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr style='background-color: #f0f0f0;'><th style='padding: 8px;'>Test</th><th style='padding: 8px;'>Status</th><th style='padding: 8px;'>Details</th></tr>\n";
    
    echo "<tr>";
    echo "<td style='padding: 8px;'>File Exists</td>";
    echo "<td style='padding: 8px; color: " . ($logo_file_exists ? 'green' : 'red') . ";'>" . ($logo_file_exists ? '✓ Pass' : '✗ Fail') . "</td>";
    echo "<td style='padding: 8px;'>logo_management_consolidated.php</td>";
    echo "</tr>\n";
    
    echo "<tr>";
    echo "<td style='padding: 8px;'>Permission Defined</td>";
    echo "<td style='padding: 8px; color: " . ($logo_permission_exists ? 'green' : 'red') . ";'>" . ($logo_permission_exists ? '✓ Pass' : '✗ Fail') . "</td>";
    echo "<td style='padding: 8px;'>RBAC permissions configured</td>";
    echo "</tr>\n";
    
    echo "</table>\n";
    
    // Test 2: Check Social Media link
    echo "<h3>2. Social Media Link Test</h3>\n";
    
    $social_file_exists = file_exists(__DIR__ . '/social_media_integration.php');
    $social_permission_exists = array_key_exists('social_media_integration.php', $page_permissions);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr style='background-color: #f0f0f0;'><th style='padding: 8px;'>Test</th><th style='padding: 8px;'>Status</th><th style='padding: 8px;'>Details</th></tr>\n";
    
    echo "<tr>";
    echo "<td style='padding: 8px;'>File Exists</td>";
    echo "<td style='padding: 8px; color: " . ($social_file_exists ? 'green' : 'red') . ";'>" . ($social_file_exists ? '✓ Pass' : '✗ Fail') . "</td>";
    echo "<td style='padding: 8px;'>social_media_integration.php</td>";
    echo "</tr>\n";
    
    echo "<tr>";
    echo "<td style='padding: 8px;'>Permission Defined</td>";
    echo "<td style='padding: 8px; color: " . ($social_permission_exists ? 'green' : 'red') . ";'>" . ($social_permission_exists ? '✓ Pass' : '✗ Fail') . "</td>";
    echo "<td style='padding: 8px;'>RBAC permissions configured</td>";
    echo "</tr>\n";
    
    echo "</table>\n";
    
    // Test 3: Check other important links
    echo "<h3>3. Other Important Links Test</h3>\n";
    
    $test_files = [
        'notifications.php' => 'Notifications',
        'settings.php' => 'Settings',
        'appearance_settings.php' => 'Appearance',
        'branding_settings.php' => 'Branding',
        'security_audit.php' => 'Security Audit',
        'security_settings.php' => 'Security Settings',
        'backup_management.php' => 'Database Backup',
        'profile.php' => 'My Profile'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>\n";
    echo "<tr style='background-color: #f0f0f0;'><th style='padding: 8px;'>Page</th><th style='padding: 8px;'>File Exists</th><th style='padding: 8px;'>Permission</th><th style='padding: 8px;'>URL</th></tr>\n";
    
    foreach ($test_files as $file => $name) {
        $file_exists = file_exists(__DIR__ . '/' . $file);
        $permission_exists = array_key_exists($file, $page_permissions);
        $url = admin_url_for($file);
        
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($name) . "</td>";
        echo "<td style='padding: 8px; color: " . ($file_exists ? 'green' : 'red') . ";'>" . ($file_exists ? '✓' : '✗') . "</td>";
        echo "<td style='padding: 8px; color: " . ($permission_exists ? 'green' : 'red') . ";'>" . ($permission_exists ? '✓' : '✗') . "</td>";
        echo "<td style='padding: 8px;'><a href='" . htmlspecialchars($url) . "' target='_blank'>" . htmlspecialchars($url) . "</a></td>";
        echo "</tr>\n";
    }
    
    echo "</table>\n";
    
    // Test 4: Check admin_url_for function
    echo "<h3>4. admin_url_for Function Test</h3>\n";
    
    $test_urls = [
        'notifications.php',
        'logo_management_consolidated.php',
        'social_media_integration.php',
        'settings.php',
        'dashboard.php'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>\n";
    echo "<tr style='background-color: #f0f0f0;'><th style='padding: 8px;'>Input</th><th style='padding: 8px;'>Generated URL</th><th style='padding: 8px;'>Status</th></tr>\n";
    
    foreach ($test_urls as $page) {
        $url = admin_url_for($page);
        $is_correct = strpos($url, $page) !== false;
        
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($page) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($url) . "</td>";
        echo "<td style='padding: 8px; color: " . ($is_correct ? 'green' : 'red') . ";'>" . ($is_correct ? '✓ Correct' : '✗ Incorrect') . "</td>";
        echo "</tr>\n";
    }
    
    echo "</table>\n";
    
    // Test 5: Check ADMIN_URL constant
    echo "<h3>5. ADMIN_URL Constant Test</h3>\n";
    
    $admin_url_defined = defined('ADMIN_URL');
    $admin_url_value = $admin_url_defined ? ADMIN_URL : 'Not defined';
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr style='background-color: #f0f0f0;'><th style='padding: 8px;'>Test</th><th style='padding: 8px;'>Status</th><th style='padding: 8px;'>Value</th></tr>\n";
    
    echo "<tr>";
    echo "<td style='padding: 8px;'>ADMIN_URL Defined</td>";
    echo "<td style='padding: 8px; color: " . ($admin_url_defined ? 'green' : 'red') . ";'>" . ($admin_url_defined ? '✓ Yes' : '✗ No') . "</td>";
    echo "<td style='padding: 8px;'>" . htmlspecialchars($admin_url_value) . "</td>";
    echo "</tr>\n";
    
    echo "</table>\n";
    
    // Summary
    echo "<h3>6. Summary</h3>\n";
    
    $all_tests_passed = $logo_file_exists && $logo_permission_exists && $social_file_exists && $social_permission_exists && $admin_url_defined;
    
    if ($all_tests_passed) {
        echo "<div style='background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
        echo "<h4>✅ All Tests Passed!</h4>\n";
        echo "<p>The sidebar link fixes are working correctly:</p>\n";
        echo "<ul>\n";
        echo "<li>✅ Notifications link restored with bell icon</li>\n";
        echo "<li>✅ Logo Management link points to correct file</li>\n";
        echo "<li>✅ Social Media link points to correct file</li>\n";
        echo "<li>✅ RBAC permissions are properly configured</li>\n";
        echo "<li>✅ admin_url_for function is working correctly</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
    } else {
        echo "<div style='background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
        echo "<h4>⚠️ Some Tests Failed</h4>\n";
        echo "<p>Please check the failed tests above and ensure all files exist and permissions are configured correctly.</p>\n";
        echo "</div>\n";
    }
    
    echo "<p><a href='dashboard.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Go to Dashboard</a></p>\n";
    echo "<p><a href='create_admin_users.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Test Create Admin Users</a></p>\n";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</h3>\n";
}
?>
