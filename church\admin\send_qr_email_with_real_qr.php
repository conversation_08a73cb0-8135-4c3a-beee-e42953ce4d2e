<?php
// QR Code Email with REAL QR Code Images
require_once '../config.php';
require_once '../vendor/autoload.php';

use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;

echo "<h1>📧 Sending QR Code Email with REAL QR Images</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .step{background:#f5f5f5;padding:10px;margin:10px 0;border-left:4px solid #007bff;}</style>";

$target_email = '<EMAIL>';

try {
    echo "<div class='step'>";
    echo "<h2>Step 1: Load Data</h2>";
    
    // Load SMTP settings
    $emailSettings = [];
    $stmt = $pdo->query("SELECT * FROM email_settings");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $emailSettings[$row['setting_key']] = $row['setting_value'];
    }
    
    // Get member and QR data
    $stmt = $pdo->prepare("
        SELECT m.*, mqr.*, e.title as event_title, e.event_date, e.location as event_location
        FROM members m
        JOIN member_qr_codes mqr ON m.email = mqr.attendee_email
        JOIN events e ON mqr.event_id = e.id
        WHERE m.email = ?
        ORDER BY mqr.created_at DESC
        LIMIT 1
    ");
    $stmt->execute([$target_email]);
    $data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$data) {
        throw new Exception("Member or QR code not found for {$target_email}");
    }
    
    echo "<span class='success'>✅ Data loaded for: {$data['full_name']}</span><br>";
    echo "<span class='info'>🎫 QR Token: {$data['qr_token']}</span><br>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 2: Generate QR Code Image</h2>";
    
    // Generate QR URL
    $base_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
    $qr_url = $base_url . "/member_checkin.php?token=" . $data['qr_token'];
    
    // Create QR code
    $qrCode = new QrCode($qr_url);
    $qrCode->setSize(300);
    $qrCode->setMargin(10);
    
    $writer = new PngWriter();
    $result = $writer->write($qrCode);
    
    // Convert to base64 for email embedding
    $qrCodeBase64 = base64_encode($result->getString());
    $qrCodeDataUri = 'data:image/png;base64,' . $qrCodeBase64;
    
    echo "<span class='success'>✅ QR Code image generated successfully</span><br>";
    echo "<span class='info'>🔗 QR URL: <a href='{$qr_url}' target='_blank'>{$qr_url}</a></span><br>";
    echo "<span class='info'>📏 QR Code Size: 300x300 pixels</span><br>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 3: Create and Send Email with Real QR Code</h2>";
    
    // Create PHPMailer instance
    $mail = new PHPMailer(true);
    
    // Server settings
    $mail->isSMTP();
    $mail->Host       = $emailSettings['smtp_host'];
    $mail->SMTPAuth   = true;
    $mail->Username   = $emailSettings['smtp_username'];
    $mail->Password   = $emailSettings['smtp_password'];
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
    $mail->Port       = $emailSettings['smtp_port'];
    
    // Recipients
    $mail->setFrom($emailSettings['sender_email'], $emailSettings['sender_name']);
    $mail->addAddress($target_email, $data['full_name']);
    $mail->addReplyTo($emailSettings['reply_to_email'], $emailSettings['sender_name']);
    
    // Content
    $mail->isHTML(true);
    $mail->Subject = 'Your Event Check-in QR Code - ' . $data['event_title'];
    
    // HTML email body with embedded QR code
    $mail->Body = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>Event Check-in QR Code</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; }
            .header h1 { margin: 0; font-size: 24px; }
            .content { padding: 30px 20px; background: white; }
            .checkin-section { text-align: center; margin: 30px 0; padding: 30px; background: #e8f5e8; border-radius: 15px; border: 3px solid #28a745; }
            .checkin-button { display: inline-block; padding: 20px 40px; background: #28a745; color: white; text-decoration: none; border-radius: 10px; font-size: 20px; font-weight: bold; margin: 20px 0; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
            .qr-code-box { background: white; border: 3px solid #007bff; padding: 30px; margin: 20px 0; border-radius: 10px; text-align: center; }
            .qr-image { max-width: 250px; height: auto; border: 2px solid #ddd; border-radius: 8px; }
            .qr-token { font-size: 16px; font-weight: bold; color: #007bff; letter-spacing: 1px; margin-top: 15px; }
            .instructions { background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3; }
            .instructions h3 { margin-top: 0; color: #1976d2; }
            .event-details { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px; }
            .highlight { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Your Event Check-in QR Code</h1>
                <p>Freedom Assembly Church</p>
            </div>
            
            <div class='content'>
                <h2>Hello " . htmlspecialchars($data['full_name']) . "!</h2>
                <p>You're all set for <strong>" . htmlspecialchars($data['event_title']) . "</strong>! Here's your personal QR code for quick check-in.</p>
                
                <div class='event-details'>
                    <h3>Event Details</h3>
                    <p><strong>Event:</strong> " . htmlspecialchars($data['event_title']) . "</p>
                    <p><strong>Date:</strong> " . date('F j, Y g:i A', strtotime($data['event_date'])) . "</p>
                    <p><strong>Location:</strong> " . htmlspecialchars($data['event_location']) . "</p>
                </div>
                
                <div class='checkin-section'>
                    <h2>INSTANT CHECK-IN</h2>
                    <p style='font-size: 18px; margin: 20px 0;'><strong>Click the button below OR scan the QR code:</strong></p>
                    
                    <a href='{$qr_url}' class='checkin-button' style='color: white; text-decoration: none;'>
                        CHECK IN NOW
                    </a>
                    
                    <div class='qr-code-box'>
                        <h3>Your Personal QR Code</h3>
                        <img src='{$qrCodeDataUri}' alt='QR Code for Check-in' class='qr-image' />
                        <div class='qr-token'>{$data['qr_token']}</div>
                        <p style='margin: 15px 0 5px 0; font-size: 14px; color: #666;'>Scan this QR code or show it to staff</p>
                    </div>
                </div>
                
                <div class='instructions'>
                    <h3>Three Ways to Check In</h3>
                    <ol style='font-size: 16px; line-height: 1.6;'>
                        <li><strong>EASIEST:</strong> Click the \"CHECK IN NOW\" button above</li>
                        <li><strong>SCAN:</strong> Show the QR code above to staff for scanning</li>
                        <li><strong>MANUAL:</strong> Provide your name at the check-in desk</li>
                    </ol>
                </div>
                
                <div class='highlight'>
                    <h3>Super Fast Check-in Process</h3>
                    <p><strong>Two instant options:</strong> Click the button for immediate check-in, or show your QR code to staff for scanning. Both work perfectly!</p>
                </div>
                
                <p style='font-size: 16px; margin: 30px 0;'>We're excited to see you at the event! The new check-in system will get you inside quickly so you don't miss anything.</p>
                
                <p><strong>Blessings,</strong><br>
                Freedom Assembly Church Event Team</p>
            </div>
            
            <div class='footer'>
                <p>This is an automated message from Freedom Assembly Church</p>
                <p>Event Management System | QR Code Check-in</p>
                <p><strong>Direct Check-in Link:</strong><br>
                <a href='{$qr_url}' style='color: #007bff; word-break: break-all;'>{$qr_url}</a></p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    // Plain text version
    $mail->AltBody = "
Hello " . $data['full_name'] . "!

You're all set for " . $data['event_title'] . "!

EVENT DETAILS:
Event: " . $data['event_title'] . "
Date: " . date('F j, Y g:i A', strtotime($data['event_date'])) . "
Location: " . $data['event_location'] . "

INSTANT CHECK-IN:
Click this link to check in: {$qr_url}

YOUR CHECK-IN CODE: " . $data['qr_token'] . "

THREE WAYS TO CHECK IN:
1. EASIEST: Click the check-in link above
2. SCAN: Show your QR code to staff for scanning
3. MANUAL: Provide your name at check-in desk

If you have any issues, just show this email to staff or tell them your check-in code.

Blessings,
Freedom Assembly Church Event Team
    ";
    
    echo "<span class='info'>📧 Sending email with real QR code to: {$target_email}</span><br>";
    echo "<span class='info'>📋 Subject: {$mail->Subject}</span><br>";
    
    // Send the email
    $mail->send();
    
    echo "<span class='success'>✅ Email with real QR code sent successfully!</span><br>";
    
    // Update database
    $stmt = $pdo->prepare("
        UPDATE member_qr_codes 
        SET email_sent = 1, email_sent_at = NOW() 
        WHERE qr_token = ?
    ");
    $stmt->execute([$data['qr_token']]);
    
    echo "<span class='success'>✅ Database updated</span><br>";
    echo "</div>";
    
    echo "<div class='step' style='background:#e8f5e8;border-color:#28a745;'>";
    echo "<h2>🎉 SUCCESS! Real QR Code Email Sent</h2>";
    echo "<span class='success'>✅ Email sent to {$target_email} with REAL QR code image</span><br>";
    echo "<span class='success'>✅ QR code is embedded as base64 image (works in all email clients)</span><br>";
    echo "<span class='success'>✅ Clean subject line without encoding issues</span><br>";
    echo "<span class='success'>✅ Large, scannable QR code (300x300 pixels)</span><br>";
    echo "<span class='success'>✅ Multiple check-in options provided</span><br>";
    echo "<br>";
    echo "<strong>📧 The member will receive:</strong><br>";
    echo "<span class='info'>• Clean subject line: \"Your Event Check-in QR Code - Evening Service\"</span><br>";
    echo "<span class='info'>• <strong>LARGE \"CHECK IN NOW\" BUTTON</strong></span><br>";
    echo "<span class='info'>• <strong>REAL, SCANNABLE QR CODE IMAGE</strong></span><br>";
    echo "<span class='info'>• Check-in code clearly displayed</span><br>";
    echo "<span class='info'>• Three different ways to check in</span><br>";
    echo "<span class='info'>• Professional church branding</span><br>";
    echo "<br>";
    echo "<strong>🎯 Next Steps:</strong><br>";
    echo "<span class='info'>1. Check {$target_email} for the new email with REAL QR code</span><br>";
    echo "<span class='info'>2. You should see an actual QR code image (not placeholder)</span><br>";
    echo "<span class='info'>3. Test both the button and QR code scanning</span><br>";
    echo "<span class='info'>4. System is ready for production with real QR codes!</span><br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='step' style='background:#ffe8e8;border-color:#dc3545;'>";
    echo "<h2>❌ Email Sending Failed</h2>";
    echo "<span class='error'>Error: " . $e->getMessage() . "</span><br>";
    if (isset($mail)) {
        echo "<span class='error'>PHPMailer Error: " . $mail->ErrorInfo . "</span><br>";
    }
    echo "</div>";
}
?>

<script>
console.log('Real QR Code email sending completed');
</script>
