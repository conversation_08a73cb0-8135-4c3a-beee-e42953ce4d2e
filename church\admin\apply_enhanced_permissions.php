<?php
/**
 * Apply Enhanced Granular Permissions
 * This script applies comprehensive permissions covering all admin functionality
 */

session_start();
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/includes/route_protection.php';

// Protect this page - Super Admin only
protectSuperAdminRoute();

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['apply_permissions'])) {
    try {
        // Read and execute the enhanced permissions SQL
        $sql_file = __DIR__ . '/enhanced_granular_permissions.sql';
        
        if (!file_exists($sql_file)) {
            throw new Exception('Enhanced permissions SQL file not found');
        }
        
        $sql_content = file_get_contents($sql_file);
        
        // Split SQL into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql_content)));
        
        $pdo->beginTransaction();
        
        $executed_count = 0;
        foreach ($statements as $statement) {
            if (!empty($statement) && !str_starts_with(trim($statement), '--')) {
                $pdo->exec($statement);
                $executed_count++;
            }
        }
        
        $pdo->commit();
        
        $message = "Enhanced permissions applied successfully! Executed {$executed_count} SQL statements.";
        
        // Log the action
        error_log("Enhanced permissions applied by admin ID: " . $_SESSION['admin_id']);
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        $error = "Error applying enhanced permissions: " . $e->getMessage();
        error_log("Error applying enhanced permissions: " . $e->getMessage());
    }
}

// Check current system status
$stats = [];
try {
    // Count categories
    $stmt = $pdo->query("SELECT COUNT(*) FROM permission_categories WHERE is_active = 1");
    $stats['categories'] = $stmt->fetchColumn();
    
    // Count permissions
    $stmt = $pdo->query("SELECT COUNT(*) FROM granular_permissions WHERE is_active = 1");
    $stats['permissions'] = $stmt->fetchColumn();
    
    // Count admin users
    $stmt = $pdo->query("SELECT COUNT(*) FROM admins");
    $stats['admin_users'] = $stmt->fetchColumn();
    
    // Count active permission assignments
    $stmt = $pdo->query("SELECT COUNT(*) FROM user_individual_permissions WHERE is_active = 1");
    $stats['active_assignments'] = $stmt->fetchColumn();
    
} catch (Exception $e) {
    $stats = ['categories' => 0, 'permissions' => 0, 'admin_users' => 0, 'active_assignments' => 0];
}

// Get sample of current permissions by category
$current_permissions = [];
try {
    $stmt = $pdo->query("
        SELECT 
            pc.category_display_name,
            COUNT(gp.id) as permission_count
        FROM permission_categories pc
        LEFT JOIN granular_permissions gp ON pc.id = gp.category_id AND gp.is_active = 1
        WHERE pc.is_active = 1
        GROUP BY pc.id, pc.category_display_name
        ORDER BY pc.sort_order
    ");
    $current_permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $current_permissions = [];
}

$page_title = 'Apply Enhanced Permissions';
$page_header = 'Enhanced Permission System Setup';
$page_description = 'Apply comprehensive permissions covering all admin functionality';

include __DIR__ . '/includes/header.php';
?>

<style>
.setup-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
}

.stats-card {
    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.permission-preview {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.category-item {
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    display: flex;
    justify-content-between;
    align-items: center;
}

.apply-section {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="setup-header p-4 text-center">
            <h2 class="mb-2">
                <i class="bi bi-gear-wide-connected"></i> <?php echo $page_header; ?>
            </h2>
            <p class="mb-0"><?php echo $page_description; ?></p>
        </div>
    </div>
</div>

<!-- Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Current System Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card text-center">
            <h3><?php echo $stats['categories']; ?></h3>
            <p class="mb-0">Permission Categories</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <h3><?php echo $stats['permissions']; ?></h3>
            <p class="mb-0">Total Permissions</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <h3><?php echo $stats['admin_users']; ?></h3>
            <p class="mb-0">Admin Users</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <h3><?php echo $stats['active_assignments']; ?></h3>
            <p class="mb-0">Active Assignments</p>
        </div>
    </div>
</div>

<!-- Current Permissions Overview -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list-check"></i> Current Permission Categories
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($current_permissions)): ?>
                    <div class="row">
                        <?php foreach ($current_permissions as $category): ?>
                            <div class="col-md-6 col-lg-4 mb-2">
                                <div class="category-item">
                                    <span><?php echo htmlspecialchars($category['category_display_name']); ?></span>
                                    <span class="badge bg-primary"><?php echo $category['permission_count']; ?> permissions</span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-exclamation-triangle fs-3 d-block mb-2"></i>
                        <p>No permission categories found. The system needs to be set up.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Permissions Preview -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-stars"></i> Enhanced Permissions Preview
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted mb-3">The enhanced permission system will add comprehensive coverage for:</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="permission-preview">
                            <h6><i class="bi bi-speedometer2"></i> Dashboard Access</h6>
                            <small>Main dashboard, analytics, realtime tracking, super admin dashboard</small>
                        </div>
                        <div class="permission-preview">
                            <h6><i class="bi bi-people"></i> Member Management</h6>
                            <small>Members, profiles, families, skills, requests, volunteers</small>
                        </div>
                        <div class="permission-preview">
                            <h6><i class="bi bi-calendar-event"></i> Event Management</h6>
                            <small>Events, sessions, attendance, categories, reports, realtime tracking</small>
                        </div>
                        <div class="permission-preview">
                            <h6><i class="bi bi-envelope"></i> Email & Communication</h6>
                            <small>Bulk email, templates, contacts, birthday messages, WhatsApp</small>
                        </div>
                        <div class="permission-preview">
                            <h6><i class="bi bi-currency-dollar"></i> Donations & Finance</h6>
                            <small>Donations, gifts, payments, enhanced features</small>
                        </div>
                        <div class="permission-preview">
                            <h6><i class="bi bi-link-45deg"></i> Integrations</h6>
                            <small>Calendar integration, social media, third-party APIs</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="permission-preview">
                            <h6><i class="bi bi-gear"></i> System Settings</h6>
                            <small>General settings, organization setup, appearance, security, custom fields</small>
                        </div>
                        <div class="permission-preview">
                            <h6><i class="bi bi-graph-up"></i> Reports & Analytics</h6>
                            <small>Universal analytics, event reports, member reports, exports</small>
                        </div>
                        <div class="permission-preview">
                            <h6><i class="bi bi-bell"></i> Notifications</h6>
                            <small>View notifications, manage alerts and system messages</small>
                        </div>
                        <div class="permission-preview">
                            <h6><i class="bi bi-shield-check"></i> Admin Management</h6>
                            <small>RBAC management, create users, permissions, system setup</small>
                        </div>
                        <div class="permission-preview">
                            <h6><i class="bi bi-robot"></i> AI & Predictions</h6>
                            <small>AI-powered predictions, analytics, and insights</small>
                        </div>
                        <div class="permission-preview">
                            <h6><i class="bi bi-chat-dots"></i> SMS Management</h6>
                            <small>Single SMS, bulk SMS, templates, analytics</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Apply Enhanced Permissions -->
<div class="apply-section">
    <h5><i class="bi bi-rocket-takeoff"></i> Apply Enhanced Permissions</h5>
    <p>This will add comprehensive permissions covering all admin functionality. Existing permissions will be preserved.</p>
    
    <div class="alert alert-info">
        <i class="bi bi-info-circle"></i>
        <strong>What this will do:</strong>
        <ul class="mb-0 mt-2">
            <li>Add 12 comprehensive permission categories</li>
            <li>Add 80+ granular permissions covering all admin pages</li>
            <li>Preserve existing permission assignments</li>
            <li>Enable fine-grained access control for all functionality</li>
        </ul>
    </div>
    
    <form method="POST" onsubmit="return confirm('Are you sure you want to apply the enhanced permissions? This action cannot be undone.');">
        <button type="submit" name="apply_permissions" class="btn btn-primary btn-lg">
            <i class="bi bi-gear-wide-connected"></i> Apply Enhanced Permissions
        </button>
        <a href="manage_user_permissions.php" class="btn btn-secondary btn-lg ms-2">
            <i class="bi bi-arrow-left"></i> Back to Permission Management
        </a>
    </form>
</div>

<?php include __DIR__ . '/includes/footer.php'; ?>
