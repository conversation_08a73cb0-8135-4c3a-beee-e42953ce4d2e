<?php
session_start();

// Include the configuration file
require_once '../config.php';

$token = $_GET['token'] ?? '';
$event_id = $_GET['event_id'] ?? '';
$message = '';
$error = '';
$attendee_data = null;

// If token is provided, validate and process check-in
if (!empty($token)) {
    try {
        $stmt = $pdo->prepare("
            SELECT 
                mqr.*,
                e.title as event_title,
                e.event_date,
                e.location as event_location
            FROM member_qr_codes mqr
            JOIN events e ON mqr.event_id = e.id
            WHERE mqr.qr_token = ?
        ");
        $stmt->execute([$token]);
        $attendee_data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$attendee_data) {
            $error = "Invalid QR code. Please contact event staff.";
        } elseif ($attendee_data['is_used']) {
            $message = "Welcome back! You were already checked in at " . 
                      date('M j, Y g:i A', strtotime($attendee_data['used_at']));
        } else {
            // Mark as checked in
            $pdo->beginTransaction();
            
            // Update QR code as used
            $stmt = $pdo->prepare("
                UPDATE member_qr_codes 
                SET is_used = 1, used_at = NOW() 
                WHERE qr_token = ?
            ");
            $stmt->execute([$token]);
            
            // Mark attendance in event_rsvps (check table structure)
            if ($attendee_data['attendee_type'] === 'member' && $attendee_data['member_id']) {
                // Check if event_rsvps has member_id or user_id column
                $stmt = $pdo->query("DESCRIBE event_rsvps");
                $rsvp_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

                if (in_array('member_id', $rsvp_columns)) {
                    $stmt = $pdo->prepare("
                        UPDATE event_rsvps
                        SET actually_attended = 1
                        WHERE event_id = ? AND member_id = ?
                    ");
                    $stmt->execute([$attendee_data['event_id'], $attendee_data['member_id']]);
                } elseif (in_array('user_id', $rsvp_columns)) {
                    $stmt = $pdo->prepare("
                        UPDATE event_rsvps
                        SET actually_attended = 1
                        WHERE event_id = ? AND user_id = ?
                    ");
                    $stmt->execute([$attendee_data['event_id'], $attendee_data['member_id']]);
                }
            } else {
                // Handle guest check-in if guest table exists
                $stmt = $pdo->query("SHOW TABLES LIKE 'event_rsvps_guests'");
                if ($stmt->rowCount() > 0) {
                    $stmt = $pdo->prepare("
                        UPDATE event_rsvps_guests
                        SET actually_attended = 1
                        WHERE event_id = ? AND guest_email = ?
                    ");
                    $stmt->execute([$attendee_data['event_id'], $attendee_data['guest_email']]);
                }
            }
            
            $pdo->commit();
            $message = "✅ Successfully checked in! Welcome to the event.";
        }
        
    } catch (PDOException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $error = "Database error: " . $e->getMessage();
    }
}

// Handle manual QR scanner interface
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'manual_checkin') {
            $qr_token = trim($_POST['qr_token'] ?? '');
            
            if (empty($qr_token)) {
                throw new Exception("Please enter a QR code token.");
            }
            
            // Redirect to process the QR code
            header("Location: member_checkin.php?token=" . urlencode($qr_token));
            exit();
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get event details if event_id is provided
$event = null;
if (!empty($event_id)) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
        $stmt->execute([$event_id]);
        $event = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error loading event: " . $e->getMessage());
    }
}

// Get recent check-ins for this event
$recent_checkins = [];
if ($event) {
    try {
        $stmt = $pdo->prepare("
            SELECT * FROM member_qr_codes 
            WHERE event_id = ? AND is_used = 1 
            ORDER BY used_at DESC 
            LIMIT 10
        ");
        $stmt->execute([$event['id']]);
        $recent_checkins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error loading recent check-ins: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Check-in Scanner</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-size: 1.1rem;
        }
        .scanner-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .scanner-card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border: none;
        }
        .scanner-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 20px;
        }
        .checkin-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin: 20px 0;
        }
        .checkin-error {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin: 20px 0;
        }
        .scanner-interface {
            padding: 30px;
            text-align: center;
        }
        .qr-scanner-area {
            border: 3px dashed #007bff;
            border-radius: 15px;
            padding: 40px;
            margin: 20px 0;
            background: #f8f9ff;
        }
        .recent-checkin {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            background: white;
            transition: all 0.3s ease;
        }
        .recent-checkin:hover {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        .manual-input {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        @media (max-width: 576px) {
            .scanner-container {
                padding: 10px;
            }
            .scanner-interface {
                padding: 20px;
            }
        }
    </style>
</head>
<body>

<div class="scanner-container">
    <?php if ($attendee_data): ?>
        <!-- Check-in Result -->
        <div class="card scanner-card">
            <?php if ($message): ?>
                <div class="checkin-success">
                    <i class="bi bi-check-circle" style="font-size: 4rem;"></i>
                    <h2 class="mt-3">Check-in Successful!</h2>
                    <h4><?php echo htmlspecialchars($attendee_data['attendee_name']); ?></h4>
                    <p class="mb-0"><?php echo htmlspecialchars($message); ?></p>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="checkin-error">
                    <i class="bi bi-exclamation-triangle" style="font-size: 4rem;"></i>
                    <h2 class="mt-3">Check-in Error</h2>
                    <p class="mb-0"><?php echo htmlspecialchars($error); ?></p>
                </div>
            <?php endif; ?>
            
            <div class="card-body">
                <h5><i class="bi bi-calendar-event"></i> Event Details</h5>
                <div class="row">
                    <div class="col-md-6">
                        <strong>Event:</strong> <?php echo htmlspecialchars($attendee_data['event_title']); ?><br>
                        <strong>Date:</strong> <?php echo date('F j, Y g:i A', strtotime($attendee_data['event_date'])); ?><br>
                        <strong>Location:</strong> <?php echo htmlspecialchars($attendee_data['event_location']); ?>
                    </div>
                    <div class="col-md-6">
                        <strong>Attendee:</strong> <?php echo htmlspecialchars($attendee_data['attendee_name']); ?><br>
                        <strong>Email:</strong> <?php echo htmlspecialchars($attendee_data['attendee_email']); ?><br>
                        <strong>Type:</strong> <?php echo ucfirst($attendee_data['attendee_type']); ?>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <a href="member_checkin.php?event_id=<?php echo $attendee_data['event_id']; ?>" class="btn btn-primary btn-lg">
                        <i class="bi bi-arrow-left"></i> Back to Scanner
                    </a>
                </div>
            </div>
        </div>
        
    <?php else: ?>
        <!-- QR Scanner Interface -->
        <div class="card scanner-card">
            <div class="scanner-header">
                <h2 class="text-center mb-1">
                    <i class="bi bi-qr-code-scan"></i> Member Check-in Scanner
                </h2>
                <?php if ($event): ?>
                    <p class="text-center text-white-50 mb-0"><?php echo htmlspecialchars($event['title']); ?></p>
                    <small class="d-block text-center text-white-50">
                        <?php echo date('F j, Y g:i A', strtotime($event['event_date'])); ?>
                    </small>
                <?php else: ?>
                    <p class="text-center text-white-50 mb-0">Scan member QR codes for instant check-in</p>
                <?php endif; ?>
            </div>
            
            <div class="scanner-interface">
                <!-- Error Messages -->
                <?php if ($error && !$attendee_data): ?>
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <!-- QR Scanner Area -->
                <div class="qr-scanner-area">
                    <i class="bi bi-camera text-primary" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-primary">QR Code Scanner</h4>
                    <p class="text-muted">Position member's QR code in front of camera</p>
                    
                    <div class="mt-4">
                        <button class="btn btn-primary btn-lg" onclick="startCamera()">
                            <i class="bi bi-camera"></i> Start Camera Scanner
                        </button>
                    </div>
                    
                    <!-- Camera preview area -->
                    <div id="camera-preview" style="display: none; margin-top: 20px;">
                        <video id="video" width="300" height="200" style="border-radius: 10px;"></video>
                        <canvas id="canvas" style="display: none;"></canvas>
                        <div class="mt-2">
                            <button class="btn btn-danger" onclick="stopCamera()">
                                <i class="bi bi-stop-circle"></i> Stop Camera
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Manual Input -->
                <div class="manual-input">
                    <h6><i class="bi bi-keyboard"></i> Manual QR Code Entry</h6>
                    <p class="text-muted small">If camera scanning doesn't work, enter QR code manually</p>
                    
                    <form method="POST">
                        <input type="hidden" name="action" value="manual_checkin">
                        <div class="input-group">
                            <input type="text" class="form-control form-control-lg" 
                                   name="qr_token" placeholder="Enter QR code token" required>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle"></i> Check In
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Quick Actions -->
                <div class="row mt-4">
                    <div class="col-6">
                        <?php if ($event): ?>
                            <a href="member_qr_system.php?event_id=<?php echo $event['id']; ?>" class="btn btn-outline-primary w-100">
                                <i class="bi bi-gear"></i> Manage QR Codes
                            </a>
                        <?php endif; ?>
                    </div>
                    <div class="col-6">
                        <a href="event_attendance_detail.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-secondary w-100">
                            <i class="bi bi-list"></i> View Attendance
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Check-ins -->
        <?php if (!empty($recent_checkins)): ?>
            <div class="card scanner-card mt-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-clock-history"></i> Recent Check-ins</h6>
                </div>
                <div class="card-body" style="max-height: 300px; overflow-y: auto;">
                    <?php foreach ($recent_checkins as $checkin): ?>
                        <div class="recent-checkin">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><?php echo htmlspecialchars($checkin['attendee_name']); ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        <i class="bi bi-envelope"></i> <?php echo htmlspecialchars($checkin['attendee_email']); ?>
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-success">Checked In</span>
                                    <br>
                                    <small class="text-muted">
                                        <?php echo date('g:i A', strtotime($checkin['used_at'])); ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<!-- QR Code Scanner Library -->
<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
let video, canvas, context;
let scanning = false;

function startCamera() {
    video = document.getElementById('video');
    canvas = document.getElementById('canvas');
    context = canvas.getContext('2d');
    
    // Show camera preview
    document.getElementById('camera-preview').style.display = 'block';
    
    // Request camera access
    navigator.mediaDevices.getUserMedia({ 
        video: { 
            facingMode: 'environment' // Use back camera if available
        } 
    })
    .then(function(stream) {
        video.srcObject = stream;
        video.play();
        scanning = true;
        scanQRCode();
    })
    .catch(function(err) {
        console.error('Error accessing camera:', err);
        alert('Unable to access camera. Please check permissions or use manual entry.');
    });
}

function stopCamera() {
    scanning = false;
    
    if (video && video.srcObject) {
        video.srcObject.getTracks().forEach(track => track.stop());
    }
    
    document.getElementById('camera-preview').style.display = 'none';
}

function scanQRCode() {
    if (!scanning) return;
    
    if (video.readyState === video.HAVE_ENOUGH_DATA) {
        canvas.height = video.videoHeight;
        canvas.width = video.videoWidth;
        context.drawImage(video, 0, 0, canvas.width, canvas.height);
        
        const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
        const code = jsQR(imageData.data, imageData.width, imageData.height);
        
        if (code) {
            console.log('QR Code detected:', code.data);
            
            // Extract token from QR code URL
            const url = new URL(code.data);
            const token = url.searchParams.get('token');
            
            if (token) {
                // Stop scanning and redirect
                stopCamera();
                window.location.href = 'member_checkin.php?token=' + encodeURIComponent(token);
            } else {
                alert('Invalid QR code format. Please try again.');
            }
        }
    }
    
    if (scanning) {
        requestAnimationFrame(scanQRCode);
    }
}

// Auto-refresh recent check-ins every 10 seconds
setInterval(function() {
    if (!scanning && <?php echo $event ? $event['id'] : 'null'; ?>) {
        // Only refresh if not currently scanning
        location.reload();
    }
}, 10000);

// Handle page visibility changes
document.addEventListener('visibilitychange', function() {
    if (document.hidden && scanning) {
        stopCamera();
    }
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Press 'S' to start scanner
    if (e.key === 's' || e.key === 'S') {
        if (!scanning) {
            startCamera();
        }
    }
    
    // Press 'Escape' to stop scanner
    if (e.key === 'Escape') {
        if (scanning) {
            stopCamera();
        }
    }
});
</script>

</body>
</html>
