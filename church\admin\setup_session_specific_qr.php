<?php
/**
 * Setup Session-Specific QR Code System
 * 
 * This script creates the database schema and initial setup for person-specific session QR codes.
 * Each person gets a unique QR code for each session they register for.
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config.php';

// Ensure only super admins can run this setup
if (!isset($_SESSION['admin_role']) || $_SESSION['admin_role'] !== 'super_admin') {
    die("Access denied. Super admin privileges required.");
}

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'create_schema') {
            // Create the session_attendance_qr_codes table
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS session_attendance_qr_codes (
                    id INT(11) AUTO_INCREMENT PRIMARY KEY,
                    session_id INT(11) NOT NULL,
                    member_id INT(11) DEFAULT NULL,
                    guest_name VARCHAR(255) DEFAULT NULL,
                    guest_email VARCHAR(255) DEFAULT NULL,
                    qr_token VARCHAR(255) NOT NULL UNIQUE,
                    attendee_name VARCHAR(255) NOT NULL,
                    attendee_email VARCHAR(255) NOT NULL,
                    attendee_type ENUM('member', 'guest') DEFAULT 'member',
                    is_used TINYINT(1) DEFAULT 0,
                    used_at TIMESTAMP NULL,
                    email_sent TINYINT(1) DEFAULT 0,
                    email_sent_at TIMESTAMP NULL,
                    expires_at DATETIME NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    
                    INDEX idx_session_id (session_id),
                    INDEX idx_qr_token (qr_token),
                    INDEX idx_member_id (member_id),
                    INDEX idx_guest_email (guest_email),
                    INDEX idx_expires_at (expires_at),
                    
                    UNIQUE KEY unique_session_member (session_id, member_id),
                    UNIQUE KEY unique_session_guest (session_id, guest_name, guest_email),
                    
                    FOREIGN KEY (session_id) REFERENCES event_sessions(id) ON DELETE CASCADE,
                    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
            ");
            
            $message = "✅ Session-specific QR codes table created successfully!";
            
        } elseif ($_POST['action'] === 'migrate_existing') {
            // Migrate existing session registrations to have QR codes
            $stmt = $pdo->prepare("
                SELECT 
                    sa.session_id,
                    sa.member_id,
                    sa.guest_name,
                    sa.guest_email,
                    CASE 
                        WHEN sa.member_id IS NOT NULL THEN m.full_name
                        ELSE sa.guest_name
                    END as attendee_name,
                    CASE 
                        WHEN sa.member_id IS NOT NULL THEN m.email
                        ELSE sa.guest_email
                    END as attendee_email,
                    CASE 
                        WHEN sa.member_id IS NOT NULL THEN 'member'
                        ELSE 'guest'
                    END as attendee_type,
                    es.end_datetime
                FROM session_attendance sa
                LEFT JOIN members m ON sa.member_id = m.id
                JOIN event_sessions es ON sa.session_id = es.id
                WHERE sa.attendance_status IN ('registered', 'attended')
                AND NOT EXISTS (
                    SELECT 1 FROM session_attendance_qr_codes saqr 
                    WHERE saqr.session_id = sa.session_id 
                    AND (
                        (saqr.member_id = sa.member_id AND sa.member_id IS NOT NULL)
                        OR (saqr.guest_name = sa.guest_name AND saqr.guest_email = sa.guest_email AND sa.member_id IS NULL)
                    )
                )
            ");
            $stmt->execute();
            $registrations = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $migrated_count = 0;
            foreach ($registrations as $reg) {
                // Generate unique QR token
                $qr_token = 'SQR_' . $reg['session_id'] . '_' . bin2hex(random_bytes(16));
                
                // Set expiration to 24 hours after session end or 24 hours from now, whichever is later
                $session_end = strtotime($reg['end_datetime']);
                $expires_at = date('Y-m-d H:i:s', max($session_end + 86400, time() + 86400));
                
                // Insert QR code record
                $stmt = $pdo->prepare("
                    INSERT INTO session_attendance_qr_codes 
                    (session_id, member_id, guest_name, guest_email, qr_token, attendee_name, attendee_email, attendee_type, expires_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $reg['session_id'],
                    $reg['member_id'],
                    $reg['guest_name'],
                    $reg['guest_email'],
                    $qr_token,
                    $reg['attendee_name'],
                    $reg['attendee_email'],
                    $reg['attendee_type'],
                    $expires_at
                ]);
                
                $migrated_count++;
            }
            
            $message = "✅ Migrated {$migrated_count} existing session registrations to have QR codes!";
            
        } elseif ($_POST['action'] === 'test_system') {
            // Test the new system with a sample session
            $stmt = $pdo->prepare("
                SELECT es.*, e.title as event_title 
                FROM event_sessions es 
                JOIN events e ON es.event_id = e.id 
                WHERE es.status = 'active' 
                ORDER BY es.start_datetime DESC 
                LIMIT 1
            ");
            $stmt->execute();
            $test_session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($test_session) {
                // Get a test member
                $stmt = $pdo->prepare("SELECT * FROM members WHERE email IS NOT NULL LIMIT 1");
                $stmt->execute();
                $test_member = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($test_member) {
                    // Generate test QR code
                    $qr_token = 'TEST_SQR_' . $test_session['id'] . '_' . bin2hex(random_bytes(8));
                    $expires_at = date('Y-m-d H:i:s', strtotime('+24 hours'));
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO session_attendance_qr_codes 
                        (session_id, member_id, qr_token, attendee_name, attendee_email, attendee_type, expires_at)
                        VALUES (?, ?, ?, ?, ?, 'member', ?)
                        ON DUPLICATE KEY UPDATE
                        qr_token = VALUES(qr_token),
                        expires_at = VALUES(expires_at),
                        updated_at = NOW()
                    ");
                    $stmt->execute([
                        $test_session['id'],
                        $test_member['id'],
                        $qr_token,
                        $test_member['full_name'],
                        $test_member['email'],
                        $expires_at
                    ]);
                    
                    $message = "✅ Test QR code created! Session: {$test_session['session_title']}, Member: {$test_member['full_name']}, Token: {$qr_token}";
                } else {
                    $error = "No test member found in database.";
                }
            } else {
                $error = "No active sessions found for testing.";
            }
        }
        
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get system status
$tables_exist = false;
$record_count = 0;

try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'session_attendance_qr_codes'");
    $tables_exist = $stmt->rowCount() > 0;
    
    if ($tables_exist) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM session_attendance_qr_codes");
        $record_count = $stmt->fetchColumn();
    }
} catch (Exception $e) {
    // Table doesn't exist yet
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Session-Specific QR Codes</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .setup-card {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .setup-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .status-badge {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }
        .step-number {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
        }
    </style>
</head>
<body class="bg-light">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="bi bi-qr-code"></i> Setup Session-Specific QR Codes</h1>
                <a href="index.php" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Dashboard
                </a>
            </div>
            
            <!-- System Status -->
            <div class="card setup-card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-info-circle"></i> System Status</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Database Schema:</strong> 
                                <?php if ($tables_exist): ?>
                                    <span class="badge bg-success status-badge">✅ Ready</span>
                                <?php else: ?>
                                    <span class="badge bg-warning status-badge">⚠️ Not Created</span>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>QR Code Records:</strong> 
                                <span class="badge bg-info status-badge"><?php echo number_format($record_count); ?> records</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Messages -->
            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Setup Steps -->
            <div class="row">
                <!-- Step 1: Create Schema -->
                <div class="col-md-4 mb-4">
                    <div class="card setup-card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="step-number">1</div>
                                <h5 class="mb-0">Create Database Schema</h5>
                            </div>
                            <p class="text-muted">Create the session_attendance_qr_codes table to store person-specific session QR codes.</p>
                            
                            <form method="POST" class="mt-3">
                                <input type="hidden" name="action" value="create_schema">
                                <button type="submit" class="btn btn-primary w-100" 
                                        <?php echo $tables_exist ? 'disabled' : ''; ?>>
                                    <i class="bi bi-database"></i> 
                                    <?php echo $tables_exist ? 'Schema Created' : 'Create Schema'; ?>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Step 2: Migrate Existing -->
                <div class="col-md-4 mb-4">
                    <div class="card setup-card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="step-number">2</div>
                                <h5 class="mb-0">Migrate Existing Data</h5>
                            </div>
                            <p class="text-muted">Generate QR codes for all existing session registrations.</p>
                            
                            <form method="POST" class="mt-3">
                                <input type="hidden" name="action" value="migrate_existing">
                                <button type="submit" class="btn btn-warning w-100" 
                                        <?php echo !$tables_exist ? 'disabled' : ''; ?>>
                                    <i class="bi bi-arrow-repeat"></i> Migrate Existing
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Step 3: Test System -->
                <div class="col-md-4 mb-4">
                    <div class="card setup-card h-100">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <div class="step-number">3</div>
                                <h5 class="mb-0">Test System</h5>
                            </div>
                            <p class="text-muted">Create a test QR code to verify the system is working correctly.</p>
                            
                            <form method="POST" class="mt-3">
                                <input type="hidden" name="action" value="test_system">
                                <button type="submit" class="btn btn-success w-100" 
                                        <?php echo !$tables_exist ? 'disabled' : ''; ?>>
                                    <i class="bi bi-check-circle"></i> Test System
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Next Steps -->
            <?php if ($tables_exist && $record_count > 0): ?>
                <div class="card setup-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="bi bi-check-circle"></i> Setup Complete - Next Steps</h5>
                    </div>
                    <div class="card-body">
                        <p>The session-specific QR code system is now ready! Here's what you can do next:</p>
                        <ul>
                            <li><strong>Test QR Code Emails:</strong> <a href="send_test_session_qr_email.php" class="btn btn-sm btn-outline-primary">Send Test Email</a></li>
                            <li><strong>Mobile Check-in:</strong> <a href="mobile_checkin.php" class="btn btn-sm btn-outline-primary">Test Mobile Check-in</a></li>
                            <li><strong>Session Management:</strong> <a href="session_attendance.php" class="btn btn-sm btn-outline-primary">Manage Sessions</a></li>
                            <li><strong>QR Code Management:</strong> <a href="session_qr_management.php" class="btn btn-sm btn-outline-primary">Manage QR Codes</a></li>
                        </ul>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
