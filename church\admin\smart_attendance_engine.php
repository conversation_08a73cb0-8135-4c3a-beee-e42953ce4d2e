<?php
require_once '../config.php';
require_once 'includes/auth_check.php';

$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    header('Location: events.php');
    exit();
}

// Get event details
$stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
$stmt->execute([$event_id]);
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    header('Location: events.php');
    exit();
}

$message = '';
$error = '';

// Handle smart attendance operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'analyze_patterns') {
            // This will be handled by the analysis below
        } elseif ($action === 'apply_smart_rules') {
            $rules = $_POST['rules'] ?? [];
            $pdo->beginTransaction();
            
            $total_updated = 0;
            
            foreach ($rules as $rule) {
                $rule_type = $rule['type'];
                $threshold = (float)$rule['threshold'];
                $apply = isset($rule['apply']) && $rule['apply'] === '1';
                
                if (!$apply) continue;
                
                if ($rule_type === 'minimum_sessions') {
                    // Mark as attended if attended at least X sessions
                    $stmt = $pdo->prepare("
                        UPDATE event_rsvps er
                        SET actually_attended = 1
                        WHERE er.event_id = ? 
                        AND er.user_id IN (
                            SELECT sa.member_id
                            FROM session_attendance sa
                            JOIN event_sessions es ON sa.session_id = es.id
                            WHERE es.event_id = ? 
                            AND sa.attendance_status = 'attended'
                            AND sa.member_id IS NOT NULL
                            GROUP BY sa.member_id
                            HAVING COUNT(DISTINCT sa.session_id) >= ?
                        )
                    ");
                    $stmt->execute([$event_id, $event_id, $threshold]);
                    $total_updated += $stmt->rowCount();
                    
                    // Handle guests
                    $stmt = $pdo->prepare("
                        UPDATE event_rsvps_guests erg
                        SET actually_attended = 1
                        WHERE erg.event_id = ? 
                        AND erg.id IN (
                            SELECT DISTINCT guest_ref.id
                            FROM event_rsvps_guests guest_ref
                            JOIN session_attendance sa ON sa.guest_name = guest_ref.guest_name
                            JOIN event_sessions es ON sa.session_id = es.id
                            WHERE es.event_id = ? 
                            AND sa.attendance_status = 'attended'
                            AND sa.guest_name IS NOT NULL
                            GROUP BY guest_ref.id
                            HAVING COUNT(DISTINCT sa.session_id) >= ?
                        )
                    ");
                    $stmt->execute([$event_id, $event_id, $threshold]);
                    $total_updated += $stmt->rowCount();
                    
                } elseif ($rule_type === 'percentage_threshold') {
                    // Mark as attended if attended X% of registered sessions
                    $stmt = $pdo->prepare("
                        UPDATE event_rsvps er
                        SET actually_attended = 1
                        WHERE er.event_id = ? 
                        AND er.user_id IN (
                            SELECT member_stats.member_id
                            FROM (
                                SELECT 
                                    sa.member_id,
                                    COUNT(DISTINCT CASE WHEN sa.attendance_status = 'attended' THEN sa.session_id END) as attended_sessions,
                                    COUNT(DISTINCT sa.session_id) as registered_sessions,
                                    (COUNT(DISTINCT CASE WHEN sa.attendance_status = 'attended' THEN sa.session_id END) / 
                                     NULLIF(COUNT(DISTINCT sa.session_id), 0)) * 100 as attendance_percentage
                                FROM session_attendance sa
                                JOIN event_sessions es ON sa.session_id = es.id
                                WHERE es.event_id = ? AND sa.member_id IS NOT NULL
                                GROUP BY sa.member_id
                                HAVING attendance_percentage >= ?
                            ) member_stats
                        )
                    ");
                    $stmt->execute([$event_id, $event_id, $threshold]);
                    $total_updated += $stmt->rowCount();
                    
                } elseif ($rule_type === 'core_sessions') {
                    // Mark as attended if attended specific "core" sessions
                    $core_sessions = $rule['core_sessions'] ?? [];
                    if (!empty($core_sessions)) {
                        $placeholders = str_repeat('?,', count($core_sessions) - 1) . '?';
                        $stmt = $pdo->prepare("
                            UPDATE event_rsvps er
                            SET actually_attended = 1
                            WHERE er.event_id = ? 
                            AND er.user_id IN (
                                SELECT sa.member_id
                                FROM session_attendance sa
                                WHERE sa.session_id IN ($placeholders)
                                AND sa.attendance_status = 'attended'
                                AND sa.member_id IS NOT NULL
                                GROUP BY sa.member_id
                                HAVING COUNT(DISTINCT sa.session_id) >= ?
                            )
                        ");
                        $params = array_merge([$event_id], $core_sessions, [$threshold]);
                        $stmt->execute($params);
                        $total_updated += $stmt->rowCount();
                    }
                }
            }
            
            $pdo->commit();
            $message = "Smart attendance rules applied successfully! Updated {$total_updated} attendee records.";
        }
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        $error = "Error: " . $e->getMessage();
    }
}

// Analyze current attendance patterns
$attendance_analysis = [];

// Get session attendance statistics
$stmt = $pdo->prepare("
    SELECT 
        es.id,
        es.session_title,
        es.start_datetime,
        COUNT(sa.id) as total_registered,
        COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as total_attended,
        ROUND((COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) / 
               NULLIF(COUNT(sa.id), 0)) * 100, 1) as attendance_rate
    FROM event_sessions es
    LEFT JOIN session_attendance sa ON es.id = sa.session_id
    WHERE es.event_id = ? AND es.status = 'active'
    GROUP BY es.id
    ORDER BY es.start_datetime
");
$stmt->execute([$event_id]);
$session_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get attendee session participation patterns
$stmt = $pdo->prepare("
    SELECT 
        COALESCE(sa.member_id, CONCAT('guest_', ROW_NUMBER() OVER (ORDER BY sa.guest_name))) as attendee_id,
        COALESCE(m.full_name, sa.guest_name) as attendee_name,
        CASE WHEN sa.member_id IS NOT NULL THEN 'member' ELSE 'guest' END as attendee_type,
        COUNT(DISTINCT sa.session_id) as sessions_registered,
        COUNT(DISTINCT CASE WHEN sa.attendance_status = 'attended' THEN sa.session_id END) as sessions_attended,
        ROUND((COUNT(DISTINCT CASE WHEN sa.attendance_status = 'attended' THEN sa.session_id END) / 
               NULLIF(COUNT(DISTINCT sa.session_id), 0)) * 100, 1) as attendance_percentage,
        GROUP_CONCAT(
            CASE WHEN sa.attendance_status = 'attended' 
            THEN es.session_title 
            END ORDER BY es.start_datetime SEPARATOR ', '
        ) as attended_sessions
    FROM session_attendance sa
    JOIN event_sessions es ON sa.session_id = es.id
    LEFT JOIN members m ON sa.member_id = m.id
    WHERE es.event_id = ?
    GROUP BY attendee_id, attendee_name, attendee_type
    ORDER BY sessions_attended DESC, attendee_name
");
$stmt->execute([$event_id]);
$attendee_patterns = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate smart attendance recommendations
$recommendations = [];

// Recommendation 1: Minimum sessions threshold
$session_counts = array_column($attendee_patterns, 'sessions_attended');
if (!empty($session_counts)) {
    $avg_sessions = round(array_sum($session_counts) / count($session_counts), 1);
    $median_sessions = $session_counts[floor(count($session_counts) / 2)];
    
    $recommendations[] = [
        'type' => 'minimum_sessions',
        'title' => 'Minimum Sessions Attended',
        'description' => "Mark as attended if attended at least X sessions",
        'suggested_threshold' => max(1, floor($median_sessions * 0.6)),
        'impact_preview' => count(array_filter($attendee_patterns, function($p) use ($median_sessions) {
            return $p['sessions_attended'] >= floor($median_sessions * 0.6);
        }))
    ];
}

// Recommendation 2: Percentage threshold
$percentages = array_filter(array_column($attendee_patterns, 'attendance_percentage'), function($p) { return $p > 0; });
if (!empty($percentages)) {
    $avg_percentage = round(array_sum($percentages) / count($percentages), 1);
    
    $recommendations[] = [
        'type' => 'percentage_threshold',
        'title' => 'Attendance Percentage',
        'description' => "Mark as attended if attended X% of registered sessions",
        'suggested_threshold' => max(25, floor($avg_percentage * 0.7)),
        'impact_preview' => count(array_filter($attendee_patterns, function($p) use ($avg_percentage) {
            return $p['attendance_percentage'] >= floor($avg_percentage * 0.7);
        }))
    ];
}

// Recommendation 3: Core sessions (highest attendance rates)
$core_sessions = array_filter($session_stats, function($s) {
    return $s['attendance_rate'] >= 70; // Sessions with 70%+ attendance
});
usort($core_sessions, function($a, $b) {
    return $b['attendance_rate'] <=> $a['attendance_rate'];
});

if (!empty($core_sessions)) {
    $recommendations[] = [
        'type' => 'core_sessions',
        'title' => 'Core Sessions Attendance',
        'description' => "Mark as attended if attended key sessions with high attendance rates",
        'suggested_threshold' => min(2, count($core_sessions)),
        'core_sessions' => array_slice($core_sessions, 0, 3), // Top 3 core sessions
        'impact_preview' => 0 // Will be calculated dynamically
    ];
}

$page_title = 'Smart Attendance Engine';
include 'includes/header.php';
?>

<style>
.analysis-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.recommendation-card {
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.2s;
}

.recommendation-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.recommendation-card.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.pattern-table {
    max-height: 400px;
    overflow-y: auto;
}

.attendance-bar {
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.attendance-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    transition: width 0.3s ease;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1><i class="bi bi-lightbulb"></i> Smart Attendance Engine</h1>
                    <p class="text-muted mb-0">
                        <strong><?php echo htmlspecialchars($event['title']); ?></strong> •
                        <?php echo date('M j, Y', strtotime($event['event_date'])); ?>
                    </p>
                </div>
                <div>
                    <a href="advanced_bulk_attendance.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-primary me-2">
                        <i class="bi bi-diagram-3"></i> Bulk Operations
                    </a>
                    <a href="multi_session_dashboard.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Dashboard
                    </a>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Analysis Overview -->
            <div class="analysis-card">
                <h4><i class="bi bi-graph-up"></i> Attendance Pattern Analysis</h4>
                <div class="row">
                    <div class="col-md-3">
                        <h3><?php echo count($session_stats); ?></h3>
                        <p class="mb-0">Total Sessions</p>
                    </div>
                    <div class="col-md-3">
                        <h3><?php echo count($attendee_patterns); ?></h3>
                        <p class="mb-0">Active Participants</p>
                    </div>
                    <div class="col-md-3">
                        <h3><?php echo !empty($session_counts) ? round(array_sum($session_counts) / count($session_counts), 1) : 0; ?></h3>
                        <p class="mb-0">Avg Sessions/Person</p>
                    </div>
                    <div class="col-md-3">
                        <h3><?php echo !empty($percentages) ? round(array_sum($percentages) / count($percentages), 1) : 0; ?>%</h3>
                        <p class="mb-0">Avg Attendance Rate</p>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Smart Recommendations -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-magic"></i> Smart Attendance Recommendations</h5>
                            <small class="text-muted">AI-powered suggestions based on attendance patterns</small>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="smartRulesForm">
                                <input type="hidden" name="action" value="apply_smart_rules">

                                <?php foreach ($recommendations as $index => $rec): ?>
                                    <div class="recommendation-card" onclick="toggleRecommendation(<?php echo $index; ?>)">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   name="rules[<?php echo $index; ?>][apply]" value="1"
                                                   id="rule_<?php echo $index; ?>">
                                            <input type="hidden" name="rules[<?php echo $index; ?>][type]" value="<?php echo $rec['type']; ?>">
                                            <input type="hidden" name="rules[<?php echo $index; ?>][threshold]" value="<?php echo $rec['suggested_threshold']; ?>">

                                            <?php if ($rec['type'] === 'core_sessions'): ?>
                                                <?php foreach ($rec['core_sessions'] as $cs_index => $core_session): ?>
                                                    <input type="hidden" name="rules[<?php echo $index; ?>][core_sessions][]" value="<?php echo $core_session['id']; ?>">
                                                <?php endforeach; ?>
                                            <?php endif; ?>

                                            <label class="form-check-label w-100" for="rule_<?php echo $index; ?>">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="mb-1"><?php echo htmlspecialchars($rec['title']); ?></h6>
                                                        <p class="text-muted mb-2"><?php echo htmlspecialchars($rec['description']); ?></p>

                                                        <?php if ($rec['type'] === 'minimum_sessions'): ?>
                                                            <div class="mb-2">
                                                                <label class="form-label">Minimum sessions:</label>
                                                                <input type="number" class="form-control form-control-sm d-inline-block w-auto"
                                                                       name="rules[<?php echo $index; ?>][threshold]"
                                                                       value="<?php echo $rec['suggested_threshold']; ?>"
                                                                       min="1" max="<?php echo count($session_stats); ?>">
                                                            </div>
                                                        <?php elseif ($rec['type'] === 'percentage_threshold'): ?>
                                                            <div class="mb-2">
                                                                <label class="form-label">Percentage threshold:</label>
                                                                <input type="number" class="form-control form-control-sm d-inline-block w-auto"
                                                                       name="rules[<?php echo $index; ?>][threshold]"
                                                                       value="<?php echo $rec['suggested_threshold']; ?>"
                                                                       min="0" max="100" step="0.1">%
                                                            </div>
                                                        <?php elseif ($rec['type'] === 'core_sessions'): ?>
                                                            <div class="mb-2">
                                                                <label class="form-label">Core sessions (<?php echo count($rec['core_sessions']); ?> identified):</label>
                                                                <ul class="list-unstyled mb-0">
                                                                    <?php foreach ($rec['core_sessions'] as $core_session): ?>
                                                                        <li><small class="text-success">
                                                                            • <?php echo htmlspecialchars($core_session['session_title']); ?>
                                                                            (<?php echo $core_session['attendance_rate']; ?>% attendance)
                                                                        </small></li>
                                                                    <?php endforeach; ?>
                                                                </ul>
                                                                <div class="mt-2">
                                                                    <label class="form-label">Minimum core sessions to attend:</label>
                                                                    <input type="number" class="form-control form-control-sm d-inline-block w-auto"
                                                                           name="rules[<?php echo $index; ?>][threshold]"
                                                                           value="<?php echo $rec['suggested_threshold']; ?>"
                                                                           min="1" max="<?php echo count($rec['core_sessions']); ?>">
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="text-end">
                                                        <span class="badge bg-primary fs-6"><?php echo $rec['impact_preview']; ?> people</span>
                                                        <br><small class="text-muted">would be marked</small>
                                                    </div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>

                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary btn-lg" id="applyRules" disabled>
                                        <i class="bi bi-magic"></i> Apply Selected Rules
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-lg ms-2" onclick="previewImpact()">
                                        <i class="bi bi-eye"></i> Preview Impact
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Session Statistics -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-bar-chart"></i> Session Performance</h6>
                        </div>
                        <div class="card-body">
                            <?php foreach ($session_stats as $session): ?>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <small class="fw-bold"><?php echo htmlspecialchars($session['session_title']); ?></small>
                                        <small class="text-muted"><?php echo $session['attendance_rate']; ?>%</small>
                                    </div>
                                    <div class="attendance-bar">
                                        <div class="attendance-fill" style="width: <?php echo $session['attendance_rate']; ?>%"></div>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo $session['total_attended']; ?>/<?php echo $session['total_registered']; ?> attended
                                    </small>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Attendance Patterns Table -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-table"></i> Individual Attendance Patterns</h5>
                    <small class="text-muted">Detailed view of each participant's session attendance</small>
                </div>
                <div class="card-body">
                    <div class="pattern-table">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Attendee</th>
                                    <th>Type</th>
                                    <th>Sessions Registered</th>
                                    <th>Sessions Attended</th>
                                    <th>Attendance Rate</th>
                                    <th>Sessions Attended</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($attendee_patterns as $pattern): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($pattern['attendee_name']); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo $pattern['attendee_type'] === 'member' ? 'primary' : 'secondary'; ?>">
                                                <?php echo ucfirst($pattern['attendee_type']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo $pattern['sessions_registered']; ?></td>
                                        <td><?php echo $pattern['sessions_attended']; ?></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="attendance-bar me-2" style="width: 60px; height: 15px;">
                                                    <div class="attendance-fill" style="width: <?php echo $pattern['attendance_percentage']; ?>%"></div>
                                                </div>
                                                <small><?php echo $pattern['attendance_percentage']; ?>%</small>
                                            </div>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo $pattern['attended_sessions'] ?: 'None'; ?>
                                            </small>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleRecommendation(index) {
    const checkbox = document.getElementById('rule_' + index);
    const card = checkbox.closest('.recommendation-card');

    checkbox.checked = !checkbox.checked;

    if (checkbox.checked) {
        card.classList.add('selected');
    } else {
        card.classList.remove('selected');
    }

    updateApplyButton();
}

function updateApplyButton() {
    const checkedRules = document.querySelectorAll('input[name*="[apply]"]:checked');
    const applyButton = document.getElementById('applyRules');
    applyButton.disabled = checkedRules.length === 0;
}

function previewImpact() {
    const checkedRules = document.querySelectorAll('input[name*="[apply]"]:checked');
    if (checkedRules.length === 0) {
        alert('Please select at least one rule to preview.');
        return;
    }

    let totalImpact = 0;
    checkedRules.forEach(rule => {
        const card = rule.closest('.recommendation-card');
        const badge = card.querySelector('.badge');
        totalImpact += parseInt(badge.textContent);
    });

    alert(`Preview: ${totalImpact} people would be marked as attended for the main event.`);
}

// Track checkbox changes
document.addEventListener('change', function(e) {
    if (e.target.name && e.target.name.includes('[apply]')) {
        updateApplyButton();
    }
});

// Form validation
document.getElementById('smartRulesForm').addEventListener('submit', function(e) {
    const checkedRules = document.querySelectorAll('input[name*="[apply]"]:checked');
    if (checkedRules.length === 0) {
        e.preventDefault();
        alert('Please select at least one rule to apply.');
        return;
    }

    if (!confirm('This will update event attendance records based on session attendance patterns. Continue?')) {
        e.preventDefault();
    }
});

// Initialize
updateApplyButton();
</script>

<?php include 'includes/footer.php'; ?>
