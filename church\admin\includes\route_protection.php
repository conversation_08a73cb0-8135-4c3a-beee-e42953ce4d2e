<?php
/**
 * Route Protection System
 * Prevents unauthorized access to admin pages via direct URLs
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database connection
require_once __DIR__ . '/../../config.php';

// Include RBAC system
require_once 'rbac_access_control.php';

// Ensure we don't have conflicts with old RBAC system
if (class_exists('RBACSystem')) {
    // Old system exists, but we'll use the new one
}

/**
 * Protect current page based on user role and permissions
 */
function protectRoute($required_roles = null) {
    global $pdo, $rbac;
    
    // Check if user is logged in
    if (!isset($_SESSION['admin_id'])) {
        redirectToLogin();
        return;
    }
    
    // Initialize RBAC if not already done
    if (!isset($rbac)) {
        $rbac = new RBACAccessControl($pdo, $_SESSION['admin_id']);
    }
    
    // Get current page
    $current_page = basename($_SERVER['PHP_SELF']);
    
    // If specific roles are required, check them
    if ($required_roles !== null) {
        $has_required_role = false;
        foreach ($required_roles as $role) {
            if ($rbac->hasRole($role)) {
                $has_required_role = true;
                break;
            }
        }
        
        if (!$has_required_role) {
            redirectToAccessDenied("You don't have permission to access this page. Required role: " . implode(' or ', $required_roles));
            return;
        }
    }
    
    // Use RBAC system to check page access
    if (!$rbac->canAccessPage($current_page)) {
        $user_role = $rbac->getPrimaryRole();
        redirectToAccessDenied("Access denied. Your role ($user_role) cannot access this page.");
        return;
    }
    
    // Log access attempt for security monitoring
    logPageAccess($current_page, $_SESSION['admin_id'], true);
}

/**
 * Protect Super Admin only pages
 */
function protectSuperAdminRoute() {
    protectRoute(['super_admin']);
}

/**
 * Protect Admin pages (Super Admin + Limited Admin)
 */
function protectAdminRoute() {
    protectRoute(['super_admin', 'limited_admin']);
}

/**
 * Protect Event Coordinator pages
 */
function protectEventCoordinatorRoute() {
    protectRoute(['super_admin', 'event_coordinator']);
}

/**
 * Protect Session Moderator pages
 */
function protectSessionModeratorRoute() {
    protectRoute(['super_admin', 'session_moderator']);
}

/**
 * Protect Staff pages
 */
function protectStaffRoute() {
    protectRoute(['super_admin', 'staff']);
}

/**
 * Redirect to login page
 */
function redirectToLogin($message = null) {
    $redirect_url = "login.php";
    if ($message) {
        $redirect_url .= "?message=" . urlencode($message);
    }
    header("Location: $redirect_url");
    exit();
}

/**
 * Redirect to access denied page
 */
function redirectToAccessDenied($reason = null) {
    $redirect_url = "access_denied.php";
    if ($reason) {
        $redirect_url .= "?reason=" . urlencode($reason);
    }
    header("Location: $redirect_url");
    exit();
}

/**
 * Get user's default dashboard based on their role and assignments
 */
function getUserDefaultDashboard() {
    global $pdo;
    $user_id = $_SESSION['admin_id'];

    // First check if user has any active session or event assignments
    try {
        // Check for active session assignments
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM session_assignments sa
            JOIN event_sessions es ON sa.session_id = es.id
            WHERE sa.user_id = ? AND sa.is_active = 1 AND es.end_datetime >= NOW()
        ");
        $stmt->execute([$user_id]);
        $active_sessions = $stmt->fetchColumn();

        // Check for active event assignments
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM event_assignments ea
            JOIN events e ON ea.event_id = e.id
            WHERE ea.user_id = ? AND ea.is_active = 1 AND e.event_date >= CURDATE()
        ");
        $stmt->execute([$user_id]);
        $active_events = $stmt->fetchColumn();

        // If user has assignments but no role, redirect to assignment dashboard
        if (($active_sessions > 0 || $active_events > 0)) {
            // Check if user has any active role
            $stmt = $pdo->prepare("
                SELECT COUNT(*) FROM user_role_assignments
                WHERE user_id = ? AND is_active = 1 AND (expires_at IS NULL OR expires_at > NOW())
            ");
            $stmt->execute([$user_id]);
            $has_role = $stmt->fetchColumn() > 0;

            // If no role but has assignments, go to assignment dashboard
            if (!$has_role) {
                return 'assignment_dashboard.php';
            }
        }
    } catch (PDOException $e) {
        // Tables might not exist, fall back to role-based routing
    }

    // Fall back to role-based dashboard routing
    global $rbac;
    if (!isset($rbac)) {
        $rbac = new RBACAccessControl($pdo, $user_id);
    }

    return $rbac->getDefaultDashboard();
}

/**
 * Redirect user to their appropriate dashboard
 */
function redirectToUserDashboard() {
    $dashboard = getUserDefaultDashboard();
    header("Location: $dashboard");
    exit();
}

/**
 * Log page access for security monitoring
 */
function logPageAccess($page, $user_id, $success) {
    global $pdo;
    
    try {
        // Create access log table if it doesn't exist
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS admin_access_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                page_accessed VARCHAR(255) NOT NULL,
                access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                success BOOLEAN NOT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                INDEX idx_user_time (user_id, access_time),
                INDEX idx_page_time (page_accessed, access_time)
            )
        ");
        
        // Log the access attempt
        $stmt = $pdo->prepare("
            INSERT INTO admin_access_log (user_id, page_accessed, success, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $stmt->execute([$user_id, $page, $success ? 1 : 0, $ip_address, $user_agent]);
        
    } catch (PDOException $e) {
        // Log error but don't break the application
        error_log("Failed to log page access: " . $e->getMessage());
    }
}

/**
 * Check if user has specific permission for current context
 */
function hasContextPermission($context, $resource_id = null) {
    global $rbac, $pdo;
    
    if (!isset($rbac)) {
        $rbac = new RBACAccessControl($pdo, $_SESSION['admin_id']);
    }
    
    $user_role = $rbac->getPrimaryRole();
    
    switch ($context) {
        case 'event_management':
            // Event coordinators can only manage their assigned events
            if ($user_role === 'event_coordinator' && $resource_id) {
                return isEventAssignedToUser($_SESSION['admin_id'], $resource_id);
            }
            return $rbac->hasRole('super_admin');
            
        case 'session_management':
            // Session moderators can only manage their assigned sessions
            if ($user_role === 'session_moderator' && $resource_id) {
                return isSessionAssignedToUser($_SESSION['admin_id'], $resource_id);
            }
            return $rbac->hasRole('super_admin');
            
        case 'member_management':
            return $rbac->isAdmin();
            
        case 'system_administration':
            return $rbac->isSuperAdmin();
            
        default:
            return false;
    }
}

/**
 * Check if event is assigned to user
 */
function isEventAssignedToUser($user_id, $event_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM event_assignments 
            WHERE user_id = ? AND event_id = ? AND is_active = 1
        ");
        $stmt->execute([$user_id, $event_id]);
        return $stmt->fetchColumn() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Check if session is assigned to user
 */
function isSessionAssignedToUser($user_id, $session_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM session_assignments 
            WHERE user_id = ? AND session_id = ? AND is_active = 1
        ");
        $stmt->execute([$user_id, $session_id]);
        return $stmt->fetchColumn() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Get security headers for enhanced protection
 */
function setSecurityHeaders() {
    // Prevent clickjacking
    header('X-Frame-Options: DENY');
    
    // Prevent MIME type sniffing
    header('X-Content-Type-Options: nosniff');
    
    // Enable XSS protection
    header('X-XSS-Protection: 1; mode=block');
    
    // Referrer policy
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    // Content Security Policy (basic)
    header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; img-src 'self' data:;");
}

// Set security headers by default
setSecurityHeaders();

/**
 * Initialize route protection for current page
 * Call this at the top of protected pages
 */
function initializeRouteProtection() {
    // Protect the current route
    protectRoute();
    
    // Set additional security measures
    setSecurityHeaders();
}

// Auto-protect if this file is included and not in login/public pages
$current_page = basename($_SERVER['PHP_SELF']);
$public_pages = ['login.php', 'logout.php', 'forgot_password.php', 'reset_password.php'];

if (!in_array($current_page, $public_pages)) {
    // Auto-initialize protection for non-public pages
    initializeRouteProtection();
}
?>
