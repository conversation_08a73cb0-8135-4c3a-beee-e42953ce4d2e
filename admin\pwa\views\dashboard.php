<?php
/**
 * PWA Dashboard View
 * Universal dashboard that adapts to any organization type
 */

// Get recent events and statistics
$stmt = $pdo->prepare("
    SELECT 
        e.id,
        e.title,
        e.event_date,
        e.status,
        e.max_attendees,
        COUNT(DISTINCT ea.user_id) as registered_count,
        COUNT(DISTINCT CASE WHEN ea.attendance_status = 'attended' THEN ea.user_id END) as attended_count
    FROM events e
    LEFT JOIN event_attendance ea ON e.id = ea.event_id
    WHERE e.event_date >= CURDATE() - INTERVAL 7 DAY
    GROUP BY e.id
    ORDER BY e.event_date DESC
    LIMIT 5
");
$stmt->execute();
$recent_events = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get today's statistics
$stmt = $pdo->prepare("
    SELECT 
        COUNT(DISTINCT e.id) as events_today,
        COUNT(DISTINCT es.id) as sessions_today,
        COUNT(DISTINCT ea.user_id) as attendees_today,
        COUNT(DISTINCT CASE WHEN ea.attendance_status = 'attended' THEN ea.user_id END) as checked_in_today
    FROM events e
    LEFT JOIN event_sessions es ON e.id = es.event_id
    LEFT JOIN event_attendance ea ON e.id = ea.event_id
    WHERE DATE(e.event_date) = CURDATE()
");
$stmt->execute();
$today_stats = $stmt->fetch(PDO::FETCH_ASSOC);

// Get active sessions
$stmt = $pdo->prepare("
    SELECT 
        es.id,
        es.session_title,
        es.start_datetime,
        es.end_datetime,
        es.max_attendees,
        e.title as event_title,
        COUNT(DISTINCT sa.user_id) as current_attendance
    FROM event_sessions es
    JOIN events e ON es.event_id = e.id
    LEFT JOIN session_attendance sa ON es.id = sa.session_id AND sa.attendance_status = 'attended'
    WHERE es.start_datetime <= NOW() AND es.end_datetime >= NOW()
    GROUP BY es.id
    ORDER BY es.start_datetime ASC
    LIMIT 3
");
$stmt->execute();
$active_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="dashboard-container">
    <!-- Welcome Header -->
    <div class="pwa-card fade-in">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-1">Welcome back, <?php echo htmlspecialchars($user_name); ?>!</h5>
                <p class="text-muted mb-0">
                    <?php echo date('l, F j, Y'); ?> • 
                    <?php echo ucfirst($org_config['type']); ?> Dashboard
                </p>
            </div>
            <div class="text-end">
                <div class="status-indicator online"></div>
                <small class="text-muted">Live Updates</small>
            </div>
        </div>
    </div>

    <!-- Today's Statistics -->
    <div class="pwa-card fade-in">
        <div class="pwa-card-header">
            <h6 class="pwa-card-title">
                <i class="bi bi-graph-up"></i> Today's Overview
            </h6>
            <button class="btn btn-sm btn-outline-primary" onclick="refreshStats()">
                <i class="bi bi-arrow-clockwise"></i>
            </button>
        </div>
        
        <div class="row text-center">
            <div class="col-6 col-md-3">
                <div class="stat-item">
                    <h4 class="text-primary mb-1"><?php echo $today_stats['events_today'] ?? 0; ?></h4>
                    <small class="text-muted"><?php echo ucfirst($org_config['terminology']['events']); ?></small>
                </div>
            </div>
            <div class="col-6 col-md-3">
                <div class="stat-item">
                    <h4 class="text-success mb-1"><?php echo $today_stats['sessions_today'] ?? 0; ?></h4>
                    <small class="text-muted"><?php echo ucfirst($org_config['terminology']['sessions']); ?></small>
                </div>
            </div>
            <div class="col-6 col-md-3">
                <div class="stat-item">
                    <h4 class="text-info mb-1"><?php echo $today_stats['attendees_today'] ?? 0; ?></h4>
                    <small class="text-muted">Registered</small>
                </div>
            </div>
            <div class="col-6 col-md-3">
                <div class="stat-item">
                    <h4 class="text-warning mb-1"><?php echo $today_stats['checked_in_today'] ?? 0; ?></h4>
                    <small class="text-muted">Checked In</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Sessions -->
    <?php if (!empty($active_sessions)): ?>
    <div class="pwa-card fade-in">
        <div class="pwa-card-header">
            <h6 class="pwa-card-title">
                <i class="bi bi-play-circle"></i> Active <?php echo ucfirst($org_config['terminology']['sessions']); ?>
            </h6>
            <span class="badge bg-success"><?php echo count($active_sessions); ?> Live</span>
        </div>
        
        <?php foreach ($active_sessions as $session): ?>
            <div class="session-item">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-1"><?php echo htmlspecialchars($session['session_title']); ?></h6>
                        <p class="text-muted mb-1"><?php echo htmlspecialchars($session['event_title']); ?></p>
                        <small class="text-success">
                            <i class="bi bi-clock"></i>
                            <?php echo date('g:i A', strtotime($session['start_datetime'])); ?> - 
                            <?php echo date('g:i A', strtotime($session['end_datetime'])); ?>
                        </small>
                    </div>
                    <div class="text-end">
                        <div class="attendance-badge">
                            <?php echo $session['current_attendance']; ?>/<?php echo $session['max_attendees']; ?>
                        </div>
                        <button class="btn btn-sm btn-primary mt-1" onclick="manageSession(<?php echo $session['id']; ?>)">
                            Manage
                        </button>
                    </div>
                </div>
                
                <!-- Progress bar -->
                <?php 
                $utilization = $session['max_attendees'] > 0 ? 
                    ($session['current_attendance'] / $session['max_attendees']) * 100 : 0;
                $progress_class = $utilization > 90 ? 'bg-danger' : ($utilization > 70 ? 'bg-warning' : 'bg-success');
                ?>
                <div class="progress mt-2" style="height: 4px;">
                    <div class="progress-bar <?php echo $progress_class; ?>" 
                         style="width: <?php echo min($utilization, 100); ?>%"></div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>

    <!-- Recent Events -->
    <div class="pwa-card fade-in">
        <div class="pwa-card-header">
            <h6 class="pwa-card-title">
                <i class="bi bi-calendar-event"></i> Recent <?php echo ucfirst($org_config['terminology']['events']); ?>
            </h6>
            <a href="?action=events" class="btn btn-sm btn-outline-primary">View All</a>
        </div>
        
        <?php if (empty($recent_events)): ?>
            <div class="text-center py-4">
                <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
                <p class="text-muted mt-2">No recent <?php echo $org_config['terminology']['events']; ?> found</p>
                <a href="../events.php" class="btn btn-primary">
                    Create <?php echo ucfirst($org_config['terminology']['event']); ?>
                </a>
            </div>
        <?php else: ?>
            <?php foreach ($recent_events as $event): ?>
                <div class="event-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1"><?php echo htmlspecialchars($event['title']); ?></h6>
                            <p class="text-muted mb-1">
                                <i class="bi bi-calendar"></i>
                                <?php echo date('M j, Y g:i A', strtotime($event['event_date'])); ?>
                            </p>
                            <div class="d-flex gap-2">
                                <span class="badge bg-<?php echo $event['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                    <?php echo ucfirst($event['status']); ?>
                                </span>
                                <small class="text-muted">
                                    <?php echo $event['attended_count']; ?>/<?php echo $event['registered_count']; ?> attended
                                </small>
                            </div>
                        </div>
                        <div class="text-end">
                            <button class="btn btn-sm btn-outline-primary" onclick="viewEvent(<?php echo $event['id']; ?>)">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="manageEvent(<?php echo $event['id']; ?>)">
                                <i class="bi bi-gear"></i>
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Quick Actions -->
    <div class="pwa-card fade-in">
        <div class="pwa-card-header">
            <h6 class="pwa-card-title">
                <i class="bi bi-lightning"></i> Quick Actions
            </h6>
        </div>
        
        <div class="quick-actions-grid">
            <button class="quick-action-btn" onclick="quickCheckIn()">
                <i class="bi bi-check-circle"></i>
                <span>Quick Check-In</span>
            </button>
            <button class="quick-action-btn" onclick="scanQR()">
                <i class="bi bi-qr-code-scan"></i>
                <span>Scan QR Code</span>
            </button>
            <button class="quick-action-btn" onclick="viewPredictions()">
                <i class="bi bi-robot"></i>
                <span>AI Predictions</span>
            </button>
            <button class="quick-action-btn" onclick="exportData()">
                <i class="bi bi-download"></i>
                <span>Export Data</span>
            </button>
        </div>
    </div>

    <!-- AI Insights (if available) -->
    <div class="pwa-card fade-in">
        <div class="pwa-card-header">
            <h6 class="pwa-card-title">
                <i class="bi bi-lightbulb"></i> AI Insights
            </h6>
            <a href="?action=ai" class="btn btn-sm btn-outline-primary">View All</a>
        </div>
        
        <div class="insight-item">
            <div class="d-flex align-items-start gap-3">
                <div class="insight-icon">
                    <i class="bi bi-graph-up-arrow text-success"></i>
                </div>
                <div>
                    <h6 class="mb-1">Attendance Trending Up</h6>
                    <p class="text-muted mb-0">
                        <?php echo ucfirst($org_config['terminology']['attendee']); ?> engagement has increased 
                        15% compared to last week.
                    </p>
                </div>
            </div>
        </div>
        
        <div class="insight-item">
            <div class="d-flex align-items-start gap-3">
                <div class="insight-icon">
                    <i class="bi bi-clock text-warning"></i>
                </div>
                <div>
                    <h6 class="mb-1">Peak Time Prediction</h6>
                    <p class="text-muted mb-0">
                        Expect highest attendance between 2:00 PM - 4:00 PM today.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-item {
    padding: 1rem 0;
}

.session-item,
.event-item {
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.session-item:last-child,
.event-item:last-child {
    border-bottom: none;
}

.attendance-badge {
    background: var(--secondary-color);
    color: var(--primary-color);
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.insight-item {
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.insight-item:last-child {
    border-bottom: none;
}

.insight-icon {
    font-size: 1.5rem;
}

@media (max-width: 768px) {
    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<script>
function refreshStats() {
    // Show loading state
    const button = event.target.closest('button');
    const originalContent = button.innerHTML;
    button.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';
    button.disabled = true;
    
    // Simulate refresh
    setTimeout(() => {
        button.innerHTML = originalContent;
        button.disabled = false;
        window.pwaApp.showNotification('Statistics refreshed', 'success');
    }, 1000);
}

function manageSession(sessionId) {
    window.location.href = `../session_management.php?session_id=${sessionId}`;
}

function viewEvent(eventId) {
    window.location.href = `../multi_session_dashboard.php?event_id=${eventId}`;
}

function manageEvent(eventId) {
    window.location.href = `../event_management.php?event_id=${eventId}`;
}

// Add spin animation for refresh button
const style = document.createElement('style');
style.textContent = `
    .spin {
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>
