<?php
/**
 * Session Activity Update Endpoint
 *
 * Updates the last activity timestamp to prevent session timeout
 * This is called by the JavaScript heartbeat to keep sessions alive
 */

// Include AJAX session handler for proper session management
require_once '../includes/ajax-session-handler.php';

// For heartbeat requests, we want to be more lenient
// Try to extend the session first
if (extendAjaxSession()) {
    // Session extended successfully
    sendAjaxResponse([
        'success' => true,
        'message' => 'Session activity updated',
        'remainingTime' => 7200, // 2 hours
        'admin_id' => $_SESSION['admin_id'] ?? 'unknown',
        'timestamp' => time()
    ]);
} else {
    // No valid session to extend
    sendAjaxResponse([
        'success' => false,
        'status' => 'not_logged_in',
        'message' => 'No valid session found'
    ]);
}
?>
