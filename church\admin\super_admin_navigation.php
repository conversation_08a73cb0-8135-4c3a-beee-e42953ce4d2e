<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file and RBAC system
require_once '../config.php';
require_once 'includes/rbac_system.php';

// Initialize RBAC system
$rbac = new RBACSystem($pdo);

// Check if RBAC system is initialized
if (!$rbac->isInitialized()) {
    header("Location: initialize_rbac_database.php");
    exit();
}

// Require Super Admin role
$rbac->requireRole('super_admin', 'access_denied.php');

// Page title and header info
$page_title = 'Super Admin Navigation';
$page_header = 'Super Admin Navigation';
$page_description = 'Complete navigation guide for Super Admin access';

// Include header
include 'includes/header.php';
?>

<style>
.nav-card {
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
    border-radius: 10px;
    margin-bottom: 20px;
}
.nav-card:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}
.nav-header {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    color: white;
    border-radius: 10px;
}
.category-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
}
.nav-link-item {
    padding: 12px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin: 5px 0;
    transition: all 0.2s ease;
    text-decoration: none;
    display: block;
}
.nav-link-item:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
    text-decoration: none;
}
.priority-high { border-left: 4px solid #dc3545; }
.priority-medium { border-left: 4px solid #ffc107; }
.priority-low { border-left: 4px solid #28a745; }
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card nav-header">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="text-white mb-1">
                            <i class="bi bi-compass"></i> Super Admin Navigation Guide
                        </h2>
                        <p class="text-white-50 mb-0">Complete access map to all system features and pages</p>
                    </div>
                    <div>
                        <a href="super_admin_dashboard.php" class="btn btn-outline-light">
                            <i class="bi bi-speedometer2"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Primary Access Points -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card nav-card">
            <div class="card-header category-header">
                <h5 class="text-white mb-0">
                    <i class="bi bi-star-fill"></i> Primary Access Points
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <a href="super_admin_dashboard.php" class="nav-link-item priority-high">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><i class="bi bi-speedometer2"></i> Super Admin Dashboard</strong>
                                    <br><small class="text-muted">Central command center with system overview</small>
                                </div>
                                <i class="bi bi-arrow-right text-primary"></i>
                            </div>
                        </a>
                        
                        <a href="setup_rbac_system.php" class="nav-link-item priority-high">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><i class="bi bi-shield-check"></i> RBAC Management</strong>
                                    <br><small class="text-muted">Assign roles, events, and sessions to users</small>
                                </div>
                                <i class="bi bi-arrow-right text-primary"></i>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="system_test_dashboard.php" class="nav-link-item priority-medium">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><i class="bi bi-bug"></i> System Test Dashboard</strong>
                                    <br><small class="text-muted">Test all features and role simulation</small>
                                </div>
                                <i class="bi bi-arrow-right text-primary"></i>
                            </div>
                        </a>
                        
                        <a href="event_attendance.php" class="nav-link-item priority-medium">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><i class="bi bi-list-check"></i> Event Attendance Overview</strong>
                                    <br><small class="text-muted">Main event attendance management</small>
                                </div>
                                <i class="bi bi-arrow-right text-primary"></i>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Role-Specific Dashboards -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card nav-card">
            <div class="card-header category-header">
                <h5 class="text-white mb-0">
                    <i class="bi bi-people"></i> Role-Specific Dashboards
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <a href="event_coordinator_dashboard.php" class="nav-link-item priority-medium">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><i class="bi bi-calendar-event"></i> Event Coordinator Dashboard</strong>
                                    <br><small class="text-muted">Multi-session oversight for assigned events</small>
                                </div>
                                <i class="bi bi-arrow-right text-warning"></i>
                            </div>
                        </a>
                        
                        <a href="session_moderator_dashboard.php" class="nav-link-item priority-medium">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><i class="bi bi-person-video2"></i> Session Moderator Dashboard</strong>
                                    <br><small class="text-muted">Individual session management</small>
                                </div>
                                <i class="bi bi-arrow-right text-success"></i>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="staff_dashboard.php" class="nav-link-item priority-low">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><i class="bi bi-person-check"></i> Staff Dashboard</strong>
                                    <br><small class="text-muted">Basic check-in and attendance marking</small>
                                </div>
                                <i class="bi bi-arrow-right text-secondary"></i>
                            </div>
                        </a>
                        
                        <div class="nav-link-item" style="background-color: #f8f9fa; cursor: default;">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><i class="bi bi-person-workspace"></i> Organizer Dashboard</strong>
                                    <br><small class="text-muted">Coming soon - Event planning tools</small>
                                </div>
                                <i class="bi bi-clock text-muted"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Event & Session Management -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card nav-card">
            <div class="card-header category-header">
                <h5 class="text-white mb-0">
                    <i class="bi bi-calendar-event"></i> Event & Session Management
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6 class="text-primary">Event Management</h6>
                        <a href="events.php" class="nav-link-item priority-high">
                            <strong><i class="bi bi-plus-circle"></i> Create Events</strong>
                            <br><small class="text-muted">Create new events</small>
                        </a>
                        <a href="event_attendance.php" class="nav-link-item priority-high">
                            <strong><i class="bi bi-list-check"></i> Event Attendance</strong>
                            <br><small class="text-muted">Manage event attendance</small>
                        </a>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-success">Session Management</h6>
                        <a href="event_sessions.php" class="nav-link-item priority-medium">
                            <strong><i class="bi bi-plus-square"></i> Create Sessions</strong>
                            <br><small class="text-muted">Add sessions to events</small>
                        </a>
                        <a href="session_attendance.php" class="nav-link-item priority-medium">
                            <strong><i class="bi bi-person-check"></i> Session Attendance</strong>
                            <br><small class="text-muted">Individual session attendance</small>
                        </a>
                    </div>
                    <div class="col-md-4">
                        <h6 class="text-info">Advanced Tools</h6>
                        <div class="nav-link-item priority-low" style="background-color: #f8f9fa;">
                            <strong><i class="bi bi-speedometer2"></i> Multi-Session Dashboard</strong>
                            <br><small class="text-muted">Requires event ID parameter</small>
                        </div>
                        <div class="nav-link-item priority-low" style="background-color: #f8f9fa;">
                            <strong><i class="bi bi-diagram-3"></i> Cross-Session Operations</strong>
                            <br><small class="text-muted">Requires event ID parameter</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Access Summary -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card nav-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i> Super Admin Quick Reference
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>🎯 Most Important Pages:</h6>
                        <ul>
                            <li><strong><a href="super_admin_dashboard.php">Super Admin Dashboard</a></strong> - Your main control center</li>
                            <li><strong><a href="setup_rbac_system.php">RBAC Management</a></strong> - Assign roles and permissions</li>
                            <li><strong><a href="event_attendance.php">Event Attendance</a></strong> - Access all event tools</li>
                            <li><strong><a href="system_test_dashboard.php">System Test Dashboard</a></strong> - Test and simulate roles</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>🔄 How to Assign Roles:</h6>
                        <ol>
                            <li>Go to <strong>RBAC Management</strong></li>
                            <li><strong>Assign User Roles</strong> section - Give basic role permissions</li>
                            <li><strong>Assign Event Coordinators/Organizers</strong> section - Assign to specific events</li>
                            <li><strong>Assign Session Moderators</strong> section - Assign to specific sessions</li>
                        </ol>
                    </div>
                </div>

                <div class="mt-3 p-3 bg-light rounded">
                    <h6><i class="bi bi-lightbulb"></i> Pro Tips:</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="mb-0 small">
                                <li>Use <strong>Role Simulation</strong> to test user experiences</li>
                                <li>Event-specific tools are accessed via the Event Attendance page</li>
                                <li>The Super Admin Dashboard shows system-wide overview</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="mb-0 small">
                                <li>RBAC Management is your main control center for user permissions</li>
                                <li>Each role has a tailored dashboard with appropriate features</li>
                                <li>System Test Dashboard helps verify everything works correctly</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<?php include 'includes/footer.php'; ?>
