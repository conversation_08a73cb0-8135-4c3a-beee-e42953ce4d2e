<?php
/**
 * Update RBAC Roles - Add Admin Role and Fix Session Assignments
 * This script adds the missing "Admin" (limited_admin) role and updates the system
 */

// Include the configuration file
require_once '../config.php';

try {
    // Start transaction
    $pdo->beginTransaction();
    
    echo "<h2>Updating RBAC System...</h2>\n";
    
    // 1. Insert the limited_admin role if it doesn't exist
    echo "<p>Adding Admin (limited_admin) role...</p>\n";
    $stmt = $pdo->prepare("
        INSERT IGNORE INTO user_roles (role_name, role_display_name, role_description, hierarchy_level, dashboard_route) VALUES
        ('limited_admin', 'Admin', 'Basic administrative functions with restricted access to system administration', 2, 'dashboard.php')
    ");
    $stmt->execute();
    echo "<p>✓ Admin role added successfully</p>\n";
    
    // 2. Update hierarchy levels for existing roles
    echo "<p>Updating role hierarchy levels...</p>\n";
    $updates = [
        ['super_admin', 1],
        ['limited_admin', 2], 
        ['event_coordinator', 3],
        ['organizer', 4],
        ['session_moderator', 5],
        ['staff', 6]
    ];
    
    foreach ($updates as $update) {
        $stmt = $pdo->prepare("UPDATE user_roles SET hierarchy_level = ? WHERE role_name = ?");
        $stmt->execute([$update[1], $update[0]]);
    }
    echo "<p>✓ Hierarchy levels updated</p>\n";
    
    // 3. Update super_admin dashboard route to use the original dashboard
    echo "<p>Updating dashboard routes...</p>\n";
    $stmt = $pdo->prepare("UPDATE user_roles SET dashboard_route = 'dashboard.php' WHERE role_name = 'super_admin'");
    $stmt->execute();
    echo "<p>✓ Dashboard routes updated</p>\n";
    
    // 4. Add permissions for limited_admin role
    echo "<p>Adding permissions for Admin role...</p>\n";
    $permissions = [
        'member.view_all', 'member.manage_all', 'member.export_data',
        'event.view_all', 'event.manage_all', 'event.export_data',
        'session.view_all', 'session.manage_all', 'session.mark_attendance', 'session.export_data',
        'report.view_all', 'report.export'
    ];
    
    foreach ($permissions as $permission) {
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO role_permissions (role_id, permission_id)
            SELECT ur.id, p.id
            FROM user_roles ur, permissions p
            WHERE ur.role_name = 'limited_admin' AND p.permission_name = ?
        ");
        $stmt->execute([$permission]);
    }
    echo "<p>✓ Permissions added for Admin role</p>\n";
    
    // 5. Show current roles
    echo "<h3>Current Roles:</h3>\n";
    $stmt = $pdo->query("SELECT role_name, role_display_name, hierarchy_level, dashboard_route FROM user_roles ORDER BY hierarchy_level");
    $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>Role Name</th><th>Display Name</th><th>Level</th><th>Dashboard</th></tr>\n";
    foreach ($roles as $role) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($role['role_name']) . "</td>";
        echo "<td>" . htmlspecialchars($role['role_display_name']) . "</td>";
        echo "<td>" . htmlspecialchars($role['hierarchy_level']) . "</td>";
        echo "<td>" . htmlspecialchars($role['dashboard_route']) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // 6. Show users who can moderate sessions
    echo "<h3>Users Available for Session Moderation:</h3>\n";
    $stmt = $pdo->query("
        SELECT DISTINCT a.id, a.username, a.email, ur.role_display_name
        FROM admins a
        JOIN user_role_assignments ura ON a.id = ura.user_id
        JOIN user_roles ur ON ura.role_id = ur.id
        WHERE ur.role_name IN ('session_moderator', 'event_coordinator', 'limited_admin', 'super_admin') 
        AND ura.is_active = 1
        ORDER BY ur.hierarchy_level ASC, a.username
    ");
    $moderators = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Role</th></tr>\n";
    foreach ($moderators as $moderator) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($moderator['id']) . "</td>";
        echo "<td>" . htmlspecialchars($moderator['username']) . "</td>";
        echo "<td>" . htmlspecialchars($moderator['email']) . "</td>";
        echo "<td>" . htmlspecialchars($moderator['role_display_name']) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Commit transaction
    $pdo->commit();
    echo "<h3 style='color: green;'>✓ RBAC System Updated Successfully!</h3>\n";
    
    echo "<p><a href='setup_rbac_system.php'>Go to RBAC Management</a></p>\n";
    echo "<p><a href='dashboard.php'>Go to Dashboard</a></p>\n";
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo "<h3 style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</h3>\n";
}
?>
