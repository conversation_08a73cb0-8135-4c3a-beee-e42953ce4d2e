<?php
// Include configuration first (which sets up session configuration)
require_once '../config.php';

// Start session after configuration is loaded
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

header('Content-Type: application/json');

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

$userId = $_SESSION['user_id'];
$eventId = $_GET['id'] ?? null;

if (!$eventId) {
    echo json_encode(['success' => false, 'message' => 'Event ID required']);
    exit;
}

try {
    // Get event details with RSVP information
    $stmt = $pdo->prepare("
        SELECT e.*,
               (
                   (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'attending') +
                   (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = e.id AND erg.status = 'attending')
               ) as attending_count,
               (
                   (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'maybe') +
                   (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = e.id AND erg.status = 'maybe')
               ) as maybe_count,
               (
                   (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = e.id AND er.status = 'not_attending') +
                   (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = e.id AND erg.status = 'not_attending')
               ) as not_attending_count,
               user_rsvp.status as user_rsvp_status,
               user_rsvp.notes as user_rsvp_notes
        FROM events e
        LEFT JOIN event_rsvps user_rsvp ON e.id = user_rsvp.event_id AND user_rsvp.user_id = ?
        WHERE e.id = ?
    ");
    $stmt->execute([$userId, $eventId]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);

    // Get event materials
    $eventMaterials = [];
    try {
        $stmt = $pdo->prepare("
            SELECT id, file_name, file_path, file_type, file_size, file_category,
                   is_header_banner, alt_text, display_order, upload_date
            FROM event_files
            WHERE event_id = ?
            ORDER BY is_header_banner DESC, display_order ASC, upload_date DESC
        ");
        $stmt->execute([$eventId]);
        $eventMaterials = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error loading event materials: " . $e->getMessage());
        $eventMaterials = [];
    }

    if (!$event) {
        echo json_encode(['success' => false, 'message' => 'Event not found', 'debug' => "Event ID: $eventId, User ID: $userId"]);
        exit;
    }

    // Get sessions for this event
    $eventSessions = [];
    try {
        $stmt = $pdo->prepare("
            SELECT es.*,
                   COUNT(sa.id) as registered_count,
                   MAX(CASE WHEN sa.member_id = ? THEN sa.id END) as user_registration_id,
                   MAX(CASE WHEN sa.member_id = ? THEN sa.attendance_status END) as user_status
            FROM event_sessions es
            LEFT JOIN session_attendance sa ON es.id = sa.session_id
            WHERE es.event_id = ? AND es.status = 'active'
            GROUP BY es.id
            ORDER BY es.start_datetime ASC
        ");
        $stmt->execute([$userId, $userId, $eventId]);
        $eventSessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error loading event sessions: " . $e->getMessage());
        $eventSessions = [];
    }

    // Format the event date and time
    $eventDateTime = new DateTime($event['event_date']);
    $formattedDate = $eventDateTime->format('l, F j, Y');
    $formattedTime = $eventDateTime->format('g:i A');

    // Check if event has passed
    $now = new DateTime();
    $isPastEvent = $eventDateTime < $now;

    // Function to fix file paths for user directory access
    function fixFilePath($path) {
        // The paths in database are stored as ../uploads/events/... (relative to admin)
        // From user directory, we need ../uploads/events/... as well
        // But the actual files are at uploads/events/... from church root

        // If path starts with ../, remove it and add back ../
        if (strpos($path, '../') === 0) {
            $cleanPath = substr($path, 3); // Remove ../
            return '../' . $cleanPath; // Add back ../
        }

        // If path doesn't start with ../, assume it's relative to church root
        return '../' . $path;
    }

    // Get header banner
    $headerBanner = null;
    $firstImage = null;

    // First, look for explicitly marked header banner
    foreach ($eventMaterials as $material) {
        if ($material['is_header_banner'] == 1) {
            $headerBanner = $material;
            break;
        }
        // Keep track of first image as fallback
        if (!$firstImage && strpos($material['file_type'], 'image/') === 0) {
            $firstImage = $material;
        }
    }

    // If no explicit header banner, use first image as fallback
    if (!$headerBanner && $firstImage) {
        $headerBanner = $firstImage;
    }





    // Generate HTML for modal body
    ob_start();
    ?>

    <!-- Header Banner -->
    <?php if ($headerBanner): ?>
    <div class="mb-4">
        <?php if (strpos($headerBanner['file_type'], 'image/') === 0): ?>
            <img src="<?php echo htmlspecialchars(fixFilePath($headerBanner['file_path'])); ?>"
                 alt="<?php echo htmlspecialchars($headerBanner['alt_text'] ?: $event['title']); ?>"
                 class="img-fluid rounded shadow-sm w-100"
                 style="max-height: 300px; object-fit: cover;">
        <?php else: ?>
            <div class="bg-primary text-white p-4 rounded text-center">
                <i class="bi bi-calendar-event display-4"></i>
                <h4 class="mt-2"><?php echo htmlspecialchars($event['title']); ?></h4>
            </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-8">
            <div class="mb-3">
                <h6><i class="bi bi-calendar-event"></i> Date & Time</h6>
                <p class="mb-1"><?php echo htmlspecialchars($formattedDate); ?></p>
                <p class="text-muted"><?php echo htmlspecialchars($formattedTime); ?></p>
            </div>

            <div class="mb-3">
                <h6><i class="bi bi-geo-alt"></i> Location</h6>
                <p><?php echo htmlspecialchars($event['location'] ?? 'Location TBD'); ?></p>
            </div>

            <div class="mb-3">
                <h6><i class="bi bi-people"></i> Capacity</h6>
                <p><?php echo $event['attending_count']; ?> / <?php echo $event['max_attendees'] ?: 'Unlimited'; ?> attendees</p>
                <?php if ($event['max_attendees']): ?>
                <div class="progress" style="height: 8px;">
                    <div class="progress-bar" role="progressbar"
                         style="width: <?php echo ($event['attending_count'] / $event['max_attendees']) * 100; ?>%"></div>
                </div>
                <?php endif; ?>
            </div>

            <?php if ($event['description']): ?>
            <div class="mb-3">
                <h6><i class="bi bi-card-text"></i> Description</h6>
                <p><?php echo nl2br(htmlspecialchars($event['description'])); ?></p>
            </div>
            <?php endif; ?>

            <!-- Requirements & Notes Section -->
            <?php if (!empty($event['requirements']) || !empty($event['notes']) || !empty($event['special_instructions'])): ?>
            <div class="mb-3">
                <h6><i class="bi bi-exclamation-triangle text-warning"></i> Requirements & Notes</h6>
                <div class="alert alert-info">
                    <?php if (!empty($event['requirements'])): ?>
                        <div class="mb-2">
                            <strong>Requirements:</strong><br>
                            <?php echo nl2br(htmlspecialchars($event['requirements'])); ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($event['notes'])): ?>
                        <div class="mb-2">
                            <strong>Notes:</strong><br>
                            <?php echo nl2br(htmlspecialchars($event['notes'])); ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($event['special_instructions'])): ?>
                        <div class="mb-2">
                            <strong>Special Instructions:</strong><br>
                            <?php echo nl2br(htmlspecialchars($event['special_instructions'])); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Default requirements if no specific field exists -->
                    <?php if (empty($event['requirements']) && empty($event['notes']) && empty($event['special_instructions'])): ?>
                        <div class="mb-2">
                            <strong>Please come with shoes</strong><br>
                            This is a requirement for all attendees.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php else: ?>
            <!-- Default Requirements & Notes Section -->
            <div class="mb-3">
                <h6><i class="bi bi-exclamation-triangle text-warning"></i> Requirements & Notes</h6>
                <div class="alert alert-info">
                    <div class="mb-2">
                        <strong>Please come with shoes</strong><br>
                        This is a requirement for all attendees.
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php if (!empty($event['image_path'])): ?>
            <div class="mb-3">
                <h6><i class="bi bi-image"></i> Event Image</h6>
                <img src="../<?php echo htmlspecialchars($event['image_path']); ?>"
                     alt="Event Image" class="img-fluid rounded" style="max-height: 300px;">
            </div>
            <?php endif; ?>

            <!-- Event Materials Section -->
            <?php if (!empty($eventMaterials)): ?>
            <div class="mb-4">
                <h6><i class="bi bi-folder2-open"></i> Event Materials</h6>

                <?php
                // Separate materials by category
                $promotionalMaterials = [];
                $documents = [];

                foreach ($eventMaterials as $material) {
                    if (!$material['is_header_banner']) {
                        if ($material['file_category'] === 'promotional') {
                            $promotionalMaterials[] = $material;
                        } else {
                            $documents[] = $material;
                        }
                    }
                }
                ?>

                <!-- Promotional Materials -->
                <?php if (!empty($promotionalMaterials)): ?>
                <div class="mb-4">
                    <h6 class="text-muted mb-3"><i class="bi bi-images text-success"></i> Promotional Materials</h6>
                    <div class="row">
                        <?php foreach (array_slice($promotionalMaterials, 0, 3) as $material): ?>
                        <div class="col-4 mb-3">
                            <div class="card border-0 shadow-sm h-100">
                                <?php if (strpos($material['file_type'], 'image/') === 0): ?>
                                    <div class="position-relative">
                                        <img src="<?php echo htmlspecialchars(fixFilePath($material['file_path'])); ?>"
                                             class="card-img-top"
                                             style="height: 120px; object-fit: cover;"
                                             alt="<?php echo htmlspecialchars($material['alt_text'] ?: $material['file_name']); ?>">
                                        <div class="position-absolute top-0 end-0 m-2">
                                            <span class="badge bg-success">
                                                <i class="bi bi-image"></i>
                                            </span>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="card-img-top d-flex align-items-center justify-content-center bg-gradient" style="height: 120px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                        <i class="bi bi-file-earmark-pdf text-primary fs-2"></i>
                                    </div>
                                <?php endif; ?>
                                <div class="card-body p-2">
                                    <p class="card-text small mb-2 fw-bold"><?php echo htmlspecialchars(substr($material['file_name'], 0, 15)) . (strlen($material['file_name']) > 15 ? '...' : ''); ?></p>
                                    <div class="d-grid">
                                        <a href="<?php echo htmlspecialchars(fixFilePath($material['file_path'])); ?>"
                                           class="btn btn-outline-success btn-sm"
                                           target="_blank"
                                           download="<?php echo htmlspecialchars($material['file_name']); ?>"
                                           title="<?php echo htmlspecialchars($material['file_name']); ?>">
                                            <i class="bi bi-download me-1"></i> Get
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php if (count($promotionalMaterials) > 3): ?>
                    <div class="text-center mt-2">
                        <small class="text-muted">
                            <i class="bi bi-info-circle"></i>
                            + <?php echo count($promotionalMaterials) - 3; ?> more material<?php echo count($promotionalMaterials) - 3 > 1 ? 's' : ''; ?> available
                        </small>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <!-- Documents -->
                <?php if (!empty($documents)): ?>
                <div class="mb-3">
                    <h6 class="text-muted mb-3"><i class="bi bi-file-earmark-text text-primary"></i> Documents & Resources</h6>
                    <div class="row">
                        <?php foreach (array_slice($documents, 0, 4) as $index => $document): ?>
                        <div class="col-6 mb-2">
                            <div class="card border-0 shadow-sm h-100">
                                <div class="card-body p-3">
                                    <div class="d-flex align-items-center mb-2">
                                        <?php
                                        $fileExt = strtolower(pathinfo($document['file_name'], PATHINFO_EXTENSION));
                                        $iconClass = 'bi-file-earmark';
                                        $iconColor = 'text-secondary';

                                        switch($fileExt) {
                                            case 'pdf':
                                                $iconClass = 'bi-file-earmark-pdf';
                                                $iconColor = 'text-danger';
                                                break;
                                            case 'doc':
                                            case 'docx':
                                                $iconClass = 'bi-file-earmark-word';
                                                $iconColor = 'text-primary';
                                                break;
                                            case 'xls':
                                            case 'xlsx':
                                                $iconClass = 'bi-file-earmark-excel';
                                                $iconColor = 'text-success';
                                                break;
                                            case 'ppt':
                                            case 'pptx':
                                                $iconClass = 'bi-file-earmark-ppt';
                                                $iconColor = 'text-warning';
                                                break;
                                        }
                                        ?>
                                        <i class="bi <?php echo $iconClass; ?> <?php echo $iconColor; ?> me-2 fs-4"></i>
                                        <div class="flex-grow-1">
                                            <div class="fw-bold small"><?php echo htmlspecialchars(substr($document['file_name'], 0, 15)) . (strlen($document['file_name']) > 15 ? '...' : ''); ?></div>
                                            <small class="text-muted"><?php echo strtoupper($fileExt); ?></small>
                                        </div>
                                    </div>
                                    <div class="d-grid">
                                        <a href="<?php echo htmlspecialchars(fixFilePath($document['file_path'])); ?>"
                                           class="btn btn-outline-primary btn-sm"
                                           target="_blank"
                                           title="<?php echo htmlspecialchars($document['file_name']); ?>">
                                            <i class="bi bi-download me-1"></i> Download
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php if (count($documents) > 4): ?>
                    <div class="text-center mt-2">
                        <small class="text-muted">
                            <i class="bi bi-info-circle"></i>
                            + <?php echo count($documents) - 4; ?> more document<?php echo count($documents) - 4 > 1 ? 's' : ''; ?> available
                        </small>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>

        <div class="col-md-4">
            <!-- Sessions Section -->
            <?php if (!empty($eventSessions)): ?>
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-collection"></i> Event Sessions</h6>
                    </div>
                    <div class="card-body">
                        <div class="sessions-list">
                            <?php foreach ($eventSessions as $session): ?>
                                <?php
                                $is_past = strtotime($session['start_datetime']) < time();
                                $is_full = $session['max_attendees'] && $session['registered_count'] >= $session['max_attendees'];
                                $is_registered = !empty($session['user_registration_id']);
                                ?>
                                <div class="session-item mb-3 p-2 border rounded <?php echo $is_registered ? 'border-success bg-light' : ''; ?>">
                                    <div class="d-flex justify-content-between align-items-start mb-1">
                                        <h6 class="mb-0 small"><?php echo htmlspecialchars($session['session_title']); ?></h6>
                                        <?php if ($is_registered): ?>
                                            <span class="badge bg-success">✓</span>
                                        <?php elseif ($is_full): ?>
                                            <span class="badge bg-warning">Full</span>
                                        <?php elseif ($is_past): ?>
                                            <span class="badge bg-secondary">Past</span>
                                        <?php endif; ?>
                                    </div>

                                    <div class="session-details small text-muted">
                                        <div class="mb-1">
                                            <i class="bi bi-calendar3 me-1"></i>
                                            <?php echo date('M j', strtotime($session['start_datetime'])); ?>
                                            <i class="bi bi-clock ms-2 me-1"></i>
                                            <?php echo date('g:i A', strtotime($session['start_datetime'])); ?>
                                        </div>

                                        <?php if (!empty($session['instructor_name'])): ?>
                                            <div class="mb-1">
                                                <i class="bi bi-person me-1"></i>
                                                <?php echo htmlspecialchars($session['instructor_name']); ?>
                                            </div>
                                        <?php endif; ?>

                                        <div>
                                            <i class="bi bi-people me-1"></i>
                                            <?php echo $session['registered_count']; ?>
                                            <?php if ($session['max_attendees']): ?>
                                                / <?php echo $session['max_attendees']; ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="text-center">
                            <a href="event_sessions.php?event_id=<?php echo $eventId; ?>" class="btn btn-primary btn-sm">
                                <i class="bi bi-collection"></i> Manage Sessions
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-clipboard-check"></i> RSVP Status</h6>
                </div>
                <div class="card-body">
                    <?php if ($event['user_rsvp_status']): ?>
                        <div class="alert alert-info mb-3">
                            <strong>Your RSVP:</strong> 
                            <?php 
                            $statusLabels = [
                                'attending' => 'Attending',
                                'maybe' => 'Maybe',
                                'not_attending' => 'Not Attending'
                            ];
                            echo $statusLabels[$event['user_rsvp_status']] ?? $event['user_rsvp_status'];
                            ?>
                            <?php if (!empty($event['user_rsvp_notes'])): ?>
                                <br><small class="text-muted"><?php echo htmlspecialchars($event['user_rsvp_notes']); ?></small>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning mb-3">
                            You haven't RSVP'd for this event yet.
                        </div>
                    <?php endif; ?>

                    <div class="rsvp-summary">
                        <h6>RSVP Summary</h6>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-success">
                                <i class="bi bi-check-circle"></i> Attending
                            </span>
                            <span class="badge bg-success"><?php echo $event['attending_count']; ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-warning">
                                <i class="bi bi-question-circle"></i> Maybe
                            </span>
                            <span class="badge bg-warning"><?php echo $event['maybe_count']; ?></span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span class="text-danger">
                                <i class="bi bi-x-circle"></i> Not Attending
                            </span>
                            <span class="badge bg-danger"><?php echo $event['not_attending_count']; ?></span>
                        </div>
                    </div>

                    <?php if ($isPastEvent): ?>
                        <div class="alert alert-secondary mt-3">
                            <i class="bi bi-clock-history"></i> This event has already passed.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php
    $html = ob_get_clean();

    echo json_encode([
        'success' => true,
        'event' => $event,
        'html' => $html
    ]);

} catch (PDOException $e) {
    error_log("Get event details error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
}

// Add formatFileSize function if it doesn't exist
if (!function_exists('formatFileSize')) {
    function formatFileSize($bytes) {
        if ($bytes == 0) return '0 Bytes';

        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));

        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }
}
?>
