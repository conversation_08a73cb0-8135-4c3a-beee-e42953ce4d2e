<?php

/**
 * Event Status Manager
 * Handles comprehensive event status management including activation, deactivation, 
 * cancellation, archiving, and late registration controls
 */
class EventStatusManager {
    private $pdo;
    private $notificationManager;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        
        // Initialize notification manager if available
        if (class_exists('SessionNotificationManager')) {
            try {
                $this->notificationManager = new SessionNotificationManager($pdo);
            } catch (Exception $e) {
                error_log("Failed to initialize notification manager: " . $e->getMessage());
            }
        }
    }
    
    /**
     * Activate an event (make it visible and allow registrations)
     */
    public function activateEvent($eventId, $adminId, $reason = null) {
        try {
            $this->pdo->beginTransaction();
            
            // Get current event status
            $currentStatus = $this->getEventStatus($eventId);
            if (!$currentStatus) {
                throw new Exception("Event not found");
            }
            
            // Update event status
            $stmt = $this->pdo->prepare("
                UPDATE events 
                SET status = 'published', is_active = 1, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$eventId]);
            
            // Log status change
            $this->logStatusChange($eventId, $currentStatus['status'], 'published', $reason, $adminId);
            
            // Send notifications
            $this->sendStatusChangeNotifications($eventId, 'activated', $adminId);
            
            $this->pdo->commit();
            return ['success' => true, 'message' => 'Event activated successfully'];
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error activating event: " . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Deactivate an event (hide from users but preserve data)
     */
    public function deactivateEvent($eventId, $adminId, $reason = null) {
        try {
            $this->pdo->beginTransaction();
            
            $currentStatus = $this->getEventStatus($eventId);
            if (!$currentStatus) {
                throw new Exception("Event not found");
            }
            
            // Update event status
            $stmt = $this->pdo->prepare("
                UPDATE events 
                SET status = 'draft', is_active = 0, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$eventId]);
            
            // Log status change
            $this->logStatusChange($eventId, $currentStatus['status'], 'draft', $reason, $adminId);
            
            // Send notifications
            $this->sendStatusChangeNotifications($eventId, 'deactivated', $adminId);
            
            $this->pdo->commit();
            return ['success' => true, 'message' => 'Event deactivated successfully'];
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error deactivating event: " . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Cancel an event with proper notification handling
     */
    public function cancelEvent($eventId, $adminId, $reason = null) {
        try {
            $this->pdo->beginTransaction();
            
            $currentStatus = $this->getEventStatus($eventId);
            if (!$currentStatus) {
                throw new Exception("Event not found");
            }
            
            // Update event status
            $stmt = $this->pdo->prepare("
                UPDATE events 
                SET status = 'cancelled', is_active = 0, 
                    cancellation_reason = ?, cancelled_at = NOW(), cancelled_by = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$reason, $adminId, $eventId]);
            
            // Log status change
            $this->logStatusChange($eventId, $currentStatus['status'], 'cancelled', $reason, $adminId);
            
            // Send cancellation notifications to all registered attendees
            $this->sendCancellationNotifications($eventId, $reason, $adminId);
            
            $this->pdo->commit();
            return ['success' => true, 'message' => 'Event cancelled successfully'];
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error cancelling event: " . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Archive a completed event
     */
    public function archiveEvent($eventId, $adminId, $reason = null) {
        try {
            $this->pdo->beginTransaction();
            
            $currentStatus = $this->getEventStatus($eventId);
            if (!$currentStatus) {
                throw new Exception("Event not found");
            }
            
            // Update event status
            $stmt = $this->pdo->prepare("
                UPDATE events 
                SET status = 'archived', is_active = 0,
                    archived_at = NOW(), archived_by = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$adminId, $eventId]);
            
            // Log status change
            $this->logStatusChange($eventId, $currentStatus['status'], 'archived', $reason, $adminId);
            
            $this->pdo->commit();
            return ['success' => true, 'message' => 'Event archived successfully'];
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error archiving event: " . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Update late registration settings for an event
     */
    public function updateLateRegistrationSettings($eventId, $allowLateRegistration, $cutoffHours = 0, $adminId = null) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE events 
                SET allow_late_registration = ?, 
                    late_registration_cutoff_hours = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$allowLateRegistration ? 1 : 0, $cutoffHours, $eventId]);
            
            return ['success' => true, 'message' => 'Late registration settings updated successfully'];
            
        } catch (Exception $e) {
            error_log("Error updating late registration settings: " . $e->getMessage());
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Check if late registration is allowed for an event
     */
    public function isLateRegistrationAllowed($eventId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT allow_late_registration, late_registration_cutoff_hours, event_date
                FROM events 
                WHERE id = ?
            ");
            $stmt->execute([$eventId]);
            $event = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$event) {
                return false;
            }
            
            // If late registration is disabled, return false
            if (!$event['allow_late_registration']) {
                return false;
            }
            
            // If no cutoff hours set, allow registration
            if ($event['late_registration_cutoff_hours'] == 0) {
                return true;
            }
            
            // Check if we're within the cutoff period
            $eventTime = new DateTime($event['event_date']);
            $cutoffTime = clone $eventTime;
            $cutoffTime->sub(new DateInterval('PT' . $event['late_registration_cutoff_hours'] . 'H'));
            $now = new DateTime();
            
            return $now <= $cutoffTime;
            
        } catch (Exception $e) {
            error_log("Error checking late registration: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get current event status and details
     */
    public function getEventStatus($eventId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT id, title, status, is_active, allow_late_registration, 
                       late_registration_cutoff_hours, event_date,
                       cancellation_reason, cancelled_at, cancelled_by,
                       archived_at, archived_by
                FROM events 
                WHERE id = ?
            ");
            $stmt->execute([$eventId]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error getting event status: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log status change to history
     */
    private function logStatusChange($eventId, $oldStatus, $newStatus, $reason, $adminId) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO event_status_history 
                (event_id, old_status, new_status, reason, changed_by)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$eventId, $oldStatus, $newStatus, $reason, $adminId]);
            
        } catch (Exception $e) {
            error_log("Error logging status change: " . $e->getMessage());
        }
    }
    
    /**
     * Send status change notifications
     */
    private function sendStatusChangeNotifications($eventId, $action, $adminId) {
        // Implementation will depend on existing notification system
        // This is a placeholder for notification logic
        try {
            if ($this->notificationManager) {
                // Send notifications to relevant parties
                // Implementation details would go here
            }
        } catch (Exception $e) {
            error_log("Error sending status change notifications: " . $e->getMessage());
        }
    }
    
    /**
     * Send cancellation notifications to all registered attendees
     */
    private function sendCancellationNotifications($eventId, $reason, $adminId) {
        try {
            // Get all registered attendees
            $stmt = $this->pdo->prepare("
                SELECT DISTINCT m.id, m.email, m.full_name
                FROM event_rsvps er
                JOIN members m ON er.member_id = m.id
                WHERE er.event_id = ? AND er.status = 'attending'
            ");
            $stmt->execute([$eventId]);
            $attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Get event details
            $stmt = $this->pdo->prepare("SELECT title, event_date FROM events WHERE id = ?");
            $stmt->execute([$eventId]);
            $event = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Send notifications (implementation would depend on existing notification system)
            foreach ($attendees as $attendee) {
                // Create notification or send email
                // Implementation details would go here
            }
            
        } catch (Exception $e) {
            error_log("Error sending cancellation notifications: " . $e->getMessage());
        }
    }
    
    /**
     * Get status history for an event
     */
    public function getStatusHistory($eventId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT esh.*, a.username as changed_by_name
                FROM event_status_history esh
                LEFT JOIN admins a ON esh.changed_by = a.id
                WHERE esh.event_id = ?
                ORDER BY esh.changed_at DESC
            ");
            $stmt->execute([$eventId]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Error getting status history: " . $e->getMessage());
            return [];
        }
    }
}
