-- Granular Permission System Enhancement for RBAC
-- This extends the existing RBAC system to support individual user permissions

-- Permission Categories Table (for organizing permissions in the UI)
CREATE TABLE IF NOT EXISTS permission_categories (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    category_name VARCHAR(50) NOT NULL UNIQUE,
    category_display_name VARCHAR(100) NOT NULL,
    category_description TEXT,
    sort_order INT(11) DEFAULT 0,
    icon_class VARCHAR(50) DEFAULT 'bi-gear',
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert permission categories
INSERT INTO permission_categories (category_name, category_display_name, category_description, sort_order, icon_class) VALUES
('dashboard', 'Dashboard Access', 'Access to main dashboard and analytics', 1, 'bi-speedometer2'),
('member_management', 'Member Management', 'Manage church members, add/edit/view member information', 2, 'bi-people'),
('event_management', 'Event Management', 'Create and manage events, sessions, and attendance', 3, 'bi-calendar-event'),
('email_management', 'Email Management', 'Send emails, manage templates, and view analytics', 4, 'bi-envelope'),
('sms_management', 'SMS Management', 'Send SMS messages and manage SMS templates', 5, 'bi-chat-dots'),
('donations', 'Donations & Finance', 'Manage donations, gifts, and financial records', 6, 'bi-currency-dollar'),
('integrations', 'Integrations', 'Manage external integrations and APIs', 7, 'bi-link-45deg'),
('system_settings', 'System Settings', 'Configure system settings, appearance, and security', 8, 'bi-gear'),
('reports', 'Reports & Analytics', 'View and export various reports and analytics', 9, 'bi-graph-up'),
('notifications', 'Notifications', 'View and manage system notifications', 10, 'bi-bell');

-- Granular Permissions Table (replaces the hardcoded page permissions)
CREATE TABLE IF NOT EXISTS granular_permissions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    permission_key VARCHAR(100) NOT NULL UNIQUE,
    permission_name VARCHAR(150) NOT NULL,
    permission_description TEXT,
    category_id INT(11) NOT NULL,
    page_file VARCHAR(100), -- The actual PHP file this permission controls
    sort_order INT(11) DEFAULT 0,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES permission_categories(id) ON DELETE CASCADE,
    INDEX idx_category_id (category_id),
    INDEX idx_permission_key (permission_key),
    INDEX idx_page_file (page_file),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- User Individual Permissions Table
CREATE TABLE IF NOT EXISTS user_individual_permissions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) NOT NULL,
    permission_id INT(11) NOT NULL,
    granted_by INT(11) NOT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active TINYINT(1) DEFAULT 1,
    notes TEXT,
    UNIQUE KEY unique_user_permission (user_id, permission_id),
    FOREIGN KEY (permission_id) REFERENCES granular_permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES admins(id) ON DELETE RESTRICT,
    INDEX idx_user_id (user_id),
    INDEX idx_permission_id (permission_id),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert granular permissions for each category
-- Dashboard Access
INSERT INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('dashboard.view', 'View Dashboard', 'Access to main admin dashboard', 1, 'dashboard.php', 1),
('dashboard.analytics', 'View Analytics', 'Access to dashboard analytics and charts', 1, NULL, 2);

-- Member Management
INSERT INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('members.view', 'View Members', 'View member list and profiles', 2, 'members.php', 1),
('members.add', 'Add Members', 'Add new members to the system', 2, 'add_member.php', 2),
('members.edit', 'Edit Members', 'Edit existing member information', 2, 'edit_member.php', 3),
('members.delete', 'Delete Members', 'Delete members from the system', 2, NULL, 4),
('members.view_profile', 'View Member Profiles', 'View detailed member profiles', 2, 'view_member.php', 5),
('members.family_management', 'Family Management', 'Manage family relationships', 2, 'family_management.php', 6),
('members.skills', 'Member Skills', 'Manage member skills and talents', 2, 'member_skills.php', 7),
('members.requests', 'Prayer Requests', 'Manage member prayer requests', 2, 'requests.php', 8),
('members.volunteers', 'Volunteer Opportunities', 'Manage volunteer opportunities', 2, 'volunteer_opportunities.php', 9);

-- Event Management
INSERT INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('events.view', 'View Events', 'View event list and details', 3, 'events.php', 1),
('events.create', 'Create Events', 'Create new events', 3, NULL, 2),
('events.edit', 'Edit Events', 'Edit existing events', 3, NULL, 3),
('events.delete', 'Delete Events', 'Delete events', 3, NULL, 4),
('events.attendance', 'Event Attendance', 'Manage event attendance', 3, 'event_attendance.php', 5),
('events.sessions', 'Event Sessions', 'Manage event sessions', 3, 'event_sessions.php', 6),
('events.categories', 'Event Categories', 'Manage event categories', 3, 'event_categories.php', 7),
('events.reports', 'Event Reports', 'View event reports and analytics', 3, 'event_reports.php', 8);

-- Email Management
INSERT INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('email.bulk_send', 'Send Bulk Email', 'Send bulk emails to members', 4, 'bulk_email.php', 1),
('email.scheduler', 'Email Scheduler', 'Schedule emails for future sending', 4, 'email_scheduler.php', 2),
('email.templates', 'Email Templates', 'Manage email templates', 4, 'email_templates.php', 3),
('email.contacts', 'Manage Contacts', 'Manage email contacts and groups', 4, 'contacts.php', 4),
('email.contact_groups', 'Contact Groups', 'Manage contact groups', 4, 'contact_groups.php', 5),
('email.birthday', 'Birthday Messages', 'Manage birthday email messages', 4, 'birthday.php', 6),
('email.birthday_send', 'Send Birthday Emails', 'Send bulk birthday emails', 4, 'send_birthday_emails.php', 7),
('email.birthday_test', 'Test Birthday Emails', 'Test birthday email functionality', 4, 'test_birthday_email.php', 8),
('email.birthday_notifications', 'Birthday Notifications', 'Send birthday notifications', 4, 'send_birthday_notification.php', 9),
('email.automated_templates', 'Automated Templates', 'Manage automated email templates', 4, 'automated_templates.php', 10),
('email.whatsapp', 'WhatsApp Messages', 'Send WhatsApp messages', 4, 'whatsapp_messages.php', 11),
('email.analytics', 'Email Analytics', 'View email analytics and reports', 4, 'email_analytics.php', 12);

-- SMS Management
INSERT INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('sms.single_send', 'Send Single SMS', 'Send individual SMS messages', 5, 'single_sms.php', 1),
('sms.bulk_send', 'Send Bulk SMS', 'Send bulk SMS messages', 5, 'bulk_sms.php', 2),
('sms.templates', 'SMS Templates', 'Manage SMS templates', 5, 'sms_templates.php', 3),
('sms.analytics', 'SMS Analytics', 'View SMS analytics and reports', 5, 'sms_analytics.php', 4);

-- Donations & Finance
INSERT INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('donations.view', 'View Donations', 'View donation records', 6, 'donations.php', 1),
('donations.manage', 'Manage Donations', 'Add and edit donation records', 6, NULL, 2),
('donations.gifts', 'Gift Management', 'Manage birthday and special gifts', 6, 'gift_management.php', 3),
('donations.enhanced', 'Enhanced Donations', 'Access enhanced donation features', 6, 'enhanced_donate.php', 4),
('donations.payment_integration', 'Payment Integration', 'Manage payment gateway integration', 6, 'payment_integration.php', 5),
('donations.payment_tables', 'Payment Tables', 'Manage payment table structure', 6, 'payment_tables.php', 6);

-- Integrations
INSERT INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('integrations.calendar', 'Calendar Integration', 'Manage calendar integration', 7, 'calendar_integration.php', 1),
('integrations.social_media', 'Social Media Integration', 'Manage social media integration', 7, 'social_media_integration.php', 2);

-- System Settings
INSERT INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('settings.general', 'General Settings', 'Manage general system settings', 8, 'settings.php', 1),
('settings.appearance', 'Appearance Settings', 'Manage system appearance and themes', 8, 'appearance_settings.php', 2),
('settings.branding', 'Branding Settings', 'Manage system branding and logos', 8, 'branding_settings.php', 3),
('settings.logo', 'Logo Management', 'Manage system logos', 8, 'logo_management_consolidated.php', 4),
('settings.custom_fields', 'Custom Fields', 'Manage custom fields', 8, 'custom_fields.php', 5),
('settings.security_audit', 'Security Audit', 'View security audit logs', 8, 'security_audit.php', 6),
('settings.security_settings', 'Security Settings', 'Manage security settings', 8, 'security_settings.php', 7),
('settings.backup', 'Database Backup', 'Manage database backups', 8, 'backup_management.php', 8),
('settings.profile', 'My Profile', 'Manage personal profile', 8, 'profile.php', 9);

-- Notifications
INSERT INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('notifications.view', 'View Notifications', 'View system notifications', 10, 'notifications.php', 1);

-- Super Admin Only Permissions (these should only be assigned to super admins)
INSERT INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('admin.rbac_management', 'RBAC Management', 'Manage role-based access control', 8, 'setup_rbac_system.php', 10),
('admin.create_users', 'Create Admin Users', 'Create new admin users', 8, 'create_admin_users.php', 11),
('admin.super_dashboard', 'Super Admin Dashboard', 'Access super admin dashboard', 1, 'super_admin_dashboard.php', 3),
('admin.permission_management', 'Permission Management', 'Manage individual user permissions', 8, 'manage_user_permissions.php', 12);
