<?php
session_start();

// Include the configuration file
require_once '../config.php';
require_once 'includes/rbac_access_control.php';

// Initialize RBAC system
$rbac = new RBACAccessControl($pdo, $_SESSION['admin_id'] ?? null);

// Get user's current role for display
$user_roles = $rbac->getUserRoles();
$primary_role = $rbac->getPrimaryRole();
$appropriate_dashboard = $rbac->getDefaultDashboard();

// Get reason from URL parameter
$reason = $_GET['reason'] ?? 'You do not have permission to access this resource';

// Page title and header info
$page_title = 'Access Denied';
$page_header = 'Access Denied';
$page_description = 'You do not have permission to access this resource';

// Include header
include 'includes/header.php';
?>

<style>
.access-denied-container {
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
}
.access-denied-card {
    max-width: 600px;
    text-align: center;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.access-denied-icon {
    font-size: 4rem;
    color: #dc3545;
    margin-bottom: 1rem;
}
.role-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}
</style>

<div class="access-denied-container">
    <div class="card access-denied-card">
        <div class="card-body p-5">
            <div class="access-denied-icon">
                <i class="bi bi-shield-exclamation"></i>
            </div>
            
            <h2 class="text-danger mb-3">Access Denied</h2>
            
            <p class="lead text-muted mb-4">
                <?php echo htmlspecialchars($reason); ?>
            </p>

            <?php if (!empty($user_roles)): ?>
                <div class="role-info">
                    <h5 class="text-white mb-2">
                        <i class="bi bi-person-badge"></i> Your Current Role<?php echo count($user_roles) > 1 ? 's' : ''; ?>
                    </h5>
                    <?php foreach ($user_roles as $role): ?>
                        <h4 class="text-white"><?php echo htmlspecialchars($role['role_display_name']); ?></h4>
                        <?php if (count($user_roles) === 1) break; ?>
                    <?php endforeach; ?>
                    <?php if (count($user_roles) > 1): ?>
                        <p class="text-white-50 mb-0">
                            You have <?php echo count($user_roles); ?> active roles
                        </p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <div class="mt-4">
                <h6 class="text-muted mb-3">What you can do:</h6>
                
                <div class="d-grid gap-2 d-md-block">
                    <?php if ($appropriate_dashboard && $appropriate_dashboard !== 'access_denied.php'): ?>
                        <a href="<?php echo htmlspecialchars($appropriate_dashboard); ?>" class="btn btn-primary">
                            <i class="bi bi-speedometer2"></i> Go to Your Dashboard
                        </a>
                    <?php endif; ?>

                    <a href="javascript:history.back()" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Go Back
                    </a>

                    <?php if ($rbac->isSuperAdmin()): ?>
                        <a href="setup_rbac_system.php" class="btn btn-outline-info">
                            <i class="bi bi-gear"></i> Manage Permissions
                        </a>
                    <?php endif; ?>

                    <a href="logout.php" class="btn btn-outline-danger">
                        <i class="bi bi-box-arrow-right"></i> Logout
                    </a>
                </div>
            </div>
            
            <div class="mt-4 pt-4 border-top">
                <small class="text-muted">
                    <i class="bi bi-info-circle"></i>
                    If you believe you should have access to this resource, please contact your system administrator.
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Access Guide -->
<div class="row mt-5">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-question-circle"></i> Dashboard Access Guide
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Available Dashboards:</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="bi bi-shield-check text-danger"></i>
                                <strong>Super Admin Dashboard</strong>
                                <br>
                                <small class="text-muted">Complete system oversight and user management</small>
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-calendar-event text-warning"></i>
                                <strong>Event Coordinator Dashboard</strong>
                                <br>
                                <small class="text-muted">Multi-session oversight for assigned events</small>
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-person-workspace text-info"></i>
                                <strong>Organizer Dashboard</strong>
                                <br>
                                <small class="text-muted">Event planning and setup tools</small>
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-person-video2 text-success"></i>
                                <strong>Session Moderator Dashboard</strong>
                                <br>
                                <small class="text-muted">Individual session management</small>
                            </li>
                            <li class="mb-2">
                                <i class="bi bi-person-check text-secondary"></i>
                                <strong>Staff Dashboard</strong>
                                <br>
                                <small class="text-muted">Check-in and basic attendance marking</small>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary">How to Get Access:</h6>
                        <ol>
                            <li class="mb-2">
                                <strong>Contact System Administrator</strong>
                                <br>
                                <small class="text-muted">Request the appropriate role for your responsibilities</small>
                            </li>
                            <li class="mb-2">
                                <strong>Role Assignment</strong>
                                <br>
                                <small class="text-muted">Admin will assign you the correct role and permissions</small>
                            </li>
                            <li class="mb-2">
                                <strong>Event/Session Assignment</strong>
                                <br>
                                <small class="text-muted">For coordinators and moderators, specific assignments are required</small>
                            </li>
                            <li class="mb-2">
                                <strong>Login Again</strong>
                                <br>
                                <small class="text-muted">Log out and log back in to refresh your permissions</small>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<?php include 'includes/footer.php'; ?>
