<?php
/**
 * Smart Attendance Rule Functions
 * 
 * This file contains all the functions for implementing smart attendance rules
 * that automatically mark event attendance based on session participation patterns.
 */

/**
 * Apply Session Percentage Rule
 * Mark event as attended if attendee participated in >= X% of their registered sessions
 */
function applySessionPercentageRule($pdo, $event_id, $percentage) {
    $marked_count = 0;
    
    // Get all attendees and their session participation
    $stmt = $pdo->prepare("
        SELECT 
            attendee_id,
            attendee_type,
            total_registered,
            total_attended,
            (total_attended / total_registered * 100) as attendance_percentage
        FROM (
            SELECT 
                CASE 
                    WHEN sa.member_id IS NOT NULL THEN CONCAT('member_', sa.member_id)
                    ELSE CONCAT('guest_', sa.guest_name, '_', sa.guest_email)
                END as attendee_id,
                CASE 
                    WHEN sa.member_id IS NOT NULL THEN 'member'
                    ELSE 'guest'
                END as attendee_type,
                sa.member_id,
                sa.guest_name,
                sa.guest_email,
                COUNT(*) as total_registered,
                COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as total_attended
            FROM session_attendance sa
            JOIN event_sessions es ON sa.session_id = es.id
            WHERE es.event_id = ? AND es.status = 'active'
            GROUP BY attendee_id, attendee_type, sa.member_id, sa.guest_name, sa.guest_email
        ) session_stats
        WHERE (total_attended / total_registered * 100) >= ?
    ");
    $stmt->execute([$event_id, $percentage]);
    $qualified_attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($qualified_attendees as $attendee) {
        if ($attendee['attendee_type'] === 'member') {
            // Update member RSVP
            $update_stmt = $pdo->prepare("
                UPDATE event_rsvps 
                SET actually_attended = 1 
                WHERE event_id = ? AND member_id = ? AND status = 'attending' AND actually_attended IS NULL
            ");
            $update_stmt->execute([$event_id, $attendee['member_id']]);
            $marked_count += $update_stmt->rowCount();
        } else {
            // Update guest RSVP
            $update_stmt = $pdo->prepare("
                UPDATE event_rsvps_guests 
                SET actually_attended = 1 
                WHERE event_id = ? AND guest_name = ? AND guest_email = ? AND status = 'attending' AND actually_attended IS NULL
            ");
            $update_stmt->execute([$event_id, $attendee['guest_name'], $attendee['guest_email']]);
            $marked_count += $update_stmt->rowCount();
        }
    }
    
    return $marked_count;
}

/**
 * Preview Session Percentage Rule
 * Count how many attendees would be marked with the percentage rule
 */
function previewSessionPercentageRule($pdo, $event_id, $percentage) {
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM (
            SELECT 
                CASE 
                    WHEN sa.member_id IS NOT NULL THEN CONCAT('member_', sa.member_id)
                    ELSE CONCAT('guest_', sa.guest_name, '_', sa.guest_email)
                END as attendee_id,
                CASE 
                    WHEN sa.member_id IS NOT NULL THEN 'member'
                    ELSE 'guest'
                END as attendee_type,
                sa.member_id,
                sa.guest_name,
                sa.guest_email,
                COUNT(*) as total_registered,
                COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as total_attended
            FROM session_attendance sa
            JOIN event_sessions es ON sa.session_id = es.id
            WHERE es.event_id = ? AND es.status = 'active'
            GROUP BY attendee_id, attendee_type, sa.member_id, sa.guest_name, sa.guest_email
        ) session_stats
        WHERE (total_attended / total_registered * 100) >= ?
    ");
    $stmt->execute([$event_id, $percentage]);
    return $stmt->fetchColumn();
}

/**
 * Apply Session Count Rule
 * Mark event as attended if attendee participated in >= X sessions
 */
function applySessionCountRule($pdo, $event_id, $min_sessions) {
    $marked_count = 0;
    
    // Get all attendees who attended minimum number of sessions
    $stmt = $pdo->prepare("
        SELECT 
            attendee_id,
            attendee_type,
            member_id,
            guest_name,
            guest_email,
            total_attended
        FROM (
            SELECT 
                CASE 
                    WHEN sa.member_id IS NOT NULL THEN CONCAT('member_', sa.member_id)
                    ELSE CONCAT('guest_', sa.guest_name, '_', sa.guest_email)
                END as attendee_id,
                CASE 
                    WHEN sa.member_id IS NOT NULL THEN 'member'
                    ELSE 'guest'
                END as attendee_type,
                sa.member_id,
                sa.guest_name,
                sa.guest_email,
                COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as total_attended
            FROM session_attendance sa
            JOIN event_sessions es ON sa.session_id = es.id
            WHERE es.event_id = ? AND es.status = 'active'
            GROUP BY attendee_id, attendee_type, sa.member_id, sa.guest_name, sa.guest_email
        ) session_stats
        WHERE total_attended >= ?
    ");
    $stmt->execute([$event_id, $min_sessions]);
    $qualified_attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($qualified_attendees as $attendee) {
        if ($attendee['attendee_type'] === 'member') {
            // Update member RSVP
            $update_stmt = $pdo->prepare("
                UPDATE event_rsvps 
                SET actually_attended = 1 
                WHERE event_id = ? AND member_id = ? AND status = 'attending' AND actually_attended IS NULL
            ");
            $update_stmt->execute([$event_id, $attendee['member_id']]);
            $marked_count += $update_stmt->rowCount();
        } else {
            // Update guest RSVP
            $update_stmt = $pdo->prepare("
                UPDATE event_rsvps_guests 
                SET actually_attended = 1 
                WHERE event_id = ? AND guest_name = ? AND guest_email = ? AND status = 'attending' AND actually_attended IS NULL
            ");
            $update_stmt->execute([$event_id, $attendee['guest_name'], $attendee['guest_email']]);
            $marked_count += $update_stmt->rowCount();
        }
    }
    
    return $marked_count;
}

/**
 * Preview Session Count Rule
 */
function previewSessionCountRule($pdo, $event_id, $min_sessions) {
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM (
            SELECT 
                CASE 
                    WHEN sa.member_id IS NOT NULL THEN CONCAT('member_', sa.member_id)
                    ELSE CONCAT('guest_', sa.guest_name, '_', sa.guest_email)
                END as attendee_id,
                COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as total_attended
            FROM session_attendance sa
            JOIN event_sessions es ON sa.session_id = es.id
            WHERE es.event_id = ? AND es.status = 'active'
            GROUP BY attendee_id
        ) session_stats
        WHERE total_attended >= ?
    ");
    $stmt->execute([$event_id, $min_sessions]);
    return $stmt->fetchColumn();
}

/**
 * Apply Core Sessions Rule
 * Mark event as attended if attendee participated in ALL specified core sessions
 */
function applyCoreSessionRule($pdo, $event_id, $core_session_ids) {
    $marked_count = 0;
    $core_session_count = count($core_session_ids);
    
    if ($core_session_count === 0) {
        return 0;
    }
    
    $placeholders = str_repeat('?,', $core_session_count - 1) . '?';
    
    // Get attendees who attended ALL core sessions
    $stmt = $pdo->prepare("
        SELECT 
            attendee_id,
            attendee_type,
            member_id,
            guest_name,
            guest_email
        FROM (
            SELECT 
                CASE 
                    WHEN sa.member_id IS NOT NULL THEN CONCAT('member_', sa.member_id)
                    ELSE CONCAT('guest_', sa.guest_name, '_', sa.guest_email)
                END as attendee_id,
                CASE 
                    WHEN sa.member_id IS NOT NULL THEN 'member'
                    ELSE 'guest'
                END as attendee_type,
                sa.member_id,
                sa.guest_name,
                sa.guest_email,
                COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as core_attended
            FROM session_attendance sa
            WHERE sa.session_id IN ($placeholders)
            GROUP BY attendee_id, attendee_type, sa.member_id, sa.guest_name, sa.guest_email
        ) core_stats
        WHERE core_attended = ?
    ");
    
    $params = array_merge($core_session_ids, [$core_session_count]);
    $stmt->execute($params);
    $qualified_attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($qualified_attendees as $attendee) {
        if ($attendee['attendee_type'] === 'member') {
            // Update member RSVP
            $update_stmt = $pdo->prepare("
                UPDATE event_rsvps 
                SET actually_attended = 1 
                WHERE event_id = ? AND member_id = ? AND status = 'attending' AND actually_attended IS NULL
            ");
            $update_stmt->execute([$event_id, $attendee['member_id']]);
            $marked_count += $update_stmt->rowCount();
        } else {
            // Update guest RSVP
            $update_stmt = $pdo->prepare("
                UPDATE event_rsvps_guests 
                SET actually_attended = 1 
                WHERE event_id = ? AND guest_name = ? AND guest_email = ? AND status = 'attending' AND actually_attended IS NULL
            ");
            $update_stmt->execute([$event_id, $attendee['guest_name'], $attendee['guest_email']]);
            $marked_count += $update_stmt->rowCount();
        }
    }
    
    return $marked_count;
}

/**
 * Preview Core Sessions Rule
 */
function previewCoreSessionRule($pdo, $event_id, $core_session_ids) {
    $core_session_count = count($core_session_ids);
    
    if ($core_session_count === 0) {
        return 0;
    }
    
    $placeholders = str_repeat('?,', $core_session_count - 1) . '?';
    
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM (
            SELECT 
                CASE 
                    WHEN sa.member_id IS NOT NULL THEN CONCAT('member_', sa.member_id)
                    ELSE CONCAT('guest_', sa.guest_name, '_', sa.guest_email)
                END as attendee_id,
                COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as core_attended
            FROM session_attendance sa
            WHERE sa.session_id IN ($placeholders)
            GROUP BY attendee_id
        ) core_stats
        WHERE core_attended = ?
    ");
    
    $params = array_merge($core_session_ids, [$core_session_count]);
    $stmt->execute($params);
    return $stmt->fetchColumn();
}

/**
 * Apply Time-Based Rule
 * Mark event as attended if attendee was present during core event hours
 */
function applyTimeBasedRule($pdo, $event_id, $start_time, $end_time) {
    $marked_count = 0;

    // Get attendees who attended sessions during core hours
    $stmt = $pdo->prepare("
        SELECT DISTINCT
            CASE
                WHEN sa.member_id IS NOT NULL THEN CONCAT('member_', sa.member_id)
                ELSE CONCAT('guest_', sa.guest_name, '_', sa.guest_email)
            END as attendee_id,
            CASE
                WHEN sa.member_id IS NOT NULL THEN 'member'
                ELSE 'guest'
            END as attendee_type,
            sa.member_id,
            sa.guest_name,
            sa.guest_email
        FROM session_attendance sa
        JOIN event_sessions es ON sa.session_id = es.id
        WHERE es.event_id = ?
        AND es.status = 'active'
        AND sa.attendance_status = 'attended'
        AND TIME(es.start_datetime) >= ?
        AND TIME(es.end_datetime) <= ?
    ");
    $stmt->execute([$event_id, $start_time, $end_time]);
    $qualified_attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($qualified_attendees as $attendee) {
        if ($attendee['attendee_type'] === 'member') {
            // Update member RSVP
            $update_stmt = $pdo->prepare("
                UPDATE event_rsvps
                SET actually_attended = 1
                WHERE event_id = ? AND member_id = ? AND status = 'attending' AND actually_attended IS NULL
            ");
            $update_stmt->execute([$event_id, $attendee['member_id']]);
            $marked_count += $update_stmt->rowCount();
        } else {
            // Update guest RSVP
            $update_stmt = $pdo->prepare("
                UPDATE event_rsvps_guests
                SET actually_attended = 1
                WHERE event_id = ? AND guest_name = ? AND guest_email = ? AND status = 'attending' AND actually_attended IS NULL
            ");
            $update_stmt->execute([$event_id, $attendee['guest_name'], $attendee['guest_email']]);
            $marked_count += $update_stmt->rowCount();
        }
    }

    return $marked_count;
}

/**
 * Preview Time-Based Rule
 */
function previewTimeBasedRule($pdo, $event_id, $start_time, $end_time) {
    $stmt = $pdo->prepare("
        SELECT COUNT(DISTINCT
            CASE
                WHEN sa.member_id IS NOT NULL THEN CONCAT('member_', sa.member_id)
                ELSE CONCAT('guest_', sa.guest_name, '_', sa.guest_email)
            END
        ) as count
        FROM session_attendance sa
        JOIN event_sessions es ON sa.session_id = es.id
        WHERE es.event_id = ?
        AND es.status = 'active'
        AND sa.attendance_status = 'attended'
        AND TIME(es.start_datetime) >= ?
        AND TIME(es.end_datetime) <= ?
    ");
    $stmt->execute([$event_id, $start_time, $end_time]);
    return $stmt->fetchColumn();
}

/**
 * Apply Reverse Propagation Rule
 * For attendees marked as attended at event level, mark all their registered sessions as attended
 */
function applyReversePropagationRule($pdo, $event_id) {
    $marked_count = 0;

    // Get members marked as attended at event level
    $stmt = $pdo->prepare("
        SELECT member_id
        FROM event_rsvps
        WHERE event_id = ? AND status = 'attending' AND actually_attended = 1
    ");
    $stmt->execute([$event_id]);
    $attended_members = $stmt->fetchAll(PDO::FETCH_COLUMN);

    foreach ($attended_members as $member_id) {
        // Mark all their registered sessions as attended
        $update_stmt = $pdo->prepare("
            UPDATE session_attendance sa
            JOIN event_sessions es ON sa.session_id = es.id
            SET sa.attendance_status = 'attended',
                sa.attendance_date = COALESCE(sa.attendance_date, NOW())
            WHERE es.event_id = ?
            AND sa.member_id = ?
            AND sa.attendance_status != 'attended'
        ");
        $update_stmt->execute([$event_id, $member_id]);
        $marked_count += $update_stmt->rowCount();
    }

    // Get guests marked as attended at event level
    $stmt = $pdo->prepare("
        SELECT guest_name, guest_email
        FROM event_rsvps_guests
        WHERE event_id = ? AND status = 'attending' AND actually_attended = 1
    ");
    $stmt->execute([$event_id]);
    $attended_guests = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($attended_guests as $guest) {
        // Mark all their registered sessions as attended
        $update_stmt = $pdo->prepare("
            UPDATE session_attendance sa
            JOIN event_sessions es ON sa.session_id = es.id
            SET sa.attendance_status = 'attended',
                sa.attendance_date = COALESCE(sa.attendance_date, NOW())
            WHERE es.event_id = ?
            AND sa.guest_name = ?
            AND sa.guest_email = ?
            AND sa.attendance_status != 'attended'
        ");
        $update_stmt->execute([$event_id, $guest['guest_name'], $guest['guest_email']]);
        $marked_count += $update_stmt->rowCount();
    }

    return $marked_count;
}

/**
 * Preview Reverse Propagation Rule
 */
function previewReversePropagationRule($pdo, $event_id) {
    // Count session attendance records that would be updated
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM session_attendance sa
        JOIN event_sessions es ON sa.session_id = es.id
        WHERE es.event_id = ?
        AND sa.attendance_status != 'attended'
        AND (
            (sa.member_id IS NOT NULL AND sa.member_id IN (
                SELECT member_id FROM event_rsvps
                WHERE event_id = ? AND status = 'attending' AND actually_attended = 1
            ))
            OR
            (sa.member_id IS NULL AND CONCAT(sa.guest_name, '|', sa.guest_email) IN (
                SELECT CONCAT(guest_name, '|', guest_email) FROM event_rsvps_guests
                WHERE event_id = ? AND status = 'attending' AND actually_attended = 1
            ))
        )
    ");
    $stmt->execute([$event_id, $event_id, $event_id]);
    return $stmt->fetchColumn();
}
?>
