/**
 * Service Worker for Universal Event Management Platform PWA
 * Provides offline functionality, caching, and background sync
 */

const CACHE_NAME = 'universal-event-manager-v1.0.0';
const OFFLINE_URL = './offline.html';

// Get base path dynamically
const BASE_PATH = self.location.pathname.replace('/pwa/sw.js', '');

// Files to cache for offline functionality
const CACHE_URLS = [
    './',
    './index.php',
    './manifest.json.php',
    '../dashboard.php',
    '../events.php',
    '../universal_ai_dashboard.php',
    '../universal_analytics_dashboard.php',
    '../realtime_dashboard.php',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
    OFFLINE_URL
];

// Install event - cache essential files
self.addEventListener('install', event => {
    console.log('Service Worker installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('Caching essential files...');
                return cache.addAll(CACHE_URLS);
            })
            .then(() => {
                console.log('Service Worker installed successfully');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker installation failed:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker activated');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', event => {
    // Skip non-GET requests
    if (event.request.method !== 'GET') {
        return;
    }
    
    // Skip chrome-extension and other non-http requests
    if (!event.request.url.startsWith('http')) {
        return;
    }
    
    event.respondWith(
        caches.match(event.request)
            .then(cachedResponse => {
                // Return cached version if available
                if (cachedResponse) {
                    return cachedResponse;
                }
                
                // Try to fetch from network
                return fetch(event.request)
                    .then(response => {
                        // Don't cache non-successful responses
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }
                        
                        // Clone the response for caching
                        const responseToCache = response.clone();
                        
                        // Cache the response for future use
                        caches.open(CACHE_NAME)
                            .then(cache => {
                                cache.put(event.request, responseToCache);
                            });
                        
                        return response;
                    })
                    .catch(() => {
                        // Network failed, try to serve offline page for navigation requests
                        if (event.request.mode === 'navigate') {
                            return caches.match(OFFLINE_URL);
                        }
                        
                        // For other requests, return a generic offline response
                        return new Response('Offline', {
                            status: 503,
                            statusText: 'Service Unavailable',
                            headers: new Headers({
                                'Content-Type': 'text/plain'
                            })
                        });
                    });
            })
    );
});

// Background sync for offline actions
self.addEventListener('sync', event => {
    console.log('Background sync triggered:', event.tag);
    
    if (event.tag === 'attendance-sync') {
        event.waitUntil(syncAttendanceData());
    } else if (event.tag === 'event-sync') {
        event.waitUntil(syncEventData());
    }
});

// Push notification handling
self.addEventListener('push', event => {
    console.log('Push notification received:', event);
    
    const options = {
        body: 'You have new updates in your event management system',
        icon: '../assets/images/icon-192x192.png',
        badge: '../assets/images/icon-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'View Details',
                icon: '/campaign/church/admin/assets/images/icon-192x192.png'
            },
            {
                action: 'close',
                title: 'Close',
                icon: '/campaign/church/admin/assets/images/icon-192x192.png'
            }
        ]
    };
    
    if (event.data) {
        const data = event.data.json();
        options.body = data.body || options.body;
        options.title = data.title || 'Event Management Update';
    }
    
    event.waitUntil(
        self.registration.showNotification('Event Management Platform', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
    console.log('Notification clicked:', event);
    
    event.notification.close();
    
    if (event.action === 'explore') {
        // Open the app
        event.waitUntil(
            clients.openWindow('./')
        );
    } else if (event.action === 'close') {
        // Just close the notification
        return;
    } else {
        // Default action - open the app
        event.waitUntil(
            clients.openWindow('./')
        );
    }
});

// Sync attendance data when back online
async function syncAttendanceData() {
    try {
        console.log('Syncing attendance data...');
        
        // Get pending attendance data from IndexedDB
        const pendingData = await getPendingAttendanceData();
        
        if (pendingData.length > 0) {
            for (const data of pendingData) {
                try {
                    const response = await fetch('../ajax/sync_attendance.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data)
                    });
                    
                    if (response.ok) {
                        await removePendingAttendanceData(data.id);
                        console.log('Attendance data synced successfully');
                    }
                } catch (error) {
                    console.error('Failed to sync attendance data:', error);
                }
            }
        }
    } catch (error) {
        console.error('Attendance sync failed:', error);
    }
}

// Sync event data when back online
async function syncEventData() {
    try {
        console.log('Syncing event data...');
        
        // Get pending event data from IndexedDB
        const pendingData = await getPendingEventData();
        
        if (pendingData.length > 0) {
            for (const data of pendingData) {
                try {
                    const response = await fetch('../ajax/sync_events.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data)
                    });
                    
                    if (response.ok) {
                        await removePendingEventData(data.id);
                        console.log('Event data synced successfully');
                    }
                } catch (error) {
                    console.error('Failed to sync event data:', error);
                }
            }
        }
    } catch (error) {
        console.error('Event sync failed:', error);
    }
}

// IndexedDB helper functions (simplified for demo)
async function getPendingAttendanceData() {
    // In a real implementation, this would query IndexedDB
    return [];
}

async function removePendingAttendanceData(id) {
    // In a real implementation, this would remove from IndexedDB
    console.log('Removing pending attendance data:', id);
}

async function getPendingEventData() {
    // In a real implementation, this would query IndexedDB
    return [];
}

async function removePendingEventData(id) {
    // In a real implementation, this would remove from IndexedDB
    console.log('Removing pending event data:', id);
}

// Message handling from main thread
self.addEventListener('message', event => {
    console.log('Service Worker received message:', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});

// Periodic background sync (if supported)
self.addEventListener('periodicsync', event => {
    console.log('Periodic sync triggered:', event.tag);
    
    if (event.tag === 'attendance-periodic-sync') {
        event.waitUntil(syncAttendanceData());
    }
});

// Share target handling (if the app is registered as a share target)
self.addEventListener('fetch', event => {
    const url = new URL(event.request.url);
    
    if (url.pathname.endsWith('/pwa/share-target') && event.request.method === 'POST') {
        event.respondWith(handleShareTarget(event.request));
    }
});

async function handleShareTarget(request) {
    const formData = await request.formData();
    const title = formData.get('title');
    const text = formData.get('text');
    const url = formData.get('url');
    
    // Handle shared content (e.g., create a new event from shared data)
    console.log('Shared content:', { title, text, url });
    
    // Redirect to the main app
    return Response.redirect('./', 303);
}
