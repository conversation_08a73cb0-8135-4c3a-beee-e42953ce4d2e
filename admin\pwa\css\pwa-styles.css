/**
 * Universal Event Manager PWA Styles
 * Responsive design that works for any organization type
 */

/* CSS Variables for theming */
:root {
    --primary-color: #0066cc;
    --secondary-color: #f8f9fa;
    --accent-color: #28a745;
    --text-color: #212529;
    --border-color: #dee2e6;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --header-height: 60px;
    --sidebar-width: 280px;
    --bottom-nav-height: 70px;
}

/* Base styles */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--secondary-color);
    color: var(--text-color);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* PWA Container */
.pwa-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    position: relative;
}

/* Header */
.pwa-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    height: var(--header-height);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: var(--shadow);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 1rem;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-title h6 {
    font-weight: 600;
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.user-avatar {
    color: white;
}

/* Sidebar */
.pwa-sidebar {
    position: fixed;
    top: var(--header-height);
    left: -var(--sidebar-width);
    width: var(--sidebar-width);
    height: calc(100vh - var(--header-height));
    background: white;
    box-shadow: var(--shadow);
    transition: left 0.3s ease;
    z-index: 999;
    overflow-y: auto;
}

.pwa-sidebar.open {
    left: 0;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar-large {
    font-size: 2.5rem;
    color: var(--primary-color);
}

.sidebar-menu {
    padding: 1rem 0;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1.5rem;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.2s ease;
}

.menu-item:hover {
    background: var(--secondary-color);
    color: var(--primary-color);
}

.menu-item.active {
    background: var(--primary-color);
    color: white;
    border-right: 3px solid var(--accent-color);
}

.menu-item i {
    font-size: 1.2rem;
    width: 20px;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    border-top: 1px solid var(--border-color);
    background: white;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--accent-color);
}

/* Main Content */
.pwa-main {
    flex: 1;
    margin-top: var(--header-height);
    margin-bottom: var(--bottom-nav-height);
    padding: 1rem;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

/* Bottom Navigation */
.pwa-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: var(--bottom-nav-height);
    background: white;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 998;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem;
    color: #6c757d;
    text-decoration: none;
    font-size: 0.8rem;
    transition: color 0.2s ease;
    min-width: 60px;
}

.nav-item:hover,
.nav-item.active {
    color: var(--primary-color);
}

.nav-item i {
    font-size: 1.2rem;
}

/* Floating Action Button */
.fab-container {
    position: fixed;
    bottom: calc(var(--bottom-nav-height) + 1rem);
    right: 1rem;
    z-index: 997;
}

.fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: var(--accent-color);
    color: white;
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0,0,0,0.4);
}

/* Install Banner */
.install-banner {
    position: fixed;
    top: var(--header-height);
    left: 0;
    right: 0;
    background: var(--accent-color);
    color: white;
    z-index: 996;
    animation: slideDown 0.3s ease;
}

.install-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
}

.install-icon {
    font-size: 1.5rem;
}

.install-text {
    flex: 1;
}

.install-text strong {
    display: block;
}

.install-text small {
    opacity: 0.9;
}

.install-actions {
    display: flex;
    gap: 0.5rem;
}

/* Offline Banner */
.offline-banner {
    position: fixed;
    top: var(--header-height);
    left: 0;
    right: 0;
    background: #dc3545;
    color: white;
    z-index: 995;
    animation: slideDown 0.3s ease;
}

.offline-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 0.75rem;
    font-size: 0.9rem;
}

/* Quick Actions */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem;
    background: var(--secondary-color);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.2s ease;
}

.quick-action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.quick-action-btn i {
    font-size: 2rem;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
}

/* Cards */
.pwa-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.pwa-card-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.pwa-card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

/* Responsive Design */
@media (min-width: 768px) {
    .pwa-bottom-nav {
        display: none;
    }
    
    .pwa-main {
        margin-bottom: 0;
        margin-left: 0;
    }
    
    .fab-container {
        bottom: 1rem;
    }
    
    .pwa-sidebar {
        position: relative;
        left: 0;
        width: 250px;
        height: calc(100vh - var(--header-height));
    }
    
    .pwa-container {
        flex-direction: row;
    }
    
    .pwa-main {
        margin-left: 250px;
    }
}

@media (min-width: 1200px) {
    .pwa-sidebar {
        width: var(--sidebar-width);
    }
    
    .pwa-main {
        margin-left: var(--sidebar-width);
    }
}

/* Animations */
@keyframes slideDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease;
}

/* Touch-friendly styles */
@media (hover: none) and (pointer: coarse) {
    .menu-item,
    .nav-item,
    .quick-action-btn {
        min-height: 44px;
    }
    
    .fab {
        width: 64px;
        height: 64px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --secondary-color: #1a1a1a;
        --text-color: #ffffff;
        --border-color: #333333;
    }
    
    body {
        background: var(--secondary-color);
        color: var(--text-color);
    }
    
    .pwa-card,
    .pwa-sidebar,
    .pwa-bottom-nav {
        background: #2d2d2d;
        border-color: var(--border-color);
    }
}

/* Sidebar overlay for mobile */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.online {
    background: var(--accent-color);
}

.status-indicator.offline {
    background: #dc3545;
}

.status-indicator.syncing {
    background: #ffc107;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Swipe gestures */
.swipe-container {
    position: relative;
    overflow: hidden;
}

.swipe-item {
    transition: transform 0.3s ease;
}

.swipe-item.swiping {
    transition: none;
}

/* Pull to refresh */
.pull-to-refresh {
    position: absolute;
    top: -60px;
    left: 0;
    right: 0;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    color: white;
    transition: transform 0.3s ease;
}

.pull-to-refresh.active {
    transform: translateY(60px);
}

/* Print styles */
@media print {
    .pwa-header,
    .pwa-sidebar,
    .pwa-bottom-nav,
    .fab-container,
    .install-banner,
    .offline-banner {
        display: none !important;
    }

    .pwa-main {
        margin: 0 !important;
    }
}
