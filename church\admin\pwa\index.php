<?php
/**
 * Progressive Web App (PWA) Entry Point
 * Native-like mobile experience with offline capabilities
 */

require_once '../../config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    // Redirect to admin login with full path
    header('Location: ' . admin_url_for('login.php'));
    exit();
}

// Get organization configuration
$org_type = get_site_setting('organization_type', 'church');
$org_name = get_site_setting('organization_name', 'Organization');
$primary_color = get_site_setting('primary_color', '#007bff');

$org_config = [
    'church' => ['attendee' => 'Member', 'session' => 'Service', 'event' => 'Event'],
    'corporate' => ['attendee' => 'Employee', 'session' => 'Meeting', 'event' => 'Conference'],
    'educational' => ['attendee' => 'Student', 'session' => 'Class', 'event' => 'Course'],
    'sports' => ['attendee' => 'Fan', 'session' => 'Game', 'event' => 'Tournament'],
    'entertainment' => ['attendee' => 'Guest', 'session' => 'Show', 'event' => 'Festival'],
    'healthcare' => ['attendee' => 'Patient', 'session' => 'Appointment', 'event' => 'Clinic'],
    'government' => ['attendee' => 'Citizen', 'session' => 'Meeting', 'event' => 'Forum'],
    'nonprofit' => ['attendee' => 'Volunteer', 'session' => 'Activity', 'event' => 'Campaign']
];

$terminology = $org_config[$org_type] ?? $org_config['church'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($org_name); ?> - Mobile App</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="<?php echo $primary_color; ?>">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="<?php echo htmlspecialchars($org_name); ?>">
    <meta name="description" content="<?php echo htmlspecialchars($org_name); ?> Mobile App - Universal Event Management Platform">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json.php">
    
    <!-- Icons -->
    <link rel="apple-touch-icon" href="../assets/images/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="192x192" href="../assets/images/icon-192x192.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    
    <style>
        :root {
            --primary-color: <?php echo $primary_color; ?>;
            --primary-rgb: <?php 
                $hex = str_replace('#', '', $primary_color);
                $r = hexdec(substr($hex, 0, 2));
                $g = hexdec(substr($hex, 2, 2));
                $b = hexdec(substr($hex, 4, 2));
                echo "$r, $g, $b";
            ?>;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, rgba(var(--primary-rgb), 0.8) 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .pwa-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 1rem;
            min-height: 100vh;
        }
        
        .app-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .app-icon {
            width: 80px;
            height: 80px;
            background: var(--primary-color);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2rem;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: none;
            transition: transform 0.2s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-2px);
        }
        
        .feature-icon {
            width: 50px;
            height: 50px;
            background: var(--primary-color);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-right: 1rem;
        }
        
        .install-prompt {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: center;
            display: none;
        }
        
        .install-prompt.show {
            display: block;
        }
        
        .btn-install {
            background: var(--primary-color);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            color: white;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .btn-install:hover {
            background: rgba(var(--primary-rgb), 0.9);
            transform: translateY(-1px);
        }
        
        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
            z-index: 1000;
        }
        
        .status-indicator.offline {
            background: #dc3545;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .nav-item {
            text-align: center;
            color: #6c757d;
            text-decoration: none;
            transition: color 0.2s ease;
        }
        
        .nav-item:hover, .nav-item.active {
            color: var(--primary-color);
        }
        
        .nav-item i {
            font-size: 1.5rem;
            display: block;
            margin-bottom: 0.25rem;
        }
        
        .nav-item span {
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <!-- Online/Offline Status Indicator -->
    <div class="status-indicator" id="statusIndicator" title="Online"></div>
    
    <div class="pwa-container">
        <!-- App Header -->
        <div class="app-header">
            <div class="app-icon">
                <i class="bi bi-calendar-event"></i>
            </div>
            <h2><?php echo htmlspecialchars($org_name); ?></h2>
            <p class="text-muted mb-0">Universal Event Management</p>
            <small class="badge bg-primary"><?php echo ucfirst($org_type); ?> Organization</small>
        </div>
        
        <!-- Install Prompt -->
        <div class="install-prompt" id="installPrompt">
            <h5><i class="bi bi-download"></i> Install App</h5>
            <p class="text-muted">Install this app on your device for a better experience!</p>
            <button class="btn btn-install" id="installButton">
                <i class="bi bi-plus-circle"></i> Add to Home Screen
            </button>
        </div>
        
        <!-- Quick Actions -->
        <div class="feature-card">
            <h5><i class="bi bi-lightning"></i> Quick Actions</h5>
            <div class="row g-2">
                <div class="col-6">
                    <a href="<?php echo admin_url_for('events.php'); ?>" class="btn btn-outline-primary w-100">
                        <i class="bi bi-calendar"></i><br>
                        <small><?php echo $terminology['event']; ?>s</small>
                    </a>
                </div>
                <div class="col-6">
                    <a href="<?php echo admin_url_for('members.php'); ?>" class="btn btn-outline-success w-100">
                        <i class="bi bi-people"></i><br>
                        <small><?php echo $terminology['attendee']; ?>s</small>
                    </a>
                </div>
                <div class="col-6">
                    <a href="<?php echo admin_url_for('ai/universal_ai_dashboard.php'); ?>" class="btn btn-outline-info w-100">
                        <i class="bi bi-robot"></i><br>
                        <small>AI Predictions</small>
                    </a>
                </div>
                <div class="col-6">
                    <a href="<?php echo admin_url_for('analytics/universal_analytics_dashboard.php'); ?>" class="btn btn-outline-warning w-100">
                        <i class="bi bi-graph-up"></i><br>
                        <small>Analytics</small>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Features -->
        <div class="feature-card">
            <div class="d-flex align-items-center">
                <div class="feature-icon">
                    <i class="bi bi-qr-code-scan"></i>
                </div>
                <div>
                    <h6 class="mb-1">QR Code Scanner</h6>
                    <small class="text-muted">Quick check-in for <?php echo strtolower($terminology['attendee']); ?>s</small>
                </div>
            </div>
        </div>
        
        <div class="feature-card">
            <div class="d-flex align-items-center">
                <div class="feature-icon">
                    <i class="bi bi-wifi-off"></i>
                </div>
                <div>
                    <h6 class="mb-1">Offline Mode</h6>
                    <small class="text-muted">Works without internet connection</small>
                </div>
            </div>
        </div>
        
        <div class="feature-card">
            <div class="d-flex align-items-center">
                <div class="feature-icon">
                    <i class="bi bi-bell"></i>
                </div>
                <div>
                    <h6 class="mb-1">Push Notifications</h6>
                    <small class="text-muted">Real-time updates and alerts</small>
                </div>
            </div>
        </div>
        
        <div class="feature-card">
            <div class="d-flex align-items-center">
                <div class="feature-icon">
                    <i class="bi bi-broadcast"></i>
                </div>
                <div>
                    <h6 class="mb-1">Real-Time Sync</h6>
                    <small class="text-muted">Live updates across all devices</small>
                </div>
            </div>
        </div>
        
        <!-- PWA Info -->
        <div class="feature-card">
            <h6><i class="bi bi-info-circle"></i> Progressive Web App</h6>
            <p class="text-muted small mb-2">This is a Progressive Web App (PWA) that provides:</p>
            <ul class="small text-muted">
                <li>Native-like mobile experience</li>
                <li>Offline functionality</li>
                <li>Push notifications</li>
                <li>Installable on any device</li>
                <li>Automatic updates</li>
            </ul>
        </div>
        
        <!-- Bottom spacing for fixed nav -->
        <div style="height: 100px;"></div>
    </div>
    
    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <div class="row g-0">
            <div class="col">
                <a href="#" class="nav-item active">
                    <i class="bi bi-house"></i>
                    <span>Home</span>
                </a>
            </div>
            <div class="col">
                <a href="<?php echo admin_url_for('events.php'); ?>" class="nav-item">
                    <i class="bi bi-calendar"></i>
                    <span><?php echo $terminology['event']; ?>s</span>
                </a>
            </div>
            <div class="col">
                <a href="<?php echo admin_url_for('ai/universal_ai_dashboard.php'); ?>" class="nav-item">
                    <i class="bi bi-robot"></i>
                    <span>AI</span>
                </a>
            </div>
            <div class="col">
                <a href="<?php echo admin_url_for('analytics/universal_analytics_dashboard.php'); ?>" class="nav-item">
                    <i class="bi bi-graph-up"></i>
                    <span>Analytics</span>
                </a>
            </div>
            <div class="col">
                <a href="<?php echo admin_url_for('dashboard.php'); ?>" class="nav-item">
                    <i class="bi bi-grid"></i>
                    <span>Admin</span>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- PWA Service Worker -->
    <script>
        // Register Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
        
        // Install Prompt
        let deferredPrompt;
        const installButton = document.getElementById('installButton');
        const installPrompt = document.getElementById('installPrompt');
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            installPrompt.classList.add('show');
        });
        
        installButton.addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`User response to the install prompt: ${outcome}`);
                deferredPrompt = null;
                installPrompt.classList.remove('show');
            }
        });
        
        // Online/Offline Status
        const statusIndicator = document.getElementById('statusIndicator');
        
        function updateOnlineStatus() {
            if (navigator.onLine) {
                statusIndicator.classList.remove('offline');
                statusIndicator.title = 'Online';
            } else {
                statusIndicator.classList.add('offline');
                statusIndicator.title = 'Offline';
            }
        }
        
        window.addEventListener('online', updateOnlineStatus);
        window.addEventListener('offline', updateOnlineStatus);
        updateOnlineStatus();
        
        // Touch gestures for mobile
        let startY = 0;
        let endY = 0;
        
        document.addEventListener('touchstart', e => {
            startY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchend', e => {
            endY = e.changedTouches[0].clientY;
            handleSwipe();
        });
        
        function handleSwipe() {
            const swipeThreshold = 50;
            const diff = startY - endY;
            
            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    // Swipe up - could trigger refresh or navigation
                    console.log('Swipe up detected');
                } else {
                    // Swipe down - could trigger pull-to-refresh
                    console.log('Swipe down detected');
                }
            }
        }
        
        // Haptic feedback for supported devices
        function vibrate(pattern = [100]) {
            if ('vibrate' in navigator) {
                navigator.vibrate(pattern);
            }
        }
        
        // Add haptic feedback to buttons
        document.querySelectorAll('.btn, .nav-item').forEach(element => {
            element.addEventListener('click', () => {
                vibrate([50]);
            });
        });
    </script>
</body>
</html>
