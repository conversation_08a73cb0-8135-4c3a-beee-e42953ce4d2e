<?php
session_start();

// Include the configuration file
require_once '../config.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    echo "Not logged in. Please login first.";
    exit();
}

echo "<h2>RBAC System Debug Information</h2>";
echo "<p><strong>Current User ID:</strong> " . $_SESSION['admin_id'] . "</p>";

// Get current user info
try {
    $stmt = $pdo->prepare("SELECT * FROM admins WHERE id = ?");
    $stmt->execute([$_SESSION['admin_id']]);
    $current_user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Current User:</strong> " . htmlspecialchars($current_user['username']) . "</p>";
    echo "<p><strong>Email:</strong> " . htmlspecialchars($current_user['email'] ?? 'N/A') . "</p>";
} catch (PDOException $e) {
    echo "<p><strong>Error getting user info:</strong> " . $e->getMessage() . "</p>";
}

// Check if RBAC tables exist
echo "<h3>RBAC Tables Status:</h3>";

// Check user_roles table
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM user_roles");
    $role_count = $stmt->fetchColumn();
    echo "<p>✅ user_roles table exists with $role_count roles</p>";
    
    // Show all roles
    $stmt = $pdo->query("SELECT * FROM user_roles ORDER BY hierarchy_level");
    $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<h4>Available Roles:</h4>";
    foreach ($roles as $role) {
        echo "<p>- {$role['role_name']} (Level {$role['hierarchy_level']}): {$role['role_display_name']}</p>";
    }
} catch (PDOException $e) {
    echo "<p>❌ user_roles table error: " . $e->getMessage() . "</p>";
}

// Check user_role_assignments table
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM user_role_assignments");
    $assignment_count = $stmt->fetchColumn();
    echo "<p>✅ user_role_assignments table exists with $assignment_count assignments</p>";
    
    // Show current user's role assignments
    $stmt = $pdo->prepare("
        SELECT ura.*, ur.role_name, ur.role_display_name, ur.hierarchy_level
        FROM user_role_assignments ura
        JOIN user_roles ur ON ura.role_id = ur.id
        WHERE ura.user_id = ? AND ura.is_active = 1
        ORDER BY ur.hierarchy_level
    ");
    $stmt->execute([$_SESSION['admin_id']]);
    $user_roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h4>Current User's Role Assignments:</h4>";
    if (empty($user_roles)) {
        echo "<p>❌ <strong>NO ROLES ASSIGNED TO CURRENT USER!</strong></p>";
        echo "<p>This is the problem - the 'admin' user has no roles assigned.</p>";
    } else {
        foreach ($user_roles as $role) {
            echo "<p>✅ {$role['role_name']} (Level {$role['hierarchy_level']}): {$role['role_display_name']}</p>";
            echo "<p>&nbsp;&nbsp;&nbsp;Assigned: {$role['assigned_at']}, Active: " . ($role['is_active'] ? 'Yes' : 'No') . "</p>";
        }
    }
} catch (PDOException $e) {
    echo "<p>❌ user_role_assignments table error: " . $e->getMessage() . "</p>";
}

// Test RBAC class
echo "<h3>RBAC Class Test:</h3>";
try {
    require_once 'includes/rbac_access_control.php';
    $rbac = new RBACAccessControl($pdo, $_SESSION['admin_id']);
    
    echo "<p>✅ RBAC class loaded successfully</p>";
    
    $primary_role = $rbac->getPrimaryRole();
    echo "<p><strong>Primary Role:</strong> " . ($primary_role ?? 'NONE') . "</p>";
    
    $user_roles = $rbac->getUserRoles();
    echo "<p><strong>User Roles Count:</strong> " . count($user_roles) . "</p>";
    
    $is_super_admin = $rbac->isSuperAdmin();
    echo "<p><strong>Is Super Admin:</strong> " . ($is_super_admin ? 'YES' : 'NO') . "</p>";
    
    $default_dashboard = $rbac->getDefaultDashboard();
    echo "<p><strong>Default Dashboard:</strong> " . $default_dashboard . "</p>";
    
} catch (Exception $e) {
    echo "<p>❌ RBAC class error: " . $e->getMessage() . "</p>";
}

// Show all user role assignments for debugging
echo "<h3>All User Role Assignments:</h3>";
try {
    $stmt = $pdo->query("
        SELECT ura.*, ur.role_name, ur.role_display_name, a.username
        FROM user_role_assignments ura
        JOIN user_roles ur ON ura.role_id = ur.id
        JOIN admins a ON ura.user_id = a.id
        WHERE ura.is_active = 1
        ORDER BY a.username, ur.hierarchy_level
    ");
    $all_assignments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($all_assignments)) {
        echo "<p>❌ <strong>NO ROLE ASSIGNMENTS FOUND IN SYSTEM!</strong></p>";
    } else {
        foreach ($all_assignments as $assignment) {
            echo "<p>User: {$assignment['username']} → Role: {$assignment['role_name']} ({$assignment['role_display_name']})</p>";
        }
    }
} catch (PDOException $e) {
    echo "<p>❌ Error getting all assignments: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='setup_rbac_system.php'>Go to RBAC Management</a></p>";
echo "<p><a href='super_admin_dashboard.php'>Go to Super Admin Dashboard</a></p>";
?>
