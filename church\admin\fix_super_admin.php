<?php
session_start();

// Include the configuration file
require_once '../config.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    echo "Not logged in. Please login first.";
    exit();
}

echo "<h2>Super Admin Role Assignment Fix</h2>";

try {
    // Step 1: Ensure RBAC system is initialized
    echo "<h3>Step 1: Initialize RBAC System</h3>";
    
    // Create user_roles table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS user_roles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            role_name VARCHAR(50) UNIQUE NOT NULL,
            role_display_name VARCHAR(100) NOT NULL,
            role_description TEXT,
            hierarchy_level INT NOT NULL,
            dashboard_url VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_role_name (role_name),
            INDEX idx_hierarchy (hierarchy_level)
        )
    ");
    echo "<p>✅ user_roles table created/verified</p>";
    
    // Create user_role_assignments table if it doesn't exist
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS user_role_assignments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            role_id INT NOT NULL,
            assigned_by INT,
            assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NULL,
            is_active BOOLEAN DEFAULT TRUE,
            UNIQUE KEY unique_user_role (user_id, role_id),
            INDEX idx_user_id (user_id),
            INDEX idx_role_id (role_id),
            INDEX idx_active (is_active)
        )
    ");
    echo "<p>✅ user_role_assignments table created/verified</p>";
    
    // Step 2: Insert default roles
    echo "<h3>Step 2: Create Default Roles</h3>";
    
    $default_roles = [
        [
            'role_name' => 'super_admin',
            'role_display_name' => 'Super Administrator',
            'role_description' => 'Complete system oversight and user management with full access to all functions',
            'hierarchy_level' => 1,
            'dashboard_url' => 'super_admin_dashboard.php'
        ],
        [
            'role_name' => 'limited_admin',
            'role_display_name' => 'Limited Administrator',
            'role_description' => 'Basic administrative functions with restricted access',
            'hierarchy_level' => 2,
            'dashboard_url' => 'dashboard.php'
        ],
        [
            'role_name' => 'event_coordinator',
            'role_display_name' => 'Event Coordinator',
            'role_description' => 'Multi-session oversight for assigned events',
            'hierarchy_level' => 3,
            'dashboard_url' => 'event_coordinator_dashboard.php'
        ],
        [
            'role_name' => 'session_moderator',
            'role_display_name' => 'Session Moderator',
            'role_description' => 'Individual session management and attendance tracking',
            'hierarchy_level' => 4,
            'dashboard_url' => 'session_moderator_dashboard.php'
        ],
        [
            'role_name' => 'staff',
            'role_display_name' => 'Staff Member',
            'role_description' => 'Check-in and basic attendance marking capabilities',
            'hierarchy_level' => 5,
            'dashboard_url' => 'staff_dashboard.php'
        ]
    ];
    
    foreach ($default_roles as $role) {
        $stmt = $pdo->prepare("
            INSERT INTO user_roles (role_name, role_display_name, role_description, hierarchy_level, dashboard_url)
            VALUES (?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
                role_display_name = VALUES(role_display_name),
                role_description = VALUES(role_description),
                hierarchy_level = VALUES(hierarchy_level),
                dashboard_url = VALUES(dashboard_url)
        ");
        $stmt->execute([
            $role['role_name'],
            $role['role_display_name'],
            $role['role_description'],
            $role['hierarchy_level'],
            $role['dashboard_url']
        ]);
        echo "<p>✅ Role created/updated: {$role['role_display_name']}</p>";
    }
    
    // Step 3: Assign super_admin role to "admin" user
    echo "<h3>Step 3: Assign Super Admin Role to 'admin' User</h3>";
    
    // Find the "admin" user
    $stmt = $pdo->prepare("SELECT id, username FROM admins WHERE username = 'admin'");
    $stmt->execute();
    $admin_user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin_user) {
        echo "<p>❌ 'admin' user not found in database!</p>";
        echo "<p>Available users:</p>";
        $stmt = $pdo->query("SELECT id, username FROM admins ORDER BY username");
        $all_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($all_users as $user) {
            echo "<p>- ID: {$user['id']}, Username: {$user['username']}</p>";
        }
    } else {
        echo "<p>✅ Found 'admin' user with ID: {$admin_user['id']}</p>";
        
        // Get super_admin role ID
        $stmt = $pdo->prepare("SELECT id FROM user_roles WHERE role_name = 'super_admin'");
        $stmt->execute();
        $super_admin_role_id = $stmt->fetchColumn();
        
        if (!$super_admin_role_id) {
            echo "<p>❌ super_admin role not found!</p>";
        } else {
            echo "<p>✅ Found super_admin role with ID: {$super_admin_role_id}</p>";
            
            // Assign super_admin role to admin user
            $stmt = $pdo->prepare("
                INSERT INTO user_role_assignments (user_id, role_id, assigned_by, is_active)
                VALUES (?, ?, ?, 1)
                ON DUPLICATE KEY UPDATE 
                    is_active = 1,
                    assigned_at = CURRENT_TIMESTAMP
            ");
            $stmt->execute([$admin_user['id'], $super_admin_role_id, $admin_user['id']]);
            
            echo "<p>✅ <strong>Super Admin role assigned to 'admin' user successfully!</strong></p>";
        }
    }
    
    // Step 4: Verify the assignment
    echo "<h3>Step 4: Verify Assignment</h3>";
    
    if (isset($admin_user['id'])) {
        $stmt = $pdo->prepare("
            SELECT ura.*, ur.role_name, ur.role_display_name, ur.hierarchy_level
            FROM user_role_assignments ura
            JOIN user_roles ur ON ura.role_id = ur.id
            WHERE ura.user_id = ? AND ura.is_active = 1
            ORDER BY ur.hierarchy_level
        ");
        $stmt->execute([$admin_user['id']]);
        $user_roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($user_roles)) {
            echo "<p>❌ No roles found for admin user after assignment!</p>";
        } else {
            echo "<p>✅ Admin user now has the following roles:</p>";
            foreach ($user_roles as $role) {
                echo "<p>- {$role['role_name']} (Level {$role['hierarchy_level']}): {$role['role_display_name']}</p>";
            }
        }
    }
    
    echo "<h3>✅ Fix Complete!</h3>";
    echo "<p><strong>The 'admin' user should now have super admin privileges.</strong></p>";
    echo "<p><a href='logout.php'>Logout and Login Again</a> to refresh your session.</p>";
    echo "<p><a href='super_admin_dashboard.php'>Go to Super Admin Dashboard</a></p>";
    echo "<p><a href='debug_rbac.php'>View Debug Information</a></p>";
    
} catch (PDOException $e) {
    echo "<p>❌ <strong>Database Error:</strong> " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p>❌ <strong>Error:</strong> " . $e->getMessage() . "</p>";
}
?>
