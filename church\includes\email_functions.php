<?php
/**
 * Email Functions
 * 
 * This file contains helper functions for sending emails and managing email-related operations.
 * These functions are used by the email scheduling system.
 */

// Include PHPMailer if not already included
if (!class_exists('PHP<PERSON>ailer\PHPMailer\PHPMailer')) {
    require_once __DIR__ . '/../vendor/autoload.php';
}

use P<PERSON>Mailer\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\Exception;

/**
 * Send an email using PHP<PERSON>ailer
 * 
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $body Email body
 * @param string $fromName Sender name
 * @param string $fromEmail Sender email
 * @param bool $isHTML Whether the email body is HTML
 * @param array $attachments Optional array of attachments
 * @return array Result of sending email
 */
function sendEmailWithPHPMailer($to, $subject, $body, $fromName, $fromEmail, $isHTML = true, $attachments = []) {
    global $pdo;
    
    try {
        // Get SMTP settings from database - try email_settings table first, then settings table
        $settings = [];

        // First try email_settings table
        try {
            $stmt = $pdo->query("SELECT setting_key, setting_value FROM email_settings");
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        } catch (PDOException $e) {
            // Table might not exist, continue to settings table
        }

        // If no settings found or missing required settings, try settings table
        $required_keys = ['smtp_host', 'smtp_username', 'smtp_password', 'smtp_port'];
        $missing_keys = array_diff($required_keys, array_keys($settings));

        if (!empty($missing_keys)) {
            // Try to get missing settings from settings table
            $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption', 'from_email', 'from_name', 'reply_to_email')");
            $stmt->execute();
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                // Map settings table keys to email_settings table keys
                $key = $row['setting_key'];
                if ($key === 'smtp_encryption') {
                    $settings['smtp_secure'] = $row['setting_value'];
                } elseif ($key === 'from_email') {
                    $settings['sender_email'] = $row['setting_value'];
                } elseif ($key === 'from_name') {
                    $settings['sender_name'] = $row['setting_value'];
                } elseif ($key === 'reply_to_email') {
                    $settings['reply_to_email'] = $row['setting_value'];
                    $settings['replyToEmail'] = $row['setting_value']; // Alternative key format
                } else {
                    $settings[$key] = $row['setting_value'];
                }
            }
        }
        
        // Create a new PHPMailer instance
        $mail = new PHPMailer(true);

        // Server settings
        $mail->isSMTP();
        $mail->Host = $settings['smtp_host'] ?? 'smtp.example.com';
        $mail->SMTPAuth = isset($settings['smtp_auth']) && $settings['smtp_auth'] == '1';
        $mail->Username = $settings['smtp_username'] ?? '';
        $mail->Password = $settings['smtp_password'] ?? '';
        // Fix port/encryption mismatch - Port 465 requires SSL, Port 587 uses TLS
        $port = $settings['smtp_port'] ?? 587;
        $security = $settings['smtp_secure'] ?? 'tls';

        if ($port == 465) {
            $mail->SMTPSecure = 'ssl';  // Port 465 requires SSL
            $mail->Port = 465;
        } else {
            $mail->SMTPSecure = 'tls';  // Port 587 uses TLS
            $mail->Port = 587;
        }
        $mail->Timeout = 30; // Increased SMTP connection timeout (seconds)
        $mail->SMTPKeepAlive = true; // Keep connection alive for multiple emails
        $mail->SMTPOptions = [
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true,
                'cafile' => false,
                'capath' => false,
                'ciphers' => 'DEFAULT:!DH'
            ]
        ];
        
        // Recipients
        $mail->setFrom($fromEmail, $fromName);
        $mail->addAddress($to);

        // Set reply-to if available
        $replyToEmail = $settings['reply_to_email'] ?? $settings['replyToEmail'] ?? null;
        if (!empty($replyToEmail)) {
            $mail->addReplyTo($replyToEmail, $fromName);
        }
        
        // Content
        $mail->isHTML($isHTML);
        $mail->Subject = $subject;
        $mail->Body = $body;
        
        // Plain text alternative
        if ($isHTML) {
            $mail->AltBody = strip_tags(str_replace(['<br>', '<br/>', '<br />'], "\n", $body));
        }
        
        // Attachments
        // Skip attachments if _skip_attachments flag is set in the attachments array (for birthday notifications)
        $skipAttachments = false;
        if (is_array($attachments) && isset($attachments['_skip_attachments']) && $attachments['_skip_attachments']) {
            $skipAttachments = true;
        }
        if (!$skipAttachments && !empty($attachments)) {
            foreach ($attachments as $attachment) {
                if (is_array($attachment) && isset($attachment['path']) && isset($attachment['name'])) {
                    $mail->addAttachment($attachment['path'], $attachment['name']);
                } elseif (is_string($attachment) && file_exists($attachment)) {
                    $mail->addAttachment($attachment);
                }
            }
        }
        
        // Send the email with retry logic
        $maxRetries = 3;
        $retryCount = 0;

        while ($retryCount < $maxRetries) {
            try {
                $mail->send();
                return [
                    'success' => true,
                    'message' => 'Email sent successfully'
                ];
            } catch (Exception $e) {
                $retryCount++;
                $errorMsg = $mail->ErrorInfo;

                // Log the error
                error_log("Email send attempt $retryCount failed: " . $errorMsg);

                if ($retryCount >= $maxRetries) {
                    // Final attempt failed, return error
                    if (stripos($errorMsg, 'timeout') !== false || stripos($errorMsg, 'timed out') !== false) {
                        $errorMsg = 'SMTP connection timed out after multiple attempts. Please check your mail server or try again later.';
                    } elseif (stripos($errorMsg, 'failed to connect') !== false) {
                        $errorMsg = 'Could not connect to SMTP server after multiple attempts. Please check your mail server settings.';
                    } elseif (stripos($errorMsg, 'authentication') !== false) {
                        $errorMsg = 'SMTP authentication failed. Please check your email credentials.';
                    }

                    return [
                        'success' => false,
                        'message' => 'Email could not be sent after ' . $maxRetries . ' attempts. Error: ' . $errorMsg
                    ];
                } else {
                    // Wait before retry
                    sleep(2);
                }
            }
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Email could not be sent. Error: ' . $e->getMessage()
        ];
    }
}

/**
 * Send a scheduled email
 * 
 * @param string $to Recipient email
 * @param string $toName Recipient name
 * @param string $subject Email subject
 * @param string $body Email body
 * @param array $schedule Schedule data
 * @param array $recipient Recipient data
 * @return bool Success status
 */
function sendScheduledEmail($to, $toName, $subject, $body, $schedule = [], $recipient = []) {
    global $pdo, $emailSettings;
    
    // Create a log entry
    $log_file = __DIR__ . '/../logs/scheduled_email_debug.log';
    file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] Sending scheduled email to $toName <$to>\n", FILE_APPEND);
    
    try {
        // Get email settings if not already loaded
        if (empty($emailSettings)) {
            $emailSettings = [];

            // First try email_settings table
            try {
                $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM email_settings");
                $stmt->execute();
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $emailSettings[$row['setting_key']] = $row['setting_value'];
                }
            } catch (PDOException $e) {
                // Table might not exist, continue to settings table
            }

            // If no settings found or missing required settings, try settings table
            $required_keys = ['smtp_host', 'smtp_username', 'smtp_password', 'smtp_port'];
            $missing_keys = array_diff($required_keys, array_keys($emailSettings));

            if (!empty($missing_keys)) {
                // Get settings from settings table
                $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption', 'from_email', 'from_name', 'sender_email', 'sender_name', 'reply_to_email')");
                $stmt->execute();
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $key = $row['setting_key'];
                    if ($key === 'smtp_encryption') {
                        $emailSettings['smtp_secure'] = $row['setting_value'];
                    } elseif ($key === 'from_email') {
                        $emailSettings['sender_email'] = $row['setting_value'];
                    } elseif ($key === 'from_name') {
                        $emailSettings['sender_name'] = $row['setting_value'];
                    } elseif ($key === 'reply_to_email') {
                        $emailSettings['reply_to_email'] = $row['setting_value'];
                        $emailSettings['replyToEmail'] = $row['setting_value']; // Alternative key format
                    } else {
                        $emailSettings[$key] = $row['setting_value'];
                    }
                }
            }
        }
        
        // Initialize PHPMailer
        $mail = new PHPMailer(true);
        $mail->isSMTP();
        $mail->Host = $emailSettings['smtp_host'] ?? 'localhost';
        $mail->SMTPAuth = true;
        $mail->Username = $emailSettings['smtp_username'] ?? '';
        $mail->Password = $emailSettings['smtp_password'] ?? '';
        // Fix port/encryption mismatch - Port 465 requires SSL, Port 587 uses TLS
        $port = $emailSettings['smtp_port'] ?? 587;
        $security = $emailSettings['smtp_secure'] ?? 'tls';

        if ($port == 465) {
            $mail->SMTPSecure = 'ssl';  // Port 465 requires SSL
            $mail->Port = 465;
        } else {
            $mail->SMTPSecure = 'tls';  // Port 587 uses TLS
            $mail->Port = 587;
        }
        $mail->CharSet = 'UTF-8';
        
        // Set sender
        $senderEmail = $emailSettings['sender_email'] ?? $emailSettings['smtp_username'] ?? '<EMAIL>';
        $senderName = $emailSettings['sender_name'] ?? 'Church System';
        $mail->setFrom($senderEmail, $senderName);
        $mail->addAddress($to, $toName);

        // Set reply-to if available
        $replyToEmail = $emailSettings['reply_to_email'] ?? $emailSettings['replyToEmail'] ?? null;
        if (!empty($replyToEmail)) {
            $mail->addReplyTo($replyToEmail, $senderName);
        }
        
        // Set email content
        $mail->isHTML(true);
        
        // Process the subject with proper placeholder replacement
        $memberData = [
            'full_name' => $toName,
            'first_name' => explode(' ', $toName)[0] ?? '',
            'last_name' => (count(explode(' ', $toName)) > 1) ? explode(' ', $toName, 2)[1] : '',
            'email' => $to,
            'recipient_full_name' => $toName,
            'recipient_first_name' => explode(' ', $toName)[0] ?? '',
            'recipient_email' => $to
        ];
        
        // If we have additional recipient data, merge it
        if (!empty($recipient) && is_array($recipient)) {
            // Ensure we're using the correct member data for placeholders
            if (isset($recipient['full_name'])) {
                $memberData['full_name'] = $recipient['full_name'];
                $memberData['first_name'] = explode(' ', $recipient['full_name'])[0] ?? '';
                $memberData['last_name'] = (count(explode(' ', $recipient['full_name'])) > 1) ? explode(' ', $recipient['full_name'], 2)[1] : '';
                $memberData['recipient_full_name'] = $recipient['full_name'];
                $memberData['recipient_first_name'] = explode(' ', $recipient['full_name'])[0] ?? '';
            }
            
            // Now merge all other recipient data
            $memberData = array_merge($memberData, $recipient);
        }
        
        // CRITICAL FIX: Check if subject is already clean (from birthday reminder system)
        // If subject starts with "Birthday Celebration!" or "Happy Birthday" and has no placeholders, don't reprocess
        $isAlreadyCleanSubject = (
            (strpos($subject, 'Birthday Celebration!') === 0 || strpos($subject, 'Happy Birthday') === 0) &&
            strpos($subject, '{') === false &&
            strpos($subject, '}') === false
        );

        if ($isAlreadyCleanSubject) {
            $processedSubject = $subject; // Use as-is, don't reprocess
            error_log("SUBJECT PROTECTION: Using pre-cleaned subject: '$subject'");
        } else {
            // Process subject and body with placeholder replacement
            $processedSubject = replaceTemplatePlaceholders($subject, $memberData, false, true); // Mark as subject line
            error_log("SUBJECT PROCESSING: Processed subject from '$subject' to '$processedSubject'");
        }

        // Only remove script tags for security, but preserve all other HTML and styles
        $processedBody = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $body);
        $processedBody = replaceTemplatePlaceholders($processedBody, $memberData);

        // ONE-TIME FIX: Only embed images actually referenced in HTML
        $embeddedImages = []; // Track what we've embedded: ['url' => 'cid', ...]

        // Check if this is a birthday notification
        $isBirthdayNotification = false;
        if (isset($memberData['birthday_member_name']) ||
            isset($memberData['birthday_member_full_name']) ||
            isset($memberData['birthday_member_age']) ||
            isset($memberData['birthday_member_photo_url'])) {
            $isBirthdayNotification = true;
            file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] SCHEDULED: Birthday notification detected\n", FILE_APPEND);
        }

        // Process and embed images in the email body
        if ($memberData) {
            file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] SCHEDULED: ONE-TIME FIX - Processing HTML email with single image embedding\n", FILE_APPEND);

            // STEP 1: Find all images actually referenced in the HTML body
            $referencedImages = [];
            if (preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $processedBody, $matches)) {
                $referencedImages = array_unique($matches[1]);
                file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] SCHEDULED: Found " . count($referencedImages) . " images referenced in HTML\n", FILE_APPEND);
            }

            // STEP 2: Process birthday notification images (only if referenced)
            if ($isBirthdayNotification) {
                // Get potential birthday image URLs
                $birthdayImageUrls = [];
                if (!empty($memberData['birthday_member_photo_url'])) {
                    $birthdayImageUrls[] = $memberData['birthday_member_photo_url'];
                }
                if (!empty($memberData['birthday_member_image_url'])) {
                    $birthdayImageUrls[] = $memberData['birthday_member_image_url'];
                }
                if (!empty($memberData['member_image_url'])) {
                    $birthdayImageUrls[] = $memberData['member_image_url'];
                }

                // Find which birthday image is actually referenced in HTML
                $birthdayImageToEmbed = null;
                foreach ($birthdayImageUrls as $imageUrl) {
                    if (in_array($imageUrl, $referencedImages)) {
                        $birthdayImageToEmbed = $imageUrl;
                        file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] SCHEDULED: Found birthday image referenced in HTML: $imageUrl\n", FILE_APPEND);
                        break;
                    }
                }

                // Embed ONLY the referenced birthday image
                if ($birthdayImageToEmbed) {
                    $siteUrl = defined('SITE_URL') ? SITE_URL : '';
                    $localPath = '';

                    if (strpos($birthdayImageToEmbed, $siteUrl) === 0) {
                        $relativePath = str_replace($siteUrl, '', $birthdayImageToEmbed);
                        $localPath = __DIR__ . '/../' . ltrim($relativePath, '/');
                    } else if (strpos($birthdayImageToEmbed, '/') === 0) {
                        $localPath = __DIR__ . '/../' . ltrim($birthdayImageToEmbed, '/');
                    }

                    $localPath = str_replace('\\', '/', $localPath);
                    $localPath = preg_replace('#/+#', '/', $localPath);

                    if (file_exists($localPath)) {
                        $mime = mime_content_type($localPath) ?: 'image/jpeg';
                        $uniqueCid = 'birthday_image_' . md5($birthdayImageToEmbed . time());

                        $tempPath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'scheduled_email_image_' . uniqid();
                        copy($localPath, $tempPath);

                        // CRITICAL FIX: Use invisible filename to prevent attachment behavior
                        // PHPMailer automatically uses the file basename if name is empty, so we use a space
                        $mail->addEmbeddedImage($tempPath, $uniqueCid, ' ', 'base64', $mime, 'inline');

                        // Track this embedding
                        $embeddedImages[$birthdayImageToEmbed] = $uniqueCid;

                        // Replace ONLY this specific image URL with CID
                        $processedBody = str_replace($birthdayImageToEmbed, 'cid:' . $uniqueCid, $processedBody);

                        // Clean up temporary file
                        register_shutdown_function(function() use ($tempPath) {
                            if (file_exists($tempPath)) {
                                unlink($tempPath);
                            }
                        });

                        file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] SCHEDULED: ONE-TIME FIX - Embedded birthday image: $localPath as CID: $uniqueCid\n", FILE_APPEND);
                    }
                } else {
                    file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] SCHEDULED: ONE-TIME FIX - No birthday image referenced in HTML body\n", FILE_APPEND);
                }
            }

            // STEP 3: Process other images referenced in HTML (skip if already processed)
            foreach ($referencedImages as $imgSrc) {
                // Skip already processed images
                if (isset($embeddedImages[$imgSrc])) {
                    file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] SCHEDULED: ONE-TIME FIX - Skipping already processed image: $imgSrc\n", FILE_APPEND);
                    continue;
                }

                // Skip external images, tracking pixels, etc.
                if (strpos($imgSrc, 'track.php') !== false ||
                    strpos($imgSrc, 'data:image') === 0 ||
                    strpos($imgSrc, 'cid:') === 0 ||
                    (strpos($imgSrc, 'http://') === 0 && strpos($imgSrc, SITE_URL) === false) ||
                    (strpos($imgSrc, 'https://') === 0 && strpos($imgSrc, SITE_URL) === false)) {
                    continue;
                }

                // Convert to local path
                $siteUrl = defined('SITE_URL') ? SITE_URL : '';
                $localPath = '';

                if (strpos($imgSrc, $siteUrl) === 0) {
                    $relativePath = str_replace($siteUrl, '', $imgSrc);
                    $localPath = __DIR__ . '/../' . ltrim($relativePath, '/');
                } else if (strpos($imgSrc, '/') === 0) {
                    $localPath = __DIR__ . '/../' . ltrim($imgSrc, '/');
                }

                $localPath = str_replace('\\', '/', $localPath);
                $localPath = preg_replace('#/+#', '/', $localPath);

                if (file_exists($localPath)) {
                    $mime = mime_content_type($localPath) ?: 'image/jpeg';
                    $cid = 'image_' . md5($imgSrc . time());

                    $tempPath = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'scheduled_email_image_' . uniqid();
                    copy($localPath, $tempPath);

                    // CRITICAL FIX: Use invisible filename to prevent attachment behavior
                    // PHPMailer automatically uses the file basename if name is empty, so we use a space
                    $mail->addEmbeddedImage($tempPath, $cid, ' ', 'base64', $mime, 'inline');

                    // Track this embedding
                    $embeddedImages[$imgSrc] = $cid;

                    // Replace in HTML
                    $processedBody = str_replace($imgSrc, 'cid:' . $cid, $processedBody);

                    file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] SCHEDULED: ONE-TIME FIX - Embedded general image: $localPath as CID: $cid\n", FILE_APPEND);

                    // Clean up temporary file
                    register_shutdown_function(function() use ($tempPath) {
                        if (file_exists($tempPath)) {
                            unlink($tempPath);
                        }
                    });
                }
            }
        }

        // Legacy fallback for {member_image} placeholder (if not already processed)
        if (strpos($processedBody, '{member_image}') !== false && !empty($memberData['member_image_url'])) {
            $imgTag = '<img src="' . htmlspecialchars($memberData['member_image_url']) . '" alt="Profile Image" style="display:block; width:160px; height:160px; border-radius:50%; margin:15px auto; object-fit:cover; border:6px solid #fff; box-shadow:0 4px 8px rgba(0,0,0,0.1);">';
            $processedBody = str_replace('{member_image}', $imgTag, $processedBody);
        }

        $mail->Subject = $processedSubject;
        $mail->Body = $processedBody;

        // Log final processing summary
        file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] SCHEDULED: ONE-TIME FIX - Embedded " . count($embeddedImages) . " images\n", FILE_APPEND);
        if (!empty($embeddedImages)) {
            file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] SCHEDULED: Embedded images: " . implode(', ', array_keys($embeddedImages)) . "\n", FILE_APPEND);
        }
        
        // Add tracking pixel if enabled
        if (!empty($schedule['track_opens'])) {
            // Generate tracking ID
            $trackingId = uniqid('scheduled_', true);
            
            // Create tracking pixel
            $siteUrl = defined('SITE_URL') ? SITE_URL : 
                ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);
            $trackingUrl = $siteUrl . '/track.php?id=' . urlencode($trackingId) . '&type=scheduled';
            $trackingPixel = '<img src="' . $trackingUrl . '" alt="" width="1" height="1" style="display:none;width:1px;height:1px;">';
            
            // Add tracking pixel to email body
            if (strpos($mail->Body, '{tracking_pixel}') !== false) {
                $mail->Body = str_replace('{tracking_pixel}', $trackingPixel, $mail->Body);
            } else if (stripos($mail->Body, '</body>') !== false) {
                $mail->Body = str_replace('</body>', $trackingPixel . '</body>', $mail->Body);
            } else {
                $mail->Body .= $trackingPixel;
            }
            
            // Log tracking information
            file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] Added tracking pixel with ID: $trackingId\n", FILE_APPEND);
            
            // Save tracking information to database if possible
            try {
                // Check if the email_tracking table has the necessary columns
                $stmt = $pdo->prepare("SHOW COLUMNS FROM email_tracking");
                $stmt->execute();
                $columns = [];
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $columns[] = $row['Field'];
                }
                
                // Build the query based on available columns
                if (in_array('recipient_email', $columns) && in_array('recipient_name', $columns) && in_array('subject', $columns)) {
                    $stmt = $pdo->prepare("
                        INSERT INTO email_tracking 
                        (tracking_id, email_type, recipient_email, recipient_name, subject, sent_at) 
                        VALUES (?, 'scheduled', ?, ?, ?, NOW())
                    ");
                    $stmt->execute([$trackingId, $to, $toName, $processedSubject]);
                } else {
                    // Fallback to basic tracking (only if member exists in database)
                    $memberId = null;
                    if (!empty($recipient['id'])) {
                        // Verify member exists before inserting tracking record
                        try {
                            $checkStmt = $pdo->prepare("SELECT id FROM members WHERE id = ?");
                            $checkStmt->execute([$recipient['id']]);
                            if ($checkStmt->fetch()) {
                                $memberId = $recipient['id'];

                                $stmt = $pdo->prepare("
                                    INSERT INTO email_tracking
                                    (tracking_id, member_id, email_type, sent_at)
                                    VALUES (?, ?, 'scheduled', NOW())
                                ");
                                $stmt->execute([$trackingId, $memberId]);
                                file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] Saved tracking for member_id: $memberId\n", FILE_APPEND);
                            } else {
                                file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] Member ID {$recipient['id']} not found, skipping tracking\n", FILE_APPEND);
                            }
                        } catch (Exception $trackingError) {
                            file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] Error with member tracking: " . $trackingError->getMessage() . "\n", FILE_APPEND);
                        }
                    } else {
                        file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] No member ID provided, skipping member tracking\n", FILE_APPEND);
                    }
                }
            } catch (Exception $e) {
                file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] Error saving tracking: " . $e->getMessage() . "\n", FILE_APPEND);
            }
        }
        
        // Send the email
        $result = $mail->send();
        file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] Email sent successfully to $to\n", FILE_APPEND);
        return $result;
    } catch (Exception $e) {
        file_put_contents($log_file, "[" . date('Y-m-d H:i:s') . "] Error sending email: " . $e->getMessage() . "\n", FILE_APPEND);
        return false;
    }
}

/**
 * Get recipients from a group
 * 
 * @param int $groupId Group ID
 * @return array Array of recipients
 */
function getGroupRecipients($groupId) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT 
                c.id, c.name, c.email
            FROM 
                contact_group_members cgm
            JOIN 
                contacts c ON cgm.contact_id = c.id
            WHERE 
                cgm.group_id = ?
                AND c.email IS NOT NULL
        ");
        $stmt->execute([$groupId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error getting group recipients: " . $e->getMessage());
        return [];
    }
}

/**
 * Format a date for display
 * 
 * @param string $dateTime Date/time string
 * @param string $format Format string
 * @return string Formatted date
 */
function formatScheduleDate($dateTime, $format = 'Y-m-d H:i:s') {
    if (empty($dateTime)) {
        return 'N/A';
    }
    
    return date($format, strtotime($dateTime));
}

/**
 * Get status badge HTML
 * 
 * @param string $status Status string
 * @return string HTML for status badge
 */
function getStatusBadge($status) {
    $badgeClass = match($status) {
        'pending' => 'secondary',
        'active' => 'success',
        'paused' => 'warning',
        'completed' => 'info',
        'failed' => 'danger',
        'sent' => 'success',
        'skipped' => 'warning',
        default => 'secondary'
    };
    
    return '<span class="badge bg-' . $badgeClass . '">' . ucfirst($status) . '</span>';
}

/**
 * Calculate progress percentage
 * 
 * @param int $sent Number of sent emails
 * @param int $total Total number of emails
 * @return int Progress percentage
 */
function calculateProgress($sent, $total) {
    if ($total <= 0) {
        return 0;
    }
    
    return round(($sent / $total) * 100);
}

/**
 * Get HTML for progress bar
 * 
 * @param int $percent Progress percentage
 * @return string HTML for progress bar
 */
function getProgressBar($percent) {
    $html = '<div class="progress">';
    $html .= '<div class="progress-bar" role="progressbar" style="width: ' . $percent . '%;" ';
    $html .= 'aria-valuenow="' . $percent . '" aria-valuemin="0" aria-valuemax="100">';
    $html .= $percent . '%';
    $html .= '</div>';
    $html .= '</div>';
    
    return $html;
}

/**
 * Log an action to the admin activity logs
 * 
 * @param int $adminId Admin ID
 * @param string $action Action description
 * @param string $module Module name
 */
function logSchedulerAction($adminId, $action, $module = 'email_scheduler') {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO admin_activity_logs 
            (admin_id, action, module, ip_address, created_at) 
            VALUES (?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $adminId,
            $action,
            $module,
            $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0'
        ]);
    } catch (Exception $e) {
        error_log("Error logging admin action: " . $e->getMessage());
    }
}

/**
 * Get email open rate
 * 
 * @param int $scheduleId Schedule ID
 * @return array Open rate data
 */
function getEmailOpenRate($scheduleId) {
    global $pdo;
    
    try {
        // Get total sent emails
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total_sent
            FROM email_schedule_recipients
            WHERE schedule_id = ? AND status = 'sent'
        ");
        $stmt->execute([$scheduleId]);
        $totalSent = $stmt->fetch(PDO::FETCH_ASSOC)['total_sent'] ?? 0;
        
        // Get total opened emails
        $stmt = $pdo->prepare("
            SELECT COUNT(DISTINCT email) as total_opened
            FROM email_tracking
            WHERE schedule_id = ? AND tracking_type = 'open' AND opened_at IS NOT NULL
        ");
        $stmt->execute([$scheduleId]);
        $totalOpened = $stmt->fetch(PDO::FETCH_ASSOC)['total_opened'] ?? 0;
        
        // Calculate open rate
        $openRate = $totalSent > 0 ? round(($totalOpened / $totalSent) * 100, 2) : 0;
        
        return [
            'total_sent' => $totalSent,
            'total_opened' => $totalOpened,
            'open_rate' => $openRate
        ];
    } catch (Exception $e) {
        error_log("Error getting email open rate: " . $e->getMessage());
        return [
            'total_sent' => 0,
            'total_opened' => 0,
            'open_rate' => 0
        ];
    }
}

/**
 * Get email click rate
 * 
 * @param int $scheduleId Schedule ID
 * @return array Click rate data
 */
function getEmailClickRate($scheduleId) {
    global $pdo;
    
    try {
        // Get total sent emails
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total_sent
            FROM email_schedule_recipients
            WHERE schedule_id = ? AND status = 'sent'
        ");
        $stmt->execute([$scheduleId]);
        $totalSent = $stmt->fetch(PDO::FETCH_ASSOC)['total_sent'] ?? 0;
        
        // Get total clicked emails
        $stmt = $pdo->prepare("
            SELECT COUNT(DISTINCT email) as total_clicked
            FROM email_tracking
            WHERE schedule_id = ? AND tracking_type = 'click' AND clicked_at IS NOT NULL
        ");
        $stmt->execute([$scheduleId]);
        $totalClicked = $stmt->fetch(PDO::FETCH_ASSOC)['total_clicked'] ?? 0;
        
        // Calculate click rate
        $clickRate = $totalSent > 0 ? round(($totalClicked / $totalSent) * 100, 2) : 0;
        
        return [
            'total_sent' => $totalSent,
            'total_clicked' => $totalClicked,
            'click_rate' => $clickRate
        ];
    } catch (Exception $e) {
        error_log("Error getting email click rate: " . $e->getMessage());
        return [
            'total_sent' => 0,
            'total_clicked' => 0,
            'click_rate' => 0
        ];
    }
}

/**
 * Generate a unique tracking ID for email tracking
 * 
 * @return string Unique tracking ID
 */
function generateTrackingId() {
    return uniqid() . '-' . bin2hex(random_bytes(8));
}

/**
 * Get the tracking pixel HTML for email tracking
 * 
 * @param string $trackingId Unique tracking ID for this email
 * @return string HTML code for tracking pixel
 */
function getTrackingPixel($trackingId) {
    $trackingUrl = rtrim(SITE_URL, '/') . '/track.php?id=' . urlencode($trackingId) . '&type=open';
    return '<img src="' . $trackingUrl . '" width="1" height="1" alt="" style="display:none;" />';
}

/**
 * Get member ID by email address
 * 
 * @param string $email Email address to look up
 * @return array|null Returns array with 'id' and 'type' or null if not found
 */
function getMemberIdByEmail($email) {
    global $pdo;
    
    try {
        // First check members table
        $stmt = $pdo->prepare("SELECT id FROM members WHERE email = ? LIMIT 1");
        $stmt->execute([$email]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            return $result['id']; // Return member ID
        }
        
        // If we're here, it's not a member - return null to avoid foreign key issues
        // The email_tracking table has a foreign key constraint on member_id
        return null;
    } catch (Exception $e) {
        error_log('Error getting member ID by email: ' . $e->getMessage());
        return null;
    }
}
