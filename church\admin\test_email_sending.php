<?php
// Email sending diagnostic and test script
require_once '../config.php';

echo "<h1>📧 Email Sending Diagnostic & Test</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .step{background:#f5f5f5;padding:10px;margin:10px 0;border-left:4px solid #007bff;}</style>";

$target_email = 'bointa@<EMAIL>';

try {
    echo "<div class='step'>";
    echo "<h2>Step 1: PHP Mail Configuration Check</h2>";
    
    // Check PHP mail configuration
    echo "<span class='info'>📋 PHP Mail Settings:</span><br>";
    echo "<span class='info'>• SMTP: " . ini_get('SMTP') . "</span><br>";
    echo "<span class='info'>• smtp_port: " . ini_get('smtp_port') . "</span><br>";
    echo "<span class='info'>• sendmail_from: " . ini_get('sendmail_from') . "</span><br>";
    echo "<span class='info'>• sendmail_path: " . ini_get('sendmail_path') . "</span><br>";
    
    // Check if mail function is available
    if (function_exists('mail')) {
        echo "<span class='success'>✅ PHP mail() function is available</span><br>";
    } else {
        echo "<span class='error'>❌ PHP mail() function is not available</span><br>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 2: Simple Email Test</h2>";
    
    // Test simple email first
    $simple_subject = "Test Email from Church System";
    $simple_message = "This is a test email to verify SMTP configuration is working.";
    $simple_headers = "From: <EMAIL>\r\n";
    $simple_headers .= "Reply-To: <EMAIL>\r\n";
    $simple_headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
    
    echo "<span class='info'>📧 Sending simple test email to: {$target_email}</span><br>";
    
    $simple_result = mail($target_email, $simple_subject, $simple_message, $simple_headers);
    
    if ($simple_result) {
        echo "<span class='success'>✅ Simple email sent successfully!</span><br>";
    } else {
        echo "<span class='error'>❌ Simple email failed to send</span><br>";
        $error = error_get_last();
        if ($error) {
            echo "<span class='error'>Error: " . $error['message'] . "</span><br>";
        }
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 3: Enhanced Email Headers Test</h2>";
    
    // Test with enhanced headers
    $enhanced_subject = "Enhanced Test Email - Church QR System";
    $enhanced_message = "
    Hello,
    
    This is an enhanced test email from the Church QR Code System.
    
    If you receive this email, the SMTP configuration is working correctly.
    
    Best regards,
    Freedom Assembly Church
    ";
    
    $enhanced_headers = "MIME-Version: 1.0\r\n";
    $enhanced_headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
    $enhanced_headers .= "From: Freedom Assembly Church <<EMAIL>>\r\n";
    $enhanced_headers .= "Reply-To: <EMAIL>\r\n";
    $enhanced_headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";
    $enhanced_headers .= "X-Priority: 3\r\n";
    
    echo "<span class='info'>📧 Sending enhanced test email to: {$target_email}</span><br>";
    
    $enhanced_result = mail($target_email, $enhanced_subject, $enhanced_message, $enhanced_headers);
    
    if ($enhanced_result) {
        echo "<span class='success'>✅ Enhanced email sent successfully!</span><br>";
    } else {
        echo "<span class='error'>❌ Enhanced email failed to send</span><br>";
        $error = error_get_last();
        if ($error) {
            echo "<span class='error'>Error: " . $error['message'] . "</span><br>";
        }
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 4: QR Code Email Test</h2>";
    
    // Get the member and QR code data
    $stmt = $pdo->prepare("SELECT * FROM members WHERE email = ?");
    $stmt->execute([$target_email]);
    $member = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$member) {
        echo "<span class='error'>❌ Member not found</span><br>";
    } else {
        // Get QR code data
        $stmt = $pdo->prepare("
            SELECT mqr.*, e.title as event_title, e.event_date, e.location as event_location
            FROM member_qr_codes mqr
            JOIN events e ON mqr.event_id = e.id
            WHERE mqr.attendee_email = ?
            ORDER BY mqr.created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$target_email]);
        $qr_data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$qr_data) {
            echo "<span class='error'>❌ No QR code found for this member</span><br>";
        } else {
            echo "<span class='info'>📧 Sending QR code email to: {$target_email}</span><br>";
            echo "<span class='info'>🎫 QR Token: {$qr_data['qr_token']}</span><br>";
            echo "<span class='info'>🎪 Event: {$qr_data['event_title']}</span><br>";
            
            // Generate QR code URL
            $base_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
            $qr_url = $base_url . "/member_checkin.php?token=" . $qr_data['qr_token'];
            
            // Create QR code email
            $qr_subject = "Your QR Code for " . $qr_data['event_title'];
            
            // Simplified HTML email (avoiding complex JavaScript)
            $qr_message = "
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset='UTF-8'>
                <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                <title>Event QR Code</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
                    .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; }
                    .header h1 { margin: 0; font-size: 24px; }
                    .content { padding: 30px 20px; background: white; }
                    .qr-section { text-align: center; margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 10px; }
                    .qr-placeholder { border: 3px solid #007bff; padding: 40px; background: white; border-radius: 10px; display: inline-block; margin: 20px 0; font-size: 18px; font-weight: bold; color: #007bff; }
                    .instructions { background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3; }
                    .instructions h3 { margin-top: 0; color: #1976d2; }
                    .event-details { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
                    .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px; }
                    .btn { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
                </style>
            </head>
            <body>
                <div class='container'>
                    <div class='header'>
                        <h1>🎟️ Your Event QR Code</h1>
                        <p>Freedom Assembly Church</p>
                    </div>
                    
                    <div class='content'>
                        <h2>Hello " . htmlspecialchars($member['full_name']) . "!</h2>
                        <p>Thank you for registering for <strong>" . htmlspecialchars($qr_data['event_title']) . "</strong>. Your personal QR code is ready!</p>
                        
                        <div class='event-details'>
                            <h3>📅 Event Details</h3>
                            <p><strong>Event:</strong> " . htmlspecialchars($qr_data['event_title']) . "</p>
                            <p><strong>Date:</strong> " . date('F j, Y g:i A', strtotime($qr_data['event_date'])) . "</p>
                            <p><strong>Location:</strong> " . htmlspecialchars($qr_data['event_location']) . "</p>
                        </div>
                        
                        <div class='qr-section'>
                            <h3>🎯 Your Personal QR Code</h3>
                            <div class='qr-placeholder'>
                                QR CODE<br>
                                <small style='font-size: 12px;'>" . htmlspecialchars($qr_data['qr_token']) . "</small>
                            </div>
                            <p><strong>QR Code ID:</strong> " . htmlspecialchars($qr_data['qr_token']) . "</p>
                            <a href='{$qr_url}' class='btn'>🔗 Open Check-in Link</a>
                        </div>
                        
                        <div class='instructions'>
                            <h3>📱 How to Use Your QR Code</h3>
                            <ol>
                                <li><strong>Save this email</strong> or screenshot the QR code</li>
                                <li><strong>Arrive at the event</strong> and look for check-in stations</li>
                                <li><strong>Show your QR code</strong> to staff at the entrance</li>
                                <li><strong>Get checked in instantly</strong> - no manual process needed!</li>
                            </ol>
                            
                            <p><strong>💡 Pro Tips:</strong></p>
                            <ul>
                                <li>You can show the QR code on your phone or print this email</li>
                                <li>The QR code works even if you're offline</li>
                                <li>Each QR code is unique and can only be used once</li>
                                <li>If QR scanning doesn't work, use the check-in link above</li>
                            </ul>
                        </div>
                        
                        <p>If you have any questions or need assistance, please contact our event team.</p>
                        <p>We look forward to seeing you at the event!</p>
                        
                        <p><strong>Blessings,</strong><br>
                        Freedom Assembly Church Event Team</p>
                    </div>
                    
                    <div class='footer'>
                        <p>This is an automated message from Freedom Assembly Church</p>
                        <p>Event Management System | QR Code Check-in</p>
                        <p>Check-in URL: <a href='{$qr_url}'>{$qr_url}</a></p>
                    </div>
                </div>
            </body>
            </html>
            ";
            
            // QR Email headers
            $qr_headers = "MIME-Version: 1.0\r\n";
            $qr_headers .= "Content-type: text/html; charset=UTF-8\r\n";
            $qr_headers .= "From: Freedom Assembly Church <<EMAIL>>\r\n";
            $qr_headers .= "Reply-To: <EMAIL>\r\n";
            $qr_headers .= "X-Mailer: Church QR System\r\n";
            $qr_headers .= "X-Priority: 3\r\n";
            
            $qr_result = mail($target_email, $qr_subject, $qr_message, $qr_headers);
            
            if ($qr_result) {
                echo "<span class='success'>✅ QR code email sent successfully!</span><br>";
                
                // Update database to mark email as sent
                $stmt = $pdo->prepare("
                    UPDATE member_qr_codes 
                    SET email_sent = 1, email_sent_at = NOW() 
                    WHERE qr_token = ?
                ");
                $stmt->execute([$qr_data['qr_token']]);
                
                echo "<span class='success'>✅ Database updated - email marked as sent</span><br>";
            } else {
                echo "<span class='error'>❌ QR code email failed to send</span><br>";
                $error = error_get_last();
                if ($error) {
                    echo "<span class='error'>Error: " . $error['message'] . "</span><br>";
                }
            }
        }
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 5: Alternative Email Methods</h2>";
    
    // Try alternative email method using different approach
    echo "<span class='info'>🔧 Testing alternative email method...</span><br>";
    
    $alt_to = $target_email;
    $alt_subject = "Alternative Method - Church QR Test";
    $alt_message = "This email was sent using an alternative method to test email delivery.";
    
    // Try with minimal headers
    $alt_headers = "From: <EMAIL>";
    
    $alt_result = @mail($alt_to, $alt_subject, $alt_message, $alt_headers);
    
    if ($alt_result) {
        echo "<span class='success'>✅ Alternative email method worked!</span><br>";
    } else {
        echo "<span class='error'>❌ Alternative email method also failed</span><br>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 6: Email Troubleshooting Information</h2>";
    
    echo "<span class='info'>📋 <strong>Troubleshooting Checklist:</strong></span><br>";
    echo "<span class='info'>1. Check spam/junk folder in {$target_email}</span><br>";
    echo "<span class='info'>2. Verify SMTP server is running and accessible</span><br>";
    echo "<span class='info'>3. Check firewall settings for outbound email ports</span><br>";
    echo "<span class='info'>4. Verify email server authentication if required</span><br>";
    echo "<span class='info'>5. Check email server logs for delivery attempts</span><br>";
    echo "<br>";
    
    echo "<span class='info'>📧 <strong>Email Details Sent:</strong></span><br>";
    echo "<span class='info'>• Recipient: {$target_email}</span><br>";
    echo "<span class='info'>• From: <EMAIL></span><br>";
    echo "<span class='info'>• Subject: Various test subjects</span><br>";
    echo "<span class='info'>• Content: Both plain text and HTML</span><br>";
    echo "</div>";
    
    echo "<div class='step' style='background:#e8f5e8;border-color:#28a745;'>";
    echo "<h2>🎯 Email Test Summary</h2>";
    
    $total_tests = 0;
    $successful_tests = 0;
    
    if ($simple_result) $successful_tests++;
    $total_tests++;
    
    if ($enhanced_result) $successful_tests++;
    $total_tests++;
    
    if (isset($qr_result) && $qr_result) $successful_tests++;
    if (isset($qr_result)) $total_tests++;
    
    if ($alt_result) $successful_tests++;
    $total_tests++;
    
    echo "<span class='info'>📊 Test Results: {$successful_tests}/{$total_tests} emails sent successfully</span><br>";
    
    if ($successful_tests > 0) {
        echo "<span class='success'>✅ At least some emails were sent - check {$target_email} inbox and spam folder</span><br>";
        echo "<span class='success'>✅ SMTP configuration appears to be working</span><br>";
    } else {
        echo "<span class='error'>❌ No emails were sent successfully</span><br>";
        echo "<span class='error'>❌ SMTP configuration may need adjustment</span><br>";
    }
    
    echo "<br>";
    echo "<strong>📱 Next Steps:</strong><br>";
    echo "<span class='info'>1. Check {$target_email} for test emails (including spam folder)</span><br>";
    echo "<span class='info'>2. If no emails received, check server email logs</span><br>";
    echo "<span class='info'>3. Verify SMTP server settings in php.ini or server config</span><br>";
    echo "<span class='info'>4. Test with a different email address if needed</span><br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='step' style='background:#ffe8e8;border-color:#dc3545;'>";
    echo "<h2>❌ Email Test Failed</h2>";
    echo "<span class='error'>Error: " . $e->getMessage() . "</span><br>";
    echo "<span class='error'>File: " . $e->getFile() . " Line: " . $e->getLine() . "</span>";
    echo "</div>";
}
?>

<script>
// Auto-refresh to show updates
setTimeout(function() {
    console.log('Email diagnostic completed');
}, 5000);
</script>
