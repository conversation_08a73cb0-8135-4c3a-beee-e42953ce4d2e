<?php
session_start();

// Include route protection (this will handle login check and role verification)
require_once 'includes/route_protection.php';

// Protect this page - Super Admin only
protectSuperAdminRoute();

// Include the configuration file
require_once '../config.php';

$message = '';
$error = '';
$setup_complete = false;

// Handle setup actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'setup_database') {
            $pdo->beginTransaction();
            
            // Read and execute the SQL file
            $sql_file = __DIR__ . '/setup_granular_permissions.sql';
            if (!file_exists($sql_file)) {
                throw new Exception('SQL setup file not found: ' . $sql_file);
            }
            
            $sql_content = file_get_contents($sql_file);
            if ($sql_content === false) {
                throw new Exception('Could not read SQL setup file');
            }
            
            // Split SQL into individual statements
            $statements = array_filter(array_map('trim', explode(';', $sql_content)));
            
            foreach ($statements as $statement) {
                if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
                    $pdo->exec($statement);
                }
            }
            
            $pdo->commit();
            $message = "Database tables created successfully!";
            
        } elseif ($_POST['action'] === 'initialize_permissions') {
            $pdo->beginTransaction();
            
            // Check if tables exist
            $stmt = $pdo->query("SHOW TABLES LIKE 'granular_permissions'");
            if ($stmt->rowCount() == 0) {
                throw new Exception('Granular permissions tables not found. Please set up the database first.');
            }
            
            // Get all users with their current roles
            $stmt = $pdo->prepare("
                SELECT DISTINCT ura.user_id, ur.role_name, a.username
                FROM user_role_assignments ura
                JOIN user_roles ur ON ura.role_id = ur.id
                JOIN admins a ON ura.user_id = a.id
                WHERE ura.is_active = 1
                ORDER BY a.username
            ");
            $stmt->execute();
            $user_roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $permissions_assigned = 0;
            
            // For each user, assign default permissions based on their role
            foreach ($user_roles as $user_role) {
                $user_id = $user_role['user_id'];
                $role_name = $user_role['role_name'];
                $username = $user_role['username'];
                
                // Get default permissions for this role
                $default_permissions = getDefaultPermissionsForRole($role_name);
                
                foreach ($default_permissions as $permission_key) {
                    // Get permission ID
                    $stmt = $pdo->prepare("SELECT id FROM granular_permissions WHERE permission_key = ? AND is_active = 1");
                    $stmt->execute([$permission_key]);
                    $permission_id = $stmt->fetchColumn();
                    
                    if ($permission_id) {
                        // Check if permission already assigned
                        $stmt = $pdo->prepare("
                            SELECT COUNT(*) FROM user_individual_permissions 
                            WHERE user_id = ? AND permission_id = ?
                        ");
                        $stmt->execute([$user_id, $permission_id]);
                        
                        if ($stmt->fetchColumn() == 0) {
                            // Assign permission to user
                            $stmt = $pdo->prepare("
                                INSERT INTO user_individual_permissions 
                                (user_id, permission_id, granted_by, is_active) 
                                VALUES (?, ?, ?, 1)
                            ");
                            $stmt->execute([$user_id, $permission_id, $_SESSION['admin_id']]);
                            $permissions_assigned++;
                        }
                    }
                }
            }
            
            $pdo->commit();
            $message = "Permissions initialized successfully! Assigned {$permissions_assigned} permissions to " . count($user_roles) . " users.";
            $setup_complete = true;
            
        } elseif ($_POST['action'] === 'test_system') {
            // Test the granular permission system
            $test_results = [];
            
            // Test 1: Check if tables exist
            $tables = ['permission_categories', 'granular_permissions', 'user_individual_permissions'];
            foreach ($tables as $table) {
                $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
                $test_results[] = [
                    'test' => "Table '{$table}' exists",
                    'result' => $stmt->rowCount() > 0,
                    'details' => $stmt->rowCount() > 0 ? 'Found' : 'Missing'
                ];
            }
            
            // Test 2: Check if permissions are populated
            $stmt = $pdo->query("SELECT COUNT(*) FROM granular_permissions WHERE is_active = 1");
            $permission_count = $stmt->fetchColumn();
            $test_results[] = [
                'test' => 'Granular permissions populated',
                'result' => $permission_count > 0,
                'details' => "{$permission_count} permissions found"
            ];
            
            // Test 3: Check if categories are populated
            $stmt = $pdo->query("SELECT COUNT(*) FROM permission_categories WHERE is_active = 1");
            $category_count = $stmt->fetchColumn();
            $test_results[] = [
                'test' => 'Permission categories populated',
                'result' => $category_count > 0,
                'details' => "{$category_count} categories found"
            ];
            
            // Test 4: Check if user permissions are assigned
            $stmt = $pdo->query("SELECT COUNT(*) FROM user_individual_permissions WHERE is_active = 1");
            $user_permission_count = $stmt->fetchColumn();
            $test_results[] = [
                'test' => 'User permissions assigned',
                'result' => $user_permission_count > 0,
                'details' => "{$user_permission_count} user permissions found"
            ];
            
            // Test 5: Test the canAccessPage function with granular permissions
            $test_page = 'manage_user_permissions.php';
            $can_access = $rbac->canAccessPage($test_page);
            $test_results[] = [
                'test' => "Access check for '{$test_page}'",
                'result' => $can_access,
                'details' => $can_access ? 'Access granted' : 'Access denied'
            ];
            
            $message = "System test completed. Results displayed below.";
        }
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        $error = $e->getMessage();
    }
}

/**
 * Get default permissions for a role
 */
function getDefaultPermissionsForRole($role_name) {
    $role_permissions = [
        'super_admin' => [
            'dashboard.view', 'dashboard.analytics',
            'members.view', 'members.add', 'members.edit', 'members.delete', 'members.view_profile',
            'members.family_management', 'members.skills', 'members.requests', 'members.volunteers',
            'events.view', 'events.create', 'events.edit', 'events.delete', 'events.attendance',
            'events.sessions', 'events.categories', 'events.reports',
            'email.bulk_send', 'email.scheduler', 'email.templates', 'email.contacts',
            'email.contact_groups', 'email.birthday', 'email.birthday_send', 'email.birthday_test',
            'email.birthday_notifications', 'email.automated_templates', 'email.whatsapp', 'email.analytics',
            'sms.single_send', 'sms.bulk_send', 'sms.templates', 'sms.analytics',
            'donations.view', 'donations.manage', 'donations.gifts', 'donations.enhanced',
            'donations.payment_integration', 'donations.payment_tables',
            'integrations.calendar', 'integrations.social_media',
            'settings.general', 'settings.appearance', 'settings.branding', 'settings.logo',
            'settings.custom_fields', 'settings.security_audit', 'settings.security_settings',
            'settings.backup', 'settings.profile',
            'notifications.view',
            'admin.rbac_management', 'admin.create_users', 'admin.super_dashboard', 'admin.permission_management'
        ],
        'limited_admin' => [
            'dashboard.view',
            'members.view', 'members.add', 'members.edit', 'members.view_profile',
            'members.family_management', 'members.skills', 'members.requests', 'members.volunteers',
            'events.view', 'events.attendance', 'events.sessions', 'events.reports',
            'email.bulk_send', 'email.scheduler', 'email.templates', 'email.contacts',
            'email.contact_groups', 'email.birthday', 'email.birthday_send', 'email.analytics',
            'sms.single_send', 'sms.bulk_send', 'sms.templates', 'sms.analytics',
            'donations.view', 'donations.manage', 'donations.gifts', 'donations.enhanced',
            'settings.general', 'settings.profile',
            'notifications.view'
        ],
        'event_coordinator' => [
            'dashboard.view',
            'events.view', 'events.create', 'events.edit', 'events.attendance',
            'events.sessions', 'events.categories', 'events.reports',
            'settings.profile',
            'notifications.view'
        ],
        'session_moderator' => [
            'dashboard.view',
            'events.view', 'events.attendance', 'events.sessions',
            'settings.profile',
            'notifications.view'
        ],
        'staff' => [
            'dashboard.view',
            'settings.profile',
            'notifications.view'
        ]
    ];
    
    return $role_permissions[$role_name] ?? [];
}

// Check if system is already set up
$tables_exist = false;
$permissions_initialized = false;
$table_status = [];

try {
    // Check each table individually
    $required_tables = ['permission_categories', 'granular_permissions', 'user_individual_permissions'];
    $tables_found = 0;

    foreach ($required_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        $exists = $stmt->rowCount() > 0;
        $table_status[$table] = $exists;
        if ($exists) $tables_found++;
    }

    $tables_exist = ($tables_found === count($required_tables));

    // If tables exist, check if permissions have been initialized
    if ($tables_exist) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM user_individual_permissions WHERE is_active = 1");
        $permissions_initialized = $stmt->fetchColumn() > 0;
    }
} catch (Exception $e) {
    // Log errors for debugging
    error_log("Error checking setup status: " . $e->getMessage());
}

// Page title and header info
$page_title = 'Setup Granular Permissions System';
$page_header = 'Granular Permission System Setup';
$page_description = 'Set up and initialize the enhanced granular permission system';

// Include header
include 'includes/header.php';
?>

<style>
.setup-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
}

.setup-step {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.2s ease;
}

.setup-step:hover {
    background-color: #ffffff;
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.1);
}

.setup-step.completed {
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.setup-step.disabled {
    background-color: #f8f9fa;
    opacity: 0.6;
}

.test-result {
    padding: 10px;
    margin: 5px 0;
    border-radius: 5px;
}

.test-result.pass {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.test-result.fail {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.feature-list {
    background-color: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="setup-header p-4 text-center">
            <h2 class="mb-2">
                <i class="bi bi-gear-wide-connected"></i> <?php echo $page_header; ?>
            </h2>
            <p class="mb-0"><?php echo $page_description; ?></p>
        </div>
    </div>
</div>

<!-- Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Feature Overview -->
<div class="feature-list">
    <h4><i class="bi bi-star"></i> Enhanced Granular Permission System Features</h4>
    <div class="row">
        <div class="col-md-6">
            <ul>
                <li><strong>Individual User Permissions</strong> - Assign specific permissions to each user</li>
                <li><strong>Permission Categories</strong> - Organized permission groups for easy management</li>
                <li><strong>Flexible Assignment</strong> - Mix and match permissions as needed</li>
                <li><strong>Copy Permissions</strong> - Easily copy permission sets between users</li>
            </ul>
        </div>
        <div class="col-md-6">
            <ul>
                <li><strong>Backward Compatibility</strong> - Works alongside existing role system</li>
                <li><strong>Dynamic Sidebar</strong> - Menu items show/hide based on permissions</li>
                <li><strong>Audit Trail</strong> - Track who granted permissions and when</li>
                <li><strong>User-Friendly Interface</strong> - Intuitive permission management UI</li>
            </ul>
        </div>
    </div>
</div>

<!-- Setup Steps -->
<div class="row">
    <div class="col-md-12">

        <!-- Step 1: Database Setup -->
        <div class="setup-step <?php echo $tables_exist ? 'completed' : ''; ?>">
            <h4>
                <i class="bi bi-database"></i> Step 1: Database Setup
                <?php if ($tables_exist): ?>
                    <span class="badge bg-success ms-2">Completed</span>
                <?php endif; ?>
            </h4>
            <p>Create the necessary database tables for the granular permission system.</p>

            <?php if (!$tables_exist): ?>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="setup_database">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-database-add"></i> Create Database Tables
                    </button>
                </form>
            <?php else: ?>
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i> Database tables already exist and are ready to use.
                </div>
            <?php endif; ?>
        </div>

        <!-- Step 2: Initialize Permissions -->
        <div class="setup-step <?php echo (!$tables_exist) ? 'disabled' : ($permissions_initialized ? 'completed' : ''); ?>">
            <h4>
                <i class="bi bi-shield-check"></i> Step 2: Initialize User Permissions
                <?php if ($permissions_initialized): ?>
                    <span class="badge bg-success ms-2">Completed</span>
                <?php endif; ?>
            </h4>
            <p>Assign default permissions to existing users based on their current roles.</p>

            <?php if ($tables_exist && !$permissions_initialized): ?>
                <form method="POST" style="display: inline;">
                    <input type="hidden" name="action" value="initialize_permissions">
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-person-check"></i> Initialize User Permissions
                    </button>
                </form>
                <div class="alert alert-info mt-3">
                    <i class="bi bi-info-circle"></i> This will assign default permissions to all existing users based on their current roles. Existing individual permissions will not be affected.
                </div>
            <?php elseif (!$tables_exist): ?>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> Please complete Step 1 first.
                </div>
            <?php else: ?>
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i> User permissions have been initialized.
                </div>
            <?php endif; ?>
        </div>

        <!-- Step 3: Test System -->
        <div class="setup-step">
            <h4><i class="bi bi-check-circle"></i> Step 3: Test System</h4>
            <p>Verify that the granular permission system is working correctly.</p>

            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="test_system">
                <button type="submit" class="btn btn-info">
                    <i class="bi bi-play-circle"></i> Run System Test
                </button>
            </form>

            <!-- Debug: Show current table status -->
            <div class="mt-3">
                <h6>Current Table Status:</h6>
                <?php foreach (['permission_categories', 'granular_permissions', 'user_individual_permissions'] as $table): ?>
                    <div class="test-result <?php echo isset($table_status[$table]) && $table_status[$table] ? 'pass' : 'fail'; ?>">
                        <strong>Table '<?php echo $table; ?>' exists:</strong>
                        <?php echo isset($table_status[$table]) && $table_status[$table] ? '✓ PASS - Found' : '✗ FAIL - Missing'; ?>
                    </div>
                <?php endforeach; ?>
            </div>

            <?php if (isset($test_results)): ?>
                <div class="mt-3">
                    <h5>Test Results:</h5>
                    <?php foreach ($test_results as $test): ?>
                        <div class="test-result <?php echo $test['result'] ? 'pass' : 'fail'; ?>">
                            <strong><?php echo htmlspecialchars($test['test']); ?>:</strong>
                            <?php echo $test['result'] ? '✓ PASS' : '✗ FAIL'; ?>
                            - <?php echo htmlspecialchars($test['details']); ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Step 4: Access Management Interface -->
        <div class="setup-step <?php echo $tables_exist ? '' : 'disabled'; ?>">
            <h4><i class="bi bi-person-gear"></i> Step 4: Manage User Permissions</h4>
            <p>Access the user permission management interface to assign individual permissions.</p>

            <?php if ($tables_exist): ?>
                <a href="manage_user_permissions.php" class="btn btn-primary">
                    <i class="bi bi-gear"></i> Open Permission Management
                </a>
                <div class="alert alert-info mt-3">
                    <i class="bi bi-info-circle"></i> Use this interface to assign specific permissions to individual users, overriding their role-based permissions.
                </div>
            <?php else: ?>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> Please complete Step 1 (database setup) first.
                </div>
            <?php endif; ?>
        </div>

    </div>
</div>

<!-- Next Steps -->
<?php if ($setup_complete): ?>
<div class="row mt-4">
    <div class="col-md-12">
        <div class="alert alert-success">
            <h4><i class="bi bi-check-circle"></i> Setup Complete!</h4>
            <p>The granular permission system has been successfully set up. You can now:</p>
            <ul>
                <li><a href="manage_user_permissions.php">Manage individual user permissions</a></li>
                <li><a href="setup_rbac_system.php">Continue using the role-based system</a></li>
                <li><a href="dashboard.php">Return to the dashboard</a></li>
            </ul>

            <div class="mt-3">
                <h5>How the New System Works:</h5>
                <ol>
                    <li><strong>Individual Permissions First</strong>: The system first checks if a user has individual permissions for a page/function</li>
                    <li><strong>Role-Based Fallback</strong>: If no individual permissions are found, it falls back to the existing role-based system</li>
                    <li><strong>Flexible Assignment</strong>: You can now assign any combination of permissions to any user, regardless of their role</li>
                    <li><strong>Easy Management</strong>: Use the permission management interface to visually assign permissions using checkboxes</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>
