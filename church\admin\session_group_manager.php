<?php
require_once '../config.php';
require_once 'includes/auth_check.php';

$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    header('Location: events.php');
    exit();
}

// Get event details
$stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
$stmt->execute([$event_id]);
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    header('Location: events.php');
    exit();
}

// Create session_groups table if it doesn't exist
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS session_groups (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            event_id INT(11) NOT NULL,
            group_name VARCHAR(255) NOT NULL,
            group_type ENUM('time_based', 'category_based', 'location_based', 'custom') DEFAULT 'custom',
            group_color VARCHAR(7) DEFAULT '#007bff',
            group_description TEXT,
            sort_order INT(11) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_event_id (event_id),
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS session_group_assignments (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            session_id INT(11) NOT NULL,
            group_id INT(11) NOT NULL,
            assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_session_id (session_id),
            INDEX idx_group_id (group_id),
            UNIQUE KEY unique_session_group (session_id, group_id),
            FOREIGN KEY (session_id) REFERENCES event_sessions(id) ON DELETE CASCADE,
            FOREIGN KEY (group_id) REFERENCES session_groups(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
} catch (PDOException $e) {
    error_log("Error creating session groups tables: " . $e->getMessage());
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'create_group') {
            $group_name = trim($_POST['group_name']);
            $group_type = $_POST['group_type'];
            $group_color = $_POST['group_color'];
            $group_description = trim($_POST['group_description']);
            
            if (empty($group_name)) {
                $error = "Group name is required.";
            } else {
                $stmt = $pdo->prepare("
                    INSERT INTO session_groups (event_id, group_name, group_type, group_color, group_description)
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([$event_id, $group_name, $group_type, $group_color, $group_description]);
                $message = "Session group '{$group_name}' created successfully!";
            }
        } elseif ($action === 'assign_sessions') {
            $group_id = (int)$_POST['group_id'];
            $session_ids = $_POST['session_ids'] ?? [];
            
            if (!$group_id || empty($session_ids)) {
                $error = "Please select a group and at least one session.";
            } else {
                $pdo->beginTransaction();
                
                // Remove existing assignments for these sessions to this group
                $placeholders = str_repeat('?,', count($session_ids) - 1) . '?';
                $stmt = $pdo->prepare("
                    DELETE FROM session_group_assignments 
                    WHERE session_id IN ($placeholders) AND group_id = ?
                ");
                $params = array_merge($session_ids, [$group_id]);
                $stmt->execute($params);
                
                // Add new assignments
                $assigned_count = 0;
                foreach ($session_ids as $session_id) {
                    $stmt = $pdo->prepare("
                        INSERT IGNORE INTO session_group_assignments (session_id, group_id)
                        VALUES (?, ?)
                    ");
                    $stmt->execute([$session_id, $group_id]);
                    $assigned_count++;
                }
                
                $pdo->commit();
                $message = "Successfully assigned {$assigned_count} sessions to the group.";
            }
        } elseif ($action === 'auto_group_suggestions') {
            // Auto-create intelligent groups based on session patterns
            $suggestions = generateAutoGroupSuggestions($pdo, $event_id);
            $created_count = 0;
            
            $pdo->beginTransaction();
            
            foreach ($suggestions as $suggestion) {
                // Create the group
                $stmt = $pdo->prepare("
                    INSERT INTO session_groups (event_id, group_name, group_type, group_color, group_description)
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $event_id,
                    $suggestion['name'],
                    $suggestion['type'],
                    $suggestion['color'],
                    $suggestion['description']
                ]);
                $group_id = $pdo->lastInsertId();
                
                // Assign sessions to the group
                foreach ($suggestion['sessions'] as $session_id) {
                    $stmt = $pdo->prepare("
                        INSERT IGNORE INTO session_group_assignments (session_id, group_id)
                        VALUES (?, ?)
                    ");
                    $stmt->execute([$session_id, $group_id]);
                }
                
                $created_count++;
            }
            
            $pdo->commit();
            $message = "Auto-generated {$created_count} intelligent session groups based on patterns!";
        } elseif ($action === 'group_bulk_attendance') {
            $group_id = (int)$_POST['group_id'];
            $attendance_action = $_POST['attendance_action'];
            $selected_attendees = $_POST['selected_attendees'] ?? [];
            
            if (!$group_id || !$attendance_action || empty($selected_attendees)) {
                $error = "Please select a group, action, and at least one attendee.";
            } else {
                // Get all sessions in the group
                $stmt = $pdo->prepare("
                    SELECT session_id FROM session_group_assignments WHERE group_id = ?
                ");
                $stmt->execute([$group_id]);
                $group_sessions = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                if (empty($group_sessions)) {
                    $error = "No sessions found in the selected group.";
                } else {
                    $pdo->beginTransaction();
                    $affected_count = 0;
                    
                    foreach ($selected_attendees as $attendee_id) {
                        foreach ($group_sessions as $session_id) {
                            // Check if attendance record exists
                            $stmt = $pdo->prepare("
                                SELECT id FROM session_attendance 
                                WHERE session_id = ? AND 
                                (member_id = ? OR CONCAT('guest_', id) = ?)
                            ");
                            $stmt->execute([$session_id, $attendee_id, $attendee_id]);
                            $existing = $stmt->fetch();
                            
                            if ($existing) {
                                // Update existing record
                                $stmt = $pdo->prepare("
                                    UPDATE session_attendance 
                                    SET attendance_status = ?,
                                        attendance_date = CASE WHEN ? = 'attended' THEN NOW() ELSE attendance_date END
                                    WHERE id = ?
                                ");
                                $stmt->execute([$attendance_action, $attendance_action, $existing['id']]);
                                $affected_count++;
                            } else {
                                // Create new record if attendee is registered for event
                                if (is_numeric($attendee_id)) {
                                    $stmt = $pdo->prepare("
                                        INSERT INTO session_attendance (session_id, member_id, attendance_status, attendance_date)
                                        VALUES (?, ?, ?, CASE WHEN ? = 'attended' THEN NOW() ELSE NULL END)
                                    ");
                                    $stmt->execute([$session_id, $attendee_id, $attendance_action, $attendance_action]);
                                    $affected_count++;
                                }
                            }
                        }
                    }
                    
                    $pdo->commit();
                    $message = "Successfully updated {$affected_count} attendance records for the group.";
                }
            }
        }
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        $error = "Error: " . $e->getMessage();
    }
}

// Function to generate intelligent group suggestions
function generateAutoGroupSuggestions($pdo, $event_id) {
    $suggestions = [];
    
    // Get all sessions for analysis
    $stmt = $pdo->prepare("
        SELECT id, session_title, start_datetime, end_datetime, location, instructor
        FROM event_sessions 
        WHERE event_id = ? AND status = 'active'
        ORDER BY start_datetime
    ");
    $stmt->execute([$event_id]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($sessions)) return $suggestions;
    
    // Time-based grouping
    $time_groups = [];
    foreach ($sessions as $session) {
        $hour = (int)date('H', strtotime($session['start_datetime']));
        $date = date('Y-m-d', strtotime($session['start_datetime']));
        
        if ($hour < 12) {
            $time_groups[$date . '_morning'][] = $session;
        } elseif ($hour < 17) {
            $time_groups[$date . '_afternoon'][] = $session;
        } else {
            $time_groups[$date . '_evening'][] = $session;
        }
    }
    
    foreach ($time_groups as $key => $group_sessions) {
        if (count($group_sessions) >= 2) {
            list($date, $period) = explode('_', $key);
            $suggestions[] = [
                'name' => ucfirst($period) . ' Sessions - ' . date('M j', strtotime($date)),
                'type' => 'time_based',
                'color' => $period === 'morning' ? '#28a745' : ($period === 'afternoon' ? '#ffc107' : '#6f42c1'),
                'description' => 'Auto-grouped by time period',
                'sessions' => array_column($group_sessions, 'id')
            ];
        }
    }
    
    // Category-based grouping (by keywords in titles)
    $category_keywords = [
        'workshop' => ['workshop', 'training', 'seminar', 'class'],
        'worship' => ['worship', 'service', 'prayer', 'praise'],
        'meal' => ['lunch', 'dinner', 'breakfast', 'meal', 'food'],
        'meeting' => ['meeting', 'discussion', 'planning', 'committee']
    ];
    
    foreach ($category_keywords as $category => $keywords) {
        $category_sessions = [];
        foreach ($sessions as $session) {
            $title_lower = strtolower($session['session_title']);
            foreach ($keywords as $keyword) {
                if (strpos($title_lower, $keyword) !== false) {
                    $category_sessions[] = $session;
                    break;
                }
            }
        }
        
        if (count($category_sessions) >= 2) {
            $suggestions[] = [
                'name' => ucfirst($category) . ' Sessions',
                'type' => 'category_based',
                'color' => $category === 'workshop' ? '#17a2b8' : ($category === 'worship' ? '#dc3545' : ($category === 'meal' ? '#fd7e14' : '#6c757d')),
                'description' => 'Auto-grouped by session type',
                'sessions' => array_column($category_sessions, 'id')
            ];
        }
    }
    
    return $suggestions;
}

// Get existing groups
$stmt = $pdo->prepare("
    SELECT sg.*, COUNT(sga.session_id) as session_count
    FROM session_groups sg
    LEFT JOIN session_group_assignments sga ON sg.id = sga.group_id
    WHERE sg.event_id = ?
    GROUP BY sg.id
    ORDER BY sg.sort_order, sg.group_name
");
$stmt->execute([$event_id]);
$groups = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get all sessions with group assignments
$stmt = $pdo->prepare("
    SELECT es.*, 
           GROUP_CONCAT(sg.group_name ORDER BY sg.group_name SEPARATOR ', ') as group_names,
           GROUP_CONCAT(sg.group_color ORDER BY sg.group_name SEPARATOR ',') as group_colors,
           COUNT(sa.id) as registered_count,
           COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as attended_count
    FROM event_sessions es
    LEFT JOIN session_group_assignments sga ON es.id = sga.session_id
    LEFT JOIN session_groups sg ON sga.group_id = sg.id
    LEFT JOIN session_attendance sa ON es.id = sa.session_id
    WHERE es.event_id = ? AND es.status = 'active'
    GROUP BY es.id
    ORDER BY es.start_datetime
");
$stmt->execute([$event_id]);
$sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get event attendees for bulk operations
$stmt = $pdo->prepare("
    SELECT 
        er.user_id as id,
        m.full_name as name,
        'member' as type
    FROM event_rsvps er
    JOIN members m ON er.user_id = m.id
    WHERE er.event_id = ? AND er.status = 'attending'
    
    UNION ALL
    
    SELECT 
        erg.id,
        erg.guest_name as name,
        'guest' as type
    FROM event_rsvps_guests erg
    WHERE erg.event_id = ? AND erg.status = 'attending'
    
    ORDER BY name
");
$stmt->execute([$event_id, $event_id]);
$attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'Session Group Manager';
include 'includes/header.php';
?>

<style>
.group-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.group-card {
    border-radius: 10px;
    margin-bottom: 1rem;
    transition: all 0.2s;
    border-left: 4px solid #007bff;
}

.group-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.session-item {
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s;
}

.session-item:hover {
    background-color: #f8f9fa;
}

.session-item.grouped {
    border-left: 4px solid;
}

.group-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

.auto-suggestion {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border: 2px dashed #6c757d;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.bulk-action-panel {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="group-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="bi bi-collection"></i> Session Group Manager</h1>
                        <p class="mb-0">
                            <strong><?php echo htmlspecialchars($event['title']); ?></strong> •
                            <?php echo date('M j, Y', strtotime($event['event_date'])); ?>
                        </p>
                    </div>
                    <div>
                        <a href="advanced_bulk_attendance.php?event_id=<?php echo $event_id; ?>" class="btn btn-light me-2">
                            <i class="bi bi-diagram-3"></i> Bulk Operations
                        </a>
                        <a href="multi_session_dashboard.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-light">
                            <i class="bi bi-arrow-left"></i> Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Quick Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h4 class="text-primary"><?php echo count($groups); ?></h4>
                    <small>Session Groups</small>
                </div>
                <div class="stat-card">
                    <h4 class="text-success"><?php echo count($sessions); ?></h4>
                    <small>Total Sessions</small>
                </div>
                <div class="stat-card">
                    <h4 class="text-info"><?php echo count(array_filter($sessions, function($s) { return !empty($s['group_names']); })); ?></h4>
                    <small>Grouped Sessions</small>
                </div>
                <div class="stat-card">
                    <h4 class="text-warning"><?php echo count($attendees); ?></h4>
                    <small>Event Attendees</small>
                </div>
            </div>

            <div class="row">
                <!-- Group Management -->
                <div class="col-md-8">
                    <!-- Auto-Suggestions -->
                    <div class="auto-suggestion">
                        <h6><i class="bi bi-magic"></i> Intelligent Group Suggestions</h6>
                        <p class="text-muted mb-3">Let AI analyze your sessions and create smart groups automatically</p>
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="action" value="auto_group_suggestions">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-magic"></i> Generate Smart Groups
                            </button>
                        </form>
                    </div>

                    <!-- Create New Group -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-plus-circle"></i> Create New Group</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="create_group">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Group Name</label>
                                            <input type="text" name="group_name" class="form-control" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">Group Type</label>
                                            <select name="group_type" class="form-select">
                                                <option value="time_based">Time-Based</option>
                                                <option value="category_based">Category-Based</option>
                                                <option value="location_based">Location-Based</option>
                                                <option value="custom">Custom</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">Color</label>
                                            <input type="color" name="group_color" class="form-control form-control-color" value="#007bff">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Description</label>
                                    <textarea name="group_description" class="form-control" rows="2"></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-plus-circle"></i> Create Group
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Existing Groups -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-collection"></i> Session Groups</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($groups)): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-collection" style="font-size: 3rem;"></i>
                                    <h6 class="mt-2">No groups created yet</h6>
                                    <p>Create your first session group or use auto-suggestions above</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($groups as $group): ?>
                                    <div class="group-card card" style="border-left-color: <?php echo $group['group_color']; ?>">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <span class="badge" style="background-color: <?php echo $group['group_color']; ?>">
                                                            <?php echo htmlspecialchars($group['group_name']); ?>
                                                        </span>
                                                    </h6>
                                                    <p class="text-muted mb-2"><?php echo htmlspecialchars($group['group_description']); ?></p>
                                                    <small class="text-muted">
                                                        <i class="bi bi-tag"></i> <?php echo ucfirst(str_replace('_', ' ', $group['group_type'])); ?> •
                                                        <i class="bi bi-calendar-event"></i> <?php echo $group['session_count']; ?> sessions
                                                    </small>
                                                </div>
                                                <div class="btn-group">
                                                    <button class="btn btn-sm btn-outline-primary" onclick="showAssignSessions(<?php echo $group['id']; ?>)">
                                                        <i class="bi bi-plus"></i> Assign
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-success" onclick="showGroupBulkActions(<?php echo $group['id']; ?>)">
                                                        <i class="bi bi-check2-all"></i> Bulk
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Sessions List -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-calendar-event"></i> Sessions Overview</h6>
                        </div>
                        <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                            <?php foreach ($sessions as $session): ?>
                                <div class="session-item <?php echo !empty($session['group_names']) ? 'grouped' : ''; ?>"
                                     <?php if (!empty($session['group_colors'])): ?>
                                         style="border-left-color: <?php echo explode(',', $session['group_colors'])[0]; ?>"
                                     <?php endif; ?>>
                                    <div class="form-check">
                                        <input class="form-check-input session-checkbox" type="checkbox"
                                               value="<?php echo $session['id']; ?>" id="session_<?php echo $session['id']; ?>">
                                        <label class="form-check-label w-100" for="session_<?php echo $session['id']; ?>">
                                            <strong><?php echo htmlspecialchars($session['session_title']); ?></strong>
                                            <br>
                                            <small class="text-muted">
                                                <?php echo date('M j, g:i A', strtotime($session['start_datetime'])); ?>
                                                • <?php echo $session['attended_count']; ?>/<?php echo $session['registered_count']; ?> attended
                                            </small>
                                            <?php if (!empty($session['group_names'])): ?>
                                                <br>
                                                <?php
                                                $group_names = explode(', ', $session['group_names']);
                                                $group_colors = explode(',', $session['group_colors']);
                                                foreach ($group_names as $index => $group_name):
                                                    $color = $group_colors[$index] ?? '#007bff';
                                                ?>
                                                    <span class="group-badge" style="background-color: <?php echo $color; ?>; color: white;">
                                                        <?php echo htmlspecialchars($group_name); ?>
                                                    </span>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </label>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Assign Sessions Modal -->
<div class="modal fade" id="assignSessionsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Assign Sessions to Group</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="assignSessionsForm">
                <input type="hidden" name="action" value="assign_sessions">
                <input type="hidden" name="group_id" id="assignGroupId">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Select Sessions to Assign</label>
                        <div class="mb-2">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllSessions()">Select All</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearSessionSelection()">Clear All</button>
                            <span id="selectedSessionCount" class="badge bg-primary ms-2">0 selected</span>
                        </div>
                        <div style="max-height: 300px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 1rem;">
                            <?php foreach ($sessions as $session): ?>
                                <div class="form-check mb-2">
                                    <input class="form-check-input assign-session-checkbox" type="checkbox"
                                           name="session_ids[]" value="<?php echo $session['id']; ?>"
                                           id="assign_session_<?php echo $session['id']; ?>">
                                    <label class="form-check-label" for="assign_session_<?php echo $session['id']; ?>">
                                        <strong><?php echo htmlspecialchars($session['session_title']); ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <?php echo date('M j, g:i A', strtotime($session['start_datetime'])); ?>
                                        </small>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Assign Sessions</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Group Bulk Actions Modal -->
<div class="modal fade" id="groupBulkActionsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Group Bulk Attendance Actions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="groupBulkActionsForm">
                <input type="hidden" name="action" value="group_bulk_attendance">
                <input type="hidden" name="group_id" id="bulkGroupId">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Attendance Action</label>
                                <select name="attendance_action" class="form-select" required>
                                    <option value="">Choose action...</option>
                                    <option value="attended">Mark as Attended</option>
                                    <option value="no_show">Mark as No-Show</option>
                                    <option value="registered">Mark as Registered</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Select Attendees</label>
                                <div class="mb-2">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllAttendees()">Select All</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearAttendeeSelection()">Clear All</button>
                                    <span id="selectedAttendeeCount" class="badge bg-success ms-2">0 selected</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <input type="text" id="attendeeSearch" class="form-control" placeholder="Search attendees...">
                    </div>

                    <div style="max-height: 300px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 1rem;">
                        <?php foreach ($attendees as $attendee): ?>
                            <div class="attendee-item form-check mb-2" data-name="<?php echo strtolower($attendee['name']); ?>">
                                <input class="form-check-input bulk-attendee-checkbox" type="checkbox"
                                       name="selected_attendees[]" value="<?php echo $attendee['id']; ?>"
                                       id="bulk_attendee_<?php echo $attendee['id']; ?>">
                                <label class="form-check-label" for="bulk_attendee_<?php echo $attendee['id']; ?>">
                                    <strong><?php echo htmlspecialchars($attendee['name']); ?></strong>
                                    <span class="badge bg-<?php echo $attendee['type'] === 'member' ? 'primary' : 'secondary'; ?> ms-2">
                                        <?php echo ucfirst($attendee['type']); ?>
                                    </span>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="bulkActionSubmit" disabled>Apply to Group</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function showAssignSessions(groupId) {
    document.getElementById('assignGroupId').value = groupId;
    const modal = new bootstrap.Modal(document.getElementById('assignSessionsModal'));
    modal.show();
}

function showGroupBulkActions(groupId) {
    document.getElementById('bulkGroupId').value = groupId;
    const modal = new bootstrap.Modal(document.getElementById('groupBulkActionsModal'));
    modal.show();
}

function selectAllSessions() {
    document.querySelectorAll('.assign-session-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedSessionCount();
}

function clearSessionSelection() {
    document.querySelectorAll('.assign-session-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedSessionCount();
}

function updateSelectedSessionCount() {
    const count = document.querySelectorAll('.assign-session-checkbox:checked').length;
    document.getElementById('selectedSessionCount').textContent = count + ' selected';
}

function selectAllAttendees() {
    document.querySelectorAll('.bulk-attendee-checkbox:not([style*="display: none"])').forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedAttendeeCount();
}

function clearAttendeeSelection() {
    document.querySelectorAll('.bulk-attendee-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedAttendeeCount();
}

function updateSelectedAttendeeCount() {
    const count = document.querySelectorAll('.bulk-attendee-checkbox:checked').length;
    document.getElementById('selectedAttendeeCount').textContent = count + ' selected';

    const action = document.querySelector('select[name="attendance_action"]').value;
    document.getElementById('bulkActionSubmit').disabled = count === 0 || !action;
}

// Attendee search functionality
document.getElementById('attendeeSearch').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    document.querySelectorAll('.attendee-item').forEach(item => {
        const name = item.dataset.name;
        if (name.includes(searchTerm)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
});

// Event listeners
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('assign-session-checkbox')) {
        updateSelectedSessionCount();
    }

    if (e.target.classList.contains('bulk-attendee-checkbox')) {
        updateSelectedAttendeeCount();
    }

    if (e.target.name === 'attendance_action') {
        updateSelectedAttendeeCount();
    }
});

// Form validation
document.getElementById('assignSessionsForm').addEventListener('submit', function(e) {
    const selectedSessions = document.querySelectorAll('.assign-session-checkbox:checked');
    if (selectedSessions.length === 0) {
        e.preventDefault();
        alert('Please select at least one session to assign.');
    }
});

document.getElementById('groupBulkActionsForm').addEventListener('submit', function(e) {
    const selectedAttendees = document.querySelectorAll('.bulk-attendee-checkbox:checked');
    const action = document.querySelector('select[name="attendance_action"]').value;

    if (selectedAttendees.length === 0) {
        e.preventDefault();
        alert('Please select at least one attendee.');
        return;
    }

    if (!action) {
        e.preventDefault();
        alert('Please select an attendance action.');
        return;
    }

    if (!confirm(`This will apply "${action}" to ${selectedAttendees.length} attendees across all sessions in this group. Continue?`)) {
        e.preventDefault();
    }
});

// Initialize counts
updateSelectedSessionCount();
updateSelectedAttendeeCount();
</script>

<?php include 'includes/footer.php'; ?>
