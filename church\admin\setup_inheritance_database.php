<?php
/**
 * Database Setup Script for Enhanced Attendance Inheritance System
 * This script creates all required tables and sample data for testing
 */

require_once '../config.php';

echo "🚀 Setting up Enhanced Attendance Inheritance System Database\n";
echo "=============================================================\n\n";

$setup_results = [];
$total_steps = 0;
$completed_steps = 0;

function setupStep($description, $callback) {
    global $setup_results, $total_steps, $completed_steps, $pdo;
    
    $total_steps++;
    echo "⏳ $description... ";
    
    try {
        $result = $callback($pdo);
        $completed_steps++;
        echo "✅ DONE\n";
        $setup_results[] = ['step' => $description, 'status' => 'SUCCESS', 'details' => $result];
        return true;
    } catch (Exception $e) {
        echo "❌ FAILED\n";
        echo "   Error: " . $e->getMessage() . "\n";
        $setup_results[] = ['step' => $description, 'status' => 'FAILED', 'details' => $e->getMessage()];
        return false;
    }
}

// Step 1: Create inheritance rules table
setupStep("Creating attendance_inheritance_rules table", function($pdo) {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS attendance_inheritance_rules (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            event_id INT(11) NOT NULL,
            rule_name VARCHAR(255) NOT NULL,
            rule_type ENUM('bottom_up', 'top_down', 'peer_to_peer', 'conditional') NOT NULL,
            source_criteria JSON NOT NULL,
            target_criteria JSON NOT NULL,
            inheritance_logic JSON NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            priority_order INT(11) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_event_id (event_id),
            INDEX idx_rule_type (rule_type),
            INDEX idx_priority (priority_order),
            INDEX idx_active (is_active),
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    return "Table created with proper indexes and foreign keys";
});

// Step 2: Create inheritance log table
setupStep("Creating attendance_inheritance_log table", function($pdo) {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS attendance_inheritance_log (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            event_id INT(11) NOT NULL,
            rule_id INT(11) NOT NULL,
            attendee_id VARCHAR(255) NOT NULL,
            attendee_type ENUM('member', 'guest') NOT NULL,
            source_session_id INT(11) DEFAULT NULL,
            target_session_id INT(11) DEFAULT NULL,
            inheritance_type VARCHAR(100) NOT NULL,
            old_status VARCHAR(50),
            new_status VARCHAR(50),
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            applied_by INT(11) DEFAULT NULL,
            INDEX idx_event_id (event_id),
            INDEX idx_rule_id (rule_id),
            INDEX idx_attendee (attendee_id),
            INDEX idx_applied_at (applied_at),
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
            FOREIGN KEY (rule_id) REFERENCES attendance_inheritance_rules(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    return "Log table created with proper indexes and foreign keys";
});

// Step 3: Create sample event for testing
setupStep("Creating sample test event", function($pdo) {
    // Check if test event already exists
    $stmt = $pdo->prepare("SELECT id FROM events WHERE title = 'Test Event - Inheritance System'");
    $stmt->execute();
    $existing_event = $stmt->fetch();
    
    if ($existing_event) {
        return "Test event already exists with ID: " . $existing_event['id'];
    }
    
    $stmt = $pdo->prepare("
        INSERT INTO events (title, description, event_date, location, max_attendees, status)
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        'Test Event - Inheritance System',
        'Sample event for testing the Enhanced Attendance Inheritance System',
        '2024-02-15 09:00:00',
        'Main Conference Center',
        500,
        'published'
    ]);
    
    $event_id = $pdo->lastInsertId();
    return "Created test event with ID: $event_id";
});

// Step 4: Create sample sessions
setupStep("Creating sample test sessions", function($pdo) {
    // Get the test event ID
    $stmt = $pdo->prepare("SELECT id FROM events WHERE title = 'Test Event - Inheritance System'");
    $stmt->execute();
    $event = $stmt->fetch();
    
    if (!$event) {
        throw new Exception("Test event not found");
    }
    
    $event_id = $event['id'];
    
    // Check if sessions already exist
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM event_sessions WHERE event_id = ?");
    $stmt->execute([$event_id]);
    $existing_sessions = $stmt->fetchColumn();
    
    if ($existing_sessions > 0) {
        return "Test sessions already exist ($existing_sessions sessions)";
    }
    
    $sessions = [
        ['title' => 'Morning Worship', 'type' => 'worship', 'start' => '09:00:00', 'end' => '10:00:00'],
        ['title' => 'Workshop A: Leadership', 'type' => 'workshop', 'start' => '10:30:00', 'end' => '11:30:00'],
        ['title' => 'Workshop B: Technology', 'type' => 'workshop', 'start' => '10:30:00', 'end' => '11:30:00'],
        ['title' => 'Lunch Break', 'type' => 'meal', 'start' => '12:00:00', 'end' => '13:00:00'],
        ['title' => 'Afternoon Main Session', 'type' => 'main_session', 'start' => '14:00:00', 'end' => '15:30:00'],
        ['title' => 'Workshop C: Finance', 'type' => 'workshop', 'start' => '16:00:00', 'end' => '17:00:00']
    ];
    
    $created_sessions = 0;
    foreach ($sessions as $session) {
        $stmt = $pdo->prepare("
            INSERT INTO event_sessions (event_id, session_title, session_type, start_datetime, end_datetime, max_attendees, status)
            VALUES (?, ?, ?, CONCAT('2024-02-15 ', ?), CONCAT('2024-02-15 ', ?), 100, 'active')
        ");
        $stmt->execute([
            $event_id,
            $session['title'],
            $session['type'],
            $session['start'],
            $session['end']
        ]);
        $created_sessions++;
    }
    
    return "Created $created_sessions test sessions";
});

// Step 5: Create sample members
setupStep("Creating sample test members", function($pdo) {
    $test_members = [
        ['name' => 'John Smith', 'email' => '<EMAIL>'],
        ['name' => 'Mary Johnson', 'email' => '<EMAIL>'],
        ['name' => 'David Wilson', 'email' => '<EMAIL>'],
        ['name' => 'Sarah Brown', 'email' => '<EMAIL>'],
        ['name' => 'Michael Davis', 'email' => '<EMAIL>']
    ];
    
    $created_members = 0;
    foreach ($test_members as $member) {
        // Check if member already exists
        $stmt = $pdo->prepare("SELECT id FROM members WHERE email = ?");
        $stmt->execute([$member['email']]);
        $existing = $stmt->fetch();
        
        if (!$existing) {
            $stmt = $pdo->prepare("
                INSERT INTO members (full_name, email, status, created_at)
                VALUES (?, ?, 'active', NOW())
            ");
            $stmt->execute([$member['name'], $member['email']]);
            $created_members++;
        }
    }
    
    return "Created $created_members new test members (some may have already existed)";
});

// Step 6: Create sample RSVPs
setupStep("Creating sample event RSVPs", function($pdo) {
    // Get test event and members
    $stmt = $pdo->prepare("SELECT id FROM events WHERE title = 'Test Event - Inheritance System'");
    $stmt->execute();
    $event = $stmt->fetch();
    
    $stmt = $pdo->prepare("SELECT id FROM members WHERE email LIKE '%@testchurch.com'");
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!$event || empty($members)) {
        throw new Exception("Test event or members not found");
    }
    
    $created_rsvps = 0;
    foreach ($members as $member_id) {
        // Check if RSVP already exists
        $stmt = $pdo->prepare("SELECT id FROM event_rsvps WHERE event_id = ? AND user_id = ?");
        $stmt->execute([$event['id'], $member_id]);
        $existing = $stmt->fetch();
        
        if (!$existing) {
            $stmt = $pdo->prepare("
                INSERT INTO event_rsvps (event_id, user_id, status)
                VALUES (?, ?, 'attending')
            ");
            $stmt->execute([$event['id'], $member_id]);
            $created_rsvps++;
        }
    }
    
    return "Created $created_rsvps event RSVPs";
});

// Step 7: Create sample session attendance
setupStep("Creating sample session attendance", function($pdo) {
    // Get test event sessions and members
    $stmt = $pdo->prepare("
        SELECT es.id, es.session_title 
        FROM event_sessions es 
        JOIN events e ON es.event_id = e.id 
        WHERE e.title = 'Test Event - Inheritance System'
    ");
    $stmt->execute();
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $stmt = $pdo->prepare("SELECT id FROM members WHERE email LIKE '%@testchurch.com' LIMIT 3");
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($sessions) || empty($members)) {
        throw new Exception("Test sessions or members not found");
    }
    
    $created_attendance = 0;
    
    // Create some realistic attendance patterns
    foreach ($members as $index => $member_id) {
        foreach ($sessions as $session_index => $session) {
            // Create varied attendance patterns
            $should_attend = false;
            
            if ($index === 0) {
                // Member 1: Attends morning worship and workshops
                $should_attend = in_array($session['session_title'], ['Morning Worship', 'Workshop A: Leadership', 'Workshop C: Finance']);
            } elseif ($index === 1) {
                // Member 2: Attends workshops and main session
                $should_attend = in_array($session['session_title'], ['Workshop A: Leadership', 'Workshop B: Technology', 'Afternoon Main Session']);
            } elseif ($index === 2) {
                // Member 3: Attends everything except one workshop
                $should_attend = $session['session_title'] !== 'Workshop B: Technology';
            }
            
            if ($should_attend) {
                // Check if attendance already exists
                $stmt = $pdo->prepare("SELECT id FROM session_attendance WHERE session_id = ? AND member_id = ?");
                $stmt->execute([$session['id'], $member_id]);
                $existing = $stmt->fetch();
                
                if (!$existing) {
                    $stmt = $pdo->prepare("
                        INSERT INTO session_attendance (session_id, member_id, attendance_status, attendance_date)
                        VALUES (?, ?, 'attended', NOW())
                    ");
                    $stmt->execute([$session['id'], $member_id]);
                    $created_attendance++;
                }
            }
        }
    }
    
    return "Created $created_attendance session attendance records";
});

// Step 8: Create default inheritance rules
setupStep("Creating default inheritance rules", function($pdo) {
    // Get test event
    $stmt = $pdo->prepare("SELECT id FROM events WHERE title = 'Test Event - Inheritance System'");
    $stmt->execute();
    $event = $stmt->fetch();
    
    if (!$event) {
        throw new Exception("Test event not found");
    }
    
    // Check if rules already exist
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM attendance_inheritance_rules WHERE event_id = ?");
    $stmt->execute([$event['id']]);
    $existing_rules = $stmt->fetchColumn();
    
    if ($existing_rules > 0) {
        return "Default rules already exist ($existing_rules rules)";
    }
    
    $default_rules = [
        [
            'rule_name' => 'Session to Event Attendance (50% threshold)',
            'rule_type' => 'bottom_up',
            'source_criteria' => json_encode(['session_types' => []]),
            'target_criteria' => json_encode(['target' => 'event']),
            'inheritance_logic' => json_encode(['min_sessions' => 1, 'min_percentage' => 50]),
            'priority_order' => 1
        ],
        [
            'rule_name' => 'Event to Core Sessions',
            'rule_type' => 'top_down',
            'source_criteria' => json_encode(['target' => 'event']),
            'target_criteria' => json_encode(['session_types' => ['worship', 'main_session']]),
            'inheritance_logic' => json_encode(['target_status' => 'attended']),
            'priority_order' => 2
        ],
        [
            'rule_name' => 'Workshop Cross-Attendance',
            'rule_type' => 'conditional',
            'source_criteria' => json_encode(['session_types' => ['workshop']]),
            'target_criteria' => json_encode(['session_types' => ['workshop']]),
            'inheritance_logic' => json_encode(['condition_type' => 'time_based', 'time_window_hours' => 2, 'copy_status' => 'attended']),
            'priority_order' => 3
        ]
    ];
    
    $created_rules = 0;
    foreach ($default_rules as $rule) {
        $stmt = $pdo->prepare("
            INSERT INTO attendance_inheritance_rules 
            (event_id, rule_name, rule_type, source_criteria, target_criteria, inheritance_logic, priority_order)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $event['id'],
            $rule['rule_name'],
            $rule['rule_type'],
            $rule['source_criteria'],
            $rule['target_criteria'],
            $rule['inheritance_logic'],
            $rule['priority_order']
        ]);
        $created_rules++;
    }
    
    return "Created $created_rules default inheritance rules";
});

echo "\n";
echo "📋 Setup Summary\n";
echo "================\n";
echo "Total Steps: $total_steps\n";
echo "Completed: $completed_steps\n";
echo "Failed: " . ($total_steps - $completed_steps) . "\n";
echo "Success Rate: " . round(($completed_steps / $total_steps) * 100, 1) . "%\n\n";

if ($completed_steps === $total_steps) {
    echo "🎉 DATABASE SETUP COMPLETED SUCCESSFULLY! 🎉\n\n";
    
    echo "✅ What was created:\n";
    echo "   • attendance_inheritance_rules table with indexes\n";
    echo "   • attendance_inheritance_log table with indexes\n";
    echo "   • Sample test event with 6 sessions\n";
    echo "   • 5 test members with varied attendance patterns\n";
    echo "   • Event RSVPs and session attendance records\n";
    echo "   • 3 default inheritance rules\n\n";
    
    echo "🚀 Next Steps:\n";
    echo "   1. Run validation: php validate_inheritance_system.php\n";
    echo "   2. Run tests: Open test_inheritance_system.php in browser\n";
    echo "   3. Try the inheritance engine: Open attendance_inheritance_engine.php\n\n";
    
    // Get test event ID for reference
    $stmt = $pdo->prepare("SELECT id FROM events WHERE title = 'Test Event - Inheritance System'");
    $stmt->execute();
    $test_event = $stmt->fetch();
    
    if ($test_event) {
        echo "📝 Test Event Details:\n";
        echo "   • Event ID: " . $test_event['id'] . "\n";
        echo "   • URL: attendance_inheritance_engine.php?event_id=" . $test_event['id'] . "\n";
        echo "   • Test URL: test_inheritance_system.php?event_id=" . $test_event['id'] . "\n\n";
    }
    
} else {
    echo "⚠️  SETUP INCOMPLETE\n";
    echo "Some steps failed. Please review the errors above.\n\n";
    
    echo "❌ Failed Steps:\n";
    foreach ($setup_results as $result) {
        if ($result['status'] === 'FAILED') {
            echo "   • " . $result['step'] . ": " . $result['details'] . "\n";
        }
    }
    echo "\n";
}

echo "Setup completed at: " . date('Y-m-d H:i:s') . "\n";
?>
