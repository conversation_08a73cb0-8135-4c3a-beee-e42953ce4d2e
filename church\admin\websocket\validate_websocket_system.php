<?php
/**
 * Comprehensive WebSocket System Validation
 * Validates all components of the real-time WebSocket implementation
 */

require_once '../config.php';

echo "🚀 Real-Time WebSocket System - Comprehensive Validation\n";
echo "========================================================\n\n";

$validation_results = [];
$total_checks = 0;
$passed_checks = 0;

function validateCheck($description, $condition, $details = '') {
    global $validation_results, $total_checks, $passed_checks;
    
    $total_checks++;
    $status = $condition ? 'PASS' : 'FAIL';
    
    if ($condition) {
        $passed_checks++;
        echo "✅ $description\n";
    } else {
        echo "❌ $description\n";
        if ($details) {
            echo "   Details: $details\n";
        }
    }
    
    $validation_results[] = [
        'description' => $description,
        'status' => $status,
        'details' => $details
    ];
}

// 1. Database Schema Validation
echo "📊 Validating WebSocket Database Schema...\n";
echo "-------------------------------------------\n";

try {
    // Check WebSocket tables
    $websocket_tables = [
        'realtime_activity_log',
        'websocket_connections', 
        'realtime_session_status',
        'realtime_notifications',
        'websocket_server_config'
    ];
    
    foreach ($websocket_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        validateCheck("$table table exists", $stmt->rowCount() > 0);
    }
    
    // Check table structures
    $stmt = $pdo->query("DESCRIBE realtime_activity_log");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    validateCheck("Activity log has required columns", 
        in_array('action_data', $columns) && 
        in_array('event_id', $columns) && 
        in_array('action_type', $columns)
    );
    
    $stmt = $pdo->query("DESCRIBE websocket_connections");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    validateCheck("Connections table has required columns", 
        in_array('connection_id', $columns) && 
        in_array('last_heartbeat', $columns) && 
        in_array('is_active', $columns)
    );
    
    // Check indexes
    $stmt = $pdo->query("SHOW INDEX FROM realtime_activity_log WHERE Key_name = 'idx_event_id'");
    validateCheck("Activity log has event_id index", $stmt->rowCount() > 0);
    
    $stmt = $pdo->query("SHOW INDEX FROM websocket_connections WHERE Key_name = 'idx_connection_id'");
    validateCheck("Connections table has connection_id index", $stmt->rowCount() > 0);
    
} catch (Exception $e) {
    validateCheck("Database schema validation", false, $e->getMessage());
}

echo "\n";

// 2. Server Files Validation
echo "📁 Validating WebSocket Server Files...\n";
echo "---------------------------------------\n";

$server_files = [
    __DIR__ . '/realtime_server.php' => 'WebSocket server implementation',
    __DIR__ . '/../js/realtime-websocket-client.js' => 'Client library',
    __DIR__ . '/setup_realtime_database.php' => 'Database setup script',
    __DIR__ . '/test_websocket_system.php' => 'Test suite',
    __DIR__ . '/validate_websocket_system.php' => 'This validation script'
];

foreach ($server_files as $file => $description) {
    validateCheck("$description exists", file_exists($file));
}

// Check server file content
$server_file_path = __DIR__ . '/realtime_server.php';
$server_content = file_exists($server_file_path) ? file_get_contents($server_file_path) : '';
validateCheck("Server has WebSocket class", strpos($server_content, 'class ChurchEventWebSocketServer') !== false);
validateCheck("Server has message handling", strpos($server_content, 'onMessage') !== false);
validateCheck("Server has authentication", strpos($server_content, 'handleAuthentication') !== false);

// Check client file content
$client_file_path = __DIR__ . '/../js/realtime-websocket-client.js';
$client_content = file_exists($client_file_path) ? file_get_contents($client_file_path) : '';
validateCheck("Client has WebSocket class", strpos($client_content, 'class ChurchEventWebSocketClient') !== false);
validateCheck("Client has connection management", strpos($client_content, 'connect()') !== false);
validateCheck("Client has authentication", strpos($client_content, 'authenticate') !== false);

echo "\n";

// 3. Integration Validation
echo "🔗 Validating System Integration...\n";
echo "-----------------------------------\n";

// Check dashboard integration
$dashboard_file_path = __DIR__ . '/../realtime_dashboard.php';
$dashboard_content = file_exists($dashboard_file_path) ? file_get_contents($dashboard_file_path) : '';
validateCheck("Dashboard includes WebSocket client",
    strpos($dashboard_content, 'realtime-websocket-client.js') !== false
);
validateCheck("Dashboard has enhanced class",
    strpos($dashboard_content, 'EnhancedRealtimeDashboard') !== false
);
validateCheck("Dashboard has WebSocket initialization",
    strpos($dashboard_content, 'initializeWebSocket') !== false
);

// Check mobile integration
$mobile_file_path = __DIR__ . '/../mobile_session_manager.php';
$mobile_content = file_exists($mobile_file_path) ? file_get_contents($mobile_file_path) : '';
validateCheck("Mobile manager exists", !empty($mobile_content));

// Check inheritance integration
$inheritance_file_path = __DIR__ . '/../attendance_inheritance_engine.php';
$inheritance_content = file_exists($inheritance_file_path) ? file_get_contents($inheritance_file_path) : '';
validateCheck("Inheritance engine exists", !empty($inheritance_content));

echo "\n";

// 4. Configuration Validation
echo "⚙️  Validating WebSocket Configuration...\n";
echo "-----------------------------------------\n";

try {
    // Check server configuration
    $stmt = $pdo->query("SELECT * FROM websocket_server_config");
    $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $config_keys = array_column($configs, 'config_key');
    validateCheck("Server port configured", in_array('server_port', $config_keys));
    validateCheck("Max connections configured", in_array('max_connections', $config_keys));
    validateCheck("Heartbeat interval configured", in_array('heartbeat_interval', $config_keys));
    
    // Check if cleanup procedure exists
    $stmt = $pdo->query("SHOW PROCEDURE STATUS WHERE Name = 'CleanupOldConnections'");
    validateCheck("Cleanup procedure exists", $stmt->rowCount() > 0);
    
} catch (Exception $e) {
    validateCheck("Configuration validation", false, $e->getMessage());
}

echo "\n";

// 5. Functional Testing
echo "⚡ Validating WebSocket Functionality...\n";
echo "---------------------------------------\n";

try {
    // Get a valid event ID for testing
    $stmt = $pdo->query("SELECT id FROM events LIMIT 1");
    $test_event_id = $stmt->fetchColumn();

    if (!$test_event_id) {
        validateCheck("Can create activity log entries", false, "No events found for testing");
    } else {
        // Test activity logging
        $stmt = $pdo->prepare("
            INSERT INTO realtime_activity_log (event_id, user_id, action_type, action_data)
            VALUES (?, 1, 'validation_test', ?)
        ");
        $test_data = json_encode(['test' => 'validation', 'timestamp' => date('Y-m-d H:i:s')]);
        $result = $stmt->execute([$test_event_id, $test_data]);
        validateCheck("Can create activity log entries", $result);
    }
    
    // Test connection tracking
    $stmt = $pdo->prepare("
        INSERT INTO websocket_connections (connection_id, user_id, user_name, user_type)
        VALUES (?, 1, 'Validation Test User', 'admin')
        ON DUPLICATE KEY UPDATE last_heartbeat = NOW()
    ");
    $test_conn_id = 'validation_test_' . time();
    $result = $stmt->execute([$test_conn_id]);
    validateCheck("Can create connection records", $result);
    
    // Test notifications
    if ($test_event_id) {
        $stmt = $pdo->prepare("
            INSERT INTO realtime_notifications
            (event_id, notification_type, title, message, created_by)
            VALUES (?, 'system_update', 'Validation Test', 'Test notification for validation', 1)
        ");
        $result = $stmt->execute([$test_event_id]);
        validateCheck("Can create notifications", $result);
    } else {
        validateCheck("Can create notifications", false, "No events found for testing");
    }
    
    // Clean up test data
    $pdo->exec("DELETE FROM realtime_activity_log WHERE action_type = 'validation_test'");
    $pdo->exec("DELETE FROM websocket_connections WHERE connection_id LIKE 'validation_test_%'");
    $pdo->exec("DELETE FROM realtime_notifications WHERE title = 'Validation Test'");
    
} catch (Exception $e) {
    validateCheck("Functional testing", false, $e->getMessage());
}

echo "\n";

// 6. Performance Validation
echo "🚀 Validating Performance Optimizations...\n";
echo "------------------------------------------\n";

try {
    // Check for proper indexes
    $performance_indexes = [
        ['table' => 'realtime_activity_log', 'index' => 'idx_event_id'],
        ['table' => 'realtime_activity_log', 'index' => 'idx_user_id'],
        ['table' => 'websocket_connections', 'index' => 'idx_connection_id'],
        ['table' => 'realtime_notifications', 'index' => 'idx_event_id']
    ];
    
    foreach ($performance_indexes as $index_check) {
        $stmt = $pdo->query("SHOW INDEX FROM {$index_check['table']} WHERE Key_name = '{$index_check['index']}'");
        validateCheck("Index {$index_check['index']} exists on {$index_check['table']}", $stmt->rowCount() > 0);
    }
    
    // Check for JSON column support
    $stmt = $pdo->query("SELECT COLUMN_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'realtime_activity_log' AND COLUMN_NAME = 'action_data'");
    $column_type = $stmt->fetchColumn();
    validateCheck("JSON column support", strpos(strtolower($column_type), 'json') !== false);
    
} catch (Exception $e) {
    validateCheck("Performance validation", false, $e->getMessage());
}

echo "\n";

// 7. Security Validation
echo "🔒 Validating Security Features...\n";
echo "----------------------------------\n";

// Check for authentication in server
validateCheck("Server has authentication handling", 
    strpos($server_content, 'handleAuthentication') !== false
);

// Check for input validation
validateCheck("Server validates message format", 
    strpos($server_content, 'json_decode') !== false &&
    strpos($server_content, 'isset($data[\'type\'])') !== false
);

// Check for connection limits
validateCheck("Server has connection management", 
    strpos($server_content, 'connections') !== false
);

// Check client has error handling
validateCheck("Client has error handling", 
    strpos($client_content, 'onerror') !== false &&
    strpos($client_content, 'catch') !== false
);

echo "\n";

// Final Summary
echo "📋 Validation Summary\n";
echo "====================\n";
echo "Total Checks: $total_checks\n";
echo "Passed: $passed_checks\n";
echo "Failed: " . ($total_checks - $passed_checks) . "\n";
echo "Success Rate: " . round(($passed_checks / $total_checks) * 100, 1) . "%\n\n";

if ($passed_checks === $total_checks) {
    echo "🎉 ALL VALIDATIONS PASSED! 🎉\n";
    echo "The Real-Time WebSocket System is ready for production use.\n\n";
    
    echo "✅ Validated Components:\n";
    echo "   • Database schema with proper indexes\n";
    echo "   • WebSocket server implementation\n";
    echo "   • Client library with full functionality\n";
    echo "   • System integration with existing dashboards\n";
    echo "   • Configuration and cleanup procedures\n";
    echo "   • Performance optimizations\n";
    echo "   • Security features and error handling\n\n";
    
    echo "🚀 Ready for Production:\n";
    echo "   1. Start WebSocket server: php websocket/realtime_server.php\n";
    echo "   2. Open enhanced dashboard for real-time updates\n";
    echo "   3. Test with multiple staff devices\n";
    echo "   4. Monitor performance and connections\n\n";
    
} else {
    echo "⚠️  SOME VALIDATIONS FAILED\n";
    echo "Please review the failed checks above and fix any issues.\n\n";
    
    echo "❌ Failed Checks:\n";
    foreach ($validation_results as $result) {
        if ($result['status'] === 'FAIL') {
            echo "   • " . $result['description'];
            if ($result['details']) {
                echo " (" . $result['details'] . ")";
            }
            echo "\n";
        }
    }
    echo "\n";
}

echo "📖 Documentation:\n";
echo "   • WebSocket Server: websocket/realtime_server.php\n";
echo "   • Client Library: js/realtime-websocket-client.js\n";
echo "   • Test Suite: websocket/test_websocket_system.php\n";
echo "   • Enhanced Dashboard: realtime_dashboard.php\n\n";

echo "🔧 Troubleshooting:\n";
echo "   • If server won't start: Check port 8080 availability\n";
echo "   • If connections fail: Verify firewall settings\n";
echo "   • If authentication fails: Check user session data\n";
echo "   • If messages don't sync: Check database permissions\n\n";

echo "Validation completed at: " . date('Y-m-d H:i:s') . "\n";
?>
