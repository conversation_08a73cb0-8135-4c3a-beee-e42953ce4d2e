---
type: "always_apply"
---

# Copilot & AI Agent Coding Instructions for Campaign Church Project

## Project Overview
- This is a PHP-based web application for church management, running in a XAMPP (Windows) environment.
- Major modules: admin dashboard, user management, events, donations, email/SMS communications, and branding (logo integration).
- Code is organized by feature: `church/admin/` (admin UI), `church/` (public/user), with shared assets and configuration in top-level folders.

## Architecture & Patterns
- **Admin pages** use a consistent layout: always include `includes/header.php` and `includes/footer.php`, and set `$page_title`, `$page_header`, `$page_description` for each page.
- **Bootstrap 5** is the standard for UI components and layout. Use Bootstrap classes and utilities for all new UI work.
- **Profile dropdowns** and navigation follow the pattern in `admin/includes/header.php` (see `ADMIN_HEADER_DROPDOWN_IMPLEMENTATION.md`).
- **Logo management** uses a hierarchy: `header_logo` (primary), `main_logo` (fallback), then SVG asset. See `LOGO_INTEGRATION_COMPLETE.md` for details and code snippets.
- **AJAX** is preferred for form submissions that affect user experience (e.g., birthday emails). See `BIRTHDAY_EMAIL_FIX_COMPLETE.md` for the pattern.
- **Database access**: Use PDO, handle exceptions, and never expose credentials. See `.cursor/rules/terminal-code.mdc` for PowerShell MySQL usage.
- **Testing and validation**: Each major UI/feature change has a dedicated test page or scenario list in its corresponding `*_COMPLETE.md` or `*_SUMMARY.md` file.

## Developer Workflows
- **No build step**: PHP is interpreted; changes are live after file save.
- **Testing**: Manual via browser and dedicated test pages (e.g., `admin/test_header_dropdown.php`).
- **Database**: Use PowerShell with full path to `mysql.exe` for queries. Example:
  ```powershell
  C:\xampp\mysql\bin\mysql.exe -u root churchtest -e "SELECT * FROM tablename;"
  ```
- **Environment separation**: Respect `environment.php` and `config.php` for environment-specific logic.
- **Cleanup**: Remove one-time scripts and debug files after use. See `CLEANUP_COMPLETE.md` for what should/shouldn't be in the repo.

## Conventions & Best Practices
- Prefer simple, non-duplicated solutions. Check for similar code before adding new logic.
- Do not introduce new frameworks or patterns unless necessary; update or remove old code if you do.
- Keep files under 300 lines; refactor if they grow larger.
- Never mock or stub data outside of test code.
- Avoid scripts that are only run once—prefer reusable utilities.
- Use environment variables for secrets; never hardcode credentials.

## Key References
- `church/admin/ADMIN_HEADER_DROPDOWN_IMPLEMENTATION.md` – Admin UI patterns
- `church/LOGO_INTEGRATION_COMPLETE.md` – Logo/branding system
- `church/BIRTHDAY_EMAIL_FIX_COMPLETE.md` – AJAX and UX patterns
- `church/CLEANUP_COMPLETE.md` – Cleanup and deployment standards
- `.cursor/rules/instructions.mdc` – Coding style and refactoring rules
- `.cursor/rules/terminal-code.mdc` – MySQL/PowerShell usage

---

For any new feature or fix, check for an existing pattern in the above files before introducing new approaches. If anything is unclear or missing, please request clarification or examples from the maintainers.
