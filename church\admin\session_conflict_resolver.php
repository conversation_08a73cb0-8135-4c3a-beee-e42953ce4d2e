<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

$event_id = $_GET['event_id'] ?? '';
if (empty($event_id)) {
    header("Location: events.php");
    exit();
}

$message = '';
$error = '';

// Get event details
try {
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$event) {
        header("Location: events.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading event: " . $e->getMessage();
}

// Handle conflict resolution
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'resolve_conflict') {
            $member_id = $_POST['member_id'] ?? '';
            $keep_session = $_POST['keep_session'] ?? '';
            $remove_sessions = $_POST['remove_sessions'] ?? [];
            
            if (empty($member_id) || empty($keep_session)) {
                throw new Exception("Please select which session to keep.");
            }
            
            $pdo->beginTransaction();
            
            // Remove attendance from conflicting sessions
            $removed_count = 0;
            foreach ($remove_sessions as $session_id) {
                if ($session_id != $keep_session) {
                    $stmt = $pdo->prepare("
                        UPDATE session_attendance 
                        SET attendance_status = 'registered', attendance_date = NULL
                        WHERE session_id = ? AND member_id = ?
                    ");
                    $stmt->execute([$session_id, $member_id]);
                    $removed_count += $stmt->rowCount();
                }
            }
            
            $pdo->commit();
            $message = "Conflict resolved! Removed attendance from {$removed_count} conflicting sessions.";
            
        } elseif ($_POST['action'] === 'auto_resolve_conflicts') {
            $resolution_strategy = $_POST['strategy'] ?? 'keep_first';
            
            // Get all conflicts
            $conflicts = $this->detectAllConflicts($event_id);
            
            $pdo->beginTransaction();
            $resolved_count = 0;
            
            foreach ($conflicts as $conflict) {
                $member_id = $conflict['member_id'];
                $conflicting_sessions = $conflict['sessions'];
                
                if (count($conflicting_sessions) < 2) continue;
                
                // Apply resolution strategy
                $keep_session = null;
                switch ($resolution_strategy) {
                    case 'keep_first':
                        // Keep the earliest session
                        usort($conflicting_sessions, function($a, $b) {
                            return strtotime($a['start_datetime']) - strtotime($b['start_datetime']);
                        });
                        $keep_session = $conflicting_sessions[0]['session_id'];
                        break;
                        
                    case 'keep_last':
                        // Keep the latest session
                        usort($conflicting_sessions, function($a, $b) {
                            return strtotime($b['start_datetime']) - strtotime($a['start_datetime']);
                        });
                        $keep_session = $conflicting_sessions[0]['session_id'];
                        break;
                        
                    case 'keep_longest':
                        // Keep the session with longest duration
                        usort($conflicting_sessions, function($a, $b) {
                            $duration_a = strtotime($a['end_datetime']) - strtotime($a['start_datetime']);
                            $duration_b = strtotime($b['end_datetime']) - strtotime($b['start_datetime']);
                            return $duration_b - $duration_a;
                        });
                        $keep_session = $conflicting_sessions[0]['session_id'];
                        break;
                }
                
                // Remove from other sessions
                foreach ($conflicting_sessions as $session) {
                    if ($session['session_id'] != $keep_session) {
                        $stmt = $pdo->prepare("
                            UPDATE session_attendance 
                            SET attendance_status = 'registered', attendance_date = NULL
                            WHERE session_id = ? AND member_id = ?
                        ");
                        $stmt->execute([$session['session_id'], $member_id]);
                    }
                }
                
                $resolved_count++;
            }
            
            $pdo->commit();
            $message = "Auto-resolved {$resolved_count} conflicts using '{$resolution_strategy}' strategy.";
        }
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $error = $e->getMessage();
    }
}

// Function to detect all conflicts
function detectAllConflicts($event_id) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT 
            sa1.member_id,
            m.full_name as member_name,
            m.email as member_email,
            GROUP_CONCAT(
                CONCAT(es1.id, ':', es1.session_title, ':', es1.start_datetime, ':', es1.end_datetime)
                SEPARATOR '|'
            ) as conflicting_sessions
        FROM session_attendance sa1
        JOIN members m ON sa1.member_id = m.id
        JOIN event_sessions es1 ON sa1.session_id = es1.id
        JOIN session_attendance sa2 ON sa1.member_id = sa2.member_id AND sa1.id != sa2.id
        JOIN event_sessions es2 ON sa2.session_id = es2.id
        WHERE es1.event_id = ? 
        AND es2.event_id = ?
        AND sa1.attendance_status = 'attended'
        AND sa2.attendance_status = 'attended'
        AND (
            (es1.start_datetime <= es2.end_datetime AND es1.end_datetime >= es2.start_datetime)
        )
        GROUP BY sa1.member_id, m.full_name, m.email
        HAVING COUNT(DISTINCT sa1.session_id) > 1
    ");
    $stmt->execute([$event_id, $event_id]);
    $raw_conflicts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $conflicts = [];
    foreach ($raw_conflicts as $conflict) {
        $sessions = [];
        $session_data = explode('|', $conflict['conflicting_sessions']);
        
        foreach ($session_data as $session_info) {
            $parts = explode(':', $session_info, 4);
            if (count($parts) >= 4) {
                $sessions[] = [
                    'session_id' => $parts[0],
                    'session_title' => $parts[1],
                    'start_datetime' => $parts[2],
                    'end_datetime' => $parts[3]
                ];
            }
        }
        
        $conflicts[] = [
            'member_id' => $conflict['member_id'],
            'member_name' => $conflict['member_name'],
            'member_email' => $conflict['member_email'],
            'sessions' => $sessions
        ];
    }
    
    return $conflicts;
}

// Get all conflicts for this event
$conflicts = detectAllConflicts($event_id);

// Get session statistics
$stmt = $pdo->prepare("
    SELECT 
        es.*,
        COUNT(sa.id) as total_registered,
        COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as total_attended
    FROM event_sessions es
    LEFT JOIN session_attendance sa ON es.id = sa.session_id
    WHERE es.event_id = ? AND es.status = 'active'
    GROUP BY es.id
    ORDER BY es.start_datetime
");
$stmt->execute([$event_id]);
$sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Page title and header info
$page_title = 'Session Conflict Resolver';
$page_header = 'Session Conflict Resolver';
$page_description = 'Detect and resolve scheduling conflicts in session attendance';

// Include header
include 'includes/header.php';
?>

<style>
.conflict-card {
    border: 2px solid #dc3545;
    border-radius: 10px;
    background-color: #fff5f5;
}
.session-conflict {
    border: 1px solid #ffc107;
    border-radius: 5px;
    background-color: #fffbf0;
    margin: 5px 0;
    padding: 10px;
}
.resolution-options {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}
.conflict-timeline {
    position: relative;
    padding-left: 30px;
}
.conflict-timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #dc3545;
}
.timeline-item {
    position: relative;
    margin-bottom: 15px;
}
.timeline-item::before {
    content: '';
    position: absolute;
    left: -23px;
    top: 5px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #dc3545;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <p class="text-muted mb-0">Event: <strong><?php echo htmlspecialchars($event['title']); ?></strong></p>
                <small class="text-muted"><?php echo date('F j, Y g:i A', strtotime($event['event_date'])); ?> • <?php echo htmlspecialchars($event['location']); ?></small>
            </div>
            <div>
                <a href="event_attendance_detail.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left"></i> Back to Event
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Conflict Summary -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h3 class="text-danger"><?php echo count($conflicts); ?></h3>
                        <small class="text-muted">Conflicts Detected</small>
                    </div>
                    <div class="col-md-3">
                        <h3 class="text-primary"><?php echo count($sessions); ?></h3>
                        <small class="text-muted">Total Sessions</small>
                    </div>
                    <div class="col-md-3">
                        <h3 class="text-success"><?php echo array_sum(array_column($sessions, 'total_attended')); ?></h3>
                        <small class="text-muted">Total Attendance</small>
                    </div>
                    <div class="col-md-3">
                        <h3 class="text-info"><?php echo array_sum(array_column($sessions, 'total_registered')); ?></h3>
                        <small class="text-muted">Total Registered</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (empty($conflicts)): ?>
    <!-- No Conflicts -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="bi bi-check-circle text-success" style="font-size: 4rem;"></i>
                    <h3 class="mt-3 text-success">No Conflicts Detected</h3>
                    <p class="lead text-muted">All session attendance is properly scheduled without conflicts.</p>
                </div>
            </div>
        </div>
    </div>
<?php else: ?>
    <!-- Auto-Resolution Options -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-magic"></i> Auto-Resolution</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">Automatically resolve all conflicts using a predefined strategy:</p>
                    
                    <form method="POST" onsubmit="return confirm('This will automatically resolve all conflicts. Continue?')">
                        <input type="hidden" name="action" value="auto_resolve_conflicts">
                        
                        <div class="row align-items-end">
                            <div class="col-md-6">
                                <label class="form-label">Resolution Strategy</label>
                                <select class="form-select" name="strategy" required>
                                    <option value="">Select Strategy</option>
                                    <option value="keep_first">Keep Earliest Session</option>
                                    <option value="keep_last">Keep Latest Session</option>
                                    <option value="keep_longest">Keep Longest Duration Session</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-warning">
                                    <i class="bi bi-lightning"></i> Auto-Resolve All Conflicts
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Individual Conflicts -->
    <div class="row">
        <?php foreach ($conflicts as $index => $conflict): ?>
            <div class="col-md-6 mb-4">
                <div class="card conflict-card">
                    <div class="card-header">
                        <h6 class="mb-0 text-danger">
                            <i class="bi bi-person-exclamation"></i> 
                            <?php echo htmlspecialchars($conflict['member_name']); ?>
                        </h6>
                        <small class="text-muted"><?php echo htmlspecialchars($conflict['member_email']); ?></small>
                    </div>
                    
                    <div class="card-body">
                        <h6 class="text-danger mb-3">Conflicting Sessions:</h6>
                        
                        <!-- Conflict Timeline -->
                        <div class="conflict-timeline">
                            <?php foreach ($conflict['sessions'] as $session): ?>
                                <div class="timeline-item">
                                    <div class="session-conflict">
                                        <strong><?php echo htmlspecialchars($session['session_title']); ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <i class="bi bi-clock"></i> 
                                            <?php echo date('M j, g:i A', strtotime($session['start_datetime'])); ?> - 
                                            <?php echo date('g:i A', strtotime($session['end_datetime'])); ?>
                                        </small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <!-- Resolution Form -->
                        <div class="resolution-options">
                            <h6><i class="bi bi-tools"></i> Resolve Conflict</h6>
                            <form method="POST">
                                <input type="hidden" name="action" value="resolve_conflict">
                                <input type="hidden" name="member_id" value="<?php echo $conflict['member_id']; ?>">
                                
                                <?php foreach ($conflict['sessions'] as $session): ?>
                                    <input type="hidden" name="remove_sessions[]" value="<?php echo $session['session_id']; ?>">
                                <?php endforeach; ?>
                                
                                <div class="mb-3">
                                    <label class="form-label">Keep which session?</label>
                                    <?php foreach ($conflict['sessions'] as $session): ?>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" 
                                                   name="keep_session" value="<?php echo $session['session_id']; ?>"
                                                   id="keep_<?php echo $conflict['member_id']; ?>_<?php echo $session['session_id']; ?>">
                                            <label class="form-check-label" 
                                                   for="keep_<?php echo $conflict['member_id']; ?>_<?php echo $session['session_id']; ?>">
                                                <strong><?php echo htmlspecialchars($session['session_title']); ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo date('M j, g:i A', strtotime($session['start_datetime'])); ?>
                                                </small>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                
                                <button type="submit" class="btn btn-danger btn-sm">
                                    <i class="bi bi-check-circle"></i> Resolve Conflict
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<?php include 'includes/footer.php'; ?>
