<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

$event_id = $_GET['event_id'] ?? '';
if (empty($event_id)) {
    header("Location: events.php");
    exit();
}

$message = '';
$error = '';

// Get event details
try {
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$event) {
        header("Location: events.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading event: " . $e->getMessage();
}

// Handle QR code generation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'generate_session_qr') {
            $session_id = $_POST['session_id'] ?? '';
            
            if (empty($session_id)) {
                throw new Exception("Please select a session.");
            }
            
            // Generate unique token for this session
            $qr_token = bin2hex(random_bytes(16));
            $expires_at = date('Y-m-d H:i:s', strtotime('+24 hours'));
            
            // Store QR token in database
            $stmt = $pdo->prepare("
                INSERT INTO session_qr_codes (session_id, qr_token, expires_at, created_by, created_at)
                VALUES (?, ?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE
                qr_token = VALUES(qr_token),
                expires_at = VALUES(expires_at),
                updated_at = NOW()
            ");
            $stmt->execute([$session_id, $qr_token, $expires_at, $_SESSION['admin_id']]);
            
            $message = "QR code generated successfully for session!";
            
        } elseif ($_POST['action'] === 'generate_all_qr') {
            // Generate QR codes for all sessions in this event
            $stmt = $pdo->prepare("SELECT id FROM event_sessions WHERE event_id = ? AND status = 'active'");
            $stmt->execute([$event_id]);
            $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $generated_count = 0;
            foreach ($sessions as $session) {
                $qr_token = bin2hex(random_bytes(16));
                $expires_at = date('Y-m-d H:i:s', strtotime('+24 hours'));
                
                $stmt = $pdo->prepare("
                    INSERT INTO session_qr_codes (session_id, qr_token, expires_at, created_by, created_at)
                    VALUES (?, ?, ?, ?, NOW())
                    ON DUPLICATE KEY UPDATE
                    qr_token = VALUES(qr_token),
                    expires_at = VALUES(expires_at),
                    updated_at = NOW()
                ");
                $stmt->execute([$session['id'], $qr_token, $expires_at, $_SESSION['admin_id']]);
                $generated_count++;
            }
            
            $message = "Generated QR codes for {$generated_count} sessions!";
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Create session_qr_codes table if it doesn't exist
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS session_qr_codes (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            session_id INT(11) NOT NULL,
            qr_token VARCHAR(255) NOT NULL UNIQUE,
            expires_at DATETIME NOT NULL,
            is_active TINYINT(1) DEFAULT 1,
            scan_count INT(11) DEFAULT 0,
            created_by INT(11) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_session_id (session_id),
            INDEX idx_qr_token (qr_token),
            INDEX idx_expires_at (expires_at),
            UNIQUE KEY unique_session_qr (session_id),
            FOREIGN KEY (session_id) REFERENCES event_sessions(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
} catch (PDOException $e) {
    error_log("Error creating session_qr_codes table: " . $e->getMessage());
}

// Get all sessions for this event with QR code status
$stmt = $pdo->prepare("
    SELECT s.*,
           COUNT(sa.id) as registered_count,
           COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as attended_count,
           qr.qr_token,
           qr.expires_at as qr_expires,
           qr.is_active as qr_active,
           qr.scan_count,
           qr.created_at as qr_created
    FROM event_sessions s
    LEFT JOIN session_attendance sa ON s.id = sa.session_id
    LEFT JOIN session_qr_codes qr ON s.id = qr.session_id
    WHERE s.event_id = ? AND s.status = 'active'
    GROUP BY s.id
    ORDER BY s.start_datetime
");
$stmt->execute([$event_id]);
$sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Remove debug output for production

// Page title and header info
$page_title = 'Session QR Code Generator';
$page_header = 'Session QR Code Generator';
$page_description = 'Generate QR codes for mobile check-in at session locations';

// Include header
include 'includes/header.php';
?>

<style>
.qr-card {
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
    border-radius: 10px;
}
.qr-card.has-qr {
    border-color: #28a745;
    background-color: #f8fff9;
}
.qr-card.expired {
    border-color: #dc3545;
    background-color: #fff5f5;
}
.qr-display {
    background: white;
    border: 2px solid #28a745;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
}
.session-status {
    font-size: 0.75rem;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <p class="text-muted mb-0">Event: <strong><?php echo htmlspecialchars($event['title']); ?></strong></p>
                <small class="text-muted"><?php echo date('F j, Y g:i A', strtotime($event['event_date'])); ?> • <?php echo htmlspecialchars($event['location']); ?></small>
            </div>
            <div>
                <a href="staff_qr_scanner.php" class="btn btn-success me-2">
                    <i class="bi bi-camera-fill"></i> Staff QR Scanner
                </a>
                <a href="event_attendance_detail.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left"></i> Back to Event
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0"><i class="bi bi-lightning"></i> Quick Actions</h5>
                    </div>
                    <div>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="generate_all_qr">
                            <button type="submit" class="btn btn-success" 
                                    onclick="return confirm('Generate QR codes for all sessions in this event?')">
                                <i class="bi bi-qr-code-scan"></i> Generate All QR Codes
                            </button>
                        </form>
                        <a href="mobile_checkin.php?event_id=<?php echo $event_id; ?>" class="btn btn-primary ms-2">
                            <i class="bi bi-phone"></i> Mobile Check-in
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sessions List -->
<div class="row">
    <?php foreach ($sessions as $session):
        $has_qr = !empty($session['qr_token']);
        $is_active = !empty($session['qr_active']);
        $qr_expired = $has_qr && (!$is_active || strtotime($session['qr_expires']) < time());
        $qr_url = $has_qr ? "mobile_checkin.php?token=" . $session['qr_token'] : '';
    ?>
        <div class="col-md-6 mb-4">
            <div class="card qr-card <?php echo $has_qr ? ($qr_expired ? 'expired' : 'has-qr') : ''; ?>">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0"><?php echo htmlspecialchars($session['session_title']); ?></h6>
                            <small class="text-muted">
                                <?php echo date('M j, Y g:i A', strtotime($session['start_datetime'])); ?>
                                <?php if ($session['location']): ?>
                                    • <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($session['location']); ?>
                                <?php endif; ?>
                            </small>
                        </div>
                        <div class="text-end">
                            <?php if ($has_qr): ?>
                                <span class="badge bg-<?php echo $qr_expired ? 'danger' : 'success'; ?> session-status">
                                    <?php echo $qr_expired ? 'Expired' : 'Active'; ?>
                                </span>
                            <?php else: ?>
                                <span class="badge bg-secondary session-status">No QR Code</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Session Stats -->
                    <div class="row mb-3">
                        <div class="col-4 text-center">
                            <h6 class="text-primary mb-0"><?php echo $session['registered_count']; ?></h6>
                            <small class="text-muted">Registered</small>
                        </div>
                        <div class="col-4 text-center">
                            <h6 class="text-success mb-0"><?php echo $session['attended_count']; ?></h6>
                            <small class="text-muted">Attended</small>
                        </div>
                        <div class="col-4 text-center">
                            <h6 class="text-info mb-0"><?php echo $session['scan_count'] ?? 0; ?></h6>
                            <small class="text-muted">QR Scans</small>
                        </div>
                    </div>
                    
                    <?php if ($has_qr): ?>
                        <!-- QR Code Display -->
                        <div class="qr-display mb-3 text-center">
                            <?php if ($is_active && !$qr_expired): ?>
                                <?php
                                $qr_url = "mobile_checkin.php?token=" . $session['qr_token'];
                                $full_qr_url = "http://localhost/campaign/church/" . $qr_url;
                                ?>
                                <div class="qr-container">
                                    <!-- Display QR URL as text with copy button -->
                                    <div class="alert alert-info">
                                        <h6><i class="bi bi-qr-code"></i> Mobile Check-in</h6>
                                        <p class="mb-2"><strong>Check-in URL:</strong></p>
                                        <div class="input-group mb-2">
                                            <input type="text" class="form-control" value="<?php echo $full_qr_url; ?>" readonly id="url-<?php echo $session['id']; ?>">
                                            <button class="btn btn-outline-primary" onclick="copyToClipboard('url-<?php echo $session['id']; ?>')">
                                                <i class="bi bi-copy"></i> Copy
                                            </button>
                                        </div>
                                        <small class="text-muted">Share this URL or scan the QR code below</small>
                                    </div>

                                    <!-- Simple QR Code using QR Server API -->
                                    <div class="text-center">
                                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=<?php echo urlencode($full_qr_url); ?>"
                                             alt="QR Code for <?php echo htmlspecialchars($session['session_title']); ?>"
                                             style="border: 2px solid #28a745; border-radius: 8px;"
                                             onerror="this.style.display='none'; document.getElementById('qr-fallback-<?php echo $session['id']; ?>').style.display='block';">

                                        <!-- Fallback if QR image fails -->
                                        <div id="qr-fallback-<?php echo $session['id']; ?>" style="display: none;" class="alert alert-warning">
                                            <i class="bi bi-exclamation-triangle"></i> QR Code unavailable<br>
                                            <small>Use the URL above for mobile check-in</small>
                                        </div>
                                    </div>
                                </div>
                                <small class="text-muted d-block mt-2">
                                    Expires: <?php echo date('M j, Y g:i A', strtotime($session['qr_expires'])); ?>
                                </small>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    QR Code is <?php echo !$is_active ? 'inactive' : 'expired'; ?>
                                    <br><small>Please regenerate to create a new QR code</small>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- QR Actions -->
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="copyQRUrl('<?php echo $qr_url; ?>')">
                                <i class="bi bi-copy"></i> Copy Check-in URL
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="printQR(<?php echo $session['id']; ?>)">
                                <i class="bi bi-printer"></i> Print QR Code
                            </button>
                        </div>
                        
                    <?php else: ?>
                        <!-- Generate QR Code -->
                        <div class="text-center">
                            <?php if ($qr_expired): ?>
                                <p class="text-danger mb-3">
                                    <i class="bi bi-exclamation-triangle"></i> QR Code has expired
                                </p>
                            <?php endif; ?>
                            
                            <form method="POST">
                                <input type="hidden" name="action" value="generate_session_qr">
                                <input type="hidden" name="session_id" value="<?php echo $session['id']; ?>">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-qr-code"></i> 
                                    <?php echo $has_qr ? 'Regenerate QR Code' : 'Generate QR Code'; ?>
                                </button>
                            </form>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<!-- Bootstrap -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="../print-qr.js"></script>

<script>
// Simple utility functions for QR actions
// Copy function removed - using copyQRUrl instead

function copyQRUrl(url) {
    const fullUrl = window.location.origin + window.location.pathname.replace('session_qr_generator.php', url);
    navigator.clipboard.writeText(fullUrl).then(function() {
        alert('Check-in URL copied to clipboard!');
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        prompt('Copy this URL:', fullUrl);
    });
}

// Print QR function is now loaded from external file
</script>

<?php include 'includes/footer.php'; ?>
