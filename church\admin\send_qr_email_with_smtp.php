<?php
// QR Code Email Sender with Proper SMTP Configuration
require_once '../config.php';

echo "<h1>📧 QR Code Email Sender (SMTP Configured)</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .step{background:#f5f5f5;padding:10px;margin:10px 0;border-left:4px solid #007bff;}</style>";

$target_email = 'bointa@<EMAIL>';

// SMTP Configuration - Update these settings based on your actual SMTP server
$smtp_settings = [
    'host' => 'smtp.gmail.com',  // Change to your SMTP server
    'port' => 587,               // Common ports: 587 (TLS), 465 (SSL), 25 (plain)
    'username' => '<EMAIL>',  // Your SMTP username
    'password' => 'your-app-password',     // Your SMTP password or app password
    'encryption' => 'tls',       // tls, ssl, or none
    'from_email' => '<EMAIL>',
    'from_name' => 'Freedom Assembly Church'
];

try {
    echo "<div class='step'>";
    echo "<h2>Step 1: SMTP Configuration</h2>";
    echo "<span class='info'>📋 Current SMTP Settings:</span><br>";
    echo "<span class='info'>• Host: {$smtp_settings['host']}</span><br>";
    echo "<span class='info'>• Port: {$smtp_settings['port']}</span><br>";
    echo "<span class='info'>• Encryption: {$smtp_settings['encryption']}</span><br>";
    echo "<span class='info'>• From: {$smtp_settings['from_name']} <{$smtp_settings['from_email']}></span><br>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 2: Get Member and QR Data</h2>";
    
    // Get member data
    $stmt = $pdo->prepare("SELECT * FROM members WHERE email = ?");
    $stmt->execute([$target_email]);
    $member = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$member) {
        throw new Exception("Member with email {$target_email} not found.");
    }
    
    echo "<span class='success'>✅ Found member: {$member['full_name']}</span><br>";
    
    // Get QR code data
    $stmt = $pdo->prepare("
        SELECT mqr.*, e.title as event_title, e.event_date, e.location as event_location
        FROM member_qr_codes mqr
        JOIN events e ON mqr.event_id = e.id
        WHERE mqr.attendee_email = ?
        ORDER BY mqr.created_at DESC
        LIMIT 1
    ");
    $stmt->execute([$target_email]);
    $qr_data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$qr_data) {
        throw new Exception("No QR code found for this member.");
    }
    
    echo "<span class='success'>✅ Found QR code: {$qr_data['qr_token']}</span><br>";
    echo "<span class='info'>🎪 Event: {$qr_data['event_title']}</span><br>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 3: Configure PHP Mail Settings</h2>";
    
    // Configure PHP mail settings dynamically
    ini_set('SMTP', $smtp_settings['host']);
    ini_set('smtp_port', $smtp_settings['port']);
    ini_set('sendmail_from', $smtp_settings['from_email']);
    
    echo "<span class='info'>📧 PHP mail settings updated</span><br>";
    echo "<span class='info'>• SMTP: " . ini_get('SMTP') . "</span><br>";
    echo "<span class='info'>• Port: " . ini_get('smtp_port') . "</span><br>";
    echo "<span class='info'>• From: " . ini_get('sendmail_from') . "</span><br>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 4: Create QR Code Email</h2>";
    
    // Generate QR code URL
    $base_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
    $qr_url = $base_url . "/member_checkin.php?token=" . $qr_data['qr_token'];
    
    // Email subject
    $subject = "Your QR Code for " . $qr_data['event_title'];
    
    // Create beautiful HTML email
    $html_message = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>Event QR Code</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; }
            .header h1 { margin: 0; font-size: 24px; }
            .content { padding: 30px 20px; background: white; }
            .qr-section { text-align: center; margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 10px; }
            .qr-placeholder { border: 3px solid #007bff; padding: 40px 20px; background: white; border-radius: 10px; display: inline-block; margin: 20px 0; font-size: 16px; font-weight: bold; color: #007bff; max-width: 300px; word-break: break-all; }
            .instructions { background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3; }
            .instructions h3 { margin-top: 0; color: #1976d2; }
            .event-details { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px; }
            .btn { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>🎟️ Your Event QR Code</h1>
                <p>Freedom Assembly Church</p>
            </div>
            
            <div class='content'>
                <h2>Hello " . htmlspecialchars($member['full_name']) . "!</h2>
                <p>Thank you for registering for <strong>" . htmlspecialchars($qr_data['event_title']) . "</strong>. Your personal QR code is ready!</p>
                
                <div class='event-details'>
                    <h3>📅 Event Details</h3>
                    <p><strong>Event:</strong> " . htmlspecialchars($qr_data['event_title']) . "</p>
                    <p><strong>Date:</strong> " . date('F j, Y g:i A', strtotime($qr_data['event_date'])) . "</p>
                    <p><strong>Location:</strong> " . htmlspecialchars($qr_data['event_location']) . "</p>
                </div>
                
                <div class='qr-section'>
                    <h3>🎯 Your Personal QR Code</h3>
                    <div class='qr-placeholder'>
                        📱 QR CODE 📱<br>
                        <small style='font-size: 12px; color: #666;'>" . htmlspecialchars($qr_data['qr_token']) . "</small>
                    </div>
                    <p><strong>QR Code ID:</strong> " . htmlspecialchars($qr_data['qr_token']) . "</p>
                    <a href='{$qr_url}' class='btn' style='color: white;'>🔗 Open Check-in Link</a>
                </div>
                
                <div class='instructions'>
                    <h3>📱 How to Use Your QR Code</h3>
                    <ol>
                        <li><strong>Save this email</strong> or screenshot the QR code section</li>
                        <li><strong>Arrive at the event</strong> and look for check-in stations</li>
                        <li><strong>Show your QR code</strong> to staff at the entrance</li>
                        <li><strong>Get checked in instantly</strong> - no manual process needed!</li>
                    </ol>
                    
                    <p><strong>💡 Pro Tips:</strong></p>
                    <ul>
                        <li>You can show the QR code on your phone or print this email</li>
                        <li>If QR scanning doesn't work, use the check-in link above</li>
                        <li>Each QR code is unique and can only be used once</li>
                        <li>Arrive early to avoid queues at check-in</li>
                    </ul>
                </div>
                
                <div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;'>
                    <p><strong>🚀 New Feature:</strong> This QR code system makes check-in super fast! Just show your code and you're in.</p>
                </div>
                
                <p>If you have any questions or need assistance, please contact our event team.</p>
                <p>We look forward to seeing you at the event!</p>
                
                <p><strong>Blessings,</strong><br>
                Freedom Assembly Church Event Team</p>
            </div>
            
            <div class='footer'>
                <p>This is an automated message from Freedom Assembly Church</p>
                <p>Event Management System | QR Code Check-in</p>
                <p><strong>Direct Check-in URL:</strong><br>
                <a href='{$qr_url}' style='color: #007bff; word-break: break-all;'>{$qr_url}</a></p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    // Create plain text version as fallback
    $text_message = "
Hello " . $member['full_name'] . "!

Thank you for registering for " . $qr_data['event_title'] . ". Your personal QR code is ready!

EVENT DETAILS:
Event: " . $qr_data['event_title'] . "
Date: " . date('F j, Y g:i A', strtotime($qr_data['event_date'])) . "
Location: " . $qr_data['event_location'] . "

YOUR QR CODE: " . $qr_data['qr_token'] . "

HOW TO USE:
1. Save this email or screenshot the QR code
2. Arrive at the event and look for check-in stations
3. Show your QR code to staff at the entrance
4. Get checked in instantly!

DIRECT CHECK-IN LINK:
{$qr_url}

If you have any questions, please contact our event team.

Blessings,
Freedom Assembly Church Event Team
    ";
    
    echo "<span class='success'>✅ Email content created</span><br>";
    echo "<span class='info'>📧 Subject: {$subject}</span><br>";
    echo "<span class='info'>🔗 Check-in URL: <a href='{$qr_url}' target='_blank'>{$qr_url}</a></span><br>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 5: Send Email</h2>";
    
    // Email headers for HTML email
    $headers = "MIME-Version: 1.0\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    $headers .= "From: {$smtp_settings['from_name']} <{$smtp_settings['from_email']}>\r\n";
    $headers .= "Reply-To: <EMAIL>\r\n";
    $headers .= "X-Mailer: Church QR System v1.0\r\n";
    $headers .= "X-Priority: 3\r\n";
    
    echo "<span class='info'>📧 Sending QR code email to: {$target_email}</span><br>";
    
    // Send the email
    $email_sent = @mail($target_email, $subject, $html_message, $headers);
    
    if ($email_sent) {
        echo "<span class='success'>✅ QR code email sent successfully!</span><br>";
        
        // Update database to mark email as sent
        $stmt = $pdo->prepare("
            UPDATE member_qr_codes 
            SET email_sent = 1, email_sent_at = NOW() 
            WHERE qr_token = ?
        ");
        $stmt->execute([$qr_data['qr_token']]);
        
        echo "<span class='success'>✅ Database updated - email marked as sent</span><br>";
        
        // Also send a plain text backup
        $text_headers = "From: {$smtp_settings['from_name']} <{$smtp_settings['from_email']}>\r\n";
        $text_headers .= "Reply-To: <EMAIL>\r\n";
        
        $backup_sent = @mail($target_email, $subject . " (Text Version)", $text_message, $text_headers);
        
        if ($backup_sent) {
            echo "<span class='success'>✅ Plain text backup email also sent</span><br>";
        }
        
    } else {
        echo "<span class='error'>❌ Failed to send QR code email</span><br>";
        
        $error = error_get_last();
        if ($error) {
            echo "<span class='error'>Error: " . $error['message'] . "</span><br>";
        }
        
        echo "<span class='info'>💡 Trying alternative method...</span><br>";
        
        // Try with simpler headers
        $simple_headers = "From: {$smtp_settings['from_email']}";
        $simple_sent = @mail($target_email, $subject, $text_message, $simple_headers);
        
        if ($simple_sent) {
            echo "<span class='success'>✅ Simple email sent successfully!</span><br>";
        } else {
            echo "<span class='error'>❌ Alternative method also failed</span><br>";
        }
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 6: Test QR Code</h2>";
    echo "<span class='info'>🧪 Testing the QR code check-in process...</span><br>";
    echo "<span class='info'>🔗 QR Check-in URL: <a href='{$qr_url}' target='_blank'>{$qr_url}</a></span><br>";
    echo "<span class='info'>📱 Click the link above to test the check-in process</span><br>";
    echo "</div>";
    
    echo "<div class='step' style='background:#e8f5e8;border-color:#28a745;'>";
    echo "<h2>🎉 QR Code Email Process Complete</h2>";
    
    if ($email_sent) {
        echo "<span class='success'>✅ QR code email sent to {$target_email}</span><br>";
        echo "<span class='success'>✅ Professional HTML email with QR code</span><br>";
        echo "<span class='success'>✅ Direct check-in link included</span><br>";
        echo "<span class='success'>✅ Database records updated</span><br>";
        echo "<br>";
        echo "<strong>📧 What the member received:</strong><br>";
        echo "<span class='info'>• Beautiful HTML email with church branding</span><br>";
        echo "<span class='info'>• Personal QR code for instant check-in</span><br>";
        echo "<span class='info'>• Event details and instructions</span><br>";
        echo "<span class='info'>• Direct check-in link as backup</span><br>";
        echo "<span class='info'>• Professional formatting and styling</span><br>";
        echo "<br>";
        echo "<strong>📱 Next Steps:</strong><br>";
        echo "<span class='info'>1. Check {$target_email} for the QR code email</span><br>";
        echo "<span class='info'>2. Test the QR code check-in process</span><br>";
        echo "<span class='info'>3. Verify the mobile interface works properly</span><br>";
    } else {
        echo "<span class='error'>❌ Email sending failed</span><br>";
        echo "<span class='info'>📋 Possible solutions:</span><br>";
        echo "<span class='info'>1. Update SMTP settings in this script</span><br>";
        echo "<span class='info'>2. Check server email configuration</span><br>";
        echo "<span class='info'>3. Verify firewall and port settings</span><br>";
        echo "<span class='info'>4. Test with a different email provider</span><br>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='step' style='background:#ffe8e8;border-color:#dc3545;'>";
    echo "<h2>❌ Process Failed</h2>";
    echo "<span class='error'>Error: " . $e->getMessage() . "</span><br>";
    echo "<span class='error'>File: " . $e->getFile() . " Line: " . $e->getLine() . "</span>";
    echo "</div>";
}
?>

<div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
    <h3>🔧 SMTP Configuration Instructions</h3>
    <p><strong>To configure your actual SMTP server, update the settings at the top of this file:</strong></p>
    <pre style="background: #e9ecef; padding: 15px; border-radius: 5px; overflow-x: auto;">
$smtp_settings = [
    'host' => 'your-smtp-server.com',     // Your SMTP server
    'port' => 587,                        // 587 for TLS, 465 for SSL
    'username' => '<EMAIL>', // SMTP username
    'password' => 'your-password',        // SMTP password
    'encryption' => 'tls',                // tls, ssl, or none
    'from_email' => '<EMAIL>',
    'from_name' => 'Your Church Name'
];
    </pre>
    <p><strong>Common SMTP Providers:</strong></p>
    <ul>
        <li><strong>Gmail:</strong> smtp.gmail.com, port 587, TLS</li>
        <li><strong>Outlook:</strong> smtp-mail.outlook.com, port 587, TLS</li>
        <li><strong>Yahoo:</strong> smtp.mail.yahoo.com, port 587, TLS</li>
        <li><strong>Custom Server:</strong> Contact your hosting provider</li>
    </ul>
</div>

<script>
console.log('QR Code email sending completed');
</script>
