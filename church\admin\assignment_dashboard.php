<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Get user's assignments
$user_id = $_SESSION['admin_id'];
$session_assignments = [];
$event_assignments = [];

try {
    // Get session assignments
    $stmt = $pdo->prepare("
        SELECT sa.id, sa.session_id, es.session_title, e.title as event_title,
               es.start_datetime, es.end_datetime, e.location,
               CASE 
                   WHEN es.end_datetime < NOW() THEN 'completed'
                   WHEN es.start_datetime <= NOW() AND es.end_datetime >= NOW() THEN 'active'
                   ELSE 'upcoming'
               END as status
        FROM session_assignments sa
        JOIN event_sessions es ON sa.session_id = es.id
        JOIN events e ON es.event_id = e.id
        WHERE sa.user_id = ? AND sa.is_active = 1
        ORDER BY es.start_datetime ASC
    ");
    $stmt->execute([$user_id]);
    $session_assignments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get event assignments
    $stmt = $pdo->prepare("
        SELECT ea.id, ea.event_id, ea.role_type, e.title as event_title,
               e.event_date, e.location, e.description,
               CASE 
                   WHEN e.event_date < CURDATE() THEN 'completed'
                   WHEN e.event_date = CURDATE() THEN 'today'
                   ELSE 'upcoming'
               END as status
        FROM event_assignments ea
        JOIN events e ON ea.event_id = e.id
        WHERE ea.user_id = ? AND ea.is_active = 1
        ORDER BY e.event_date ASC
    ");
    $stmt->execute([$user_id]);
    $event_assignments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    // Tables might not exist
}

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="bi bi-person-workspace"></i> My Assignments</h2>
                    <p class="text-muted">Welcome, <?php echo htmlspecialchars($_SESSION['admin_username']); ?>! Here are your assigned sessions and events.</p>
                </div>
                <div>
                    <a href="dashboard.php" class="btn btn-outline-primary">
                        <i class="bi bi-house"></i> Main Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if (empty($session_assignments) && empty($event_assignments)): ?>
        <div class="row">
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <i class="bi bi-info-circle fs-1 d-block mb-3"></i>
                    <h4>No Assignments Found</h4>
                    <p>You don't have any session or event assignments yet. Contact your administrator to get assigned to sessions or events.</p>
                    <a href="dashboard.php" class="btn btn-primary">
                        <i class="bi bi-house"></i> Go to Main Dashboard
                    </a>
                </div>
            </div>
        </div>
    <?php else: ?>
        
        <!-- Session Assignments -->
        <?php if (!empty($session_assignments)): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-calendar-event"></i> My Session Assignments
                            <span class="badge bg-primary ms-2"><?php echo count($session_assignments); ?></span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($session_assignments as $session): ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card h-100 border-start border-4 
                                        <?php echo $session['status'] === 'active' ? 'border-success' : 
                                                   ($session['status'] === 'upcoming' ? 'border-primary' : 'border-secondary'); ?>">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0"><?php echo htmlspecialchars($session['session_title']); ?></h6>
                                                <span class="badge 
                                                    <?php echo $session['status'] === 'active' ? 'bg-success' : 
                                                               ($session['status'] === 'upcoming' ? 'bg-primary' : 'bg-secondary'); ?>">
                                                    <?php echo ucfirst($session['status']); ?>
                                                </span>
                                            </div>
                                            <p class="text-muted small mb-2">
                                                <i class="bi bi-calendar-check"></i> <?php echo htmlspecialchars($session['event_title']); ?>
                                            </p>
                                            <p class="small mb-2">
                                                <i class="bi bi-clock"></i> 
                                                <?php echo date('M j, Y g:i A', strtotime($session['start_datetime'])); ?>
                                                <?php if ($session['end_datetime']): ?>
                                                    - <?php echo date('g:i A', strtotime($session['end_datetime'])); ?>
                                                <?php endif; ?>
                                            </p>
                                            <?php if ($session['location']): ?>
                                                <p class="small mb-2">
                                                    <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($session['location']); ?>
                                                </p>
                                            <?php endif; ?>
                                            <div class="mt-3">
                                                <a href="session_attendance.php?session_id=<?php echo $session['session_id']; ?>"
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-eye"></i> View Session
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Event Assignments -->
        <?php if (!empty($event_assignments)): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-calendar-check"></i> My Event Assignments
                            <span class="badge bg-success ms-2"><?php echo count($event_assignments); ?></span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($event_assignments as $event): ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card h-100 border-start border-4 
                                        <?php echo $event['status'] === 'today' ? 'border-warning' : 
                                                   ($event['status'] === 'upcoming' ? 'border-success' : 'border-secondary'); ?>">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0"><?php echo htmlspecialchars($event['event_title']); ?></h6>
                                                <span class="badge 
                                                    <?php echo $event['status'] === 'today' ? 'bg-warning' : 
                                                               ($event['status'] === 'upcoming' ? 'bg-success' : 'bg-secondary'); ?>">
                                                    <?php echo ucfirst($event['status']); ?>
                                                </span>
                                            </div>
                                            <p class="text-muted small mb-2">
                                                <i class="bi bi-person-badge"></i> 
                                                <?php echo ucfirst($event['role_type']); ?>
                                            </p>
                                            <p class="small mb-2">
                                                <i class="bi bi-calendar"></i> 
                                                <?php echo date('M j, Y', strtotime($event['event_date'])); ?>
                                            </p>
                                            <?php if ($event['location']): ?>
                                                <p class="small mb-2">
                                                    <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($event['location']); ?>
                                                </p>
                                            <?php endif; ?>
                                            <?php if ($event['description']): ?>
                                                <p class="small text-muted mb-2">
                                                    <?php echo htmlspecialchars(substr($event['description'], 0, 100)); ?>
                                                    <?php if (strlen($event['description']) > 100): ?>...<?php endif; ?>
                                                </p>
                                            <?php endif; ?>
                                            <div class="mt-3">
                                                <a href="event_attendance_detail.php?event_id=<?php echo $event['event_id']; ?>"
                                                   class="btn btn-sm btn-outline-success">
                                                    <i class="bi bi-eye"></i> View Event
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-lightning"></i> Quick Actions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="dashboard.php" class="btn btn-outline-primary w-100">
                                    <i class="bi bi-house"></i> Main Dashboard
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="profile.php" class="btn btn-outline-info w-100">
                                    <i class="bi bi-person"></i> My Profile
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="notifications.php" class="btn btn-outline-warning w-100">
                                    <i class="bi bi-bell"></i> Notifications
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="logout.php" class="btn btn-outline-danger w-100">
                                    <i class="bi bi-box-arrow-right"></i> Logout
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php include 'includes/footer.php'; ?>
