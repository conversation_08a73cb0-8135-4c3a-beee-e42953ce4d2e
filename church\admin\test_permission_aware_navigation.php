<?php
// Test Permission-Aware Navigation and Button Visibility
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config.php';
require_once 'includes/permission_aware_navigation.php';

echo "<h2>🔧 Testing Permission-Aware Navigation & Buttons</h2>";

$user_id = $_SESSION['admin_id'];
$username = $_SESSION['admin_username'] ?? 'Unknown';
$user_role = $_SESSION['admin_role'] ?? 'No Role';

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Current User:</strong> $username (ID: $user_id)</p>";
echo "<p><strong>Role:</strong> $user_role</p>";
echo "</div>";

try {
    // 1. Test page access permissions
    echo "<h3>📋 Step 1: Page Access Permissions</h3>";
    
    $test_pages = [
        'dashboard.php' => 'Main Dashboard',
        'assignment_dashboard.php' => 'Assignment Dashboard',
        'events.php' => 'Events Management',
        'event_sessions.php' => 'Event Sessions',
        'session_attendance.php' => 'Session Attendance',
        'event_attendance_detail.php' => 'Event Attendance Detail',
        'members.php' => 'Members Management',
        'add_member.php' => 'Add Member'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>Page</th><th>Description</th><th>Access</th><th>Status</th></tr>";
    
    foreach ($test_pages as $page => $description) {
        $can_access = canAccessPageForNavigation($user_id, $page);
        $status = $can_access ? '✅ Allowed' : '❌ Denied';
        $row_color = $can_access ? 'background-color: #d4edda;' : 'background-color: #f8d7da;';
        
        echo "<tr style='$row_color'>";
        echo "<td>$page</td>";
        echo "<td>$description</td>";
        echo "<td>" . ($can_access ? 'true' : 'false') . "</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. Test bulk operation permissions
    echo "<h3>📋 Step 2: Bulk Operation Permissions</h3>";
    
    $bulk_operations = [
        'attendance_marking' => 'Mark Attendance',
        'event_management' => 'Manage Events',
        'session_management' => 'Manage Sessions'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>Operation</th><th>Description</th><th>Permission</th><th>Status</th></tr>";
    
    foreach ($bulk_operations as $operation => $description) {
        $can_perform = canPerformBulkOperation($user_id, $operation);
        $status = $can_perform ? '✅ Allowed' : '❌ Denied';
        $row_color = $can_perform ? 'background-color: #d4edda;' : 'background-color: #f8d7da;';
        
        echo "<tr style='$row_color'>";
        echo "<td>$operation</td>";
        echo "<td>$description</td>";
        echo "<td>" . ($can_perform ? 'true' : 'false') . "</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 3. Test navigation generation
    echo "<h3>📋 Step 3: Navigation Generation Test</h3>";
    
    // Get sample session and event for testing
    $stmt = $pdo->query("SELECT id, session_title FROM event_sessions LIMIT 1");
    $sample_session = $stmt->fetch();
    
    $stmt = $pdo->query("SELECT id, title FROM events LIMIT 1");
    $sample_event = $stmt->fetch();
    
    if ($sample_session) {
        echo "<h4>Session Navigation (session_attendance.php):</h4>";
        $session_nav = getBackNavigation($user_id, 'session_attendance.php', [
            'session_id' => $sample_session['id'],
            'event_id' => 1 // Assuming event ID 1 exists
        ]);
        
        echo "<ul>";
        foreach ($session_nav as $nav) {
            echo "<li><strong>" . $nav['text'] . ":</strong> <a href='" . $nav['url'] . "' class='btn " . $nav['class'] . " btn-sm'>";
            echo "<i class='" . $nav['icon'] . "'></i> " . $nav['text'] . "</a></li>";
        }
        echo "</ul>";
    }
    
    if ($sample_event) {
        echo "<h4>Event Navigation (event_attendance_detail.php):</h4>";
        $event_nav = getBackNavigation($user_id, 'event_attendance_detail.php', [
            'event_id' => $sample_event['id']
        ]);
        
        echo "<ul>";
        foreach ($event_nav as $nav) {
            echo "<li><strong>" . $nav['text'] . ":</strong> <a href='" . $nav['url'] . "' class='btn " . $nav['class'] . " btn-sm'>";
            echo "<i class='" . $nav['icon'] . "'></i> " . $nav['text'] . "</a></li>";
        }
        echo "</ul>";
    }
    
    // 4. Test assignment checking
    echo "<h3>📋 Step 4: Assignment Checking</h3>";
    
    // Check session assignments
    $stmt = $pdo->prepare("
        SELECT sa.session_id, es.session_title, e.title as event_title
        FROM session_assignments sa
        JOIN event_sessions es ON sa.session_id = es.id
        JOIN events e ON es.event_id = e.id
        WHERE sa.user_id = ? AND sa.is_active = 1
        LIMIT 3
    ");
    $stmt->execute([$user_id]);
    $user_sessions = $stmt->fetchAll();
    
    echo "<h4>Your Session Assignments:</h4>";
    if (empty($user_sessions)) {
        echo "<p>❌ No session assignments found</p>";
    } else {
        echo "<ul>";
        foreach ($user_sessions as $session) {
            $is_assigned = isAssignedToSession($user_id, $session['session_id']);
            $status = $is_assigned ? '✅ Confirmed' : '❌ Not Found';
            echo "<li><strong>" . htmlspecialchars($session['session_title']) . "</strong> (" . htmlspecialchars($session['event_title']) . ") - $status</li>";
        }
        echo "</ul>";
    }
    
    // Check event assignments
    $stmt = $pdo->prepare("
        SELECT ea.event_id, ea.role_type, e.title as event_title
        FROM event_assignments ea
        JOIN events e ON ea.event_id = e.id
        WHERE ea.user_id = ? AND ea.is_active = 1
        LIMIT 3
    ");
    $stmt->execute([$user_id]);
    $user_events = $stmt->fetchAll();
    
    echo "<h4>Your Event Assignments:</h4>";
    if (empty($user_events)) {
        echo "<p>❌ No event assignments found</p>";
    } else {
        echo "<ul>";
        foreach ($user_events as $event) {
            $is_assigned = isAssignedToEvent($user_id, $event['event_id']);
            $status = $is_assigned ? '✅ Confirmed' : '❌ Not Found';
            echo "<li><strong>" . htmlspecialchars($event['event_title']) . "</strong> (Role: " . ucfirst($event['role_type']) . ") - $status</li>";
        }
        echo "</ul>";
    }
    
    // 5. Test button rendering
    echo "<h3>📋 Step 5: Permission Button Rendering Test</h3>";
    
    echo "<h4>Sample Buttons (based on your permissions):</h4>";
    echo "<div class='btn-group mb-3' role='group'>";
    
    // Test various permission buttons
    echo renderPermissionButton($user_id, 'events.manage', 
        '<button class="btn btn-primary">Manage Events</button>'
    );
    
    echo renderPermissionButton($user_id, 'attendance.manage', 
        '<button class="btn btn-success">Mark Attendance</button>'
    );
    
    echo renderPermissionButton($user_id, 'sessions.manage', 
        '<button class="btn btn-info">Manage Sessions</button>'
    );
    
    echo renderPermissionButton($user_id, 'members.view', 
        '<button class="btn btn-secondary">View Members</button>'
    );
    
    echo "</div>";
    
    // 6. Test user home page determination
    echo "<h3>📋 Step 6: User Home Page Determination</h3>";
    
    $home_page = getUserHomePage($user_id);
    echo "<p><strong>Your Home Page:</strong> <a href='$home_page' class='btn btn-primary'>$home_page</a></p>";
    
    echo "<h3>🎯 Summary</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>✅ Permission-Aware System Active!</strong></p>";
    echo "<ul>";
    echo "<li>✅ <strong>Page Access Control:</strong> Users only see pages they can access</li>";
    echo "<li>✅ <strong>Button Visibility:</strong> Buttons hidden based on permissions</li>";
    echo "<li>✅ <strong>Smart Navigation:</strong> Context-aware back navigation</li>";
    echo "<li>✅ <strong>Assignment-Based Access:</strong> Users can access assigned sessions/events</li>";
    echo "<li>✅ <strong>Bulk Operations:</strong> Only available to authorized users</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>📝 Security Features:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Super Admin Override:</strong> Super admins can access everything</li>";
    echo "<li><strong>Assignment-Based Access:</strong> Users can view/manage their assigned sessions/events</li>";
    echo "<li><strong>Permission-Based Buttons:</strong> Buttons only appear if user has required permissions</li>";
    echo "<li><strong>Context-Aware Navigation:</strong> Back buttons adapt to user's access level</li>";
    echo "<li><strong>Graceful Degradation:</strong> Users without permissions see appropriate alternatives</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>❌ Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<p>";
echo "<a href='session_attendance.php?session_id=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🧪 Test Session Page</a>";
echo "<a href='event_attendance_detail.php?event_id=1' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🧪 Test Event Page</a>";
echo "<a href='assignment_dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📊 Assignment Dashboard</a>";
echo "<a href='dashboard.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 Main Dashboard</a>";
echo "</p>";
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h2, h3, h4 { color: #333; }
    table { width: 100%; margin: 10px 0; }
    th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
    p { margin: 8px 0; }
    ul, ol { margin: 10px 0; padding-left: 20px; }
    li { margin: 5px 0; }
    .btn { padding: 5px 10px; margin: 2px; text-decoration: none; border-radius: 3px; display: inline-block; }
    .btn-primary { background: #007bff; color: white; }
    .btn-success { background: #28a745; color: white; }
    .btn-info { background: #17a2b8; color: white; }
    .btn-secondary { background: #6c757d; color: white; }
    .btn-sm { padding: 3px 8px; font-size: 0.875rem; }
</style>
