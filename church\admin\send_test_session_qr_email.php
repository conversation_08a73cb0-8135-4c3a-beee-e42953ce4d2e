<?php
require_once '../config.php';
require_once '../includes/QRCodeEmailService.php';

echo "<h1>📧 Sending Test Session QR Email</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .step{background:#f5f5f5;padding:10px;margin:10px 0;border-left:4px solid #007bff;}</style>";

$target_email = '<EMAIL>';
$test_member_name = 'Bointa Test User';

try {
    echo "<div class='step'>";
    echo "<h2>Step 1: Initialize QR Code Email Service</h2>";
    
    $qrService = new QRCodeEmailService($pdo, [
        'qr_size' => 300,
        'qr_margin' => 10,
        'from_email' => '<EMAIL>',
        'from_name' => 'Freedom Assembly Church',
        'enable_logging' => true
    ]);
    
    echo "<span class='success'>✅ QR Code Email Service initialized</span><br>";
    echo "<span class='info'>📧 Target Email: {$target_email}</span><br>";
    echo "<span class='info'>👤 Test Member: {$test_member_name}</span><br>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 2: Get or Create Test Data</h2>";
    
    // Get or create a test member
    $stmt = $pdo->prepare("SELECT * FROM members WHERE email = ?");
    $stmt->execute([$target_email]);
    $member = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$member) {
        // Create test member
        echo "<span class='info'>🔧 Creating test member...</span><br>";
        $stmt = $pdo->prepare("
            INSERT INTO members (full_name, email, phone, address, date_joined, status)
            VALUES (?, ?, '555-0123', '123 Test St, Test City', NOW(), 'active')
        ");
        $stmt->execute([$test_member_name, $target_email]);
        $member_id = $pdo->lastInsertId();
        
        $member = [
            'id' => $member_id,
            'full_name' => $test_member_name,
            'email' => $target_email
        ];
        echo "<span class='success'>✅ Test member created with ID: {$member_id}</span><br>";
    } else {
        echo "<span class='info'>ℹ️ Using existing member: {$member['full_name']} (ID: {$member['id']})</span><br>";
    }
    
    // Get or create a test event
    $stmt = $pdo->query("SELECT * FROM events ORDER BY id DESC LIMIT 1");
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        // Create test event
        echo "<span class='info'>🔧 Creating test event...</span><br>";
        $stmt = $pdo->prepare("
            INSERT INTO events (title, description, event_date, location, max_attendees, created_by, is_active)
            VALUES ('Test Evening Service', 'Test event for QR code demonstration', ?, 'Church Main Hall', 100, 1, 1)
        ");
        $event_date = date('Y-m-d H:i:s', strtotime('+1 week'));
        $stmt->execute([$event_date]);
        $event_id = $pdo->lastInsertId();
        
        $event = [
            'id' => $event_id,
            'title' => 'Test Evening Service',
            'event_date' => $event_date,
            'location' => 'Church Main Hall'
        ];
        echo "<span class='success'>✅ Test event created with ID: {$event_id}</span><br>";
    } else {
        echo "<span class='info'>ℹ️ Using existing event: {$event['title']} (ID: {$event['id']})</span><br>";
    }
    
    // Get or create a test session
    $stmt = $pdo->prepare("SELECT * FROM event_sessions WHERE event_id = ? ORDER BY id DESC LIMIT 1");
    $stmt->execute([$event['id']]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        // Create test session
        echo "<span class='info'>🔧 Creating test session...</span><br>";
        $session_start = date('Y-m-d H:i:s', strtotime($event['event_date'] . ' +1 hour'));
        $session_end = date('Y-m-d H:i:s', strtotime($session_start . ' +2 hours'));
        
        $stmt = $pdo->prepare("
            INSERT INTO event_sessions (event_id, session_title, start_datetime, end_datetime, location, max_attendees, status)
            VALUES (?, 'Worship & Prayer Session', ?, ?, 'Prayer Room', 50, 'active')
        ");
        $stmt->execute([$event['id'], $session_start, $session_end]);
        $session_id = $pdo->lastInsertId();
        
        $session = [
            'id' => $session_id,
            'event_id' => $event['id'],
            'session_title' => 'Worship & Prayer Session',
            'start_datetime' => $session_start,
            'end_datetime' => $session_end,
            'location' => 'Prayer Room'
        ];
        echo "<span class='success'>✅ Test session created with ID: {$session_id}</span><br>";
    } else {
        echo "<span class='info'>ℹ️ Using existing session: {$session['session_title']} (ID: {$session['id']})</span><br>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 3: Simulate Session Registration</h2>";
    
    // Check if member is already registered for this session
    $stmt = $pdo->prepare("SELECT * FROM session_attendance WHERE session_id = ? AND member_id = ?");
    $stmt->execute([$session['id'], $member['id']]);
    $existing_registration = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$existing_registration) {
        // Register member for session
        echo "<span class='info'>🔧 Registering member for session...</span><br>";
        $stmt = $pdo->prepare("
            INSERT INTO session_attendance (session_id, member_id, attendance_status)
            VALUES (?, ?, 'registered')
        ");
        $stmt->execute([$session['id'], $member['id']]);
        echo "<span class='success'>✅ Member registered for session</span><br>";
    } else {
        echo "<span class='info'>ℹ️ Member already registered for this session</span><br>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 4: Generate and Send Session QR Code Email</h2>";
    
    // Generate and send QR code email
    echo "<span class='info'>📧 Generating QR code and sending email...</span><br>";
    
    $emailSent = $qrService->generateAndSendSessionQRCode($session['id'], $member['id']);
    
    if ($emailSent) {
        echo "<span class='success'>✅ Session QR code email sent successfully to {$target_email}!</span><br>";
        
        // Get the session-specific QR code details
        $stmt = $pdo->prepare("SELECT * FROM session_attendance_qr_codes WHERE session_id = ? AND member_id = ? ORDER BY created_at DESC LIMIT 1");
        $stmt->execute([$session['id'], $member['id']]);
        $qr_record = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($qr_record) {
            echo "<span class='info'>🎫 Session-Specific QR Token: {$qr_record['qr_token']}</span><br>";
            echo "<span class='info'>👤 Attendee: {$qr_record['attendee_name']} ({$qr_record['attendee_email']})</span><br>";
            echo "<span class='info'>⏰ Expires: {$qr_record['expires_at']}</span><br>";
            echo "<span class='info'>🔗 Check-in URL: mobile_checkin.php?token={$qr_record['qr_token']}</span><br>";
            echo "<span class='info'>🔒 Used: " . ($qr_record['is_used'] ? 'Yes' : 'No') . "</span><br>";
        } else {
            echo "<span class='warning'>⚠️ No session-specific QR code found. Checking legacy system...</span><br>";
            // Fallback to legacy system
            $stmt = $pdo->prepare("SELECT * FROM session_qr_codes WHERE session_id = ?");
            $stmt->execute([$session['id']]);
            $legacy_qr = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($legacy_qr) {
                echo "<span class='info'>🎫 Legacy QR Token: {$legacy_qr['qr_token']}</span><br>";
                echo "<span class='info'>⏰ Expires: {$legacy_qr['expires_at']}</span><br>";
                echo "<span class='info'>🔗 Check-in URL: mobile_checkin.php?token={$legacy_qr['qr_token']}</span><br>";
            }
        }
        
        echo "<div style='background:#e8f5e8;padding:15px;margin:15px 0;border-radius:5px;border:2px solid #28a745;'>";
        echo "<h3>📧 Email Content Summary</h3>";
        echo "<p><strong>To:</strong> {$target_email}</p>";
        echo "<p><strong>Subject:</strong> 🎟️ Your Session Check-in Code - {$session['session_title']}</p>";
        echo "<p><strong>Content Includes:</strong></p>";
        echo "<ul>";
        echo "<li>✅ Embedded QR code image (base64 inline)</li>";
        echo "<li>✅ Session details and timing</li>";
        echo "<li>✅ Multiple check-in options</li>";
        echo "<li>✅ Professional responsive design</li>";
        echo "<li>✅ Direct check-in button</li>";
        echo "</ul>";
        echo "</div>";
        
    } else {
        echo "<span class='error'>❌ Failed to send session QR code email</span><br>";
        echo "<span class='error'>Check error logs for details</span><br>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>✅ Test Complete</h2>";
    echo "<p><strong>What was sent:</strong></p>";
    echo "<ul>";
    echo "<li>📧 Professional HTML email with embedded QR code</li>";
    echo "<li>🎫 Session-specific QR code for check-in</li>";
    echo "<li>📱 Mobile-responsive design</li>";
    echo "<li>🔗 Direct check-in button and QR scan options</li>";
    echo "</ul>";
    
    echo "<p><strong>Check your email at {$target_email} to see the result!</strong></p>";
    
    echo "<p><strong>Email Features:</strong></p>";
    echo "<ul>";
    echo "<li>✅ QR code embedded inline (not as attachment)</li>";
    echo "<li>✅ Session details: {$session['session_title']}</li>";
    echo "<li>✅ Event: {$event['title']}</li>";
    echo "<li>✅ Date: " . date('F j, Y g:i A', strtotime($session['start_datetime'])) . "</li>";
    echo "<li>✅ Location: {$session['location']}</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='step'>";
    echo "<span class='error'>❌ Test failed with error: " . $e->getMessage() . "</span><br>";
    echo "<span class='error'>📍 Stack trace: " . $e->getTraceAsString() . "</span><br>";
    echo "</div>";
}

echo "<br><a href='qr_system_demo.php'>← Back to QR System Demo</a>";
echo " | <a href='test_qr_integration.php'>🧪 Run Full Integration Tests</a>";
?>
