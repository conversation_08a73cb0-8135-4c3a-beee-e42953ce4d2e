<?php
require_once '../config.php';

echo "<h3>Settings Tables Structure</h3>";

// Check for settings-related tables
$tables = ['site_settings', 'appearance_settings', 'settings'];

foreach ($tables as $table) {
    try {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->rowCount() > 0) {
            echo "<h4>Table: $table</h4>";
            
            // Show table structure
            $stmt = $pdo->prepare("DESCRIBE $table");
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table border='1'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>{$column['Field']}</td>";
                echo "<td>{$column['Type']}</td>";
                echo "<td>{$column['Null']}</td>";
                echo "<td>{$column['Key']}</td>";
                echo "<td>{$column['Default']}</td>";
                echo "</tr>";
            }
            echo "</table><br>";
            
            // Show sample data
            $stmt = $pdo->prepare("SELECT * FROM $table LIMIT 5");
            $stmt->execute();
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($data)) {
                echo "<h5>Sample Data:</h5>";
                echo "<table border='1'>";
                $headers = array_keys($data[0]);
                echo "<tr>";
                foreach ($headers as $header) {
                    echo "<th>$header</th>";
                }
                echo "</tr>";
                
                foreach ($data as $row) {
                    echo "<tr>";
                    foreach ($row as $value) {
                        echo "<td>" . htmlspecialchars($value) . "</td>";
                    }
                    echo "</tr>";
                }
                echo "</table><br>";
            }
        } else {
            echo "<p>Table '$table' does not exist.</p>";
        }
    } catch (Exception $e) {
        echo "<p>Error checking table '$table': " . $e->getMessage() . "</p>";
    }
}
?>
