<?php
/**
 * Settings Helper Functions
 * Provides easy access to settings throughout the application
 */

require_once __DIR__ . '/../classes/SettingsManager.php';

// Global settings manager instance
$GLOBALS['settings_manager'] = null;

/**
 * Get the settings manager instance
 */
function getSettingsManager() {
    global $pdo;
    
    if (!isset($GLOBALS['settings_manager']) || $GLOBALS['settings_manager'] === null) {
        $GLOBALS['settings_manager'] = new SettingsManager($pdo);
    }
    
    return $GLOBALS['settings_manager'];
}

/**
 * Get a setting value (shorthand function)
 * @param string $key Setting name
 * @param mixed $default Default value
 * @param string $table Table to check ('site', 'appearance', or 'auto')
 * @return mixed Setting value
 */
function getSetting($key, $default = null, $table = 'auto') {
    return getSettingsManager()->get($key, $default, $table);
}

/**
 * Set a setting value (shorthand function)
 * @param string $key Setting name
 * @param mixed $value Setting value
 * @param string $table Table to use ('site' or 'appearance')
 * @param string $type Setting type
 * @param string $description Setting description
 * @return bool Success
 */
function setSetting($key, $value, $table = 'site', $type = 'text', $description = '') {
    return getSettingsManager()->set($key, $value, $table, $type, $description);
}

/**
 * Get organization name
 */
function getOrganizationName() {
    return getSetting('organization_name', 'My Organization');
}

/**
 * Get organization type
 */
function getOrganizationType() {
    return getSetting('organization_type', 'organization');
}

/**
 * Get contact email
 */
function getContactEmail() {
    return getSetting('contact_email', '');
}

/**
 * Get primary color
 */
function getPrimaryColor() {
    return getSetting('primary_color', '#007bff', 'appearance');
}

/**
 * Get secondary color
 */
function getSecondaryColor() {
    return getSetting('secondary_color', '#6c757d', 'appearance');
}

/**
 * Get all organization settings
 */
function getOrganizationSettings() {
    return getSettingsManager()->getOrganizationSettings();
}

/**
 * Get all email settings
 */
function getEmailSettings() {
    return getSettingsManager()->getEmailSettings();
}

/**
 * Get all appearance settings
 */
function getAppearanceSettings() {
    return getSettingsManager()->getAppearanceSettings();
}

/**
 * Legacy compatibility function - maps old setting keys to new ones
 * @param string $oldKey Old setting key
 * @return mixed Setting value
 */
function getLegacySetting($oldKey) {
    // Map old keys to new keys
    $keyMap = [
        'email_smtp_host' => 'email_smtp_host',
        'email_smtp_port' => 'email_smtp_port',
        'email_smtp_username' => 'email_smtp_username',
        'email_smtp_password' => 'email_smtp_password',
        'email_smtp_secure' => 'email_smtp_secure',
        'email_sender_email' => 'email_sender_email',
        'email_sender_name' => 'email_sender_name',
        'smtp_host' => 'email_smtp_host',
        'smtp_port' => 'email_smtp_port',
        'smtp_username' => 'email_smtp_username',
        'smtp_password' => 'email_smtp_password',
        'smtp_secure' => 'email_smtp_secure',
        'sender_email' => 'email_sender_email',
        'sender_name' => 'email_sender_name',
    ];
    
    $newKey = $keyMap[$oldKey] ?? $oldKey;
    return getSetting($newKey);
}

/**
 * Check if settings tables are consolidated
 */
function areSettingsConsolidated() {
    global $pdo;
    
    try {
        // Check if old tables still exist and have data
        $stmt = $pdo->query("SHOW TABLES LIKE 'settings'");
        $settingsExists = $stmt->rowCount() > 0;
        
        $stmt = $pdo->query("SHOW TABLES LIKE 'email_settings'");
        $emailSettingsExists = $stmt->rowCount() > 0;
        
        if ($settingsExists || $emailSettingsExists) {
            // Check if they have data
            if ($settingsExists) {
                $stmt = $pdo->query("SELECT COUNT(*) FROM settings");
                if ($stmt->fetchColumn() > 0) return false;
            }
            
            if ($emailSettingsExists) {
                $stmt = $pdo->query("SELECT COUNT(*) FROM email_settings");
                if ($stmt->fetchColumn() > 0) return false;
            }
        }
        
        return true;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Get settings consolidation status
 */
function getSettingsStatus() {
    global $pdo;
    
    $status = [
        'consolidated' => false,
        'tables' => [],
        'total_settings' => 0,
        'needs_consolidation' => false
    ];
    
    try {
        $tables_to_check = ['settings', 'site_settings', 'email_settings', 'appearance_settings'];
        
        foreach ($tables_to_check as $table) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                $count = $stmt->fetchColumn();
                $status['tables'][$table] = $count;
                $status['total_settings'] += $count;
                
                // If old tables have data, consolidation is needed
                if (($table === 'settings' || $table === 'email_settings') && $count > 0) {
                    $status['needs_consolidation'] = true;
                }
            } catch (Exception $e) {
                $status['tables'][$table] = 0;
            }
        }
        
        $status['consolidated'] = !$status['needs_consolidation'];
        
    } catch (Exception $e) {
        // Error checking status
    }
    
    return $status;
}
?>
