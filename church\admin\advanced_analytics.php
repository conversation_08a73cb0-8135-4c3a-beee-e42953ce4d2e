<?php
require_once '../config.php';
require_once 'includes/auth_check.php';

$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    header('Location: events.php');
    exit();
}

// Get event details
$stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
$stmt->execute([$event_id]);
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    header('Location: events.php');
    exit();
}

// Analytics Functions
function getAttendanceTrends($pdo, $event_id) {
    $stmt = $pdo->prepare("
        SELECT 
            DATE(es.start_datetime) as event_date,
            HOUR(es.start_datetime) as event_hour,
            COUNT(sa.id) as total_registered,
            COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as total_attended,
            ROUND((COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) / 
                   NULLIF(COUNT(sa.id), 0)) * 100, 1) as attendance_rate
        FROM event_sessions es
        LEFT JOIN session_attendance sa ON es.id = sa.session_id
        WHERE es.event_id = ? AND es.status = 'active'
        GROUP BY DATE(es.start_datetime), HOUR(es.start_datetime)
        ORDER BY es.start_datetime
    ");
    $stmt->execute([$event_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getSessionPopularity($pdo, $event_id) {
    $stmt = $pdo->prepare("
        SELECT 
            es.id,
            es.session_title,
            es.start_datetime,
            es.max_attendees,
            COUNT(sa.id) as total_registered,
            COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as total_attended,
            COUNT(CASE WHEN sa.attendance_status = 'no_show' THEN 1 END) as total_no_show,
            ROUND((COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) / 
                   NULLIF(COUNT(sa.id), 0)) * 100, 1) as attendance_rate,
            ROUND((COUNT(sa.id) / NULLIF(es.max_attendees, 0)) * 100, 1) as capacity_utilization,
            CASE 
                WHEN COUNT(sa.id) >= es.max_attendees THEN 'Full'
                WHEN COUNT(sa.id) >= es.max_attendees * 0.8 THEN 'High Demand'
                WHEN COUNT(sa.id) >= es.max_attendees * 0.5 THEN 'Moderate'
                ELSE 'Low Demand'
            END as demand_level
        FROM event_sessions es
        LEFT JOIN session_attendance sa ON es.id = sa.session_id
        WHERE es.event_id = ? AND es.status = 'active'
        GROUP BY es.id
        ORDER BY total_registered DESC, attendance_rate DESC
    ");
    $stmt->execute([$event_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getMemberEngagement($pdo, $event_id) {
    $stmt = $pdo->prepare("
        SELECT 
            m.id,
            m.full_name,
            m.email,
            COUNT(DISTINCT sa.session_id) as sessions_registered,
            COUNT(DISTINCT CASE WHEN sa.attendance_status = 'attended' THEN sa.session_id END) as sessions_attended,
            COUNT(DISTINCT CASE WHEN sa.attendance_status = 'no_show' THEN sa.session_id END) as sessions_missed,
            ROUND((COUNT(DISTINCT CASE WHEN sa.attendance_status = 'attended' THEN sa.session_id END) / 
                   NULLIF(COUNT(DISTINCT sa.session_id), 0)) * 100, 1) as personal_attendance_rate,
            CASE 
                WHEN COUNT(DISTINCT CASE WHEN sa.attendance_status = 'attended' THEN sa.session_id END) >= 5 THEN 'High Engagement'
                WHEN COUNT(DISTINCT CASE WHEN sa.attendance_status = 'attended' THEN sa.session_id END) >= 3 THEN 'Moderate Engagement'
                WHEN COUNT(DISTINCT CASE WHEN sa.attendance_status = 'attended' THEN sa.session_id END) >= 1 THEN 'Low Engagement'
                ELSE 'No Engagement'
            END as engagement_level
        FROM members m
        JOIN event_rsvps er ON m.id = er.user_id
        LEFT JOIN session_attendance sa ON m.id = sa.member_id
        LEFT JOIN event_sessions es ON sa.session_id = es.id AND es.event_id = ?
        WHERE er.event_id = ? AND er.status = 'attending'
        GROUP BY m.id
        ORDER BY sessions_attended DESC, personal_attendance_rate DESC
    ");
    $stmt->execute([$event_id, $event_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getCapacityAnalysis($pdo, $event_id) {
    $stmt = $pdo->prepare("
        SELECT 
            es.location,
            COUNT(es.id) as sessions_count,
            AVG(es.max_attendees) as avg_capacity,
            AVG(session_stats.registered_count) as avg_registered,
            AVG(session_stats.attended_count) as avg_attended,
            ROUND(AVG(session_stats.utilization_rate), 1) as avg_utilization
        FROM event_sessions es
        LEFT JOIN (
            SELECT 
                session_id,
                COUNT(id) as registered_count,
                COUNT(CASE WHEN attendance_status = 'attended' THEN 1 END) as attended_count,
                (COUNT(id) / NULLIF(MAX(es2.max_attendees), 0)) * 100 as utilization_rate
            FROM session_attendance sa
            JOIN event_sessions es2 ON sa.session_id = es2.id
            GROUP BY session_id
        ) session_stats ON es.id = session_stats.session_id
        WHERE es.event_id = ? AND es.status = 'active'
        GROUP BY es.location
        ORDER BY avg_utilization DESC
    ");
    $stmt->execute([$event_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getPredictiveInsights($pdo, $event_id) {
    // Get historical data for predictions
    $stmt = $pdo->prepare("
        SELECT 
            AVG(attendance_rate) as avg_historical_rate,
            COUNT(DISTINCT es.id) as total_sessions,
            AVG(capacity_utilization) as avg_capacity_usage
        FROM (
            SELECT 
                es.id,
                ROUND((COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) / 
                       NULLIF(COUNT(sa.id), 0)) * 100, 1) as attendance_rate,
                ROUND((COUNT(sa.id) / NULLIF(es.max_attendees, 0)) * 100, 1) as capacity_utilization
            FROM event_sessions es
            LEFT JOIN session_attendance sa ON es.id = sa.session_id
            WHERE es.event_id = ?
            GROUP BY es.id
        ) session_metrics
    ");
    $stmt->execute([$event_id]);
    $historical = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Generate insights based on patterns
    $insights = [];
    
    if ($historical['avg_historical_rate'] > 80) {
        $insights[] = [
            'type' => 'success',
            'title' => 'High Attendance Success',
            'message' => 'Your event shows excellent attendance rates above 80%. Consider similar session formats for future events.',
            'recommendation' => 'Maintain current session structure and timing'
        ];
    } elseif ($historical['avg_historical_rate'] < 60) {
        $insights[] = [
            'type' => 'warning',
            'title' => 'Attendance Optimization Needed',
            'message' => 'Attendance rates below 60% suggest room for improvement in session appeal or timing.',
            'recommendation' => 'Review session content, timing, and marketing strategies'
        ];
    }
    
    if ($historical['avg_capacity_usage'] > 90) {
        $insights[] = [
            'type' => 'info',
            'title' => 'Capacity Strain Detected',
            'message' => 'High capacity utilization indicates strong demand. Consider larger venues or additional sessions.',
            'recommendation' => 'Plan for increased capacity in future similar events'
        ];
    }
    
    return $insights;
}

// Get all analytics data
$attendance_trends = getAttendanceTrends($pdo, $event_id);
$session_popularity = getSessionPopularity($pdo, $event_id);
$member_engagement = getMemberEngagement($pdo, $event_id);
$capacity_analysis = getCapacityAnalysis($pdo, $event_id);
$predictive_insights = getPredictiveInsights($pdo, $event_id);

// Calculate summary statistics
$total_sessions = count($session_popularity);
$avg_attendance_rate = $total_sessions > 0 ? round(array_sum(array_column($session_popularity, 'attendance_rate')) / $total_sessions, 1) : 0;
$total_registrations = array_sum(array_column($session_popularity, 'total_registered'));
$total_attended = array_sum(array_column($session_popularity, 'total_attended'));

// Engagement distribution
$engagement_distribution = [
    'High Engagement' => count(array_filter($member_engagement, function($m) { return $m['engagement_level'] === 'High Engagement'; })),
    'Moderate Engagement' => count(array_filter($member_engagement, function($m) { return $m['engagement_level'] === 'Moderate Engagement'; })),
    'Low Engagement' => count(array_filter($member_engagement, function($m) { return $m['engagement_level'] === 'Low Engagement'; })),
    'No Engagement' => count(array_filter($member_engagement, function($m) { return $m['engagement_level'] === 'No Engagement'; }))
];

$page_title = 'Advanced Analytics & Reporting';
include 'includes/header.php';
?>

<style>
.analytics-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.analytics-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.analytics-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.metric-highlight {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

.insight-card {
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.insight-card.success {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
}

.insight-card.warning {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
}

.insight-card.info {
    border-left-color: #17a2b8;
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
}

.engagement-meter {
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.engagement-fill {
    height: 100%;
    background: linear-gradient(90deg, #dc3545, #ffc107, #28a745);
    transition: width 0.5s ease;
}

.popularity-bar {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.popularity-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #28a745);
    transition: width 0.3s ease;
}

.data-table {
    max-height: 400px;
    overflow-y: auto;
}

.export-controls {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 2rem;
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="analytics-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="bi bi-graph-up"></i> Advanced Analytics & Reporting</h1>
                        <p class="mb-0">
                            <strong><?php echo htmlspecialchars($event['title']); ?></strong> •
                            <?php echo date('M j, Y', strtotime($event['event_date'])); ?>
                        </p>
                    </div>
                    <div>
                        <a href="realtime_dashboard.php?event_id=<?php echo $event_id; ?>" class="btn btn-light me-2">
                            <i class="bi bi-broadcast"></i> Real-Time
                        </a>
                        <a href="multi_session_dashboard.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-light">
                            <i class="bi bi-arrow-left"></i> Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <!-- Export Controls -->
            <div class="export-controls">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-0"><i class="bi bi-download"></i> Export Analytics Data</h6>
                        <small class="text-muted">Download comprehensive reports for further analysis</small>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="btn-group">
                            <button class="btn btn-outline-primary btn-sm" onclick="exportToPDF()">
                                <i class="bi bi-file-pdf"></i> PDF Report
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="exportToExcel()">
                                <i class="bi bi-file-excel"></i> Excel Data
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Summary Metrics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="metric-highlight">
                        <h3 class="text-primary"><?php echo $total_sessions; ?></h3>
                        <p class="mb-0">Total Sessions</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-highlight">
                        <h3 class="text-success"><?php echo number_format($total_registrations); ?></h3>
                        <p class="mb-0">Total Registrations</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-highlight">
                        <h3 class="text-info"><?php echo number_format($total_attended); ?></h3>
                        <p class="mb-0">Total Attended</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-highlight">
                        <h3 class="text-warning"><?php echo $avg_attendance_rate; ?>%</h3>
                        <p class="mb-0">Avg Attendance Rate</p>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Session Popularity Analysis -->
                <div class="col-md-8">
                    <div class="analytics-card">
                        <h5><i class="bi bi-bar-chart"></i> Session Popularity & Performance</h5>
                        <p class="text-muted">Ranked by registration numbers and attendance rates</p>

                        <div class="data-table">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Session</th>
                                        <th>Demand</th>
                                        <th>Registered</th>
                                        <th>Attended</th>
                                        <th>Rate</th>
                                        <th>Capacity</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($session_popularity as $session): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($session['session_title']); ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo date('M j, g:i A', strtotime($session['start_datetime'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php
                                                    echo $session['demand_level'] === 'Full' ? 'danger' :
                                                        ($session['demand_level'] === 'High Demand' ? 'warning' :
                                                        ($session['demand_level'] === 'Moderate' ? 'info' : 'secondary'));
                                                ?>">
                                                    <?php echo $session['demand_level']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo number_format($session['total_registered']); ?></td>
                                            <td><?php echo number_format($session['total_attended']); ?></td>
                                            <td>
                                                <div class="popularity-bar">
                                                    <div class="popularity-fill" style="width: <?php echo $session['attendance_rate']; ?>%"></div>
                                                </div>
                                                <small><?php echo $session['attendance_rate']; ?>%</small>
                                            </td>
                                            <td>
                                                <?php if ($session['max_attendees']): ?>
                                                    <?php echo $session['capacity_utilization']; ?>%
                                                    <small class="text-muted">(<?php echo $session['max_attendees']; ?> max)</small>
                                                <?php else: ?>
                                                    <small class="text-muted">No limit</small>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Predictive Insights -->
                <div class="col-md-4">
                    <div class="analytics-card">
                        <h5><i class="bi bi-lightbulb"></i> Predictive Insights</h5>
                        <p class="text-muted">AI-powered recommendations based on data patterns</p>

                        <?php if (empty($predictive_insights)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="bi bi-graph-up" style="font-size: 2rem;"></i>
                                <p class="mt-2">Gathering data for insights...</p>
                                <small>More insights will appear as data accumulates</small>
                            </div>
                        <?php else: ?>
                            <?php foreach ($predictive_insights as $insight): ?>
                                <div class="insight-card <?php echo $insight['type']; ?>">
                                    <h6 class="mb-2"><?php echo htmlspecialchars($insight['title']); ?></h6>
                                    <p class="mb-2"><?php echo htmlspecialchars($insight['message']); ?></p>
                                    <small class="fw-bold">
                                        <i class="bi bi-arrow-right"></i> <?php echo htmlspecialchars($insight['recommendation']); ?>
                                    </small>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Member Engagement Analysis -->
                <div class="col-md-6">
                    <div class="analytics-card">
                        <h5><i class="bi bi-people"></i> Member Engagement Analysis</h5>
                        <p class="text-muted">Individual participation patterns and engagement levels</p>

                        <!-- Engagement Distribution -->
                        <div class="mb-4">
                            <h6>Engagement Distribution</h6>
                            <div class="row text-center">
                                <div class="col-3">
                                    <h4 class="text-success"><?php echo $engagement_distribution['High Engagement']; ?></h4>
                                    <small class="text-muted">High</small>
                                </div>
                                <div class="col-3">
                                    <h4 class="text-info"><?php echo $engagement_distribution['Moderate Engagement']; ?></h4>
                                    <small class="text-muted">Moderate</small>
                                </div>
                                <div class="col-3">
                                    <h4 class="text-warning"><?php echo $engagement_distribution['Low Engagement']; ?></h4>
                                    <small class="text-muted">Low</small>
                                </div>
                                <div class="col-3">
                                    <h4 class="text-secondary"><?php echo $engagement_distribution['No Engagement']; ?></h4>
                                    <small class="text-muted">None</small>
                                </div>
                            </div>
                        </div>

                        <!-- Top Engaged Members -->
                        <div class="data-table">
                            <h6>Top Engaged Members</h6>
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Member</th>
                                        <th>Sessions</th>
                                        <th>Rate</th>
                                        <th>Level</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($member_engagement, 0, 10) as $member): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($member['full_name']); ?></strong>
                                            </td>
                                            <td>
                                                <?php echo $member['sessions_attended']; ?>/<?php echo $member['sessions_registered']; ?>
                                            </td>
                                            <td>
                                                <div class="engagement-meter">
                                                    <div class="engagement-fill" style="width: <?php echo $member['personal_attendance_rate']; ?>%"></div>
                                                </div>
                                                <small><?php echo $member['personal_attendance_rate']; ?>%</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php
                                                    echo $member['engagement_level'] === 'High Engagement' ? 'success' :
                                                        ($member['engagement_level'] === 'Moderate Engagement' ? 'info' :
                                                        ($member['engagement_level'] === 'Low Engagement' ? 'warning' : 'secondary'));
                                                ?>">
                                                    <?php echo str_replace(' Engagement', '', $member['engagement_level']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Capacity & Resource Analysis -->
                <div class="col-md-6">
                    <div class="analytics-card">
                        <h5><i class="bi bi-building"></i> Capacity & Resource Analysis</h5>
                        <p class="text-muted">Location utilization and capacity optimization insights</p>

                        <?php if (empty($capacity_analysis)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="bi bi-building" style="font-size: 2rem;"></i>
                                <p class="mt-2">No location data available</p>
                                <small>Add location information to sessions for capacity analysis</small>
                            </div>
                        <?php else: ?>
                            <div class="data-table">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Location</th>
                                            <th>Sessions</th>
                                            <th>Avg Capacity</th>
                                            <th>Utilization</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($capacity_analysis as $location): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($location['location'] ?: 'Not Specified'); ?></strong>
                                                </td>
                                                <td><?php echo $location['sessions_count']; ?></td>
                                                <td><?php echo round($location['avg_capacity']); ?></td>
                                                <td>
                                                    <div class="popularity-bar">
                                                        <div class="popularity-fill" style="width: <?php echo $location['avg_utilization']; ?>%"></div>
                                                    </div>
                                                    <small><?php echo $location['avg_utilization']; ?>%</small>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Capacity Recommendations -->
                            <div class="mt-3">
                                <h6>Optimization Recommendations</h6>
                                <?php
                                $high_utilization = array_filter($capacity_analysis, function($l) { return $l['avg_utilization'] > 85; });
                                $low_utilization = array_filter($capacity_analysis, function($l) { return $l['avg_utilization'] < 50; });
                                ?>

                                <?php if (!empty($high_utilization)): ?>
                                    <div class="alert alert-warning">
                                        <strong>High Utilization Alert:</strong>
                                        <?php echo count($high_utilization); ?> location(s) showing >85% utilization.
                                        Consider larger venues or additional sessions.
                                    </div>
                                <?php endif; ?>

                                <?php if (!empty($low_utilization)): ?>
                                    <div class="alert alert-info">
                                        <strong>Optimization Opportunity:</strong>
                                        <?php echo count($low_utilization); ?> location(s) showing <50% utilization.
                                        Consider consolidating sessions or reducing capacity.
                                    </div>
                                <?php endif; ?>

                                <?php if (empty($high_utilization) && empty($low_utilization)): ?>
                                    <div class="alert alert-success">
                                        <strong>Optimal Utilization:</strong>
                                        All locations showing healthy utilization rates (50-85%).
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Attendance Trends Chart -->
            <div class="row">
                <div class="col-12">
                    <div class="analytics-card">
                        <h5><i class="bi bi-graph-up"></i> Attendance Trends Over Time</h5>
                        <p class="text-muted">Hourly and daily attendance patterns for optimization</p>

                        <div class="chart-container">
                            <canvas id="attendanceTrendsChart"></canvas>
                        </div>

                        <!-- Trend Insights -->
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h6>Peak Hour</h6>
                                    <p class="text-primary">
                                        <?php
                                        if (!empty($attendance_trends)) {
                                            $peak_hour = array_reduce($attendance_trends, function($carry, $item) {
                                                return ($carry === null || $item['attendance_rate'] > $carry['attendance_rate']) ? $item : $carry;
                                            });
                                            echo $peak_hour ? date('g:i A', strtotime($peak_hour['event_hour'] . ':00')) : 'N/A';
                                        } else {
                                            echo 'N/A';
                                        }
                                        ?>
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h6>Best Day</h6>
                                    <p class="text-success">
                                        <?php
                                        if (!empty($attendance_trends)) {
                                            $daily_avg = [];
                                            foreach ($attendance_trends as $trend) {
                                                $date = $trend['event_date'];
                                                if (!isset($daily_avg[$date])) {
                                                    $daily_avg[$date] = ['total_rate' => 0, 'count' => 0];
                                                }
                                                $daily_avg[$date]['total_rate'] += $trend['attendance_rate'];
                                                $daily_avg[$date]['count']++;
                                            }

                                            $best_day = null;
                                            $best_rate = 0;
                                            foreach ($daily_avg as $date => $data) {
                                                $avg_rate = $data['total_rate'] / $data['count'];
                                                if ($avg_rate > $best_rate) {
                                                    $best_rate = $avg_rate;
                                                    $best_day = $date;
                                                }
                                            }
                                            echo $best_day ? date('M j', strtotime($best_day)) : 'N/A';
                                        } else {
                                            echo 'N/A';
                                        }
                                        ?>
                                    </p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h6>Trend Direction</h6>
                                    <p class="text-info">
                                        <?php
                                        if (count($attendance_trends) >= 2) {
                                            $first_half = array_slice($attendance_trends, 0, ceil(count($attendance_trends) / 2));
                                            $second_half = array_slice($attendance_trends, ceil(count($attendance_trends) / 2));

                                            $first_avg = array_sum(array_column($first_half, 'attendance_rate')) / count($first_half);
                                            $second_avg = array_sum(array_column($second_half, 'attendance_rate')) / count($second_half);

                                            if ($second_avg > $first_avg + 5) {
                                                echo '<i class="bi bi-arrow-up text-success"></i> Improving';
                                            } elseif ($second_avg < $first_avg - 5) {
                                                echo '<i class="bi bi-arrow-down text-danger"></i> Declining';
                                            } else {
                                                echo '<i class="bi bi-arrow-right text-info"></i> Stable';
                                            }
                                        } else {
                                            echo 'N/A';
                                        }
                                        ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Attendance Trends Chart
const attendanceTrendsData = <?php echo json_encode($attendance_trends); ?>;

const ctx = document.getElementById('attendanceTrendsChart').getContext('2d');
const attendanceTrendsChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: attendanceTrendsData.map(item => {
            const date = new Date(item.event_date + ' ' + item.event_hour + ':00');
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        }),
        datasets: [{
            label: 'Attendance Rate (%)',
            data: attendanceTrendsData.map(item => item.attendance_rate),
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }, {
            label: 'Total Registered',
            data: attendanceTrendsData.map(item => item.total_registered),
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            borderWidth: 2,
            fill: false,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            intersect: false,
            mode: 'index'
        },
        plugins: {
            legend: {
                position: 'top',
            },
            tooltip: {
                callbacks: {
                    afterLabel: function(context) {
                        if (context.datasetIndex === 0) {
                            const dataPoint = attendanceTrendsData[context.dataIndex];
                            return `Attended: ${dataPoint.total_attended}/${dataPoint.total_registered}`;
                        }
                        return '';
                    }
                }
            }
        },
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'Attendance Rate (%)'
                },
                min: 0,
                max: 100
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'Number of Registrations'
                },
                grid: {
                    drawOnChartArea: false,
                },
            },
            x: {
                title: {
                    display: true,
                    text: 'Date & Time'
                }
            }
        }
    }
});

// Export Functions
function exportToPDF() {
    // Create a comprehensive PDF report
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Title
    doc.setFontSize(20);
    doc.text('Event Analytics Report', 20, 20);

    // Event details
    doc.setFontSize(12);
    doc.text('<?php echo addslashes($event['title']); ?>', 20, 35);
    doc.text('<?php echo date('M j, Y', strtotime($event['event_date'])); ?>', 20, 45);

    // Summary metrics
    doc.setFontSize(14);
    doc.text('Summary Metrics', 20, 65);
    doc.setFontSize(10);
    doc.text('Total Sessions: <?php echo $total_sessions; ?>', 20, 75);
    doc.text('Total Registrations: <?php echo number_format($total_registrations); ?>', 20, 85);
    doc.text('Total Attended: <?php echo number_format($total_attended); ?>', 20, 95);
    doc.text('Average Attendance Rate: <?php echo $avg_attendance_rate; ?>%', 20, 105);

    // Session popularity
    doc.setFontSize(14);
    doc.text('Top Performing Sessions', 20, 125);
    doc.setFontSize(10);
    let yPos = 135;
    <?php foreach (array_slice($session_popularity, 0, 5) as $index => $session): ?>
        doc.text('<?php echo ($index + 1) . '. ' . addslashes($session['session_title']); ?>', 20, yPos);
        doc.text('Attendance: <?php echo $session['total_attended']; ?>/<?php echo $session['total_registered']; ?> (<?php echo $session['attendance_rate']; ?>%)', 25, yPos + 10);
        yPos += 20;
    <?php endforeach; ?>

    // Save the PDF
    doc.save('event-analytics-<?php echo date('Y-m-d'); ?>.pdf');

    // Show success message
    showNotification('PDF report generated successfully!', 'success');
}

function exportToExcel() {
    // Create Excel-compatible CSV data
    let csvContent = "data:text/csv;charset=utf-8,";

    // Session data
    csvContent += "Session Analytics\n";
    csvContent += "Session Title,Start Date,Registered,Attended,Attendance Rate,Demand Level\n";

    <?php foreach ($session_popularity as $session): ?>
        csvContent += "<?php echo addslashes($session['session_title']); ?>,";
        csvContent += "<?php echo date('Y-m-d H:i', strtotime($session['start_datetime'])); ?>,";
        csvContent += "<?php echo $session['total_registered']; ?>,";
        csvContent += "<?php echo $session['total_attended']; ?>,";
        csvContent += "<?php echo $session['attendance_rate']; ?>%,";
        csvContent += "<?php echo addslashes($session['demand_level']); ?>\n";
    <?php endforeach; ?>

    csvContent += "\n\nMember Engagement\n";
    csvContent += "Member Name,Sessions Registered,Sessions Attended,Attendance Rate,Engagement Level\n";

    <?php foreach ($member_engagement as $member): ?>
        csvContent += "<?php echo addslashes($member['full_name']); ?>,";
        csvContent += "<?php echo $member['sessions_registered']; ?>,";
        csvContent += "<?php echo $member['sessions_attended']; ?>,";
        csvContent += "<?php echo $member['personal_attendance_rate']; ?>%,";
        csvContent += "<?php echo addslashes($member['engagement_level']); ?>\n";
    <?php endforeach; ?>

    // Download the file
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "event-analytics-<?php echo date('Y-m-d'); ?>.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showNotification('Excel data exported successfully!', 'success');
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

<!-- jsPDF Library for PDF export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

<?php include 'includes/footer.php'; ?>
