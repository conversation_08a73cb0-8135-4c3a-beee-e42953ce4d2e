<?php
/**
 * Unified Settings Manager
 * Handles both site_settings and appearance_settings tables
 * Provides a clean interface for getting/setting all application settings
 */

class SettingsManager {
    private $pdo;
    private $cache = [];
    private $cache_loaded = false;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Get a setting value
     * @param string $key Setting name
     * @param mixed $default Default value if setting not found
     * @param string $table Which table to check ('site' or 'appearance' or 'auto')
     * @return mixed Setting value
     */
    public function get($key, $default = null, $table = 'auto') {
        $this->loadCache();
        
        if ($table === 'auto') {
            // Check both tables, prioritize site_settings
            if (isset($this->cache['site'][$key])) {
                return $this->cache['site'][$key];
            } elseif (isset($this->cache['appearance'][$key])) {
                return $this->cache['appearance'][$key];
            }
        } elseif ($table === 'site' && isset($this->cache['site'][$key])) {
            return $this->cache['site'][$key];
        } elseif ($table === 'appearance' && isset($this->cache['appearance'][$key])) {
            return $this->cache['appearance'][$key];
        }
        
        return $default;
    }
    
    /**
     * Set a setting value
     * @param string $key Setting name
     * @param mixed $value Setting value
     * @param string $table Which table to use ('site' or 'appearance')
     * @param string $type Setting type (text, number, boolean, etc.)
     * @param string $description Setting description
     * @return bool Success
     */
    public function set($key, $value, $table = 'site', $type = 'text', $description = '') {
        try {
            $tableName = $table === 'appearance' ? 'appearance_settings' : 'site_settings';
            
            $stmt = $this->pdo->prepare("
                INSERT INTO {$tableName} (setting_name, setting_value, setting_type, description, created_at, updated_at) 
                VALUES (?, ?, ?, ?, NOW(), NOW())
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value), 
                setting_type = VALUES(setting_type),
                description = VALUES(description),
                updated_at = NOW()
            ");
            
            $result = $stmt->execute([$key, $value, $type, $description]);
            
            // Update cache
            if ($result) {
                $this->cache[$table][$key] = $value;
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("SettingsManager::set error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Set multiple settings at once
     * @param array $settings Array of key => value pairs
     * @param string $table Which table to use ('site' or 'appearance')
     * @param string $defaultType Default setting type
     * @return bool Success
     */
    public function setMultiple($settings, $table = 'site', $defaultType = 'text') {
        try {
            $this->pdo->beginTransaction();
            
            foreach ($settings as $key => $value) {
                // Determine type based on key name or value
                $type = $this->determineType($key, $value, $defaultType);
                
                if (!$this->set($key, $value, $table, $type)) {
                    $this->pdo->rollBack();
                    return false;
                }
            }
            
            $this->pdo->commit();
            return true;
        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("SettingsManager::setMultiple error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get all settings from a table
     * @param string $table Which table ('site' or 'appearance')
     * @return array All settings
     */
    public function getAll($table = 'site') {
        $this->loadCache();
        return $this->cache[$table] ?? [];
    }
    
    /**
     * Get settings by category (appearance_settings only)
     * @param string $category Category name
     * @return array Settings in category
     */
    public function getByCategory($category) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT setting_name, setting_value 
                FROM appearance_settings 
                WHERE category = ?
            ");
            $stmt->execute([$category]);
            
            $settings = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $settings[$row['setting_name']] = $row['setting_value'];
            }
            
            return $settings;
        } catch (Exception $e) {
            error_log("SettingsManager::getByCategory error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Delete a setting
     * @param string $key Setting name
     * @param string $table Which table ('site' or 'appearance' or 'both')
     * @return bool Success
     */
    public function delete($key, $table = 'both') {
        try {
            $success = true;
            
            if ($table === 'both' || $table === 'site') {
                $stmt = $this->pdo->prepare("DELETE FROM site_settings WHERE setting_name = ?");
                $stmt->execute([$key]);
                unset($this->cache['site'][$key]);
            }
            
            if ($table === 'both' || $table === 'appearance') {
                $stmt = $this->pdo->prepare("DELETE FROM appearance_settings WHERE setting_name = ?");
                $stmt->execute([$key]);
                unset($this->cache['appearance'][$key]);
            }
            
            return $success;
        } catch (Exception $e) {
            error_log("SettingsManager::delete error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Clear cache and reload from database
     */
    public function clearCache() {
        $this->cache = [];
        $this->cache_loaded = false;
    }
    
    /**
     * Load all settings into cache
     */
    private function loadCache() {
        if ($this->cache_loaded) {
            return;
        }
        
        try {
            // Load site_settings
            $stmt = $this->pdo->query("SELECT setting_name, setting_value FROM site_settings");
            $this->cache['site'] = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $this->cache['site'][$row['setting_name']] = $row['setting_value'];
            }
            
            // Load appearance_settings
            $stmt = $this->pdo->query("SELECT setting_name, setting_value FROM appearance_settings");
            $this->cache['appearance'] = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $this->cache['appearance'][$row['setting_name']] = $row['setting_value'];
            }
            
            $this->cache_loaded = true;
        } catch (Exception $e) {
            error_log("SettingsManager::loadCache error: " . $e->getMessage());
            $this->cache = ['site' => [], 'appearance' => []];
            $this->cache_loaded = true;
        }
    }
    
    /**
     * Determine setting type based on key name and value
     */
    private function determineType($key, $value, $default = 'text') {
        // Email fields
        if (strpos($key, 'email') !== false) return 'email';
        
        // Color fields
        if (strpos($key, 'color') !== false || (is_string($value) && preg_match('/^#[0-9a-fA-F]{6}$/', $value))) return 'color';
        
        // URL fields
        if (strpos($key, 'url') !== false || (is_string($value) && filter_var($value, FILTER_VALIDATE_URL))) return 'url';
        
        // Number fields
        if (strpos($key, 'port') !== false || strpos($key, 'width') !== false || strpos($key, 'height') !== false || is_numeric($value)) return 'number';
        
        // Boolean fields
        if (strpos($key, 'auth') !== false || strpos($key, 'enable') !== false || strpos($key, 'is_') !== false || is_bool($value) || $value === '1' || $value === '0') return 'boolean';
        
        // JSON fields
        if (is_array($value) || (is_string($value) && json_decode($value) !== null)) return 'json';
        
        return $default;
    }
    
    /**
     * Get organization settings (commonly used settings)
     */
    public function getOrganizationSettings() {
        return [
            'organization_name' => $this->get('organization_name', 'My Organization'),
            'organization_type' => $this->get('organization_type', 'organization'),
            'contact_email' => $this->get('contact_email', ''),
            'contact_phone' => $this->get('contact_phone', ''),
            'contact_address' => $this->get('contact_address', ''),
            'website_url' => $this->get('website_url', ''),
        ];
    }
    
    /**
     * Get email settings
     */
    public function getEmailSettings() {
        return [
            'smtp_host' => $this->get('email_smtp_host', 'localhost'),
            'smtp_port' => $this->get('email_smtp_port', '587'),
            'smtp_username' => $this->get('email_smtp_username', ''),
            'smtp_password' => $this->get('email_smtp_password', ''),
            'smtp_secure' => $this->get('email_smtp_secure', 'tls'),
            'sender_email' => $this->get('email_sender_email', ''),
            'sender_name' => $this->get('email_sender_name', ''),
        ];
    }
    
    /**
     * Get appearance settings
     */
    public function getAppearanceSettings() {
        return [
            'primary_color' => $this->get('primary_color', '#007bff', 'appearance'),
            'secondary_color' => $this->get('secondary_color', '#6c757d', 'appearance'),
            'accent_color' => $this->get('accent_color', '#28a745', 'appearance'),
            'font_family' => $this->get('font_family', 'system-ui', 'appearance'),
            'logo_url' => $this->get('logo_url', '', 'appearance'),
        ];
    }
}
?>
