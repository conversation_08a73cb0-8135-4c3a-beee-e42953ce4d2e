<?php
/**
 * Cleanup Old Settings Files
 * Removes deprecated settings files and updates references
 */

require_once '../config.php';

// Start session and check admin access
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['cleanup'])) {
    try {
        $files_to_backup = [
            'settings.php',
            'appearance_settings.php', 
            'branding_settings.php'
        ];
        
        $backup_dir = 'backups/old_settings_' . date('Y-m-d_H-i-s');
        if (!is_dir($backup_dir)) {
            mkdir($backup_dir, 0755, true);
        }
        
        $backed_up = [];
        $removed = [];
        
        foreach ($files_to_backup as $file) {
            if (file_exists($file)) {
                // Create backup
                if (copy($file, $backup_dir . '/' . $file)) {
                    $backed_up[] = $file;
                    
                    // Remove original (optional - commented out for safety)
                    // unlink($file);
                    // $removed[] = $file;
                }
            }
        }
        
        $message = "Backup created in: $backup_dir\n";
        $message .= "Backed up files: " . implode(', ', $backed_up);
        
        if (!empty($removed)) {
            $message .= "\nRemoved files: " . implode(', ', $removed);
        } else {
            $message .= "\nOriginal files preserved for safety. You can manually remove them after testing.";
        }
        
    } catch (Exception $e) {
        $error = "Cleanup failed: " . $e->getMessage();
    }
}

// Check which old settings files exist
$old_files = [
    'settings.php' => 'Legacy main settings page',
    'appearance_settings.php' => 'Legacy appearance settings page',
    'branding_settings.php' => 'Legacy branding settings page'
];

$existing_files = [];
foreach ($old_files as $file => $description) {
    if (file_exists($file)) {
        $existing_files[$file] = $description;
    }
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2"><i class="bi bi-trash"></i> Cleanup Old Settings Files</h1>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="bi bi-check-circle"></i> <pre><?php echo htmlspecialchars($message); ?></pre>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Status -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-file-earmark-text"></i> Old Settings Files Status</h5>
        </div>
        <div class="card-body">
            <?php if (empty($existing_files)): ?>
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i> No old settings files found. Cleanup already completed!
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>File</th>
                                <th>Description</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($existing_files as $file => $description): ?>
                                <tr>
                                    <td><code><?php echo $file; ?></code></td>
                                    <td><?php echo $description; ?></td>
                                    <td><span class="badge bg-warning">Exists</span></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <?php if (!empty($existing_files)): ?>
        <!-- Cleanup Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-shield-check"></i> Safe Cleanup Process</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> What this cleanup will do:</h6>
                    <ul class="mb-0">
                        <li>Create a backup of all old settings files</li>
                        <li>Preserve original files for safety (manual removal recommended after testing)</li>
                        <li>Ensure you can rollback if needed</li>
                        <li>Generate a cleanup report</li>
                    </ul>
                </div>
                
                <form method="POST">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6>Ready to backup old settings files?</h6>
                            <p class="text-muted mb-0">This operation is safe and creates backups before any changes.</p>
                        </div>
                        <button type="submit" name="cleanup" class="btn btn-warning btn-lg">
                            <i class="bi bi-archive"></i> Create Backup
                        </button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>

    <!-- Migration Status -->
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h6><i class="bi bi-gear"></i> Unified Settings</h6>
                    <p class="text-muted">New consolidated settings interface</p>
                    <a href="<?php echo admin_url_for('unified_settings.php'); ?>" class="btn btn-outline-primary">
                        Open Unified Settings
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h6><i class="bi bi-database-gear"></i> Migration</h6>
                    <p class="text-muted">Run settings table migration</p>
                    <a href="<?php echo admin_url_for('migrate_settings_tables.php'); ?>" class="btn btn-outline-secondary">
                        Run Migration
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h6><i class="bi bi-list-check"></i> Verification</h6>
                    <p class="text-muted">Test the new settings system</p>
                    <a href="<?php echo admin_url_for('check_settings_tables.php'); ?>" class="btn btn-outline-info">
                        Verify Setup
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Instructions -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-book"></i> Migration Instructions</h5>
        </div>
        <div class="card-body">
            <h6>Complete Migration Process:</h6>
            <ol>
                <li><strong>Run Migration:</strong> Use the migration script to set up new settings tables</li>
                <li><strong>Test New System:</strong> Verify the unified settings page works correctly</li>
                <li><strong>Backup Old Files:</strong> Use this cleanup script to backup old settings files</li>
                <li><strong>Update Navigation:</strong> Ensure sidebar points to unified settings (already done)</li>
                <li><strong>Manual Cleanup:</strong> After testing, manually remove old files if desired</li>
            </ol>
            
            <div class="alert alert-warning mt-3">
                <strong>Important:</strong> Always test the new unified settings system thoroughly before removing old files.
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
