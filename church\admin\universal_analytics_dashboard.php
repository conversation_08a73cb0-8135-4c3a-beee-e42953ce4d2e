<?php
/**
 * Universal Analytics Dashboard
 * Comprehensive analytics platform with adaptive metrics for any organization type
 */

require_once '../config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'Universal Analytics Dashboard';
$page_header = 'Universal Analytics';
$page_description = 'Comprehensive analytics with organization-specific metrics';

include 'includes/header.php';

// Get organization configuration
$org_type = get_site_setting('organization_type', 'church');
$org_config = [
    'church' => ['attendee' => 'Member', 'session' => 'Service', 'event' => 'Event'],
    'corporate' => ['attendee' => 'Employee', 'session' => 'Meeting', 'event' => 'Conference'],
    'educational' => ['attendee' => 'Student', 'session' => 'Class', 'event' => 'Course'],
    'sports' => ['attendee' => 'Fan', 'session' => 'Game', 'event' => 'Tournament'],
    'entertainment' => ['attendee' => 'Guest', 'session' => 'Show', 'event' => 'Festival'],
    'healthcare' => ['attendee' => 'Patient', 'session' => 'Appointment', 'event' => 'Clinic'],
    'government' => ['attendee' => 'Citizen', 'session' => 'Meeting', 'event' => 'Forum'],
    'nonprofit' => ['attendee' => 'Volunteer', 'session' => 'Activity', 'event' => 'Campaign']
];

$terminology = $org_config[$org_type] ?? $org_config['church'];

// Get analytics data
try {
    // Total events and attendance
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(DISTINCT e.id) as total_events,
            COUNT(DISTINCT ea.member_id) as total_registrations,
            COUNT(DISTINCT CASE WHEN ea.attendance_status = 'attended' THEN ea.member_id END) as total_attended,
            AVG(CASE WHEN ea.attendance_status = 'attended' THEN 1 ELSE 0 END) * 100 as avg_attendance_rate
        FROM events e
        LEFT JOIN event_attendance ea ON e.id = ea.event_id
        WHERE e.event_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
    $stmt->execute();
    $overview_stats = $stmt->fetch(PDO::FETCH_ASSOC);

    // Monthly attendance trends
    $stmt = $pdo->prepare("
        SELECT 
            DATE_FORMAT(e.event_date, '%Y-%m') as month,
            COUNT(DISTINCT e.id) as events_count,
            COUNT(DISTINCT ea.member_id) as registrations,
            COUNT(DISTINCT CASE WHEN ea.attendance_status = 'attended' THEN ea.member_id END) as attended
        FROM events e
        LEFT JOIN event_attendance ea ON e.id = ea.event_id
        WHERE e.event_date >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
        GROUP BY DATE_FORMAT(e.event_date, '%Y-%m')
        ORDER BY month DESC
        LIMIT 6
    ");
    $stmt->execute();
    $monthly_trends = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Top performing events
    $stmt = $pdo->prepare("
        SELECT 
            e.title,
            e.event_date,
            COUNT(DISTINCT ea.member_id) as registrations,
            COUNT(DISTINCT CASE WHEN ea.attendance_status = 'attended' THEN ea.member_id END) as attended,
            (COUNT(DISTINCT CASE WHEN ea.attendance_status = 'attended' THEN ea.member_id END) / 
             NULLIF(COUNT(DISTINCT ea.member_id), 0)) * 100 as attendance_rate
        FROM events e
        LEFT JOIN event_attendance ea ON e.id = ea.event_id
        WHERE e.event_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY e.id
        HAVING registrations > 0
        ORDER BY attendance_rate DESC
        LIMIT 5
    ");
    $stmt->execute();
    $top_events = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $overview_stats = ['total_events' => 0, 'total_registrations' => 0, 'total_attended' => 0, 'avg_attendance_rate' => 0];
    $monthly_trends = [];
    $top_events = [];
}
?>

<style>
.analytics-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.analytics-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    border-left: 4px solid #28a745;
}

.metric-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    transition: transform 0.2s ease;
}

.metric-card:hover {
    transform: translateY(-5px);
}

.metric-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 1rem 0;
}

.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

.org-badge {
    background: #28a745;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.trend-indicator {
    font-size: 0.9rem;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
}

.trend-up {
    background: #d4edda;
    color: #155724;
}

.trend-down {
    background: #f8d7da;
    color: #721c24;
}

.trend-stable {
    background: #fff3cd;
    color: #856404;
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="analytics-header">
                <div class="text-center">
                    <h1><i class="bi bi-graph-up"></i> Universal Analytics Dashboard</h1>
                    <p class="mb-0">Comprehensive analytics for <span class="org-badge"><?php echo ucfirst($org_type); ?></span> organizations</p>
                </div>
            </div>

            <!-- Key Metrics Overview -->
            <div class="row">
                <div class="col-md-3">
                    <div class="metric-card">
                        <i class="bi bi-calendar-event text-primary" style="font-size: 2rem;"></i>
                        <div class="metric-value text-primary"><?php echo number_format($overview_stats['total_events'] ?? 0); ?></div>
                        <h6>Total <?php echo $terminology['event']; ?>s</h6>
                        <small class="text-muted">Last 30 days</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <i class="bi bi-people text-success" style="font-size: 2rem;"></i>
                        <div class="metric-value text-success"><?php echo number_format($overview_stats['total_registrations'] ?? 0); ?></div>
                        <h6>Total Registrations</h6>
                        <small class="text-muted">All <?php echo strtolower($terminology['attendee']); ?>s</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <i class="bi bi-check-circle text-info" style="font-size: 2rem;"></i>
                        <div class="metric-value text-info"><?php echo number_format($overview_stats['total_attended'] ?? 0); ?></div>
                        <h6>Total Attended</h6>
                        <small class="text-muted">Confirmed attendance</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metric-card">
                        <i class="bi bi-percent text-warning" style="font-size: 2rem;"></i>
                        <div class="metric-value text-warning"><?php echo number_format($overview_stats['avg_attendance_rate'] ?? 0, 1); ?>%</div>
                        <h6>Avg. Attendance Rate</h6>
                        <small class="text-muted">Overall performance</small>
                    </div>
                </div>
            </div>

            <!-- Monthly Trends Chart -->
            <div class="analytics-card">
                <h5><i class="bi bi-graph-up"></i> Monthly Attendance Trends</h5>
                <p class="text-muted">Track <?php echo strtolower($terminology['attendee']); ?> engagement over time</p>
                
                <div class="chart-container">
                    <canvas id="trendsChart"></canvas>
                </div>
            </div>

            <!-- Top Performing Events -->
            <div class="analytics-card">
                <h5><i class="bi bi-trophy"></i> Top Performing <?php echo $terminology['event']; ?>s</h5>
                <p class="text-muted">Highest attendance rates in the last 30 days</p>
                
                <?php if (empty($top_events)): ?>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> No <?php echo strtolower($terminology['event']); ?>s with attendance data found. Create some <?php echo strtolower($terminology['event']); ?>s to see performance analytics!
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><?php echo $terminology['event']; ?> Name</th>
                                    <th>Date</th>
                                    <th>Registered</th>
                                    <th>Attended</th>
                                    <th>Rate</th>
                                    <th>Performance</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($top_events as $event): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($event['title']); ?></strong>
                                        </td>
                                        <td><?php echo date('M j, Y', strtotime($event['event_date'])); ?></td>
                                        <td><?php echo number_format($event['registrations']); ?></td>
                                        <td><?php echo number_format($event['attended']); ?></td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo number_format($event['attendance_rate'], 1); ?>%</span>
                                        </td>
                                        <td>
                                            <?php
                                            $rate = $event['attendance_rate'];
                                            if ($rate >= 80) {
                                                echo '<span class="trend-indicator trend-up"><i class="bi bi-arrow-up"></i> Excellent</span>';
                                            } elseif ($rate >= 60) {
                                                echo '<span class="trend-indicator trend-stable"><i class="bi bi-arrow-right"></i> Good</span>';
                                            } else {
                                                echo '<span class="trend-indicator trend-down"><i class="bi bi-arrow-down"></i> Needs Improvement</span>';
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Organization-Specific Insights -->
            <div class="analytics-card">
                <h5><i class="bi bi-lightbulb"></i> <?php echo ucfirst($org_type); ?> Organization Insights</h5>
                
                <?php
                $insights = [
                    'church' => [
                        'Sunday services typically have highest attendance',
                        'Special events and holidays show increased participation',
                        'Weather conditions significantly impact attendance'
                    ],
                    'corporate' => [
                        'Mandatory meetings achieve near 100% attendance',
                        'Morning meetings generally perform better',
                        'Department size affects meeting dynamics'
                    ],
                    'educational' => [
                        'Course popularity directly correlates with attendance',
                        'Exam periods affect optional class attendance',
                        'Semester timing influences participation rates'
                    ],
                    'sports' => [
                        'Team performance drives fan attendance',
                        'Weather significantly impacts outdoor events',
                        'Rival games generate higher interest'
                    ],
                    'entertainment' => [
                        'Artist/performer popularity is key driver',
                        'Ticket pricing affects attendance decisions',
                        'Competing events impact participation'
                    ],
                    'healthcare' => [
                        'Appointment urgency affects attendance rates',
                        'Provider ratings influence patient choices',
                        'Seasonal factors impact routine visits'
                    ],
                    'government' => [
                        'Public interest drives meeting attendance',
                        'Media coverage increases participation',
                        'Election periods boost civic engagement'
                    ],
                    'nonprofit' => [
                        'Cause alignment predicts volunteer participation',
                        'Seasonal giving affects event attendance',
                        'Community involvement varies by demographics'
                    ]
                ];
                
                $org_insights = $insights[$org_type] ?? $insights['church'];
                ?>
                
                <div class="row">
                    <?php foreach ($org_insights as $index => $insight): ?>
                        <div class="col-md-4">
                            <div class="alert alert-light border-start border-4 border-success">
                                <i class="bi bi-check-circle text-success"></i> <?php echo $insight; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="analytics-card">
                <h5><i class="bi bi-lightning"></i> Quick Actions</h5>
                <div class="row">
                    <div class="col-md-3">
                        <a href="<?php echo admin_url_for('universal_ai_dashboard.php'); ?>" class="btn btn-primary w-100">
                            <i class="bi bi-robot"></i><br>AI Predictions
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="<?php echo admin_url_for('events.php'); ?>" class="btn btn-success w-100">
                            <i class="bi bi-calendar"></i><br>Manage <?php echo $terminology['event']; ?>s
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="<?php echo admin_url_for('realtime_dashboard.php'); ?>" class="btn btn-info w-100">
                            <i class="bi bi-broadcast"></i><br>Real-Time Dashboard
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="<?php echo admin_url_for('pwa/'); ?>" class="btn btn-warning w-100" target="_blank">
                            <i class="bi bi-phone"></i><br>Mobile PWA
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Monthly Trends Chart
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('trendsChart').getContext('2d');
    
    const monthlyData = <?php echo json_encode(array_reverse($monthly_trends)); ?>;
    
    const labels = monthlyData.map(item => {
        const date = new Date(item.month + '-01');
        return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    });
    
    const eventsData = monthlyData.map(item => parseInt(item.events_count));
    const registrationsData = monthlyData.map(item => parseInt(item.registrations));
    const attendedData = monthlyData.map(item => parseInt(item.attended));
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: '<?php echo $terminology['event']; ?>s',
                    data: eventsData,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Registrations',
                    data: registrationsData,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Attended',
                    data: attendedData,
                    borderColor: '#17a2b8',
                    backgroundColor: 'rgba(23, 162, 184, 0.1)',
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: 'Monthly Attendance Trends'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
