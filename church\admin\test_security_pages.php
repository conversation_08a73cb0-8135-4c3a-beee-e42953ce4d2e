<?php
/**
 * Test Security Pages Functionality
 * This page tests security_settings.php and security_audit.php functionality
 */

session_start();
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/includes/route_protection.php';
require_once __DIR__ . '/../classes/SecurityManager.php';

// Protect this page - Admin only
protectAdminRoute();

$message = '';
$error = '';
$test_results = [];

// Initialize SecurityManager
$security = new SecurityManager($pdo);

// Test security functionality
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['run_tests'])) {
    try {
        // Test 1: SecurityManager initialization
        $test_results[] = [
            'test' => 'SecurityManager Initialization',
            'status' => 'PASS',
            'message' => 'SecurityManager class loaded and initialized successfully'
        ];

        // Test 2: CSRF Token generation
        $csrf_token = $security->getCSRFToken();
        $test_results[] = [
            'test' => 'CSRF Token Generation',
            'status' => !empty($csrf_token) ? 'PASS' : 'FAIL',
            'message' => !empty($csrf_token) ? 'CSRF token generated: ' . substr($csrf_token, 0, 16) . '...' : 'Failed to generate CSRF token'
        ];

        // Test 3: Security headers
        ob_start();
        $security->setSecurityHeaders();
        $headers_set = ob_get_clean();
        $test_results[] = [
            'test' => 'Security Headers',
            'status' => 'PASS',
            'message' => 'Security headers set successfully'
        ];

        // Test 4: Input sanitization
        $test_input = '<script>alert("test")</script>';
        $sanitized = $security->sanitizeInput($test_input, 'text');
        $test_results[] = [
            'test' => 'Input Sanitization',
            'status' => ($sanitized !== $test_input) ? 'PASS' : 'FAIL',
            'message' => 'Input sanitized: ' . htmlspecialchars($sanitized)
        ];

        // Test 5: Security logging
        $log_result = $security->logSecurityEvent('Test security event', [
            'test_data' => 'Security page test',
            'admin_id' => $_SESSION['admin_id']
        ]);
        $test_results[] = [
            'test' => 'Security Logging',
            'status' => 'PASS',
            'message' => 'Security event logged successfully'
        ];

        // Test 6: Check security_logs table
        $stmt = $pdo->query("SELECT COUNT(*) FROM security_logs WHERE event_type = 'Test security event'");
        $log_count = $stmt->fetchColumn();
        $test_results[] = [
            'test' => 'Security Logs Table',
            'status' => ($log_count > 0) ? 'PASS' : 'FAIL',
            'message' => "Found {$log_count} test security log entries"
        ];

        // Test 7: Check site_settings table for security settings
        $stmt = $pdo->query("SELECT COUNT(*) FROM site_settings WHERE setting_name LIKE 'password_%' OR setting_name LIKE 'security_%'");
        $settings_count = $stmt->fetchColumn();
        $test_results[] = [
            'test' => 'Security Settings Storage',
            'status' => 'PASS',
            'message' => "Found {$settings_count} security-related settings in database"
        ];

        // Test 8: Test security_settings.php accessibility
        $security_settings_path = __DIR__ . '/security_settings.php';
        $test_results[] = [
            'test' => 'Security Settings Page File',
            'status' => file_exists($security_settings_path) ? 'PASS' : 'FAIL',
            'message' => file_exists($security_settings_path) ? 'security_settings.php file exists and is accessible' : 'security_settings.php file not found'
        ];

        // Test 9: Test security_audit.php accessibility
        $security_audit_path = __DIR__ . '/security_audit.php';
        $test_results[] = [
            'test' => 'Security Audit Page File',
            'status' => file_exists($security_audit_path) ? 'PASS' : 'FAIL',
            'message' => file_exists($security_audit_path) ? 'security_audit.php file exists and is accessible' : 'security_audit.php file not found'
        ];

        // Test 10: Check required database tables
        $required_tables = ['security_logs', 'site_settings', 'admins'];
        $tables_exist = true;
        $missing_tables = [];
        
        foreach ($required_tables as $table) {
            try {
                $stmt = $pdo->query("SELECT 1 FROM {$table} LIMIT 1");
            } catch (PDOException $e) {
                $tables_exist = false;
                $missing_tables[] = $table;
            }
        }
        
        $test_results[] = [
            'test' => 'Required Database Tables',
            'status' => $tables_exist ? 'PASS' : 'FAIL',
            'message' => $tables_exist ? 'All required tables exist' : 'Missing tables: ' . implode(', ', $missing_tables)
        ];

        $message = "Security functionality tests completed! " . count(array_filter($test_results, function($r) { return $r['status'] === 'PASS'; })) . " out of " . count($test_results) . " tests passed.";

    } catch (Exception $e) {
        $error = "Test execution error: " . $e->getMessage();
    }
}

// Get current security settings for display
$current_settings = [];
try {
    $stmt = $pdo->query("
        SELECT setting_name, setting_value 
        FROM site_settings 
        WHERE setting_name LIKE 'password_%' OR setting_name LIKE 'security_%'
        ORDER BY setting_name
    ");
    $current_settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $current_settings = [];
}

// Get recent security logs
$recent_logs = [];
try {
    $stmt = $pdo->query("
        SELECT sl.*, a.username 
        FROM security_logs sl
        LEFT JOIN admins a ON sl.user_id = a.id
        ORDER BY sl.created_at DESC
        LIMIT 10
    ");
    $recent_logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $recent_logs = [];
}

$page_title = 'Test Security Pages';
$page_header = 'Security Pages Functionality Test';
$page_description = 'Verify security_settings.php and security_audit.php are working correctly';

include __DIR__ . '/includes/header.php';
?>

<style>
.test-header {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    color: white;
    border-radius: 10px;
}

.test-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.test-result {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 10px;
}

.test-result.pass {
    border-left: 4px solid #28a745;
    background-color: #d4edda;
}

.test-result.fail {
    border-left: 4px solid #dc3545;
    background-color: #f8d7da;
}

.settings-preview {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.logs-preview {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    max-height: 400px;
    overflow-y: auto;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="test-header p-4 text-center">
            <h2 class="mb-2">
                <i class="bi bi-shield-check"></i> <?php echo $page_header; ?>
            </h2>
            <p class="mb-0"><?php echo $page_description; ?></p>
        </div>
    </div>
</div>

<!-- Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Test Results -->
<?php if (!empty($test_results)): ?>
<div class="row mb-4">
    <div class="col-md-12">
        <div class="test-card">
            <h5><i class="bi bi-clipboard-check"></i> Test Results</h5>
            <p class="text-muted">Results from security functionality tests:</p>
            
            <?php foreach ($test_results as $result): ?>
                <div class="test-result <?php echo strtolower($result['status']); ?>">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <strong><?php echo htmlspecialchars($result['test']); ?></strong>
                            <br><small><?php echo htmlspecialchars($result['message']); ?></small>
                        </div>
                        <div>
                            <span class="badge <?php echo $result['status'] === 'PASS' ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo $result['status']; ?>
                            </span>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Test Controls -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="test-card">
            <h5><i class="bi bi-play-circle"></i> Run Security Tests</h5>
            <p class="text-muted">Click the button below to run comprehensive tests on the security functionality.</p>
            
            <form method="POST">
                <button type="submit" name="run_tests" class="btn btn-primary btn-lg">
                    <i class="bi bi-shield-check"></i> Run Security Tests
                </button>
                <a href="security_settings.php" class="btn btn-outline-primary btn-lg ms-2" target="_blank">
                    <i class="bi bi-gear"></i> Open Security Settings
                </a>
                <a href="security_audit.php" class="btn btn-outline-secondary btn-lg ms-2" target="_blank">
                    <i class="bi bi-list-ul"></i> Open Security Audit
                </a>
            </form>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="test-card">
            <h5><i class="bi bi-info-circle"></i> Test Information</h5>
            <p class="text-muted">What these tests verify:</p>
            
            <ul class="list-unstyled">
                <li><i class="bi bi-check-circle text-success"></i> SecurityManager class loading</li>
                <li><i class="bi bi-check-circle text-success"></i> CSRF token generation</li>
                <li><i class="bi bi-check-circle text-success"></i> Security headers</li>
                <li><i class="bi bi-check-circle text-success"></i> Input sanitization</li>
                <li><i class="bi bi-check-circle text-success"></i> Security logging</li>
                <li><i class="bi bi-check-circle text-success"></i> Database tables</li>
                <li><i class="bi bi-check-circle text-success"></i> Page accessibility</li>
            </ul>
        </div>
    </div>
</div>

<!-- Current Settings and Logs -->
<div class="row">
    <div class="col-md-6">
        <div class="test-card">
            <h5><i class="bi bi-gear"></i> Current Security Settings</h5>
            <div class="settings-preview">
                <?php if (!empty($current_settings)): ?>
                    <?php foreach ($current_settings as $setting): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <small class="text-muted"><?php echo htmlspecialchars($setting['setting_name']); ?></small>
                            <small class="badge bg-secondary"><?php echo htmlspecialchars($setting['setting_value']); ?></small>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-gear fs-3 d-block mb-2"></i>
                        <p>No security settings found</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="test-card">
            <h5><i class="bi bi-list-ul"></i> Recent Security Logs</h5>
            <div class="logs-preview">
                <?php if (!empty($recent_logs)): ?>
                    <?php foreach ($recent_logs as $log): ?>
                        <div class="mb-3 pb-2 border-bottom">
                            <div class="d-flex justify-content-between">
                                <strong><?php echo htmlspecialchars($log['event_type']); ?></strong>
                                <small class="text-muted"><?php echo date('M j, H:i', strtotime($log['created_at'])); ?></small>
                            </div>
                            <small class="text-muted">
                                User: <?php echo htmlspecialchars($log['username'] ?? 'Unknown'); ?>
                                <?php if ($log['ip_address']): ?>
                                    | IP: <?php echo htmlspecialchars($log['ip_address']); ?>
                                <?php endif; ?>
                            </small>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-list-ul fs-3 d-block mb-2"></i>
                        <p>No security logs found</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/includes/footer.php'; ?>
