<?php
/**
 * Session Notification Manager
 * 
 * Handles notifications for session attendance and registration
 */

class SessionNotificationManager {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Send registration confirmation to a registrant
     */
    public function sendRegistrationConfirmation($session_id, $member_id = null, $guest_email = null, $guest_name = null) {
        try {
            // Get session details
            $stmt = $this->pdo->prepare("
                SELECT s.*, e.title as event_title, e.event_date, e.location 
                FROM sessions s 
                JOIN events e ON s.event_id = e.id 
                WHERE s.id = ?
            ");
            $stmt->execute([$session_id]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$session) {
                throw new Exception("Session not found");
            }
            
            $recipient_email = null;
            $recipient_name = null;
            
            if ($member_id) {
                // Get member details
                $stmt = $this->pdo->prepare("SELECT email, first_name, last_name FROM members WHERE id = ?");
                $stmt->execute([$member_id]);
                $member = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($member) {
                    $recipient_email = $member['email'];
                    $recipient_name = $member['first_name'] . ' ' . $member['last_name'];
                }
            } else {
                $recipient_email = $guest_email;
                $recipient_name = $guest_name;
            }
            
            if ($recipient_email) {
                // Log the notification (in a real implementation, this would send an email)
                error_log("SESSION NOTIFICATION: Registration confirmation would be sent to $recipient_email for session '{$session['title']}' on {$session['event_date']}");
                
                // TODO: Implement actual email sending
                // For now, just log that we would send a notification
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Error sending registration confirmation: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Notify admins about new registration
     */
    public function notifyAdminNewRegistration($session_id, $member_id = null, $guest_name = null, $guest_email = null) {
        try {
            // Get session details
            $stmt = $this->pdo->prepare("
                SELECT s.*, e.title as event_title, e.event_date, e.location 
                FROM sessions s 
                JOIN events e ON s.event_id = e.id 
                WHERE s.id = ?
            ");
            $stmt->execute([$session_id]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$session) {
                throw new Exception("Session not found");
            }
            
            $registrant_info = '';
            if ($member_id) {
                // Get member details
                $stmt = $this->pdo->prepare("SELECT email, first_name, last_name FROM members WHERE id = ?");
                $stmt->execute([$member_id]);
                $member = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($member) {
                    $registrant_info = "Member: {$member['first_name']} {$member['last_name']} ({$member['email']})";
                }
            } else {
                $registrant_info = "Guest: $guest_name ($guest_email)";
            }
            
            // Log the notification (in a real implementation, this would send an email to admins)
            error_log("SESSION NOTIFICATION: Admin notification would be sent - New registration for session '{$session['title']}' on {$session['event_date']}. Registrant: $registrant_info");
            
            // TODO: Implement actual email sending to admins
            // For now, just log that we would send a notification
            return true;
            
        } catch (Exception $e) {
            error_log("Error sending admin notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send session reminder notifications
     */
    public function sendSessionReminders($session_id) {
        try {
            // Get session and registrants
            $stmt = $this->pdo->prepare("
                SELECT s.*, e.title as event_title, e.event_date, e.location 
                FROM sessions s 
                JOIN events e ON s.event_id = e.id 
                WHERE s.id = ?
            ");
            $stmt->execute([$session_id]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$session) {
                throw new Exception("Session not found");
            }
            
            // Get all registrants
            $stmt = $this->pdo->prepare("
                SELECT sa.*, m.email, m.first_name, m.last_name, sa.guest_email, sa.guest_name
                FROM session_attendance sa
                LEFT JOIN members m ON sa.member_id = m.id
                WHERE sa.session_id = ? AND sa.status = 'registered'
            ");
            $stmt->execute([$session_id]);
            $registrants = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $sent_count = 0;
            foreach ($registrants as $registrant) {
                $email = $registrant['email'] ?: $registrant['guest_email'];
                $name = $registrant['email'] ? 
                    $registrant['first_name'] . ' ' . $registrant['last_name'] : 
                    $registrant['guest_name'];
                
                if ($email) {
                    // Log the reminder (in a real implementation, this would send an email)
                    error_log("SESSION NOTIFICATION: Reminder would be sent to $email for session '{$session['title']}' on {$session['event_date']}");
                    $sent_count++;
                }
            }
            
            error_log("SESSION NOTIFICATION: Would send $sent_count reminder notifications for session '{$session['title']}'");
            return $sent_count;
            
        } catch (Exception $e) {
            error_log("Error sending session reminders: " . $e->getMessage());
            return 0;
        }
    }
}
?>
