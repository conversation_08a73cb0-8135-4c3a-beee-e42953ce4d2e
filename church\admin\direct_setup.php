<?php
// Direct setup without dependencies
require_once '../config.php';

try {
    echo "Direct setup of granular permission system...\n\n";
    
    // Check categories
    $stmt = $pdo->query("SELECT * FROM permission_categories ORDER BY sort_order");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "Categories found: " . count($categories) . "\n";
    
    foreach ($categories as $cat) {
        echo "- {$cat['id']}: {$cat['category_name']} ({$cat['category_display_name']})\n";
    }
    
    // Get the first category ID for testing
    if (!empty($categories)) {
        $first_cat_id = $categories[0]['id'];
        echo "\nUsing category ID {$first_cat_id} for test permissions...\n";
        
        // Create basic permissions directly
        $test_permissions = [
            ['dashboard.view', 'View Dashboard', 'Access to main dashboard', 'dashboard.php'],
            ['members.view', 'View Members', 'View member list', 'members.php'],
            ['admin.permission_management', 'Permission Management', 'Manage user permissions', 'manage_user_permissions.php']
        ];
        
        foreach ($test_permissions as $i => $perm) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO granular_permissions (category_id, permission_key, permission_name, permission_description, page_file, sort_order, is_active) VALUES (?, ?, ?, ?, ?, ?, 1)");
            $result = $stmt->execute([$first_cat_id, $perm[0], $perm[1], $perm[2], $perm[3], $i + 1]);
            echo "✓ Created permission: {$perm[1]} (Result: " . ($result ? 'success' : 'failed') . ")\n";
        }
        
        // Check what we created
        $stmt = $pdo->query("SELECT COUNT(*) FROM granular_permissions");
        $perm_count = $stmt->fetchColumn();
        echo "\nTotal permissions now: {$perm_count}\n";
        
        if ($perm_count > 0) {
            // Get admin users
            $stmt = $pdo->query("SELECT id, username FROM admins LIMIT 1");
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($admin) {
                echo "\nAssigning permissions to admin: {$admin['username']}\n";
                
                // Get all permissions
                $stmt = $pdo->query("SELECT id, permission_name FROM granular_permissions");
                $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                foreach ($permissions as $perm) {
                    $stmt = $pdo->prepare("INSERT IGNORE INTO user_individual_permissions (user_id, permission_id, granted_by, is_active) VALUES (?, ?, ?, 1)");
                    $result = $stmt->execute([$admin['id'], $perm['id'], $admin['id']]);
                    echo "✓ Assigned: {$perm['permission_name']}\n";
                }
                
                // Final check
                $stmt = $pdo->query("SELECT COUNT(*) FROM user_individual_permissions");
                $user_perm_count = $stmt->fetchColumn();
                echo "\nUser permissions assigned: {$user_perm_count}\n";
            }
        }
    }
    
    echo "\n🎉 Direct setup complete!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
}
?>
