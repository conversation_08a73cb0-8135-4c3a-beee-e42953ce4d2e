<?php
/**
 * Test Custom Fields Registration Integration
 * This page tests the integration between custom fields and registration
 */

session_start();
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/includes/route_protection.php';
require_once __DIR__ . '/includes/CustomFieldHelper.php';

// Protect this page - Admin only
protectAdminRoute();

$message = '';
$error = '';

// Test custom fields functionality
$customFieldHelper = new CustomFieldHelper($pdo);

// Get custom fields for member registration
$memberCustomFields = $customFieldHelper->getCustomFields('member');

// Test data processing
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_processing'])) {
    try {
        $testResults = [];
        
        // Test each custom field
        foreach ($memberCustomFields as $field) {
            $fieldName = 'custom_field_' . $field['field_name'];
            $testValue = $_POST[$fieldName] ?? '';
            
            $testResults[] = [
                'field_name' => $field['field_name'],
                'field_label' => $field['field_label'],
                'field_type' => $field['field_type'],
                'is_required' => $field['is_required'],
                'submitted_value' => $testValue,
                'processed_value' => $testValue // In real processing, this would be processed
            ];
        }
        
        $message = "Test processing completed successfully! " . count($testResults) . " custom fields processed.";
        
    } catch (Exception $e) {
        $error = "Test processing error: " . $e->getMessage();
    }
}

$page_title = 'Test Custom Fields Registration';
$page_header = 'Custom Fields Registration Test';
$page_description = 'Test the integration between custom fields and registration process';

include __DIR__ . '/includes/header.php';
?>

<style>
.test-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border-radius: 10px;
}

.test-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.field-test-result {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 10px;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="test-header p-4 text-center">
            <h2 class="mb-2">
                <i class="bi bi-bug"></i> <?php echo $page_header; ?>
            </h2>
            <p class="mb-0"><?php echo $page_description; ?></p>
        </div>
    </div>
</div>

<!-- Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Test Results -->
<?php if (isset($testResults)): ?>
<div class="row mb-4">
    <div class="col-md-12">
        <div class="test-card">
            <h5><i class="bi bi-clipboard-check"></i> Test Results</h5>
            <p class="text-muted">Results from processing the custom fields form:</p>
            
            <?php foreach ($testResults as $result): ?>
                <div class="field-test-result">
                    <div class="row">
                        <div class="col-md-3">
                            <strong><?php echo htmlspecialchars($result['field_label']); ?></strong>
                            <br><small class="text-muted"><?php echo htmlspecialchars($result['field_name']); ?></small>
                        </div>
                        <div class="col-md-2">
                            <span class="badge bg-info"><?php echo htmlspecialchars($result['field_type']); ?></span>
                            <?php if ($result['is_required']): ?>
                                <span class="badge bg-danger">Required</span>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-7">
                            <strong>Submitted:</strong> <?php echo htmlspecialchars($result['submitted_value'] ?: '(empty)'); ?>
                            <br><strong>Processed:</strong> <?php echo htmlspecialchars($result['processed_value'] ?: '(empty)'); ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Test Form -->
<div class="row">
    <div class="col-md-8">
        <div class="test-card">
            <h5><i class="bi bi-clipboard-data"></i> Test Custom Fields Form</h5>
            <p class="text-muted">This form simulates the custom fields section of the registration form.</p>
            
            <?php if (!empty($memberCustomFields)): ?>
                <form method="POST">
                    <div class="row">
                        <?php
                        foreach ($memberCustomFields as $field) {
                            $fieldName = 'custom_field_' . $field['field_name'];
                            $required = $field['is_required'] ? 'required' : '';
                            $helpText = $field['help_text'] ? '<small class="form-text text-muted">' . htmlspecialchars($field['help_text']) . '</small>' : '';
                            $label = htmlspecialchars($field['field_label']);
                            $requiredMark = $field['is_required'] ? ' <span class="text-danger">*</span>' : '';
                            
                            echo '<div class="col-md-6 mb-3">';
                            echo '<label for="' . $fieldName . '" class="form-label">' . $label . $requiredMark . '</label>';
                            
                            switch ($field['field_type']) {
                                case 'text':
                                case 'email':
                                case 'url':
                                case 'phone':
                                case 'number':
                                    $type = $field['field_type'] === 'text' ? 'text' : $field['field_type'];
                                    if ($field['field_type'] === 'phone') $type = 'tel';
                                    echo '<input type="' . $type . '" class="form-control" id="' . $fieldName . '" name="' . $fieldName . '" placeholder="Test ' . strtolower($label) . '" ' . $required . '>';
                                    break;
                                    
                                case 'date':
                                    echo '<input type="date" class="form-control" id="' . $fieldName . '" name="' . $fieldName . '" ' . $required . '>';
                                    break;
                                    
                                case 'datetime':
                                    echo '<input type="datetime-local" class="form-control" id="' . $fieldName . '" name="' . $fieldName . '" ' . $required . '>';
                                    break;
                                    
                                case 'textarea':
                                    echo '<textarea class="form-control" id="' . $fieldName . '" name="' . $fieldName . '" rows="3" placeholder="Test ' . strtolower($label) . '" ' . $required . '></textarea>';
                                    break;
                                    
                                case 'select':
                                    echo '<select class="form-select" id="' . $fieldName . '" name="' . $fieldName . '" ' . $required . '>';
                                    echo '<option value="">Select...</option>';
                                    if ($field['field_options']) {
                                        $options = json_decode($field['field_options'], true);
                                        foreach ($options as $key => $value) {
                                            echo '<option value="' . htmlspecialchars($key) . '">' . htmlspecialchars($value) . '</option>';
                                        }
                                    }
                                    echo '</select>';
                                    break;
                                    
                                case 'radio':
                                    if ($field['field_options']) {
                                        $options = json_decode($field['field_options'], true);
                                        foreach ($options as $key => $value) {
                                            echo '<div class="form-check">';
                                            echo '<input class="form-check-input" type="radio" id="' . $fieldName . '_' . $key . '" name="' . $fieldName . '" value="' . htmlspecialchars($key) . '" ' . $required . '>';
                                            echo '<label class="form-check-label" for="' . $fieldName . '_' . $key . '">' . htmlspecialchars($value) . '</label>';
                                            echo '</div>';
                                        }
                                    }
                                    break;
                                    
                                case 'checkbox':
                                    echo '<div class="form-check">';
                                    echo '<input class="form-check-input" type="checkbox" id="' . $fieldName . '" name="' . $fieldName . '" value="1">';
                                    echo '<label class="form-check-label" for="' . $fieldName . '">' . $label . '</label>';
                                    echo '</div>';
                                    break;
                                    
                                case 'file':
                                    echo '<input type="file" class="form-control" id="' . $fieldName . '" name="' . $fieldName . '" ' . $required . '>';
                                    break;
                            }
                            
                            echo $helpText;
                            echo '</div>';
                        }
                        ?>
                    </div>
                    
                    <div class="mt-4">
                        <button type="submit" name="test_processing" class="btn btn-primary">
                            <i class="bi bi-play-circle"></i> Test Form Processing
                        </button>
                        <a href="custom_fields.php?entity=member" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Custom Fields
                        </a>
                    </div>
                </form>
            <?php else: ?>
                <div class="text-center text-muted py-4">
                    <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                    <p>No custom fields configured for member registration.</p>
                    <a href="custom_fields.php?entity=member" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Add Custom Fields
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="test-card">
            <h5><i class="bi bi-info-circle"></i> Test Information</h5>
            <p class="text-muted">Information about this test:</p>
            
            <div class="mb-3">
                <strong>Custom Fields Found:</strong>
                <span class="badge bg-primary"><?php echo count($memberCustomFields); ?></span>
            </div>
            
            <div class="mb-3">
                <strong>Required Fields:</strong>
                <span class="badge bg-danger"><?php echo count(array_filter($memberCustomFields, function($f) { return $f['is_required']; })); ?></span>
            </div>
            
            <div class="mb-3">
                <strong>Field Types:</strong>
                <?php
                $fieldTypes = array_count_values(array_column($memberCustomFields, 'field_type'));
                foreach ($fieldTypes as $type => $count) {
                    echo '<span class="badge bg-info me-1">' . $type . ' (' . $count . ')</span>';
                }
                ?>
            </div>
            
            <hr>
            
            <h6>Quick Links:</h6>
            <div class="d-grid gap-2">
                <a href="custom_fields.php?entity=member" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-gear"></i> Manage Custom Fields
                </a>
                <a href="../register.php" target="_blank" class="btn btn-outline-success btn-sm">
                    <i class="bi bi-box-arrow-up-right"></i> View Registration Form
                </a>
                <a href="members.php" class="btn btn-outline-info btn-sm">
                    <i class="bi bi-people"></i> View Members
                </a>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/includes/footer.php'; ?>
