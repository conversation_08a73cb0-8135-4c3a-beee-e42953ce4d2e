<?php
require_once '../config.php';
require_once 'includes/auth_check.php';

$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    header('Location: events.php');
    exit();
}

// Get event details
$stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
$stmt->execute([$event_id]);
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    header('Location: events.php');
    exit();
}

// Create inheritance rules table if it doesn't exist
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS attendance_inheritance_rules (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            event_id INT(11) NOT NULL,
            rule_name VARCHAR(255) NOT NULL,
            rule_type ENUM('bottom_up', 'top_down', 'peer_to_peer', 'conditional') NOT NULL,
            source_criteria JSON NOT NULL,
            target_criteria JSON NOT NULL,
            inheritance_logic JSON NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            priority_order INT(11) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_event_id (event_id),
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS attendance_inheritance_log (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            event_id INT(11) NOT NULL,
            rule_id INT(11) NOT NULL,
            attendee_id VARCHAR(255) NOT NULL,
            attendee_type ENUM('member', 'guest') NOT NULL,
            source_session_id INT(11) DEFAULT NULL,
            target_session_id INT(11) DEFAULT NULL,
            inheritance_type VARCHAR(100) NOT NULL,
            old_status VARCHAR(50),
            new_status VARCHAR(50),
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            applied_by INT(11) DEFAULT NULL,
            INDEX idx_event_id (event_id),
            INDEX idx_rule_id (rule_id),
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
            FOREIGN KEY (rule_id) REFERENCES attendance_inheritance_rules(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
} catch (PDOException $e) {
    error_log("Error creating inheritance tables: " . $e->getMessage());
}

/**
 * Enhanced Attendance Inheritance Engine
 */
class AttendanceInheritanceEngine {
    private $pdo;
    private $event_id;
    private $user_id;
    
    public function __construct($pdo, $event_id, $user_id = null) {
        $this->pdo = $pdo;
        $this->event_id = $event_id;
        $this->user_id = $user_id;
    }
    
    /**
     * Apply all active inheritance rules for the event
     */
    public function applyAllRules() {
        $results = [
            'total_applied' => 0,
            'rules_executed' => 0,
            'attendees_affected' => [],
            'errors' => []
        ];

        // Start transaction for data integrity
        $this->pdo->beginTransaction();

        try {
            // Get all active rules ordered by priority
            $stmt = $this->pdo->prepare("
                SELECT * FROM attendance_inheritance_rules
                WHERE event_id = ? AND is_active = TRUE
                ORDER BY priority_order ASC, id ASC
            ");
            $stmt->execute([$this->event_id]);
            $rules = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($rules as $rule) {
                $rule_result = $this->applyRule($rule);
                $results['total_applied'] += $rule_result['applied_count'];
                $results['rules_executed']++;
                $results['attendees_affected'] = array_merge($results['attendees_affected'], $rule_result['affected_attendees']);

                if (!empty($rule_result['errors'])) {
                    $results['errors'] = array_merge($results['errors'], $rule_result['errors']);
                }
            }

            $results['attendees_affected'] = array_unique($results['attendees_affected']);

            // Commit transaction if successful
            $this->pdo->commit();

        } catch (Exception $e) {
            // Rollback transaction on error
            $this->pdo->rollback();
            $results['errors'][] = "Engine error: " . $e->getMessage();
        }

        return $results;
    }
    
    /**
     * Apply a specific inheritance rule
     */
    public function applyRule($rule) {
        $results = [
            'applied_count' => 0,
            'affected_attendees' => [],
            'errors' => []
        ];
        
        try {
            $source_criteria = json_decode($rule['source_criteria'], true);
            $target_criteria = json_decode($rule['target_criteria'], true);
            $inheritance_logic = json_decode($rule['inheritance_logic'], true);
            
            switch ($rule['rule_type']) {
                case 'bottom_up':
                    $results = $this->applyBottomUpRule($rule, $source_criteria, $target_criteria, $inheritance_logic);
                    break;
                    
                case 'top_down':
                    $results = $this->applyTopDownRule($rule, $source_criteria, $target_criteria, $inheritance_logic);
                    break;
                    
                case 'peer_to_peer':
                    $results = $this->applyPeerToPeerRule($rule, $source_criteria, $target_criteria, $inheritance_logic);
                    break;
                    
                case 'conditional':
                    $results = $this->applyConditionalRule($rule, $source_criteria, $target_criteria, $inheritance_logic);
                    break;
            }
            
        } catch (Exception $e) {
            $results['errors'][] = "Rule '{$rule['rule_name']}' error: " . $e->getMessage();
        }
        
        return $results;
    }
    
    /**
     * Bottom-up: Session attendance → Event attendance
     */
    private function applyBottomUpRule($rule, $source_criteria, $target_criteria, $inheritance_logic) {
        $results = ['applied_count' => 0, 'affected_attendees' => [], 'errors' => []];
        
        // Get attendees who meet session attendance criteria
        $min_sessions = $inheritance_logic['min_sessions'] ?? 1;
        $min_percentage = $inheritance_logic['min_percentage'] ?? 0;
        $session_types = $source_criteria['session_types'] ?? [];
        
        $session_type_filter = '';
        $params = [$this->event_id];
        
        if (!empty($session_types)) {
            $placeholders = str_repeat('?,', count($session_types) - 1) . '?';
            $session_type_filter = "AND es.session_type IN ($placeholders)";
            $params = array_merge($params, $session_types);
        }
        
        $stmt = $this->pdo->prepare("
            SELECT 
                COALESCE(sa.member_id, CONCAT('guest_', sa.id)) as attendee_id,
                CASE WHEN sa.member_id IS NOT NULL THEN 'member' ELSE 'guest' END as attendee_type,
                COUNT(DISTINCT sa.session_id) as sessions_registered,
                COUNT(DISTINCT CASE WHEN sa.attendance_status = 'attended' THEN sa.session_id END) as sessions_attended,
                ROUND((COUNT(DISTINCT CASE WHEN sa.attendance_status = 'attended' THEN sa.session_id END) / 
                       NULLIF(COUNT(DISTINCT sa.session_id), 0)) * 100, 1) as attendance_percentage
            FROM session_attendance sa
            JOIN event_sessions es ON sa.session_id = es.id
            WHERE es.event_id = ? $session_type_filter
            GROUP BY attendee_id, attendee_type
            HAVING sessions_attended >= ? AND attendance_percentage >= ?
        ");
        
        $params[] = $min_sessions;
        $params[] = $min_percentage;
        $stmt->execute($params);
        $qualified_attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Apply inheritance to event attendance
        foreach ($qualified_attendees as $attendee) {
            if ($attendee['attendee_type'] === 'member') {
                $stmt = $this->pdo->prepare("
                    UPDATE event_rsvps 
                    SET actually_attended = 1 
                    WHERE event_id = ? AND user_id = ? AND actually_attended != 1
                ");
                $stmt->execute([$this->event_id, $attendee['attendee_id']]);
                
                if ($stmt->rowCount() > 0) {
                    $this->logInheritance($rule['id'], $attendee['attendee_id'], $attendee['attendee_type'], 
                                        null, null, 'bottom_up_session_to_event', 'not_attended', 'attended');
                    $results['applied_count']++;
                    $results['affected_attendees'][] = $attendee['attendee_id'];
                }
            } else {
                // Handle guest
                $guest_id = str_replace('guest_', '', $attendee['attendee_id']);
                $stmt = $this->pdo->prepare("
                    UPDATE event_rsvps_guests 
                    SET actually_attended = 1 
                    WHERE event_id = ? AND id = ? AND actually_attended != 1
                ");
                $stmt->execute([$this->event_id, $guest_id]);
                
                if ($stmt->rowCount() > 0) {
                    $this->logInheritance($rule['id'], $attendee['attendee_id'], $attendee['attendee_type'], 
                                        null, null, 'bottom_up_session_to_event', 'not_attended', 'attended');
                    $results['applied_count']++;
                    $results['affected_attendees'][] = $attendee['attendee_id'];
                }
            }
        }
        
        return $results;
    }
    
    /**
     * Top-down: Event attendance → Session attendance
     */
    private function applyTopDownRule($rule, $source_criteria, $target_criteria, $inheritance_logic) {
        $results = ['applied_count' => 0, 'affected_attendees' => [], 'errors' => []];
        
        $target_session_types = $target_criteria['session_types'] ?? [];
        $target_status = $inheritance_logic['target_status'] ?? 'attended';
        
        // Get event attendees
        $stmt = $this->pdo->prepare("
            SELECT user_id as attendee_id, 'member' as attendee_type
            FROM event_rsvps 
            WHERE event_id = ? AND actually_attended = 1
            
            UNION ALL
            
            SELECT id as attendee_id, 'guest' as attendee_type
            FROM event_rsvps_guests 
            WHERE event_id = ? AND actually_attended = 1
        ");
        $stmt->execute([$this->event_id, $this->event_id]);
        $event_attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get target sessions
        $session_filter = '';
        $session_params = [$this->event_id];
        
        if (!empty($target_session_types)) {
            $placeholders = str_repeat('?,', count($target_session_types) - 1) . '?';
            $session_filter = "AND session_type IN ($placeholders)";
            $session_params = array_merge($session_params, $target_session_types);
        }
        
        $stmt = $this->pdo->prepare("
            SELECT id FROM event_sessions 
            WHERE event_id = ? AND status = 'active' $session_filter
        ");
        $stmt->execute($session_params);
        $target_sessions = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Apply inheritance
        foreach ($event_attendees as $attendee) {
            foreach ($target_sessions as $session_id) {
                if ($attendee['attendee_type'] === 'member') {
                    // Check if already has attendance record
                    $stmt = $this->pdo->prepare("
                        SELECT id, attendance_status FROM session_attendance 
                        WHERE session_id = ? AND member_id = ?
                    ");
                    $stmt->execute([$session_id, $attendee['attendee_id']]);
                    $existing = $stmt->fetch();
                    
                    if ($existing && $existing['attendance_status'] !== $target_status) {
                        // Update existing
                        $stmt = $this->pdo->prepare("
                            UPDATE session_attendance 
                            SET attendance_status = ?, attendance_date = CASE WHEN ? = 'attended' THEN NOW() ELSE attendance_date END
                            WHERE id = ?
                        ");
                        $stmt->execute([$target_status, $target_status, $existing['id']]);
                        
                        $this->logInheritance($rule['id'], $attendee['attendee_id'], $attendee['attendee_type'], 
                                            null, $session_id, 'top_down_event_to_session', $existing['attendance_status'], $target_status);
                        $results['applied_count']++;
                        $results['affected_attendees'][] = $attendee['attendee_id'];
                    } elseif (!$existing) {
                        // Create new record
                        $stmt = $this->pdo->prepare("
                            INSERT INTO session_attendance (session_id, member_id, attendance_status, attendance_date)
                            VALUES (?, ?, ?, CASE WHEN ? = 'attended' THEN NOW() ELSE NULL END)
                        ");
                        $stmt->execute([$session_id, $attendee['attendee_id'], $target_status, $target_status]);
                        
                        $this->logInheritance($rule['id'], $attendee['attendee_id'], $attendee['attendee_type'], 
                                            null, $session_id, 'top_down_event_to_session', 'not_registered', $target_status);
                        $results['applied_count']++;
                        $results['affected_attendees'][] = $attendee['attendee_id'];
                    }
                }
            }
        }
        
        return $results;
    }

    /**
     * Log inheritance action
     */
    private function logInheritance($rule_id, $attendee_id, $attendee_type, $source_session_id, $target_session_id, $inheritance_type, $old_status, $new_status) {
        $stmt = $this->pdo->prepare("
            INSERT INTO attendance_inheritance_log
            (event_id, rule_id, attendee_id, attendee_type, source_session_id, target_session_id, inheritance_type, old_status, new_status, applied_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$this->event_id, $rule_id, $attendee_id, $attendee_type, $source_session_id, $target_session_id, $inheritance_type, $old_status, $new_status, $this->user_id]);
    }

    /**
     * Peer-to-peer: Workshop A → Workshop B attendance
     */
    private function applyPeerToPeerRule($rule, $source_criteria, $target_criteria, $inheritance_logic) {
        $results = ['applied_count' => 0, 'affected_attendees' => [], 'errors' => []];

        $source_sessions = $source_criteria['session_ids'] ?? [];
        $target_sessions = $target_criteria['session_ids'] ?? [];
        $copy_status = $inheritance_logic['copy_status'] ?? 'attended';

        if (empty($source_sessions) || empty($target_sessions)) {
            $results['errors'][] = "Peer-to-peer rule requires source and target session IDs";
            return $results;
        }

        // Get attendees from source sessions
        $source_placeholders = str_repeat('?,', count($source_sessions) - 1) . '?';
        $stmt = $this->pdo->prepare("
            SELECT
                COALESCE(sa.member_id, CONCAT('guest_', sa.id)) as attendee_id,
                CASE WHEN sa.member_id IS NOT NULL THEN 'member' ELSE 'guest' END as attendee_type,
                sa.member_id,
                sa.guest_name,
                sa.guest_email
            FROM session_attendance sa
            WHERE sa.session_id IN ($source_placeholders)
            AND sa.attendance_status = ?
            GROUP BY attendee_id, attendee_type, sa.member_id, sa.guest_name, sa.guest_email
        ");
        $params = array_merge($source_sessions, [$copy_status]);
        $stmt->execute($params);
        $source_attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Apply to target sessions
        foreach ($source_attendees as $attendee) {
            foreach ($target_sessions as $target_session_id) {
                if ($attendee['attendee_type'] === 'member') {
                    // Check if already exists
                    $stmt = $this->pdo->prepare("
                        SELECT id, attendance_status FROM session_attendance
                        WHERE session_id = ? AND member_id = ?
                    ");
                    $stmt->execute([$target_session_id, $attendee['member_id']]);
                    $existing = $stmt->fetch();

                    if (!$existing) {
                        // Create new record
                        $stmt = $this->pdo->prepare("
                            INSERT INTO session_attendance (session_id, member_id, attendance_status, attendance_date)
                            VALUES (?, ?, ?, CASE WHEN ? = 'attended' THEN NOW() ELSE NULL END)
                        ");
                        $stmt->execute([$target_session_id, $attendee['member_id'], $copy_status, $copy_status]);

                        $this->logInheritance($rule['id'], $attendee['attendee_id'], $attendee['attendee_type'],
                                            $source_sessions[0], $target_session_id, 'peer_to_peer', 'not_registered', $copy_status);
                        $results['applied_count']++;
                        $results['affected_attendees'][] = $attendee['attendee_id'];
                    }
                }
            }
        }

        return $results;
    }

    /**
     * Conditional: Complex logic-based inheritance
     */
    private function applyConditionalRule($rule, $source_criteria, $target_criteria, $inheritance_logic) {
        $results = ['applied_count' => 0, 'affected_attendees' => [], 'errors' => []];

        $condition_type = $inheritance_logic['condition_type'] ?? '';

        switch ($condition_type) {
            case 'time_based':
                $results = $this->applyTimeBasedInheritance($rule, $source_criteria, $target_criteria, $inheritance_logic);
                break;

            case 'attendance_pattern':
                $results = $this->applyAttendancePatternInheritance($rule, $source_criteria, $target_criteria, $inheritance_logic);
                break;

            case 'session_correlation':
                $results = $this->applySessionCorrelationInheritance($rule, $source_criteria, $target_criteria, $inheritance_logic);
                break;

            default:
                $results['errors'][] = "Unknown conditional rule type: $condition_type";
        }

        return $results;
    }

    /**
     * Time-based conditional inheritance
     */
    private function applyTimeBasedInheritance($rule, $source_criteria, $target_criteria, $inheritance_logic) {
        $results = ['applied_count' => 0, 'affected_attendees' => [], 'errors' => []];

        $time_window = $inheritance_logic['time_window_hours'] ?? 2;
        $source_session_types = $source_criteria['session_types'] ?? [];
        $target_session_types = $target_criteria['session_types'] ?? [];

        // Find sessions within time window
        $source_filter = '';
        $target_filter = '';
        $params = [$this->event_id, $time_window];

        if (!empty($source_session_types)) {
            $source_placeholders = str_repeat('?,', count($source_session_types) - 1) . '?';
            $source_filter = "AND source_es.session_type IN ($source_placeholders)";
            $params = array_merge($params, $source_session_types);
        }

        if (!empty($target_session_types)) {
            $target_placeholders = str_repeat('?,', count($target_session_types) - 1) . '?';
            $target_filter = "AND target_es.session_type IN ($target_placeholders)";
            $params = array_merge($params, $target_session_types);
        }

        $stmt = $this->pdo->prepare("
            SELECT
                source_es.id as source_session_id,
                target_es.id as target_session_id,
                source_es.session_title as source_title,
                target_es.session_title as target_title
            FROM event_sessions source_es
            JOIN event_sessions target_es ON source_es.event_id = target_es.event_id
            WHERE source_es.event_id = ?
            AND ABS(TIMESTAMPDIFF(HOUR, source_es.start_datetime, target_es.start_datetime)) <= ?
            AND source_es.id != target_es.id
            $source_filter $target_filter
        ");
        $stmt->execute($params);
        $session_pairs = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Apply inheritance for each pair
        foreach ($session_pairs as $pair) {
            $peer_result = $this->applyPeerToPeerRule($rule,
                ['session_ids' => [$pair['source_session_id']]],
                ['session_ids' => [$pair['target_session_id']]],
                $inheritance_logic
            );

            $results['applied_count'] += $peer_result['applied_count'];
            $results['affected_attendees'] = array_merge($results['affected_attendees'], $peer_result['affected_attendees']);
            $results['errors'] = array_merge($results['errors'], $peer_result['errors']);
        }

        return $results;
    }
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'apply_inheritance_rules') {
            $engine = new AttendanceInheritanceEngine($pdo, $event_id, $_SESSION['user_id'] ?? null);
            $results = $engine->applyAllRules();
            
            $message = "Inheritance rules applied successfully! " . 
                      "Updated {$results['total_applied']} attendance records across {$results['rules_executed']} rules. " .
                      "Affected " . count($results['attendees_affected']) . " attendees.";
                      
            if (!empty($results['errors'])) {
                $error = "Some errors occurred: " . implode('; ', $results['errors']);
            }
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Function to create default inheritance rules
function createDefaultInheritanceRules($pdo, $event_id) {
    $default_rules = [
        [
            'rule_name' => 'Session to Event Attendance (50% threshold)',
            'rule_type' => 'bottom_up',
            'source_criteria' => json_encode(['session_types' => []]),
            'target_criteria' => json_encode(['target' => 'event']),
            'inheritance_logic' => json_encode(['min_sessions' => 1, 'min_percentage' => 50]),
            'priority_order' => 1
        ],
        [
            'rule_name' => 'Event to Core Sessions',
            'rule_type' => 'top_down',
            'source_criteria' => json_encode(['target' => 'event']),
            'target_criteria' => json_encode(['session_types' => ['worship', 'main_session']]),
            'inheritance_logic' => json_encode(['target_status' => 'attended']),
            'priority_order' => 2
        ],
        [
            'rule_name' => 'Workshop Cross-Attendance',
            'rule_type' => 'conditional',
            'source_criteria' => json_encode(['session_types' => ['workshop']]),
            'target_criteria' => json_encode(['session_types' => ['workshop']]),
            'inheritance_logic' => json_encode(['condition_type' => 'time_based', 'time_window_hours' => 2, 'copy_status' => 'attended']),
            'priority_order' => 3
        ]
    ];

    foreach ($default_rules as $rule) {
        $stmt = $pdo->prepare("
            INSERT INTO attendance_inheritance_rules
            (event_id, rule_name, rule_type, source_criteria, target_criteria, inheritance_logic, priority_order)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $event_id,
            $rule['rule_name'],
            $rule['rule_type'],
            $rule['source_criteria'],
            $rule['target_criteria'],
            $rule['inheritance_logic'],
            $rule['priority_order']
        ]);
    }
}

// Get existing rules
$stmt = $pdo->prepare("
    SELECT * FROM attendance_inheritance_rules
    WHERE event_id = ?
    ORDER BY priority_order ASC, id ASC
");
$stmt->execute([$event_id]);
$existing_rules = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get recent inheritance log
$stmt = $pdo->prepare("
    SELECT
        ail.*,
        air.rule_name,
        COALESCE(m.full_name, ail.attendee_id) as attendee_name
    FROM attendance_inheritance_log ail
    JOIN attendance_inheritance_rules air ON ail.rule_id = air.id
    LEFT JOIN members m ON ail.attendee_id = m.id AND ail.attendee_type = 'member'
    WHERE ail.event_id = ?
    ORDER BY ail.applied_at DESC
    LIMIT 50
");
$stmt->execute([$event_id]);
$inheritance_log = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'Attendance Inheritance Engine';
include 'includes/header.php';
?>

<style>
.inheritance-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.rule-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid;
    transition: all 0.2s;
}

.rule-card.bottom_up {
    border-left-color: #28a745;
}

.rule-card.top_down {
    border-left-color: #007bff;
}

.rule-card.peer_to_peer {
    border-left-color: #ffc107;
}

.rule-card.conditional {
    border-left-color: #6f42c1;
}

.inheritance-engine {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.log-item {
    padding: 0.75rem;
    border-bottom: 1px solid #eee;
    transition: all 0.2s;
}

.log-item:hover {
    background-color: #f8f9fa;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="inheritance-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="bi bi-diagram-3"></i> Attendance Inheritance Engine</h1>
                        <p class="mb-0">
                            <strong><?php echo htmlspecialchars($event['title']); ?></strong> •
                            <?php echo date('M j, Y', strtotime($event['event_date'])); ?>
                        </p>
                    </div>
                    <div>
                        <a href="advanced_bulk_attendance.php?event_id=<?php echo $event_id; ?>" class="btn btn-light me-2">
                            <i class="bi bi-diagram-3"></i> Bulk Operations
                        </a>
                        <a href="multi_session_dashboard.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-light">
                            <i class="bi bi-arrow-left"></i> Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Inheritance Engine Controls -->
            <div class="inheritance-engine">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5><i class="bi bi-magic"></i> Automated Attendance Inheritance</h5>
                        <p class="text-muted mb-0">
                            Apply intelligent rules to automatically sync attendance between events and sessions
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <form method="POST" class="d-inline me-2">
                            <input type="hidden" name="action" value="create_default_rules">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="bi bi-plus-circle"></i> Create Default Rules
                            </button>
                        </form>
                        <a href="test_inheritance_system.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-success me-2">
                            <i class="bi bi-check2-all"></i> Test System
                        </a>
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="action" value="apply_inheritance_rules">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-play-circle"></i> Apply All Rules
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Active Rules -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-list-check"></i> Active Inheritance Rules
                                <span class="badge bg-primary ms-2"><?php echo count($existing_rules); ?></span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($existing_rules)): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-list-check" style="font-size: 3rem;"></i>
                                    <h6 class="mt-2">No Rules Configured</h6>
                                    <p>Create default rules to get started with attendance inheritance</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($existing_rules as $rule): ?>
                                    <div class="rule-card <?php echo $rule['rule_type']; ?>">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">
                                                    <?php
                                                    $icon = $rule['rule_type'] === 'bottom_up' ? 'bi-arrow-up' :
                                                           ($rule['rule_type'] === 'top_down' ? 'bi-arrow-down' :
                                                           ($rule['rule_type'] === 'peer_to_peer' ? 'bi-arrow-left-right' : 'bi-gear'));
                                                    ?>
                                                    <i class="bi <?php echo $icon; ?>"></i>
                                                    <?php echo htmlspecialchars($rule['rule_name']); ?>
                                                </h6>
                                                <p class="text-muted mb-2">
                                                    <strong>Type:</strong> <?php echo ucfirst(str_replace('_', ' ', $rule['rule_type'])); ?>
                                                </p>
                                                <small class="text-muted">
                                                    Priority: <?php echo $rule['priority_order']; ?> •
                                                    Status: <?php echo $rule['is_active'] ? 'Active' : 'Inactive'; ?>
                                                </small>
                                            </div>
                                            <div>
                                                <span class="badge bg-<?php echo $rule['is_active'] ? 'success' : 'secondary'; ?>">
                                                    <?php echo $rule['is_active'] ? 'Active' : 'Inactive'; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity Log -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="bi bi-clock-history"></i> Recent Inheritance Activity
                                <span class="badge bg-info ms-2"><?php echo count($inheritance_log); ?></span>
                            </h6>
                        </div>
                        <div class="card-body p-0" style="max-height: 400px; overflow-y: auto;">
                            <?php if (empty($inheritance_log)): ?>
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-clock-history" style="font-size: 2rem;"></i>
                                    <p class="mt-2">No activity yet</p>
                                    <small>Apply rules to see inheritance activity</small>
                                </div>
                            <?php else: ?>
                                <?php foreach ($inheritance_log as $log): ?>
                                    <div class="log-item">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <small class="fw-bold"><?php echo htmlspecialchars($log['attendee_name']); ?></small>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars($log['rule_name']); ?>
                                                </small>
                                                <br>
                                                <small class="text-success">
                                                    <?php echo $log['old_status']; ?> → <?php echo $log['new_status']; ?>
                                                </small>
                                            </div>
                                            <div>
                                                <small class="text-muted">
                                                    <?php echo date('g:i A', strtotime($log['applied_at'])); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rule Types Explanation -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-info-circle"></i> Inheritance Rule Types</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="rule-card bottom_up">
                                        <h6><i class="bi bi-arrow-up"></i> Bottom-Up</h6>
                                        <p class="small mb-0">Session attendance → Event attendance</p>
                                        <small class="text-muted">Mark event as attended if attended enough sessions</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="rule-card top_down">
                                        <h6><i class="bi bi-arrow-down"></i> Top-Down</h6>
                                        <p class="small mb-0">Event attendance → Session attendance</p>
                                        <small class="text-muted">Mark sessions as attended if attended event</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="rule-card peer_to_peer">
                                        <h6><i class="bi bi-arrow-left-right"></i> Peer-to-Peer</h6>
                                        <p class="small mb-0">Session A → Session B</p>
                                        <small class="text-muted">Copy attendance between related sessions</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="rule-card conditional">
                                        <h6><i class="bi bi-gear"></i> Conditional</h6>
                                        <p class="small mb-0">Complex logic-based rules</p>
                                        <small class="text-muted">Time-based, pattern-based inheritance</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh log every 30 seconds
setInterval(function() {
    // Only refresh if rules were recently applied
    const lastActivity = document.querySelector('.log-item');
    if (lastActivity) {
        const activityTime = lastActivity.querySelector('.text-muted').textContent;
        // Simple check - in production, you'd want proper timestamp comparison
        if (activityTime.includes('minute') || activityTime.includes('second')) {
            window.location.reload();
        }
    }
}, 30000);

// Confirmation for applying rules
document.querySelector('form[action*="apply_inheritance_rules"]')?.addEventListener('submit', function(e) {
    if (!confirm('This will apply all active inheritance rules and may update many attendance records. Continue?')) {
        e.preventDefault();
    }
});
</script>

<?php include 'includes/footer.php'; ?>
                if ($attendee['attendee_type'] === 'member') {
                    // Check if already exists
                    $stmt = $this->pdo->prepare("
                        SELECT id, attendance_status FROM session_attendance
                        WHERE session_id = ? AND member_id = ?
                    ");
                    $stmt->execute([$target_session_id, $attendee['member_id']]);
                    $existing = $stmt->fetch();

                    if (!$existing) {
                        // Create new record
                        $stmt = $this->pdo->prepare("
                            INSERT INTO session_attendance (session_id, member_id, attendance_status, attendance_date)
                            VALUES (?, ?, ?, CASE WHEN ? = 'attended' THEN NOW() ELSE NULL END)
                        ");
                        $stmt->execute([$target_session_id, $attendee['member_id'], $copy_status, $copy_status]);

                        $this->logInheritance($rule['id'], $attendee['attendee_id'], $attendee['attendee_type'],
                                            $source_sessions[0], $target_session_id, 'peer_to_peer', 'not_registered', $copy_status);
                        $results['applied_count']++;
                        $results['affected_attendees'][] = $attendee['attendee_id'];
                    }
                } else {
                    // Handle guest
                    $stmt = $this->pdo->prepare("
                        SELECT id, attendance_status FROM session_attendance
                        WHERE session_id = ? AND guest_name = ?
                    ");
                    $stmt->execute([$target_session_id, $attendee['guest_name']]);
                    $existing = $stmt->fetch();

                    if (!$existing) {
                        // Create new record
                        $stmt = $this->pdo->prepare("
                            INSERT INTO session_attendance (session_id, guest_name, guest_email, attendance_status, attendance_date)
                            VALUES (?, ?, ?, ?, CASE WHEN ? = 'attended' THEN NOW() ELSE NULL END)
                        ");
                        $stmt->execute([$target_session_id, $attendee['guest_name'], $attendee['guest_email'], $copy_status, $copy_status]);

                        $this->logInheritance($rule['id'], $attendee['attendee_id'], $attendee['attendee_type'],
                                            $source_sessions[0], $target_session_id, 'peer_to_peer', 'not_registered', $copy_status);
                        $results['applied_count']++;
                        $results['affected_attendees'][] = $attendee['attendee_id'];
                    }
                }
            }
        }

        return $results;
    }

    /**
     * Conditional: Complex logic-based inheritance
     */
    private function applyConditionalRule($rule, $source_criteria, $target_criteria, $inheritance_logic) {
        $results = ['applied_count' => 0, 'affected_attendees' => [], 'errors' => []];

        $condition_type = $inheritance_logic['condition_type'] ?? '';

        switch ($condition_type) {
            case 'time_based':
                $results = $this->applyTimeBasedInheritance($rule, $source_criteria, $target_criteria, $inheritance_logic);
                break;

            case 'attendance_pattern':
                $results = $this->applyAttendancePatternInheritance($rule, $source_criteria, $target_criteria, $inheritance_logic);
                break;

            case 'session_correlation':
                $results = $this->applySessionCorrelationInheritance($rule, $source_criteria, $target_criteria, $inheritance_logic);
                break;

            default:
                $results['errors'][] = "Unknown conditional rule type: $condition_type";
        }

        return $results;
    }

    /**
     * Time-based conditional inheritance
     */
    private function applyTimeBasedInheritance($rule, $source_criteria, $target_criteria, $inheritance_logic) {
        $results = ['applied_count' => 0, 'affected_attendees' => [], 'errors' => []];

        $time_window = $inheritance_logic['time_window_hours'] ?? 2;
        $source_session_types = $source_criteria['session_types'] ?? [];
        $target_session_types = $target_criteria['session_types'] ?? [];

        // Find sessions within time window
        $source_filter = '';
        $target_filter = '';
        $params = [$this->event_id, $time_window];

        if (!empty($source_session_types)) {
            $source_placeholders = str_repeat('?,', count($source_session_types) - 1) . '?';
            $source_filter = "AND source_es.session_type IN ($source_placeholders)";
            $params = array_merge($params, $source_session_types);
        }

        if (!empty($target_session_types)) {
            $target_placeholders = str_repeat('?,', count($target_session_types) - 1) . '?';
            $target_filter = "AND target_es.session_type IN ($target_placeholders)";
            $params = array_merge($params, $target_session_types);
        }

        $stmt = $this->pdo->prepare("
            SELECT
                source_es.id as source_session_id,
                target_es.id as target_session_id,
                source_es.session_title as source_title,
                target_es.session_title as target_title
            FROM event_sessions source_es
            JOIN event_sessions target_es ON source_es.event_id = target_es.event_id
            WHERE source_es.event_id = ?
            AND ABS(TIMESTAMPDIFF(HOUR, source_es.start_datetime, target_es.start_datetime)) <= ?
            AND source_es.id != target_es.id
            $source_filter $target_filter
        ");
        $stmt->execute($params);
        $session_pairs = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Apply inheritance for each pair
        foreach ($session_pairs as $pair) {
            $peer_result = $this->applyPeerToPeerRule($rule,
                ['session_ids' => [$pair['source_session_id']]],
                ['session_ids' => [$pair['target_session_id']]],
                $inheritance_logic
            );

            $results['applied_count'] += $peer_result['applied_count'];
            $results['affected_attendees'] = array_merge($results['affected_attendees'], $peer_result['affected_attendees']);
            $results['errors'] = array_merge($results['errors'], $peer_result['errors']);
        }

        return $results;
    }
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';

        if ($action === 'apply_inheritance_rules') {
            $engine = new AttendanceInheritanceEngine($pdo, $event_id, $_SESSION['user_id'] ?? null);
            $results = $engine->applyAllRules();

            $message = "Inheritance rules applied successfully! " .
                      "Updated {$results['total_applied']} attendance records across {$results['rules_executed']} rules. " .
                      "Affected " . count($results['attendees_affected']) . " attendees.";

            if (!empty($results['errors'])) {
                $error = "Some errors occurred: " . implode('; ', $results['errors']);
            }

        } elseif ($action === 'create_default_rules') {
            createDefaultInheritanceRules($pdo, $event_id);
            $message = "Default inheritance rules created successfully!";
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Function to create default inheritance rules
function createDefaultInheritanceRules($pdo, $event_id) {
    $default_rules = [
        [
            'rule_name' => 'Session to Event Attendance (50% threshold)',
            'rule_type' => 'bottom_up',
            'source_criteria' => json_encode(['session_types' => []]),
            'target_criteria' => json_encode(['target' => 'event']),
            'inheritance_logic' => json_encode(['min_sessions' => 1, 'min_percentage' => 50]),
            'priority_order' => 1
        ],
        [
            'rule_name' => 'Event to Core Sessions',
            'rule_type' => 'top_down',
            'source_criteria' => json_encode(['target' => 'event']),
            'target_criteria' => json_encode(['session_types' => ['worship', 'main_session']]),
            'inheritance_logic' => json_encode(['target_status' => 'attended']),
            'priority_order' => 2
        ],
        [
            'rule_name' => 'Workshop Cross-Attendance',
            'rule_type' => 'conditional',
            'source_criteria' => json_encode(['session_types' => ['workshop']]),
            'target_criteria' => json_encode(['session_types' => ['workshop']]),
            'inheritance_logic' => json_encode(['condition_type' => 'time_based', 'time_window_hours' => 2, 'copy_status' => 'attended']),
            'priority_order' => 3
        ]
    ];

    foreach ($default_rules as $rule) {
        $stmt = $pdo->prepare("
            INSERT INTO attendance_inheritance_rules
            (event_id, rule_name, rule_type, source_criteria, target_criteria, inheritance_logic, priority_order)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $event_id,
            $rule['rule_name'],
            $rule['rule_type'],
            $rule['source_criteria'],
            $rule['target_criteria'],
            $rule['inheritance_logic'],
            $rule['priority_order']
        ]);
    }
}

// Get existing rules
$stmt = $pdo->prepare("
    SELECT * FROM attendance_inheritance_rules
    WHERE event_id = ?
    ORDER BY priority_order ASC, id ASC
");
$stmt->execute([$event_id]);
$existing_rules = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get recent inheritance log
$stmt = $pdo->prepare("
    SELECT
        ail.*,
        air.rule_name,
        COALESCE(m.full_name, ail.attendee_id) as attendee_name
    FROM attendance_inheritance_log ail
    JOIN attendance_inheritance_rules air ON ail.rule_id = air.id
    LEFT JOIN members m ON ail.attendee_id = m.id AND ail.attendee_type = 'member'
    WHERE ail.event_id = ?
    ORDER BY ail.applied_at DESC
    LIMIT 50
");
$stmt->execute([$event_id]);
$inheritance_log = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'Attendance Inheritance Engine';
include 'includes/header.php';
?>
