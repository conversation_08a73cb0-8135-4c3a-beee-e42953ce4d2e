<?php
/**
 * Test Login Process - Simulate admin login and check redirection
 */

require_once '../church/config.php';
require_once '../church/classes/SecurityManager.php';

echo "<h1>Admin Login Test</h1>";

// Simulate login process
$username = 'admin';
$password = 'admin123';

try {
    // Initialize SecurityManager
    $security = new SecurityManager($pdo);
    
    echo "<h2>1. Finding Admin User</h2>";
    
    // Find admin user
    $stmt = $pdo->prepare("SELECT id, username, password, full_name FROM admins WHERE username = ?");
    $stmt->execute([$username]);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p>✓ Admin user found: " . $admin['username'] . " (ID: " . $admin['id'] . ")</p>";
        
        // Verify password
        echo "<h2>2. Password Verification</h2>";
        if ($security->verifyPassword($password, $admin['password'])) {
            echo "<p>✓ Password verification successful</p>";
            
            // Simulate session setup
            session_start();
            $_SESSION['admin_id'] = $admin['id'];
            $_SESSION['admin_name'] = $admin['full_name'];
            $_SESSION['admin_username'] = $admin['username'];
            
            echo "<h2>3. RBAC System Check</h2>";
            
            // Test RBAC system
            require_once '../church/admin/includes/rbac_access_control.php';
            $rbac = new RBACAccessControl($pdo, $admin['id']);
            
            echo "<p><strong>Primary Role:</strong> " . ($rbac->getPrimaryRole() ?: 'None') . "</p>";
            echo "<p><strong>Has super_admin role:</strong> " . ($rbac->hasRole('super_admin') ? 'Yes' : 'No') . "</p>";
            echo "<p><strong>Default Dashboard:</strong> " . $rbac->getDefaultDashboard() . "</p>";
            
            echo "<h2>4. Dashboard Redirection Test</h2>";
            
            // Test route protection
            require_once '../church/admin/includes/route_protection.php';
            $dashboard = getUserDefaultDashboard();
            echo "<p><strong>Redirect Dashboard:</strong> " . $dashboard . "</p>";
            
            // Check if super admin dashboard exists
            $dashboard_path = '../church/admin/' . $dashboard;
            if (file_exists($dashboard_path)) {
                echo "<p>✓ Dashboard file exists: " . $dashboard_path . "</p>";
            } else {
                echo "<p>✗ Dashboard file missing: " . $dashboard_path . "</p>";
            }
            
            echo "<h2>5. Sidebar Items Test</h2>";
            $sidebar_items = $rbac->getSidebarItems();
            echo "<p><strong>Number of sidebar items:</strong> " . count($sidebar_items) . "</p>";
            
            if (!empty($sidebar_items)) {
                echo "<ul>";
                foreach ($sidebar_items as $key => $item) {
                    if (isset($item['items'])) {
                        echo "<li><strong>" . $item['title'] . "</strong> (Section with " . count($item['items']) . " items)</li>";
                    } else {
                        echo "<li>" . $item['title'] . " (" . $item['url'] . ")</li>";
                    }
                }
                echo "</ul>";
            }
            
        } else {
            echo "<p>✗ Password verification failed</p>";
        }
    } else {
        echo "<p>✗ Admin user not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p><strong>✗ Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
