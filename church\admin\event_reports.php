<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$error = '';

// Handle print view generation
if (isset($_GET['action']) && $_GET['action'] === 'print_report' && isset($_GET['event_id'])) {
    $event_id = (int)$_GET['event_id'];
    generatePrintView($event_id);
    exit();
}

// Handle detailed attendance report generation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'generate_detailed_report':
            $event_id = (int)$_POST['event_id'];

            if ($event_id) {
                // Redirect to show the detailed report on the same page
                header("Location: event_reports.php?view=detailed&event_id=" . $event_id);
                exit();
            } else {
                $error = "Please select an event to generate a detailed report.";
            }
            break;
    }
}

// Function to generate print view
function generatePrintView($event_id) {
    global $pdo;

    // Get event details
    $event_stmt = $pdo->prepare("
        SELECT id, title, event_date, location, description, max_attendees
        FROM events
        WHERE id = ?
    ");
    $event_stmt->execute([$event_id]);
    $event = $event_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$event) {
        echo "Event not found.";
        return;
    }

    // Get attendees data
    $attendees_data = getEventAttendeesData($event_id);

    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>Attendance Report - <?= htmlspecialchars($event['title']) ?></title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                color: #333;
                line-height: 1.4;
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #333;
                padding-bottom: 20px;
            }
            .header h1 {
                margin: 0;
                color: #333;
                font-size: 24px;
            }
            .event-details {
                background: #f8f9fa;
                padding: 15px;
                margin-bottom: 20px;
                border-radius: 5px;
            }
            .stats {
                display: flex;
                justify-content: space-around;
                margin-bottom: 20px;
                text-align: center;
            }
            .stat-item {
                padding: 10px;
                background: #e9ecef;
                border-radius: 5px;
                min-width: 100px;
            }
            .stat-number {
                font-size: 24px;
                font-weight: bold;
                color: #007bff;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
                font-size: 12px;
            }
            th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            .member { background-color: #e8f5e8; }
            .guest { background-color: #fff3cd; }
            .attended { background-color: #d4edda; }
            .not-attended { background-color: #f8d7da; }
            @media print {
                body { margin: 0; }
                .no-print { display: none; }
                .stats { display: block; }
                .stat-item { display: inline-block; margin: 5px; }
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Event Attendance Report</h1>
            <h2><?= htmlspecialchars($event['title']) ?></h2>
            <p><strong>Date:</strong> <?= date('F j, Y \a\t g:i A', strtotime($event['event_date'])) ?></p>
            <p><strong>Location:</strong> <?= htmlspecialchars($event['location']) ?></p>
            <p><strong>Generated:</strong> <?= date('F j, Y \a\t g:i A') ?></p>
        </div>

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number"><?= $attendees_data['stats']['total'] ?></div>
                <div>Total RSVPs</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= $attendees_data['stats']['members'] ?></div>
                <div>Members</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= $attendees_data['stats']['guests'] ?></div>
                <div>Guests</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= $attendees_data['stats']['attending'] ?></div>
                <div>Attending</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= $attendees_data['stats']['actually_attended'] ?></div>
                <div>Actually Attended</div>
            </div>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Type</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>RSVP Status</th>
                    <th>Actually Attended</th>
                    <th>RSVP Date</th>
                    <th>Notes</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($attendees_data['attendees'] as $attendee): ?>
                <tr class="<?= $attendee['attendee_type'] ?> <?= $attendee['actually_attended'] == 1 ? 'attended' : ($attendee['actually_attended'] === '0' ? 'not-attended' : '') ?>">
                    <td><?= htmlspecialchars($attendee['full_name']) ?></td>
                    <td><?= ucfirst($attendee['attendee_type']) ?></td>
                    <td><?= htmlspecialchars($attendee['email']) ?></td>
                    <td><?= htmlspecialchars($attendee['phone_number']) ?></td>
                    <td><?= ucfirst(str_replace('_', ' ', $attendee['status'])) ?></td>
                    <td>
                        <?php if ($attendee['actually_attended'] === null): ?>
                            Not Marked
                        <?php elseif ($attendee['actually_attended'] == 1): ?>
                            ✓ Yes
                        <?php else: ?>
                            ✗ No
                        <?php endif; ?>
                    </td>
                    <td><?= date('M j, Y g:i A', strtotime($attendee['rsvp_date'])) ?></td>
                    <td><?= htmlspecialchars($attendee['notes']) ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <script>
            window.onload = function() {
                window.print();
            };
        </script>
    </body>
    </html>
    <?php
}

// Function to get event attendees data
function getEventAttendeesData($event_id) {
    global $pdo;

    // Ensure the actually_attended column exists in both tables
    try {
        $pdo->exec("ALTER TABLE event_rsvps ADD COLUMN actually_attended TINYINT(1) DEFAULT NULL");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    try {
        $pdo->exec("ALTER TABLE event_rsvps_guests ADD COLUMN actually_attended TINYINT(1) DEFAULT NULL");
    } catch (PDOException $e) {
        // Column might already exist, ignore error
    }

    // Check if event_rsvps table uses user_id or member_id, and rsvp_date or created_at
    $columns_stmt = $pdo->query("SHOW COLUMNS FROM event_rsvps");
    $columns = $columns_stmt->fetchAll(PDO::FETCH_COLUMN);
    $member_id_column = in_array('user_id', $columns) ? 'user_id' : 'member_id';
    $date_column = in_array('rsvp_date', $columns) ? 'rsvp_date' : 'created_at';

    // Get member attendees
    $member_query = "
        SELECT
            er.id as rsvp_id,
            er.$member_id_column as member_id,
            er.status,
            er.notes,
            er.actually_attended,
            er.$date_column as rsvp_date,
            m.full_name,
            m.email,
            m.phone_number,
            'member' as attendee_type
        FROM event_rsvps er
        JOIN members m ON er.$member_id_column = m.id
        WHERE er.event_id = ?
        ORDER BY m.full_name ASC
    ";

    $member_stmt = $pdo->prepare($member_query);
    $member_stmt->execute([$event_id]);
    $member_attendees = $member_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get guest attendees
    $guest_query = "
        SELECT
            erg.id as rsvp_id,
            NULL as member_id,
            erg.status,
            erg.special_requirements as notes,
            erg.actually_attended,
            erg.created_at as rsvp_date,
            erg.guest_name as full_name,
            erg.guest_email as email,
            erg.guest_phone as phone_number,
            'guest' as attendee_type
        FROM event_rsvps_guests erg
        WHERE erg.event_id = ?
        ORDER BY erg.guest_name ASC
    ";

    $guest_stmt = $pdo->prepare($guest_query);
    $guest_stmt->execute([$event_id]);
    $guest_attendees = $guest_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Combine all attendees
    $all_attendees = array_merge($member_attendees, $guest_attendees);

    // Calculate statistics
    $total_attendees = count($all_attendees);
    $member_count = count($member_attendees);
    $guest_count = count($guest_attendees);
    $attending_count = count(array_filter($all_attendees, function($a) { return $a['status'] === 'attending'; }));
    $actually_attended_count = count(array_filter($all_attendees, function($a) { return $a['actually_attended'] == 1; }));

    return [
        'attendees' => $all_attendees,
        'stats' => [
            'total' => $total_attendees,
            'members' => $member_count,
            'guests' => $guest_count,
            'attending' => $attending_count,
            'actually_attended' => $actually_attended_count
        ]
    ];
}

// Get current view
$current_view = $_GET['view'] ?? 'main';
$selected_event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

// Set page variables
$page_title = "Event Reports";
$page_header = "Event Reports";
$page_description = "Generate comprehensive event attendance reports and analytics.";

// Include header
include 'includes/header.php';

// Get events for dropdown
$events_stmt = $pdo->query("SELECT id, title, event_date FROM events ORDER BY event_date DESC");
$events = $events_stmt->fetchAll(PDO::FETCH_ASSOC);

// If showing detailed view, get the event data
$detailed_report_data = null;
if ($current_view === 'detailed' && $selected_event_id) {
    $detailed_report_data = getEventAttendeesData($selected_event_id);

    // Get event details
    $event_stmt = $pdo->prepare("SELECT id, title, event_date, location, description, max_attendees FROM events WHERE id = ?");
    $event_stmt->execute([$selected_event_id]);
    $selected_event = $event_stmt->fetch(PDO::FETCH_ASSOC);
}
?>

<?php if ($current_view === 'main'): ?>
<!-- Main Reports Dashboard -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i> Event Reports Dashboard
                </h5>
            </div>
            <div class="card-body">
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Quick Report Generation -->
                    <div class="col-md-6">
                        <h6>Quick Report Generation</h6>
                        <form method="POST" action="" class="mb-3">
                            <input type="hidden" name="action" value="generate_detailed_report">
                            <div class="mb-3">
                                <label for="event_id" class="form-label">Select Event for Detailed Report</label>
                                <select class="form-select" id="event_id" name="event_id" required>
                                    <option value="">Choose an event...</option>
                                    <?php foreach ($events as $event): ?>
                                        <option value="<?= $event['id'] ?>">
                                            <?= htmlspecialchars($event['title']) ?> - <?= date('M j, Y', strtotime($event['event_date'])) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-file-earmark-text"></i> Generate Detailed Report
                            </button>
                        </form>
                    </div>

                    <!-- Recent Events Summary -->
                    <div class="col-md-6">
                        <h6>Recent Events Summary</h6>
                        <div class="list-group">
                            <?php
                            $recent_events = array_slice($events, 0, 5);
                            foreach ($recent_events as $event):
                                // Get quick stats for this event
                                $stats_stmt = $pdo->prepare("
                                    SELECT
                                        (SELECT COUNT(*) FROM event_rsvps WHERE event_id = ? AND status = 'attending') +
                                        (SELECT COUNT(*) FROM event_rsvps_guests WHERE event_id = ? AND status = 'attending') as attending_count
                                ");
                                $stats_stmt->execute([$event['id'], $event['id']]);
                                $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);
                            ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><?= htmlspecialchars($event['title']) ?></strong><br>
                                    <small class="text-muted"><?= date('M j, Y g:i A', strtotime($event['event_date'])) ?></small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-primary"><?= $stats['attending_count'] ?> attending</span><br>
                                    <a href="?view=detailed&event_id=<?= $event['id'] ?>" class="btn btn-sm btn-outline-primary mt-1">
                                        View Details
                                    </a>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php elseif ($current_view === 'detailed' && $selected_event_id && $detailed_report_data): ?>
<!-- Detailed Event Report -->
<div class="row mb-3">
    <div class="col-md-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="event_reports.php">Event Reports</a></li>
                <li class="breadcrumb-item active">Detailed Report</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar-event"></i> <?= htmlspecialchars($selected_event['title']) ?>
                    </h5>
                    <small class="text-muted">
                        <?= date('F j, Y \a\t g:i A', strtotime($selected_event['event_date'])) ?>
                        <?php if ($selected_event['location']): ?>
                            • <?= htmlspecialchars($selected_event['location']) ?>
                        <?php endif; ?>
                    </small>
                </div>
                <div>
                    <a href="?action=print_report&event_id=<?= $selected_event_id ?>"
                       target="_blank" class="btn btn-outline-primary">
                        <i class="bi bi-printer"></i> Print Report
                    </a>
                    <a href="event_reports.php" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Reports
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Statistics Summary -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="text-center p-3 bg-light rounded">
                            <div class="h3 text-primary mb-0"><?= $detailed_report_data['stats']['total'] ?></div>
                            <small class="text-muted">Total RSVPs</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="text-center p-3 bg-light rounded">
                            <div class="h3 text-success mb-0"><?= $detailed_report_data['stats']['members'] ?></div>
                            <small class="text-muted">Members</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="text-center p-3 bg-light rounded">
                            <div class="h3 text-warning mb-0"><?= $detailed_report_data['stats']['guests'] ?></div>
                            <small class="text-muted">Guests</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="text-center p-3 bg-light rounded">
                            <div class="h3 text-info mb-0"><?= $detailed_report_data['stats']['attending'] ?></div>
                            <small class="text-muted">Attending</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="text-center p-3 bg-light rounded">
                            <div class="h3 text-primary mb-0"><?= $detailed_report_data['stats']['actually_attended'] ?></div>
                            <small class="text-muted">Actually Attended</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="text-center p-3 bg-light rounded">
                            <?php
                            $attendance_rate = $detailed_report_data['stats']['attending'] > 0
                                ? round(($detailed_report_data['stats']['actually_attended'] / $detailed_report_data['stats']['attending']) * 100, 1)
                                : 0;
                            ?>
                            <div class="h3 text-success mb-0"><?= $attendance_rate ?>%</div>
                            <small class="text-muted">Attendance Rate</small>
                        </div>
                    </div>
                </div>

                <!-- Attendees Table -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>RSVP Status</th>
                                <th>Actually Attended</th>
                                <th>RSVP Date</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($detailed_report_data['attendees'])): ?>
                            <tr>
                                <td colspan="8" class="text-center text-muted py-4">
                                    <i class="bi bi-inbox"></i> No RSVPs found for this event.
                                </td>
                            </tr>
                            <?php else: ?>
                                <?php foreach ($detailed_report_data['attendees'] as $attendee): ?>
                                <tr class="<?= $attendee['attendee_type'] === 'member' ? 'table-success' : 'table-warning' ?>">
                                    <td>
                                        <strong><?= htmlspecialchars($attendee['full_name']) ?></strong>
                                        <?php if ($attendee['attendee_type'] === 'member'): ?>
                                            <span class="badge bg-success ms-1">Member</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning ms-1">Guest</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= ucfirst($attendee['attendee_type']) ?></td>
                                    <td><?= htmlspecialchars($attendee['email']) ?></td>
                                    <td><?= htmlspecialchars($attendee['phone_number']) ?></td>
                                    <td>
                                        <?php
                                        $status_class = '';
                                        switch ($attendee['status']) {
                                            case 'attending':
                                                $status_class = 'bg-success';
                                                break;
                                            case 'not_attending':
                                                $status_class = 'bg-danger';
                                                break;
                                            case 'maybe':
                                                $status_class = 'bg-warning';
                                                break;
                                        }
                                        ?>
                                        <span class="badge <?= $status_class ?>"><?= ucfirst(str_replace('_', ' ', $attendee['status'])) ?></span>
                                    </td>
                                    <td>
                                        <?php if ($attendee['actually_attended'] === null): ?>
                                            <span class="text-muted">Not Marked</span>
                                        <?php elseif ($attendee['actually_attended'] == 1): ?>
                                            <span class="text-success"><i class="bi bi-check-circle-fill"></i> Yes</span>
                                        <?php else: ?>
                                            <span class="text-danger"><i class="bi bi-x-circle-fill"></i> No</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= date('M j, Y g:i A', strtotime($attendee['rsvp_date'])) ?></td>
                                    <td><?= htmlspecialchars($attendee['notes']) ?></td>
                                </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php endif; ?>

<?php include 'includes/footer.php'; ?>
