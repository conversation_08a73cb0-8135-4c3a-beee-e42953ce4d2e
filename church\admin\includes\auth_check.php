<?php
/**
 * Simple Authentication Check
 * Ensures user is logged in and has appropriate access
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_type'])) {
    // Redirect to login page
    header('Location: ../login.php');
    exit();
}

// Basic access control - ensure user has admin privileges
if ($_SESSION['user_type'] !== 'admin' && $_SESSION['user_type'] !== 'super_admin') {
    // Check if user has any admin roles through RBAC
    if (file_exists('includes/rbac_access_control.php')) {
        require_once 'includes/rbac_access_control.php';
        
        try {
            $rbac = new RBACAccessControl($pdo, $_SESSION['user_id']);
            
            // Check if user has any admin-level permissions
            $admin_roles = ['super_admin', 'limited_admin', 'event_coordinator', 'session_moderator'];
            $has_admin_access = false;
            
            foreach ($admin_roles as $role) {
                if ($rbac->hasRole($role)) {
                    $has_admin_access = true;
                    break;
                }
            }
            
            if (!$has_admin_access) {
                header('Location: ../login.php?error=access_denied');
                exit();
            }
        } catch (Exception $e) {
            // If RBAC fails, fall back to basic session check
            if ($_SESSION['user_type'] !== 'admin') {
                header('Location: ../login.php?error=access_denied');
                exit();
            }
        }
    } else {
        // No RBAC system, use basic session check
        header('Location: ../login.php?error=access_denied');
        exit();
    }
}

// Set user info for use in pages
$current_user_id = $_SESSION['user_id'];
$current_user_type = $_SESSION['user_type'];
$current_user_name = $_SESSION['user_name'] ?? 'Admin User';

// Optional: Set timezone if available
if (isset($_SESSION['timezone'])) {
    date_default_timezone_set($_SESSION['timezone']);
} else {
    date_default_timezone_set('America/New_York'); // Default timezone
}
?>
