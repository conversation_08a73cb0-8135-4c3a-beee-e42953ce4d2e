<?php
/**
 * Enhanced RBAC Access Control with Granular Permissions
 * This file provides functions for checking individual user permissions
 * in addition to role-based permissions
 */

// Include database connection
require_once dirname(__DIR__) . '/../config.php';

/**
 * Check if a user has a specific permission (either through role or individual assignment)
 * @param int $user_id The user ID to check
 * @param string $permission_key The permission key to check
 * @return bool True if user has permission, false otherwise
 */
function hasUserPermission($user_id, $permission_key) {
    global $pdo;

    // Super admins have all permissions - bypass all checks
    if (isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === 'super_admin') {
        return true;
    }

    try {
        // First check individual permissions
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM user_individual_permissions uip
            JOIN granular_permissions gp ON uip.permission_id = gp.id
            WHERE uip.user_id = ? 
            AND gp.permission_key = ? 
            AND uip.is_active = 1 
            AND gp.is_active = 1
        ");
        $stmt->execute([$user_id, $permission_key]);
        
        if ($stmt->fetchColumn() > 0) {
            return true;
        }
        
        // If no individual permission found, check role-based permissions
        return hasRolePermission($user_id, $permission_key);
        
    } catch (Exception $e) {
        error_log("Error checking user permission: " . $e->getMessage());
        return false;
    }
}

/**
 * Check if a user has permission through their assigned roles
 * @param int $user_id The user ID to check
 * @param string $permission_key The permission key to check
 * @return bool True if user has permission through roles, false otherwise
 */
function hasRolePermission($user_id, $permission_key) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM user_role_assignments ura
            JOIN role_permissions rp ON ura.role_id = rp.role_id
            JOIN permissions p ON rp.permission_id = p.id
            WHERE ura.user_id = ? 
            AND p.permission_name = ? 
            AND ura.is_active = 1 
            AND p.is_active = 1
        ");
        $stmt->execute([$user_id, $permission_key]);
        
        return $stmt->fetchColumn() > 0;
        
    } catch (Exception $e) {
        error_log("Error checking role permission: " . $e->getMessage());
        return false;
    }
}

// Note: canAccessPage function is now defined in sidebar_rbac.php to avoid conflicts

/**
 * Legacy role-based page access check (for backward compatibility)
 * @param string $page_file The PHP file name
 * @param int $user_id The user ID
 * @return bool True if user can access the page through roles, false otherwise
 */
function canAccessPageLegacy($page_file, $user_id) {
    // Legacy hardcoded permissions for backward compatibility
    $legacy_permissions = [
        // Admin Pages (Super Admin + Limited Admin)
        'dashboard.php' => ['super_admin', 'limited_admin'],
        'notifications.php' => ['super_admin', 'limited_admin', 'event_coordinator', 'session_moderator', 'staff'],
        'members.php' => ['super_admin', 'limited_admin'],
        'add_member.php' => ['super_admin', 'limited_admin'],
        'edit_member.php' => ['super_admin', 'limited_admin'],
        'view_member.php' => ['super_admin', 'limited_admin'],
        'settings.php' => ['super_admin', 'limited_admin'],
        'profile.php' => ['super_admin', 'limited_admin', 'event_coordinator', 'organizer', 'session_moderator', 'staff'],
        
        // Super Admin Only Pages
        'setup_rbac_system.php' => ['super_admin'],
        'create_admin_users.php' => ['super_admin'],
        'manage_user_permissions.php' => ['super_admin'],
        'appearance_settings.php' => ['super_admin'],
        'branding_settings.php' => ['super_admin'],
        'security_audit.php' => ['super_admin'],
        'security_settings.php' => ['super_admin'],
        'backup_management.php' => ['super_admin'],
        'logo_management_consolidated.php' => ['super_admin'],
        'social_media_integration.php' => ['super_admin'],
        'custom_fields.php' => ['super_admin'],
        'calendar_integration.php' => ['super_admin'],
        
        // Event Management
        'events.php' => ['super_admin', 'limited_admin', 'event_coordinator'],
        'event_attendance.php' => ['super_admin', 'limited_admin', 'event_coordinator', 'session_moderator'],
        'event_sessions.php' => ['super_admin', 'limited_admin', 'event_coordinator', 'session_moderator'],
        'event_categories.php' => ['super_admin', 'limited_admin', 'event_coordinator'],
        'event_reports.php' => ['super_admin', 'limited_admin', 'event_coordinator'],
        
        // Communication
        'bulk_email.php' => ['super_admin', 'limited_admin'],
        'email_scheduler.php' => ['super_admin', 'limited_admin'],
        'email_templates.php' => ['super_admin', 'limited_admin'],
        'contacts.php' => ['super_admin', 'limited_admin'],
        'contact_groups.php' => ['super_admin', 'limited_admin'],
        'birthday.php' => ['super_admin', 'limited_admin'],
        'send_birthday_emails.php' => ['super_admin', 'limited_admin'],
        'test_birthday_email.php' => ['super_admin', 'limited_admin'],
        'send_birthday_notification.php' => ['super_admin', 'limited_admin'],
        'automated_templates.php' => ['super_admin', 'limited_admin'],
        'whatsapp_messages.php' => ['super_admin', 'limited_admin'],
        'email_analytics.php' => ['super_admin', 'limited_admin'],
        'single_sms.php' => ['super_admin', 'limited_admin'],
        'bulk_sms.php' => ['super_admin', 'limited_admin'],
        'sms_templates.php' => ['super_admin', 'limited_admin'],
        'sms_analytics.php' => ['super_admin', 'limited_admin'],
        
        // Member Management Extended
        'family_management.php' => ['super_admin', 'limited_admin'],
        'member_skills.php' => ['super_admin', 'limited_admin'],
        'requests.php' => ['super_admin', 'limited_admin'],
        'volunteer_opportunities.php' => ['super_admin', 'limited_admin'],
        
        // Donations
        'donations.php' => ['super_admin', 'limited_admin'],
        'gift_management.php' => ['super_admin', 'limited_admin'],
        'enhanced_donate.php' => ['super_admin', 'limited_admin'],
        'payment_integration.php' => ['super_admin'],
        'payment_tables.php' => ['super_admin']
    ];
    
    $allowed_roles = $legacy_permissions[$page_file] ?? [];
    
    if (empty($allowed_roles)) {
        return false;
    }
    
    return hasUserRole($user_id, $allowed_roles);
}

/**
 * Check if a user has any of the specified roles
 * @param int $user_id The user ID
 * @param array $roles Array of role names to check
 * @return bool True if user has any of the roles, false otherwise
 */
function hasUserRole($user_id, $roles) {
    global $pdo;
    
    try {
        $placeholders = str_repeat('?,', count($roles) - 1) . '?';
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM user_role_assignments ura
            JOIN user_roles ur ON ura.role_id = ur.id
            WHERE ura.user_id = ? 
            AND ur.role_name IN ($placeholders) 
            AND ura.is_active = 1
        ");
        
        $params = array_merge([$user_id], $roles);
        $stmt->execute($params);
        
        return $stmt->fetchColumn() > 0;
        
    } catch (Exception $e) {
        error_log("Error checking user roles: " . $e->getMessage());
        return false;
    }
}

/**
 * Get all permissions for a user (both individual and role-based)
 * @param int $user_id The user ID
 * @return array Array of permission keys
 */
function getUserPermissions($user_id) {
    global $pdo;
    
    try {
        $permissions = [];
        
        // Get individual permissions
        $stmt = $pdo->prepare("
            SELECT gp.permission_key
            FROM user_individual_permissions uip
            JOIN granular_permissions gp ON uip.permission_id = gp.id
            WHERE uip.user_id = ? 
            AND uip.is_active = 1 
            AND gp.is_active = 1
        ");
        $stmt->execute([$user_id]);
        $individual_permissions = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Get role-based permissions
        $stmt = $pdo->prepare("
            SELECT p.permission_name
            FROM user_role_assignments ura
            JOIN role_permissions rp ON ura.role_id = rp.role_id
            JOIN permissions p ON rp.permission_id = p.id
            WHERE ura.user_id = ? 
            AND ura.is_active = 1 
            AND p.is_active = 1
        ");
        $stmt->execute([$user_id]);
        $role_permissions = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Combine and deduplicate
        $permissions = array_unique(array_merge($individual_permissions, $role_permissions));
        
        return $permissions;
        
    } catch (Exception $e) {
        error_log("Error getting user permissions: " . $e->getMessage());
        return [];
    }
}

/**
 * Get user's accessible pages based on their permissions
 * @param int $user_id The user ID
 * @return array Array of page files the user can access
 */
function getUserAccessiblePages($user_id) {
    global $pdo;
    
    try {
        $pages = [];
        
        // Get pages from individual permissions
        $stmt = $pdo->prepare("
            SELECT DISTINCT gp.page_file
            FROM user_individual_permissions uip
            JOIN granular_permissions gp ON uip.permission_id = gp.id
            WHERE uip.user_id = ? 
            AND uip.is_active = 1 
            AND gp.is_active = 1
            AND gp.page_file IS NOT NULL
        ");
        $stmt->execute([$user_id]);
        $individual_pages = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Get pages from legacy role system
        $legacy_pages = [];
        $user_roles = getUserRoles($user_id);
        
        foreach (getLegacyPagePermissions() as $page => $allowed_roles) {
            if (array_intersect($user_roles, $allowed_roles)) {
                $legacy_pages[] = $page;
            }
        }
        
        // Combine and deduplicate
        $pages = array_unique(array_merge($individual_pages, $legacy_pages));
        
        return array_filter($pages); // Remove empty values
        
    } catch (Exception $e) {
        error_log("Error getting user accessible pages: " . $e->getMessage());
        return [];
    }
}

/**
 * Get user's role names
 * @param int $user_id The user ID
 * @return array Array of role names
 */
function getUserRoles($user_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT ur.role_name
            FROM user_role_assignments ura
            JOIN user_roles ur ON ura.role_id = ur.id
            WHERE ura.user_id = ? 
            AND ura.is_active = 1
        ");
        $stmt->execute([$user_id]);
        
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
        
    } catch (Exception $e) {
        error_log("Error getting user roles: " . $e->getMessage());
        return [];
    }
}

/**
 * Get legacy page permissions array
 * @return array Legacy page permissions
 */
function getLegacyPagePermissions() {
    // This is the same array from canAccessPageLegacy but extracted for reuse
    return [
        'dashboard.php' => ['super_admin', 'limited_admin'],
        'notifications.php' => ['super_admin', 'limited_admin', 'event_coordinator', 'session_moderator', 'staff'],
        'members.php' => ['super_admin', 'limited_admin'],
        'add_member.php' => ['super_admin', 'limited_admin'],
        'edit_member.php' => ['super_admin', 'limited_admin'],
        'view_member.php' => ['super_admin', 'limited_admin'],
        'settings.php' => ['super_admin', 'limited_admin'],
        'profile.php' => ['super_admin', 'limited_admin', 'event_coordinator', 'organizer', 'session_moderator', 'staff'],
        'setup_rbac_system.php' => ['super_admin'],
        'create_admin_users.php' => ['super_admin'],
        'manage_user_permissions.php' => ['super_admin'],
        'appearance_settings.php' => ['super_admin'],
        'branding_settings.php' => ['super_admin'],
        'security_audit.php' => ['super_admin'],
        'security_settings.php' => ['super_admin'],
        'backup_management.php' => ['super_admin'],
        'logo_management_consolidated.php' => ['super_admin'],
        'social_media_integration.php' => ['super_admin'],
        'custom_fields.php' => ['super_admin'],
        'calendar_integration.php' => ['super_admin'],
        'events.php' => ['super_admin', 'limited_admin', 'event_coordinator'],
        'event_attendance.php' => ['super_admin', 'limited_admin', 'event_coordinator', 'session_moderator'],
        'event_sessions.php' => ['super_admin', 'limited_admin', 'event_coordinator', 'session_moderator'],
        'event_categories.php' => ['super_admin', 'limited_admin', 'event_coordinator'],
        'event_reports.php' => ['super_admin', 'limited_admin', 'event_coordinator'],
        'bulk_email.php' => ['super_admin', 'limited_admin'],
        'email_scheduler.php' => ['super_admin', 'limited_admin'],
        'email_templates.php' => ['super_admin', 'limited_admin'],
        'contacts.php' => ['super_admin', 'limited_admin'],
        'contact_groups.php' => ['super_admin', 'limited_admin'],
        'birthday.php' => ['super_admin', 'limited_admin'],
        'send_birthday_emails.php' => ['super_admin', 'limited_admin'],
        'test_birthday_email.php' => ['super_admin', 'limited_admin'],
        'send_birthday_notification.php' => ['super_admin', 'limited_admin'],
        'automated_templates.php' => ['super_admin', 'limited_admin'],
        'whatsapp_messages.php' => ['super_admin', 'limited_admin'],
        'email_analytics.php' => ['super_admin', 'limited_admin'],
        'single_sms.php' => ['super_admin', 'limited_admin'],
        'bulk_sms.php' => ['super_admin', 'limited_admin'],
        'sms_templates.php' => ['super_admin', 'limited_admin'],
        'sms_analytics.php' => ['super_admin', 'limited_admin'],
        'family_management.php' => ['super_admin', 'limited_admin'],
        'member_skills.php' => ['super_admin', 'limited_admin'],
        'requests.php' => ['super_admin', 'limited_admin'],
        'volunteer_opportunities.php' => ['super_admin', 'limited_admin'],
        'donations.php' => ['super_admin', 'limited_admin'],
        'gift_management.php' => ['super_admin', 'limited_admin'],
        'enhanced_donate.php' => ['super_admin', 'limited_admin'],
        'payment_integration.php' => ['super_admin'],
        'payment_tables.php' => ['super_admin']
    ];
}

/**
 * Check if current user is super admin
 * @return bool True if current user is super admin, false otherwise
 */
function isSuperAdmin() {
    if (!isset($_SESSION['admin_id'])) {
        return false;
    }
    
    return hasUserRole($_SESSION['admin_id'], ['super_admin']);
}

/**
 * Initialize granular permissions for existing users
 * This function can be called to migrate existing role-based users to the new system
 */
function initializeGranularPermissions() {
    global $pdo;
    
    try {
        // This would be called during migration to set up initial permissions
        // based on existing roles
        
        $pdo->beginTransaction();
        
        // Get all users with their roles
        $stmt = $pdo->prepare("
            SELECT DISTINCT ura.user_id, ur.role_name
            FROM user_role_assignments ura
            JOIN user_roles ur ON ura.role_id = ur.id
            WHERE ura.is_active = 1
        ");
        $stmt->execute();
        $user_roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // For each user, assign permissions based on their roles
        foreach ($user_roles as $user_role) {
            $user_id = $user_role['user_id'];
            $role_name = $user_role['role_name'];
            
            // Get permissions that should be assigned based on role
            $permissions_to_assign = getPermissionsForRole($role_name);
            
            foreach ($permissions_to_assign as $permission_key) {
                // Get permission ID
                $stmt = $pdo->prepare("SELECT id FROM granular_permissions WHERE permission_key = ?");
                $stmt->execute([$permission_key]);
                $permission_id = $stmt->fetchColumn();
                
                if ($permission_id) {
                    // Assign permission to user
                    $stmt = $pdo->prepare("
                        INSERT IGNORE INTO user_individual_permissions 
                        (user_id, permission_id, granted_by, is_active) 
                        VALUES (?, ?, 1, 1)
                    ");
                    $stmt->execute([$user_id, $permission_id]);
                }
            }
        }
        
        $pdo->commit();
        return true;
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        error_log("Error initializing granular permissions: " . $e->getMessage());
        return false;
    }
}

/**
 * Get default permissions for a role (used during migration)
 * @param string $role_name The role name
 * @return array Array of permission keys
 */
function getPermissionsForRole($role_name) {
    $role_permissions = [
        'super_admin' => [
            'dashboard.view', 'dashboard.analytics',
            'members.view', 'members.add', 'members.edit', 'members.delete', 'members.view_profile',
            'members.family_management', 'members.skills', 'members.requests', 'members.volunteers',
            'events.view', 'events.create', 'events.edit', 'events.delete', 'events.attendance',
            'events.sessions', 'events.categories', 'events.reports',
            'email.bulk_send', 'email.scheduler', 'email.templates', 'email.contacts',
            'email.contact_groups', 'email.birthday', 'email.birthday_send', 'email.birthday_test',
            'email.birthday_notifications', 'email.automated_templates', 'email.whatsapp', 'email.analytics',
            'sms.single_send', 'sms.bulk_send', 'sms.templates', 'sms.analytics',
            'donations.view', 'donations.manage', 'donations.gifts', 'donations.enhanced',
            'donations.payment_integration', 'donations.payment_tables',
            'integrations.calendar', 'integrations.social_media',
            'settings.general', 'settings.appearance', 'settings.branding', 'settings.logo',
            'settings.custom_fields', 'settings.security_audit', 'settings.security_settings',
            'settings.backup', 'settings.profile',
            'notifications.view',
            'admin.rbac_management', 'admin.create_users', 'admin.super_dashboard', 'admin.permission_management'
        ],
        'limited_admin' => [
            'dashboard.view',
            'members.view', 'members.add', 'members.edit', 'members.view_profile',
            'members.family_management', 'members.skills', 'members.requests', 'members.volunteers',
            'events.view', 'events.attendance', 'events.sessions', 'events.reports',
            'email.bulk_send', 'email.scheduler', 'email.templates', 'email.contacts',
            'email.contact_groups', 'email.birthday', 'email.birthday_send', 'email.analytics',
            'sms.single_send', 'sms.bulk_send', 'sms.templates', 'sms.analytics',
            'donations.view', 'donations.manage', 'donations.gifts', 'donations.enhanced',
            'settings.general', 'settings.profile',
            'notifications.view'
        ],
        'event_coordinator' => [
            'dashboard.view',
            'events.view', 'events.create', 'events.edit', 'events.attendance',
            'events.sessions', 'events.categories', 'events.reports',
            'settings.profile',
            'notifications.view'
        ],
        'session_moderator' => [
            'dashboard.view',
            'events.view', 'events.attendance', 'events.sessions',
            'settings.profile',
            'notifications.view'
        ],
        'staff' => [
            'dashboard.view',
            'settings.profile',
            'notifications.view'
        ]
    ];
    
    return $role_permissions[$role_name] ?? [];
}
?>
