<?php
/**
 * Permission-Aware Navigation Helper
 * 
 * This file provides functions to generate navigation and buttons
 * that respect user permissions and assignments
 */

// Include permission checking functions
require_once 'rbac_access_control_granular.php';

/**
 * Check if user has permission to access a specific page (navigation-specific)
 * @param int $user_id User ID
 * @param string $page_name Page name (e.g., 'events.php')
 * @return bool True if user has access
 */
function canAccessPageForNavigation($user_id, $page_name) {
    // Super admins can access everything
    if (isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === 'super_admin') {
        return true;
    }
    
    // Map pages to required permissions
    $page_permissions = [
        'events.php' => 'events.manage',
        'event_sessions.php' => 'events.manage',
        'event_attendance.php' => 'events.view',
        'event_attendance_detail.php' => 'events.view',
        'session_attendance.php' => 'sessions.view',
        'members.php' => 'members.view',
        'add_member.php' => 'members.create',
        'dashboard.php' => 'dashboard.view',
        'assignment_dashboard.php' => 'assignments.view'
    ];
    
    // Check if page requires specific permission
    if (isset($page_permissions[$page_name])) {
        return hasUserPermission($user_id, $page_permissions[$page_name]);
    }
    
    // Default: allow access if no specific permission required
    return true;
}

/**
 * Check if user is assigned to a specific session
 * @param int $user_id User ID
 * @param int $session_id Session ID
 * @return bool True if user is assigned
 */
function isAssignedToSession($user_id, $session_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM session_assignments 
            WHERE user_id = ? AND session_id = ? AND is_active = 1
        ");
        $stmt->execute([$user_id, $session_id]);
        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Check if user is assigned to a specific event
 * @param int $user_id User ID
 * @param int $event_id Event ID
 * @return bool True if user is assigned
 */
function isAssignedToEvent($user_id, $event_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM event_assignments 
            WHERE user_id = ? AND event_id = ? AND is_active = 1
        ");
        $stmt->execute([$user_id, $event_id]);
        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Generate appropriate back navigation for a user
 * @param int $user_id User ID
 * @param string $current_page Current page context
 * @param array $context Additional context (event_id, session_id, etc.)
 * @return array Navigation options
 */
function getBackNavigation($user_id, $current_page, $context = []) {
    $navigation = [];
    
    // Always provide option to go to main dashboard or assignment dashboard
    if (canAccessPageForNavigation($user_id, 'dashboard.php')) {
        $navigation[] = [
            'url' => 'dashboard.php',
            'text' => 'Main Dashboard',
            'icon' => 'bi-house',
            'class' => 'btn-outline-primary'
        ];
    } else {
        $navigation[] = [
            'url' => 'assignment_dashboard.php',
            'text' => 'My Assignments',
            'icon' => 'bi-person-workspace',
            'class' => 'btn-outline-primary'
        ];
    }
    
    // Context-specific navigation
    switch ($current_page) {
        case 'session_attendance.php':
            if (isset($context['event_id'])) {
                // Only show "Back to Sessions" if user can manage events
                if (canAccessPageForNavigation($user_id, 'event_sessions.php')) {
                    $navigation[] = [
                        'url' => "event_sessions.php?event_id=" . $context['event_id'],
                        'text' => 'Back to Sessions',
                        'icon' => 'bi-arrow-left',
                        'class' => 'btn-outline-secondary'
                    ];
                }
                
                // Show "Back to Event" if user can view event details
                if (canAccessPageForNavigation($user_id, 'event_attendance_detail.php') || isAssignedToEvent($user_id, $context['event_id'])) {
                    $navigation[] = [
                        'url' => "event_attendance_detail.php?event_id=" . $context['event_id'],
                        'text' => 'Back to Event',
                        'icon' => 'bi-calendar-event',
                        'class' => 'btn-outline-info'
                    ];
                }
            }
            break;
            
        case 'event_attendance_detail.php':
            // Only show "Back to Events" if user can manage events
            if (canAccessPageForNavigation($user_id, 'events.php')) {
                $navigation[] = [
                    'url' => 'events.php',
                    'text' => 'Back to Events',
                    'icon' => 'bi-arrow-left',
                    'class' => 'btn-outline-secondary'
                ];
            }
            break;
    }
    
    return $navigation;
}

/**
 * Check if user can perform bulk operations on sessions/events
 * @param int $user_id User ID
 * @param string $operation_type Type of operation (attendance, management, etc.)
 * @param int $session_id Session ID (optional)
 * @param int $event_id Event ID (optional)
 * @return bool True if user can perform operation
 */
function canPerformBulkOperation($user_id, $operation_type, $session_id = null, $event_id = null) {
    // Super admins can do everything
    if (isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === 'super_admin') {
        return true;
    }
    
    switch ($operation_type) {
        case 'attendance_marking':
            // Can mark attendance if assigned to session/event or has permission
            if ($session_id && isAssignedToSession($user_id, $session_id)) {
                return true;
            }
            if ($event_id && isAssignedToEvent($user_id, $event_id)) {
                return true;
            }
            return hasUserPermission($user_id, 'attendance.manage');
            
        case 'event_management':
            // Can manage events if has permission
            return hasUserPermission($user_id, 'events.manage');
            
        case 'session_management':
            // Can manage sessions if assigned or has permission
            if ($session_id && isAssignedToSession($user_id, $session_id)) {
                return true;
            }
            return hasUserPermission($user_id, 'sessions.manage');
            
        default:
            return false;
    }
}

/**
 * Generate permission-aware button HTML
 * @param int $user_id User ID
 * @param string $permission_check Permission or condition to check
 * @param string $button_html Button HTML to show if permitted
 * @param array $context Additional context for permission checking
 * @return string Button HTML or empty string
 */
function renderPermissionButton($user_id, $permission_check, $button_html, $context = []) {
    $show_button = false;
    
    // Super admins see everything
    if (isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === 'super_admin') {
        $show_button = true;
    } else {
        // Check specific permission or condition
        switch ($permission_check) {
            case 'events.manage':
                $show_button = hasUserPermission($user_id, 'events.manage');
                break;
                
            case 'sessions.manage':
                $show_button = hasUserPermission($user_id, 'sessions.manage');
                break;
                
            case 'attendance.manage':
                $show_button = hasUserPermission($user_id, 'attendance.manage');
                break;
                
            case 'assigned_session':
                if (isset($context['session_id'])) {
                    $show_button = isAssignedToSession($user_id, $context['session_id']);
                }
                break;
                
            case 'assigned_event':
                if (isset($context['event_id'])) {
                    $show_button = isAssignedToEvent($user_id, $context['event_id']);
                }
                break;
                
            case 'attendance_or_assigned':
                $has_attendance_perm = hasUserPermission($user_id, 'attendance.manage');
                $assigned_to_session = isset($context['session_id']) ? isAssignedToSession($user_id, $context['session_id']) : false;
                $assigned_to_event = isset($context['event_id']) ? isAssignedToEvent($user_id, $context['event_id']) : false;
                $show_button = $has_attendance_perm || $assigned_to_session || $assigned_to_event;
                break;
                
            default:
                // Try to check as a direct permission
                $show_button = hasUserPermission($user_id, $permission_check);
                break;
        }
    }
    
    return $show_button ? $button_html : '';
}

/**
 * Get user's appropriate home page based on their permissions and assignments
 * @param int $user_id User ID
 * @return string URL of appropriate home page
 */
function getUserHomePage($user_id) {
    // Super admins go to main dashboard
    if (isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === 'super_admin') {
        return 'dashboard.php';
    }
    
    // Users with dashboard permission go to main dashboard
    if (hasUserPermission($user_id, 'dashboard.view')) {
        return 'dashboard.php';
    }
    
    // Users with assignments but no dashboard permission go to assignment dashboard
    return 'assignment_dashboard.php';
}
?>
