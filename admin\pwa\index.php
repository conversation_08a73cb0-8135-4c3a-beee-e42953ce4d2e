<?php
/**
 * Universal Event Manager PWA - Main Interface
 * Progressive Web App that works for any organization type
 */

require_once '../config.php';
require_once '../includes/auth_check.php';
require_once '../ai/universal_prediction_engine.php';

// Get organization configuration
$org_manager = new UniversalOrganizationManager($pdo);
$organization_id = $_SESSION['organization_id'] ?? 'default_org';
$org_config = $org_manager->getOrganizationConfig($organization_id);

// If no organization config exists, redirect to setup
if (!$org_config) {
    header('Location: ../setup/universal_organization_setup.php');
    exit();
}

// Get current action from URL
$action = $_GET['action'] ?? 'dashboard';
$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

// Get recent events
$stmt = $pdo->query("
    SELECT id, title, event_date, status 
    FROM events 
    WHERE event_date >= CURDATE() - INTERVAL 7 DAY
    ORDER BY event_date DESC 
    LIMIT 10
");
$recent_events = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get user info
$user_name = $_SESSION['user_name'] ?? 'Staff Member';
$user_role = $_SESSION['user_role'] ?? 'staff';

$page_title = 'Universal Event Manager PWA';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title><?php echo $page_title; ?></title>
    
    <!-- PWA Meta Tags -->
    <meta name="application-name" content="Universal Event Manager">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Event Manager">
    <meta name="description" content="Universal event management platform for any organization type">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="msapplication-config" content="browserconfig.xml">
    <meta name="msapplication-TileColor" content="<?php echo $org_config['branding']['primary_color']; ?>">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="theme-color" content="<?php echo $org_config['branding']['primary_color']; ?>">
    
    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="152x152" href="icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="icons/icon-180x180.png">
    <link rel="apple-touch-icon" sizes="167x167" href="icons/icon-167x167.png">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="icons/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="icons/icon-16x16.png">
    <link rel="shortcut icon" href="icons/favicon.ico">
    
    <!-- Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <!-- Styles -->
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="css/pwa-styles.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: <?php echo $org_config['branding']['primary_color']; ?>;
            --secondary-color: <?php echo $org_config['branding']['secondary_color']; ?>;
            --accent-color: <?php echo $org_config['branding']['accent_color']; ?>;
        }
    </style>
</head>
<body>
    <!-- PWA Install Banner -->
    <div id="installBanner" class="install-banner" style="display: none;">
        <div class="install-content">
            <div class="install-icon">
                <i class="bi bi-download"></i>
            </div>
            <div class="install-text">
                <strong>Install App</strong>
                <small>Add to home screen for better experience</small>
            </div>
            <div class="install-actions">
                <button class="btn btn-primary btn-sm" onclick="installPWA()">Install</button>
                <button class="btn btn-outline-secondary btn-sm" onclick="dismissInstall()">Later</button>
            </div>
        </div>
    </div>

    <!-- Offline Banner -->
    <div id="offlineBanner" class="offline-banner" style="display: none;">
        <div class="offline-content">
            <i class="bi bi-wifi-off"></i>
            <span>You're offline. Some features may be limited.</span>
            <button class="btn btn-sm btn-outline-light" onclick="retryConnection()">Retry</button>
        </div>
    </div>

    <!-- Main App Container -->
    <div class="pwa-container">
        <!-- Header -->
        <header class="pwa-header">
            <div class="header-content">
                <div class="header-left">
                    <button class="btn btn-link text-white p-0" onclick="toggleSidebar()">
                        <i class="bi bi-list fs-4"></i>
                    </button>
                    <div class="header-title">
                        <h6 class="mb-0"><?php echo htmlspecialchars($org_config['name']); ?></h6>
                        <small class="opacity-75"><?php echo ucfirst($org_config['type']); ?> Events</small>
                    </div>
                </div>
                <div class="header-right">
                    <button class="btn btn-link text-white p-0 me-2" onclick="showNotifications()">
                        <i class="bi bi-bell fs-5"></i>
                        <span class="notification-badge" id="notificationCount">3</span>
                    </button>
                    <div class="user-avatar">
                        <i class="bi bi-person-circle fs-4"></i>
                    </div>
                </div>
            </div>
        </header>

        <!-- Sidebar -->
        <nav class="pwa-sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="user-info">
                    <div class="user-avatar-large">
                        <i class="bi bi-person-circle"></i>
                    </div>
                    <div class="user-details">
                        <strong><?php echo htmlspecialchars($user_name); ?></strong>
                        <small><?php echo ucfirst($user_role); ?></small>
                    </div>
                </div>
            </div>
            
            <div class="sidebar-menu">
                <a href="?action=dashboard" class="menu-item <?php echo $action === 'dashboard' ? 'active' : ''; ?>">
                    <i class="bi bi-speedometer2"></i>
                    <span>Dashboard</span>
                </a>
                <a href="?action=checkin" class="menu-item <?php echo $action === 'checkin' ? 'active' : ''; ?>">
                    <i class="bi bi-check-circle"></i>
                    <span>Quick Check-In</span>
                </a>
                <a href="?action=scanner" class="menu-item <?php echo $action === 'scanner' ? 'active' : ''; ?>">
                    <i class="bi bi-qr-code-scan"></i>
                    <span>QR Scanner</span>
                </a>
                <a href="?action=events" class="menu-item <?php echo $action === 'events' ? 'active' : ''; ?>">
                    <i class="bi bi-calendar-event"></i>
                    <span><?php echo ucfirst($org_config['terminology']['events']); ?></span>
                </a>
                <a href="?action=ai" class="menu-item <?php echo $action === 'ai' ? 'active' : ''; ?>">
                    <i class="bi bi-robot"></i>
                    <span>AI Predictions</span>
                </a>
                <a href="?action=analytics" class="menu-item <?php echo $action === 'analytics' ? 'active' : ''; ?>">
                    <i class="bi bi-graph-up"></i>
                    <span>Analytics</span>
                </a>
                <a href="?action=settings" class="menu-item <?php echo $action === 'settings' ? 'active' : ''; ?>">
                    <i class="bi bi-gear"></i>
                    <span>Settings</span>
                </a>
            </div>
            
            <div class="sidebar-footer">
                <div class="connection-status" id="connectionStatus">
                    <i class="bi bi-wifi"></i>
                    <span>Online</span>
                </div>
                <button class="btn btn-outline-light btn-sm w-100 mt-2" onclick="syncData()">
                    <i class="bi bi-arrow-clockwise"></i> Sync Data
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="pwa-main" id="mainContent">
            <?php
            switch ($action) {
                case 'dashboard':
                    include 'views/dashboard.php';
                    break;
                case 'checkin':
                    include 'views/checkin.php';
                    break;
                case 'scanner':
                    include 'views/scanner.php';
                    break;
                case 'events':
                    include 'views/events.php';
                    break;
                case 'ai':
                    include 'views/ai-predictions.php';
                    break;
                case 'analytics':
                    include 'views/analytics.php';
                    break;
                case 'settings':
                    include 'views/settings.php';
                    break;
                default:
                    include 'views/dashboard.php';
            }
            ?>
        </main>

        <!-- Bottom Navigation (Mobile) -->
        <nav class="pwa-bottom-nav">
            <a href="?action=dashboard" class="nav-item <?php echo $action === 'dashboard' ? 'active' : ''; ?>">
                <i class="bi bi-speedometer2"></i>
                <span>Dashboard</span>
            </a>
            <a href="?action=checkin" class="nav-item <?php echo $action === 'checkin' ? 'active' : ''; ?>">
                <i class="bi bi-check-circle"></i>
                <span>Check-In</span>
            </a>
            <a href="?action=scanner" class="nav-item <?php echo $action === 'scanner' ? 'active' : ''; ?>">
                <i class="bi bi-qr-code-scan"></i>
                <span>Scanner</span>
            </a>
            <a href="?action=ai" class="nav-item <?php echo $action === 'ai' ? 'active' : ''; ?>">
                <i class="bi bi-robot"></i>
                <span>AI</span>
            </a>
        </nav>
    </div>

    <!-- Floating Action Button -->
    <div class="fab-container">
        <button class="fab" onclick="showQuickActions()">
            <i class="bi bi-plus"></i>
        </button>
    </div>

    <!-- Quick Actions Modal -->
    <div class="modal fade" id="quickActionsModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Quick Actions</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="quick-actions-grid">
                        <button class="quick-action-btn" onclick="quickCheckIn()">
                            <i class="bi bi-check-circle"></i>
                            <span>Quick Check-In</span>
                        </button>
                        <button class="quick-action-btn" onclick="scanQR()">
                            <i class="bi bi-qr-code-scan"></i>
                            <span>Scan QR Code</span>
                        </button>
                        <button class="quick-action-btn" onclick="viewPredictions()">
                            <i class="bi bi-robot"></i>
                            <span>AI Predictions</span>
                        </button>
                        <button class="quick-action-btn" onclick="exportData()">
                            <i class="bi bi-download"></i>
                            <span>Export Data</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/bootstrap.bundle.min.js"></script>
    <script src="js/pwa-app.js"></script>
    <script src="js/qr-scanner.js"></script>
    
    <script>
        // Initialize PWA with organization config
        window.ORG_CONFIG = <?php echo json_encode($org_config); ?>;
        window.USER_INFO = {
            name: '<?php echo addslashes($user_name); ?>',
            role: '<?php echo addslashes($user_role); ?>',
            organization_id: '<?php echo addslashes($organization_id); ?>'
        };
        window.RECENT_EVENTS = <?php echo json_encode($recent_events); ?>;
        
        // Initialize app when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            PWAApp.init();
        });
    </script>
</body>
</html>
