<?php
/**
 * Unified Settings Page
 * Merges functionality from settings.php, appearance_settings.php, and branding_settings.php
 */

session_start();
require_once '../config.php';
require_once 'includes/auth_check.php';

$page_title = 'System Settings';
$page_header = 'Unified System Settings';
$page_description = 'Comprehensive settings management for your organization';

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo->beginTransaction();
        
        // Organization & Branding Settings
        if (isset($_POST['organization_settings'])) {
            $org_settings = [
                'site_title' => $_POST['site_title'] ?? '',
                'admin_title' => $_POST['admin_title'] ?? '',
                'organization_name' => $_POST['organization_name'] ?? '',
                'organization_type' => $_POST['organization_type'] ?? 'church',
                'organization_mission' => $_POST['organization_mission'] ?? '',
                'organization_vision' => $_POST['organization_vision'] ?? '',
                'organization_values' => $_POST['organization_values'] ?? '',
                'member_term' => $_POST['member_term'] ?? 'Member',
                'leader_term' => $_POST['leader_term'] ?? 'Pastor',
                'group_term' => $_POST['group_term'] ?? 'Ministry',
                'event_term' => $_POST['event_term'] ?? 'Service',
                'donation_term' => $_POST['donation_term'] ?? 'Offering',
                'footer_text' => $_POST['footer_text'] ?? ''
            ];
            
            foreach ($org_settings as $key => $value) {
                $stmt = $pdo->prepare("
                    INSERT INTO site_settings (setting_name, setting_value) 
                    VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $stmt->execute([$key, $value]);
            }
        }
        
        // Contact Information
        if (isset($_POST['contact_settings'])) {
            $contact_settings = [
                'contact_phone' => $_POST['contact_phone'] ?? '',
                'contact_email' => $_POST['contact_email'] ?? '',
                'contact_address' => $_POST['contact_address'] ?? '',
                'contact_city' => $_POST['contact_city'] ?? '',
                'contact_state' => $_POST['contact_state'] ?? '',
                'contact_zip' => $_POST['contact_zip'] ?? '',
                'contact_country' => $_POST['contact_country'] ?? '',
                'website_url' => $_POST['website_url'] ?? ''
            ];
            
            foreach ($contact_settings as $key => $value) {
                $stmt = $pdo->prepare("
                    INSERT INTO site_settings (setting_name, setting_value) 
                    VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $stmt->execute([$key, $value]);
            }
        }
        
        // Appearance Settings
        if (isset($_POST['appearance_settings'])) {
            // Create appearance_settings table if it doesn't exist
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS appearance_settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    setting_name VARCHAR(100) NOT NULL UNIQUE,
                    setting_value TEXT,
                    category VARCHAR(50) DEFAULT 'general',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )
            ");
            
            $appearance_settings = [
                'primary_color' => $_POST['primary_color'] ?? '#007bff',
                'secondary_color' => $_POST['secondary_color'] ?? '#6c757d',
                'success_color' => $_POST['success_color'] ?? '#28a745',
                'danger_color' => $_POST['danger_color'] ?? '#dc3545',
                'warning_color' => $_POST['warning_color'] ?? '#ffc107',
                'info_color' => $_POST['info_color'] ?? '#17a2b8',
                'background_color' => $_POST['background_color'] ?? '#ffffff',
                'text_color' => $_POST['text_color'] ?? '#212529',
                'primary_font' => $_POST['primary_font'] ?? 'Inter',
                'font_size_base' => $_POST['font_size_base'] ?? '16',
                'sidebar_style' => $_POST['sidebar_style'] ?? 'default',
                'navbar_style' => $_POST['navbar_style'] ?? 'default'
            ];
            
            foreach ($appearance_settings as $key => $value) {
                $stmt = $pdo->prepare("
                    INSERT INTO appearance_settings (setting_name, setting_value, category) 
                    VALUES (?, ?, 'appearance') 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = NOW()
                ");
                $stmt->execute([$key, $value]);
            }
        }
        
        $pdo->commit();
        $message = "Settings updated successfully!";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $error = "Error updating settings: " . $e->getMessage();
    }
}

// Get current settings
$current_settings = [];
try {
    // Get site settings
    $stmt = $pdo->prepare("SELECT setting_name, setting_value FROM site_settings");
    $stmt->execute();
    $site_settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
    // Get appearance settings
    $stmt = $pdo->prepare("SELECT setting_name, setting_value FROM appearance_settings WHERE category = 'appearance'");
    $stmt->execute();
    $appearance_settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
    $current_settings = array_merge($site_settings, $appearance_settings);
    
} catch (Exception $e) {
    // Use defaults if error
    $current_settings = [];
}

include 'includes/header.php';
?>

<style>
.settings-nav {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 2rem;
}

.settings-nav .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    border-radius: 0;
    color: #6c757d;
    font-weight: 500;
}

.settings-nav .nav-link.active {
    border-bottom-color: #007bff;
    color: #007bff;
    background: none;
}

.settings-section {
    display: none;
}

.settings-section.active {
    display: block;
}

.color-preview {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 2px solid #dee2e6;
    display: inline-block;
    margin-left: 10px;
    vertical-align: middle;
}

.settings-card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 15px;
    margin-bottom: 2rem;
}

.settings-card .card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 15px 15px 0 0;
    border: none;
}
</style>

<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2"><i class="bi bi-gear"></i> Unified System Settings</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportSettings()">
                    <i class="bi bi-download"></i> Export Settings
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="importSettings()">
                    <i class="bi bi-upload"></i> Import Settings
                </button>
            </div>
        </div>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Settings Navigation -->
    <ul class="nav nav-tabs settings-nav" role="tablist">
        <li class="nav-item">
            <a class="nav-link active" href="#organization" onclick="showSection('organization')">
                <i class="bi bi-building"></i> Organization
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="#contact" onclick="showSection('contact')">
                <i class="bi bi-telephone"></i> Contact Info
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="#appearance" onclick="showSection('appearance')">
                <i class="bi bi-palette"></i> Appearance
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" href="#advanced" onclick="showSection('advanced')">
                <i class="bi bi-gear-wide-connected"></i> Advanced
            </a>
        </li>
    </ul>

    <form method="POST" id="settingsForm">
        <!-- Organization Settings Section -->
        <div id="organization" class="settings-section active">
            <div class="card settings-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-building"></i> Organization Settings</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="site_title" class="form-label">Site Title</label>
                                <input type="text" class="form-control" id="site_title" name="site_title" 
                                       value="<?php echo htmlspecialchars($current_settings['site_title'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="admin_title" class="form-label">Admin Panel Title</label>
                                <input type="text" class="form-control" id="admin_title" name="admin_title" 
                                       value="<?php echo htmlspecialchars($current_settings['admin_title'] ?? ''); ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="organization_name" class="form-label">Organization Name</label>
                                <input type="text" class="form-control" id="organization_name" name="organization_name" 
                                       value="<?php echo htmlspecialchars($current_settings['organization_name'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="organization_type" class="form-label">Organization Type</label>
                                <select class="form-select" id="organization_type" name="organization_type">
                                    <option value="church" <?php echo ($current_settings['organization_type'] ?? '') === 'church' ? 'selected' : ''; ?>>Church</option>
                                    <option value="corporate" <?php echo ($current_settings['organization_type'] ?? '') === 'corporate' ? 'selected' : ''; ?>>Corporate</option>
                                    <option value="educational" <?php echo ($current_settings['organization_type'] ?? '') === 'educational' ? 'selected' : ''; ?>>Educational</option>
                                    <option value="sports" <?php echo ($current_settings['organization_type'] ?? '') === 'sports' ? 'selected' : ''; ?>>Sports</option>
                                    <option value="entertainment" <?php echo ($current_settings['organization_type'] ?? '') === 'entertainment' ? 'selected' : ''; ?>>Entertainment</option>
                                    <option value="healthcare" <?php echo ($current_settings['organization_type'] ?? '') === 'healthcare' ? 'selected' : ''; ?>>Healthcare</option>
                                    <option value="government" <?php echo ($current_settings['organization_type'] ?? '') === 'government' ? 'selected' : ''; ?>>Government</option>
                                    <option value="nonprofit" <?php echo ($current_settings['organization_type'] ?? '') === 'nonprofit' ? 'selected' : ''; ?>>Non-Profit</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="organization_mission" class="form-label">Mission Statement</label>
                        <textarea class="form-control" id="organization_mission" name="organization_mission" rows="3"><?php echo htmlspecialchars($current_settings['organization_mission'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="member_term" class="form-label">Member Term</label>
                                <input type="text" class="form-control" id="member_term" name="member_term" 
                                       value="<?php echo htmlspecialchars($current_settings['member_term'] ?? 'Member'); ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="leader_term" class="form-label">Leader Term</label>
                                <input type="text" class="form-control" id="leader_term" name="leader_term" 
                                       value="<?php echo htmlspecialchars($current_settings['leader_term'] ?? 'Pastor'); ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="event_term" class="form-label">Event Term</label>
                                <input type="text" class="form-control" id="event_term" name="event_term" 
                                       value="<?php echo htmlspecialchars($current_settings['event_term'] ?? 'Service'); ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end">
                        <button type="submit" name="organization_settings" class="btn btn-primary">
                            <i class="bi bi-check"></i> Save Organization Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information Section -->
        <div id="contact" class="settings-section">
            <div class="card settings-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-telephone"></i> Contact Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="contact_phone" name="contact_phone"
                                       value="<?php echo htmlspecialchars($current_settings['contact_phone'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_email" class="form-label">Email Address</label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email"
                                       value="<?php echo htmlspecialchars($current_settings['contact_email'] ?? ''); ?>">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="contact_address" class="form-label">Street Address</label>
                        <input type="text" class="form-control" id="contact_address" name="contact_address"
                               value="<?php echo htmlspecialchars($current_settings['contact_address'] ?? ''); ?>">
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="contact_city" class="form-label">City</label>
                                <input type="text" class="form-control" id="contact_city" name="contact_city"
                                       value="<?php echo htmlspecialchars($current_settings['contact_city'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="contact_state" class="form-label">State/Province</label>
                                <input type="text" class="form-control" id="contact_state" name="contact_state"
                                       value="<?php echo htmlspecialchars($current_settings['contact_state'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="contact_zip" class="form-label">ZIP/Postal Code</label>
                                <input type="text" class="form-control" id="contact_zip" name="contact_zip"
                                       value="<?php echo htmlspecialchars($current_settings['contact_zip'] ?? ''); ?>">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="website_url" class="form-label">Website URL</label>
                        <input type="url" class="form-control" id="website_url" name="website_url"
                               value="<?php echo htmlspecialchars($current_settings['website_url'] ?? ''); ?>">
                    </div>

                    <div class="d-flex justify-content-end">
                        <button type="submit" name="contact_settings" class="btn btn-primary">
                            <i class="bi bi-check"></i> Save Contact Information
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Appearance Settings Section -->
        <div id="appearance" class="settings-section">
            <div class="card settings-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-palette"></i> Appearance & Branding</h5>
                </div>
                <div class="card-body">
                    <h6><i class="bi bi-paint-bucket"></i> Color Scheme</h6>
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="primary_color" class="form-label">Primary Color</label>
                                <div class="d-flex align-items-center">
                                    <input type="color" class="form-control form-control-color" id="primary_color" name="primary_color"
                                           value="<?php echo $current_settings['primary_color'] ?? '#007bff'; ?>" style="width: 60px;">
                                    <span class="ms-2" id="primary_color_text"><?php echo $current_settings['primary_color'] ?? '#007bff'; ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="secondary_color" class="form-label">Secondary Color</label>
                                <div class="d-flex align-items-center">
                                    <input type="color" class="form-control form-control-color" id="secondary_color" name="secondary_color"
                                           value="<?php echo $current_settings['secondary_color'] ?? '#6c757d'; ?>" style="width: 60px;">
                                    <span class="ms-2" id="secondary_color_text"><?php echo $current_settings['secondary_color'] ?? '#6c757d'; ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="success_color" class="form-label">Success Color</label>
                                <div class="d-flex align-items-center">
                                    <input type="color" class="form-control form-control-color" id="success_color" name="success_color"
                                           value="<?php echo $current_settings['success_color'] ?? '#28a745'; ?>" style="width: 60px;">
                                    <span class="ms-2" id="success_color_text"><?php echo $current_settings['success_color'] ?? '#28a745'; ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="danger_color" class="form-label">Danger Color</label>
                                <div class="d-flex align-items-center">
                                    <input type="color" class="form-control form-control-color" id="danger_color" name="danger_color"
                                           value="<?php echo $current_settings['danger_color'] ?? '#dc3545'; ?>" style="width: 60px;">
                                    <span class="ms-2" id="danger_color_text"><?php echo $current_settings['danger_color'] ?? '#dc3545'; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h6><i class="bi bi-fonts"></i> Typography</h6>
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="primary_font" class="form-label">Primary Font</label>
                                <select class="form-select" id="primary_font" name="primary_font">
                                    <option value="Inter" <?php echo ($current_settings['primary_font'] ?? 'Inter') === 'Inter' ? 'selected' : ''; ?>>Inter</option>
                                    <option value="Arial" <?php echo ($current_settings['primary_font'] ?? '') === 'Arial' ? 'selected' : ''; ?>>Arial</option>
                                    <option value="Helvetica" <?php echo ($current_settings['primary_font'] ?? '') === 'Helvetica' ? 'selected' : ''; ?>>Helvetica</option>
                                    <option value="Georgia" <?php echo ($current_settings['primary_font'] ?? '') === 'Georgia' ? 'selected' : ''; ?>>Georgia</option>
                                    <option value="Times New Roman" <?php echo ($current_settings['primary_font'] ?? '') === 'Times New Roman' ? 'selected' : ''; ?>>Times New Roman</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="font_size_base" class="form-label">Base Font Size (px)</label>
                                <input type="number" class="form-control" id="font_size_base" name="font_size_base"
                                       value="<?php echo $current_settings['font_size_base'] ?? '16'; ?>" min="12" max="24">
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end">
                        <button type="submit" name="appearance_settings" class="btn btn-primary">
                            <i class="bi bi-check"></i> Save Appearance Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Settings Section -->
        <div id="advanced" class="settings-section">
            <div class="card settings-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-gear-wide-connected"></i> Advanced Settings</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Warning:</strong> Advanced settings should only be modified by experienced administrators.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="bi bi-database"></i> Database Settings</h6>
                            <div class="list-group">
                                <a href="<?php echo admin_url_for('database_maintenance.php'); ?>" class="list-group-item list-group-item-action">
                                    <i class="bi bi-tools"></i> Database Maintenance
                                </a>
                                <a href="<?php echo admin_url_for('backup_management.php'); ?>" class="list-group-item list-group-item-action">
                                    <i class="bi bi-archive"></i> Backup Management
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="bi bi-shield-check"></i> Security Settings</h6>
                            <div class="list-group">
                                <a href="<?php echo admin_url_for('setup_rbac_system.php'); ?>" class="list-group-item list-group-item-action">
                                    <i class="bi bi-shield-check"></i> RBAC Management
                                </a>
                                <a href="<?php echo admin_url_for('security_settings.php'); ?>" class="list-group-item list-group-item-action">
                                    <i class="bi bi-lock"></i> Security Settings
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h6><i class="bi bi-robot"></i> Universal Platform</h6>
                            <div class="list-group">
                                <a href="<?php echo admin_url_for('universal_organization_setup.php'); ?>" class="list-group-item list-group-item-action">
                                    <i class="bi bi-gear"></i> Organization Setup
                                </a>
                                <a href="<?php echo admin_url_for('universal_ai_dashboard.php'); ?>" class="list-group-item list-group-item-action">
                                    <i class="bi bi-robot"></i> AI Predictions
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="bi bi-tools"></i> System Tools</h6>
                            <div class="list-group">
                                <a href="<?php echo admin_url_for('system_test_dashboard.php'); ?>" class="list-group-item list-group-item-action">
                                    <i class="bi bi-bug"></i> System Testing
                                </a>
                                <a href="<?php echo admin_url_for('diagnostic.php'); ?>" class="list-group-item list-group-item-action">
                                    <i class="bi bi-activity"></i> System Diagnostics
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.settings-section').forEach(section => {
        section.classList.remove('active');
    });

    // Remove active class from all nav links
    document.querySelectorAll('.settings-nav .nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // Show selected section
    document.getElementById(sectionId).classList.add('active');

    // Add active class to clicked nav link
    event.target.classList.add('active');
}

// Update color text displays
document.querySelectorAll('input[type="color"]').forEach(input => {
    input.addEventListener('input', function() {
        const textSpan = document.getElementById(this.id + '_text');
        if (textSpan) {
            textSpan.textContent = this.value;
        }
    });
});

function exportSettings() {
    // Implementation for exporting settings
    alert('Export functionality will be implemented');
}

function importSettings() {
    // Implementation for importing settings
    alert('Import functionality will be implemented');
}
</script>

<?php include 'includes/footer.php'; ?>
