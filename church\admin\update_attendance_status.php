<?php
require_once '../config.php';
require_once 'includes/auth_check.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'error' => 'Invalid JSON input']);
    exit();
}

$attendee_id = $input['attendee_id'] ?? '';
$session_id = (int)($input['session_id'] ?? 0);
$status = $input['status'] ?? '';

// Validate inputs
if (!$attendee_id || !$session_id || !in_array($status, ['registered', 'attended', 'no_show', 'not_registered'])) {
    echo json_encode(['success' => false, 'error' => 'Invalid parameters']);
    exit();
}

try {
    $pdo->beginTransaction();
    
    if ($status === 'not_registered') {
        // Delete the attendance record
        $stmt = $pdo->prepare("
            DELETE FROM session_attendance 
            WHERE session_id = ? AND 
            (member_id = ? OR CONCAT('guest_', id) = ?)
        ");
        $stmt->execute([$session_id, $attendee_id, $attendee_id]);
    } else {
        // Check if attendance record exists
        $stmt = $pdo->prepare("
            SELECT id FROM session_attendance 
            WHERE session_id = ? AND 
            (member_id = ? OR CONCAT('guest_', id) = ?)
        ");
        $stmt->execute([$session_id, $attendee_id, $attendee_id]);
        $existing = $stmt->fetch();
        
        if ($existing) {
            // Update existing record
            $stmt = $pdo->prepare("
                UPDATE session_attendance 
                SET attendance_status = ?,
                    attendance_date = CASE WHEN ? = 'attended' THEN NOW() ELSE attendance_date END
                WHERE id = ?
            ");
            $stmt->execute([$status, $status, $existing['id']]);
        } else {
            // Create new record
            if (is_numeric($attendee_id)) {
                // Member
                $stmt = $pdo->prepare("
                    INSERT INTO session_attendance (session_id, member_id, attendance_status, attendance_date)
                    VALUES (?, ?, ?, CASE WHEN ? = 'attended' THEN NOW() ELSE NULL END)
                ");
                $stmt->execute([$session_id, $attendee_id, $status, $status]);
            } else {
                // Guest - need to get guest details
                $guest_id = str_replace('guest_', '', $attendee_id);
                $stmt = $pdo->prepare("
                    SELECT guest_name, guest_email FROM event_rsvps_guests WHERE id = ?
                ");
                $stmt->execute([$guest_id]);
                $guest = $stmt->fetch();
                
                if ($guest) {
                    $stmt = $pdo->prepare("
                        INSERT INTO session_attendance (session_id, guest_name, guest_email, attendance_status, attendance_date)
                        VALUES (?, ?, ?, ?, CASE WHEN ? = 'attended' THEN NOW() ELSE NULL END)
                    ");
                    $stmt->execute([$session_id, $guest['guest_name'], $guest['guest_email'], $status, $status]);
                }
            }
        }
    }
    
    $pdo->commit();
    echo json_encode(['success' => true]);
    
} catch (Exception $e) {
    $pdo->rollback();
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>
