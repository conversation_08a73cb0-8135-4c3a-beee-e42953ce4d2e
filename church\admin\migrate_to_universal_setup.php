<?php
/**
 * Migration Script: Legacy Settings to Universal Organization Setup
 * This script helps migrate data from legacy settings pages to the new universal setup
 */

session_start();
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/includes/route_protection.php';

// Protect this page - Super Admin only
protectSuperAdminRoute();

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['migrate_settings'])) {
    try {
        $pdo->beginTransaction();
        
        // Check if we have any settings to migrate
        $stmt = $pdo->query("SELECT COUNT(*) FROM site_settings");
        $settings_count = $stmt->fetchColumn();
        
        if ($settings_count == 0) {
            throw new Exception('No settings found to migrate');
        }
        
        // Set default organization type if not set
        $stmt = $pdo->prepare("SELECT setting_value FROM site_settings WHERE setting_name = 'organization_type'");
        $stmt->execute();
        $org_type = $stmt->fetchColumn();
        
        if (!$org_type) {
            $stmt = $pdo->prepare("
                INSERT INTO site_settings (setting_name, setting_value) 
                VALUES ('organization_type', 'church')
                ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
            ");
            $stmt->execute();
        }
        
        // Ensure all required settings have defaults
        $default_settings = [
            'site_title' => 'Organization Management System',
            'admin_title' => 'Admin Panel',
            'organization_name' => 'Your Organization',
            'primary_color' => '#007bff',
            'secondary_color' => '#6c757d',
            'member_term' => 'Member',
            'leader_term' => 'Pastor',
            'event_term' => 'Service',
            'donation_term' => 'Offering',
            'timezone' => 'America/New_York',
            'currency_symbol' => '$',
            'currency_code' => 'USD',
            'language' => 'en'
        ];
        
        foreach ($default_settings as $key => $default_value) {
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO site_settings (setting_name, setting_value) 
                VALUES (?, ?)
            ");
            $stmt->execute([$key, $default_value]);
        }
        
        $pdo->commit();
        $message = "Settings migration completed successfully! You can now use the Universal Organization Setup page.";
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        $error = "Migration error: " . $e->getMessage();
    }
}

// Get current settings status
$settings_status = [];
try {
    $stmt = $pdo->query("SELECT setting_name, setting_value FROM site_settings ORDER BY setting_name");
    $settings_status = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $settings_status = [];
}

$page_title = 'Migrate to Universal Setup';
$page_header = 'Settings Migration';
$page_description = 'Migrate from legacy settings to Universal Organization Setup';

include __DIR__ . '/includes/header.php';
?>

<style>
.migration-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 10px;
}

.settings-preview {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.migration-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="migration-header p-4 text-center">
            <h2 class="mb-2">
                <i class="bi bi-arrow-up-circle"></i> <?php echo $page_header; ?>
            </h2>
            <p class="mb-0"><?php echo $page_description; ?></p>
        </div>
    </div>
</div>

<!-- Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Migration Information -->
<div class="row">
    <div class="col-md-8">
        <div class="migration-card">
            <h5><i class="bi bi-info-circle"></i> About This Migration</h5>
            <p>This migration will:</p>
            <ul>
                <li>Ensure all your existing settings are preserved</li>
                <li>Add default values for any missing settings</li>
                <li>Prepare your system for the new Universal Organization Setup</li>
                <li>Maintain backward compatibility with existing functionality</li>
            </ul>
            
            <div class="alert alert-info">
                <h6><i class="bi bi-lightbulb"></i> What's New in Universal Setup?</h6>
                <ul class="mb-0">
                    <li><strong>Tabbed Interface:</strong> Organized settings by category</li>
                    <li><strong>Organization Types:</strong> Customize for different organization types</li>
                    <li><strong>Comprehensive Settings:</strong> All settings in one place</li>
                    <li><strong>Better UX:</strong> Improved user experience and validation</li>
                </ul>
            </div>

            <h6>Legacy Pages Being Replaced:</h6>
            <ul>
                <li><code>settings.php</code> - Legacy comprehensive settings</li>
                <li><code>main_settings.php</code> - Main settings page</li>
            </ul>

            <div class="mt-4">
                <form method="POST">
                    <button type="submit" name="migrate_settings" class="btn btn-success btn-lg me-3">
                        <i class="bi bi-arrow-up-circle"></i> Run Migration
                    </button>
                    <a href="universal_organization_setup.php" class="btn btn-primary btn-lg">
                        <i class="bi bi-gear-wide-connected"></i> Go to Universal Setup
                    </a>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="migration-card">
            <h5><i class="bi bi-list-ul"></i> Current Settings</h5>
            <p class="text-muted">Preview of your current settings:</p>
            
            <div class="settings-preview">
                <?php if (!empty($settings_status)): ?>
                    <?php foreach ($settings_status as $setting): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <small class="text-muted"><?php echo htmlspecialchars($setting['setting_name']); ?></small>
                            <small class="badge bg-secondary"><?php echo !empty($setting['setting_value']) ? 'Set' : 'Empty'; ?></small>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-exclamation-triangle fs-3 d-block mb-2"></i>
                        <p>No settings found</p>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="mt-3">
                <div class="d-flex justify-content-between">
                    <span>Total Settings:</span>
                    <strong><?php echo count($settings_status); ?></strong>
                </div>
                <div class="d-flex justify-content-between">
                    <span>Configured:</span>
                    <strong><?php echo count(array_filter($settings_status, function($s) { return !empty($s['setting_value']); })); ?></strong>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Links -->
<div class="row">
    <div class="col-md-12">
        <div class="migration-card">
            <h5><i class="bi bi-link-45deg"></i> Quick Links</h5>
            <div class="row">
                <div class="col-md-3">
                    <a href="universal_organization_setup.php" class="btn btn-outline-primary w-100 mb-2">
                        <i class="bi bi-gear-wide-connected"></i><br>
                        Universal Setup
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="manage_user_permissions.php" class="btn btn-outline-secondary w-100 mb-2">
                        <i class="bi bi-shield-lock"></i><br>
                        User Permissions
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="appearance_settings.php" class="btn btn-outline-info w-100 mb-2">
                        <i class="bi bi-palette"></i><br>
                        Appearance
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="security_settings.php" class="btn btn-outline-warning w-100 mb-2">
                        <i class="bi bi-shield-check"></i><br>
                        Security
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/includes/footer.php'; ?>
