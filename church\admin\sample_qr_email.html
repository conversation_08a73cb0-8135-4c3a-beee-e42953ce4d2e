<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Event QR Code - Evening Service</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background-color: #f4f4f4; 
        }
        .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background-color: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 30px 20px; 
            text-align: center; 
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .content { 
            padding: 30px 20px; 
            background: white; 
        }
        .qr-section { 
            text-align: center; 
            margin: 30px 0; 
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .qr-code { 
            border: 3px solid #007bff; 
            padding: 20px; 
            background: white; 
            border-radius: 10px;
            display: inline-block;
            margin: 20px 0;
        }
        .instructions { 
            background: #e3f2fd; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
        .event-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .test-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>🎟️ Your Event QR Code</h1>
            <p>Freedom Assembly Church</p>
        </div>
        
        <div class='content'>
            <div class='test-note'>
                <strong>📧 TEST EMAIL SAMPLE</strong><br>
                This is what bointa@<EMAIL> would receive when registering for an event.
            </div>
            
            <h2>Hello Bointa Test User!</h2>
            <p>Thank you for registering for <strong>Evening Service</strong>. Your personal QR code is ready!</p>
            
            <div class='event-details'>
                <h3>📅 Event Details</h3>
                <p><strong>Event:</strong> Evening Service</p>
                <p><strong>Date:</strong> July 24, 2025 1:31 PM</p>
                <p><strong>Location:</strong> Church Office</p>
            </div>
            
            <div class='qr-section'>
                <h3>🎯 Your Personal QR Code</h3>
                <div class='qr-code'>
                    <div id='qr-code-container'></div>
                </div>
                <p><strong>QR Code ID:</strong> QR_16_2815e4ffc10c8fa84714cd679e89b8c5</p>
                <a href='http://localhost/campaign/church/admin/member_checkin.php?token=QR_16_2815e4ffc10c8fa84714cd679e89b8c5' class='btn'>🔗 Open Check-in Link</a>
            </div>
            
            <div class='instructions'>
                <h3>📱 How to Use Your QR Code</h3>
                <ol>
                    <li><strong>Save this email</strong> or screenshot the QR code</li>
                    <li><strong>Arrive at the event</strong> and look for check-in stations</li>
                    <li><strong>Show your QR code</strong> to staff at the entrance</li>
                    <li><strong>Get checked in instantly</strong> - no manual process needed!</li>
                </ol>
                
                <p><strong>💡 Pro Tips:</strong></p>
                <ul>
                    <li>You can show the QR code on your phone or print this email</li>
                    <li>The QR code works even if you're offline</li>
                    <li>Each QR code is unique and can only be used once</li>
                </ul>
            </div>
            
            <div class='test-note'>
                <strong>🧪 TEST RESULTS:</strong><br>
                ✅ QR Code Generated Successfully<br>
                ✅ Check-in Process Working<br>
                ✅ Duplicate Prevention Active<br>
                ✅ Database Integration Complete<br>
                ✅ Mobile Interface Responsive
            </div>
            
            <p>If you have any questions or need assistance, please contact our event team.</p>
            <p>We look forward to seeing you at the event!</p>
            
            <p><strong>Blessings,</strong><br>
            Freedom Assembly Church Event Team</p>
        </div>
        
        <div class='footer'>
            <p>This is an automated message from Freedom Assembly Church</p>
            <p>Event Management System | QR Code Check-in</p>
        </div>
    </div>
    
    <script src='https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js'></script>
    <script>
        // Generate QR code when page loads
        document.addEventListener('DOMContentLoaded', function() {
            QRCode.toCanvas(document.getElementById('qr-code-container'), 'http://localhost/campaign/church/admin/member_checkin.php?token=QR_16_2815e4ffc10c8fa84714cd679e89b8c5', {
                width: 200,
                height: 200,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            }, function(error) {
                if (error) {
                    document.getElementById('qr-code-container').innerHTML = '<p style="padding:20px; border:2px solid #ccc;">QR Code: QR_16_2815e4ffc10c8fa84714cd679e89b8c5</p>';
                }
            });
        });
    </script>
</body>
</html>
