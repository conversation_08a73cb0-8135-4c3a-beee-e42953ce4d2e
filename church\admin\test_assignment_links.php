<?php
// Test Assignment Links - Verify View Session and View Event buttons work correctly
require_once '../config.php';

echo "<h2>🔧 Testing Assignment Dashboard Links</h2>";

try {
    // 1. Check if required pages exist
    echo "<h3>📋 Step 1: Checking Required Pages</h3>";
    
    $required_pages = [
        'session_attendance.php' => 'Session Details Page',
        'event_attendance_detail.php' => 'Event Details Page',
        'assignment_dashboard.php' => 'Assignment Dashboard',
        'setup_rbac_system.php' => 'RBAC System'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>Page</th><th>Description</th><th>Status</th></tr>";
    
    foreach ($required_pages as $page => $description) {
        $exists = file_exists($page);
        $status = $exists ? '✅ Exists' : '❌ Missing';
        echo "<tr>";
        echo "<td>$page</td>";
        echo "<td>$description</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. Check database for sample assignments
    echo "<h3>📋 Step 2: Sample Assignment Data</h3>";
    
    // Get session assignments
    $stmt = $pdo->query("
        SELECT sa.id, sa.user_id, sa.session_id, a.username, es.session_title, e.title as event_title
        FROM session_assignments sa
        JOIN admins a ON sa.user_id = a.id
        JOIN event_sessions es ON sa.session_id = es.id
        JOIN events e ON es.event_id = e.id
        WHERE sa.is_active = 1
        LIMIT 5
    ");
    $session_assignments = $stmt->fetchAll();
    
    echo "<h4>Session Assignments:</h4>";
    if (empty($session_assignments)) {
        echo "<p>❌ No session assignments found. Create some test assignments first.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>User</th><th>Session</th><th>Event</th><th>Test Link</th></tr>";
        foreach ($session_assignments as $assignment) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($assignment['username']) . "</td>";
            echo "<td>" . htmlspecialchars($assignment['session_title']) . "</td>";
            echo "<td>" . htmlspecialchars($assignment['event_title']) . "</td>";
            echo "<td><a href='session_attendance.php?session_id=" . $assignment['session_id'] . "' target='_blank'>Test Session Link</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Get event assignments
    $stmt = $pdo->query("
        SELECT ea.id, ea.user_id, ea.event_id, ea.role_type, a.username, e.title as event_title
        FROM event_assignments ea
        JOIN admins a ON ea.user_id = a.id
        JOIN events e ON ea.event_id = e.id
        WHERE ea.is_active = 1
        LIMIT 5
    ");
    $event_assignments = $stmt->fetchAll();
    
    echo "<h4>Event Assignments:</h4>";
    if (empty($event_assignments)) {
        echo "<p>❌ No event assignments found. Create some test assignments first.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>User</th><th>Event</th><th>Role</th><th>Test Link</th></tr>";
        foreach ($event_assignments as $assignment) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($assignment['username']) . "</td>";
            echo "<td>" . htmlspecialchars($assignment['event_title']) . "</td>";
            echo "<td>" . ucfirst($assignment['role_type']) . "</td>";
            echo "<td><a href='event_attendance_detail.php?event_id=" . $assignment['event_id'] . "' target='_blank'>Test Event Link</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 3. Test link generation
    echo "<h3>📋 Step 3: Link Generation Test</h3>";
    
    // Get sample session and event IDs
    $stmt = $pdo->query("SELECT id, session_title FROM event_sessions WHERE id > 0 LIMIT 1");
    $sample_session = $stmt->fetch();
    
    $stmt = $pdo->query("SELECT id, title FROM events WHERE id > 0 LIMIT 1");
    $sample_event = $stmt->fetch();
    
    echo "<h4>Generated Links:</h4>";
    echo "<ul>";
    
    if ($sample_session) {
        $session_link = "session_attendance.php?session_id=" . $sample_session['id'];
        echo "<li><strong>Session Link:</strong> <a href='$session_link' target='_blank'>$session_link</a> (" . htmlspecialchars($sample_session['session_title']) . ")</li>";
    } else {
        echo "<li>❌ No sessions found for testing</li>";
    }
    
    if ($sample_event) {
        $event_link = "event_attendance_detail.php?event_id=" . $sample_event['id'];
        echo "<li><strong>Event Link:</strong> <a href='$event_link' target='_blank'>$event_link</a> (" . htmlspecialchars($sample_event['title']) . ")</li>";
    } else {
        echo "<li>❌ No events found for testing</li>";
    }
    
    echo "</ul>";
    
    // 4. Show the fixes made
    echo "<h3>📋 Step 4: Fixes Applied</h3>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>✅ Link Fixes Applied:</strong></p>";
    echo "<ul>";
    echo "<li>✅ <strong>Session Links:</strong> Changed from <code>event_sessions.php?session_id=X</code> to <code>session_attendance.php?session_id=X</code></li>";
    echo "<li>✅ <strong>Event Links:</strong> Changed from <code>events.php?event_id=X</code> to <code>event_attendance_detail.php?event_id=X</code></li>";
    echo "<li>✅ <strong>Assignment Dashboard:</strong> Updated both session and event view buttons</li>";
    echo "<li>✅ <strong>RBAC System:</strong> Updated view buttons in session and event assignment sections</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>📝 What These Pages Do:</strong></p>";
    echo "<ul>";
    echo "<li><strong>session_attendance.php?session_id=X</strong> - Shows session details, attendance tracking, and management tools</li>";
    echo "<li><strong>event_attendance_detail.php?event_id=X</strong> - Shows event details, RSVP management, and attendance tracking</li>";
    echo "<li><strong>assignment_dashboard.php</strong> - Shows user's assigned sessions and events with proper view links</li>";
    echo "</ul>";
    echo "</div>";
    
    // 5. Create test assignments if none exist
    echo "<h3>📋 Step 5: Creating Test Assignments (if needed)</h3>";
    
    if (empty($session_assignments) && empty($event_assignments)) {
        echo "<p>Creating test assignments for demonstration...</p>";
        
        // Get first admin user (not super admin)
        $stmt = $pdo->query("SELECT id, username FROM admins WHERE id > 4 LIMIT 1");
        $test_user = $stmt->fetch();
        
        // Get first session
        $stmt = $pdo->query("SELECT id FROM event_sessions LIMIT 1");
        $test_session = $stmt->fetch();
        
        // Get first event
        $stmt = $pdo->query("SELECT id FROM events LIMIT 1");
        $test_event = $stmt->fetch();
        
        if ($test_user && $test_session) {
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO session_assignments (user_id, session_id, assigned_at, is_active)
                    VALUES (?, ?, NOW(), 1)
                    ON DUPLICATE KEY UPDATE is_active = 1
                ");
                $stmt->execute([$test_user['id'], $test_session['id']]);
                echo "✅ Created test session assignment for user: " . htmlspecialchars($test_user['username']) . "<br>";
            } catch (Exception $e) {
                echo "❌ Failed to create session assignment: " . $e->getMessage() . "<br>";
            }
        }
        
        if ($test_user && $test_event) {
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO event_assignments (user_id, event_id, role_type, assigned_at, is_active)
                    VALUES (?, ?, 'coordinator', NOW(), 1)
                    ON DUPLICATE KEY UPDATE is_active = 1
                ");
                $stmt->execute([$test_user['id'], $test_event['id']]);
                echo "✅ Created test event assignment for user: " . htmlspecialchars($test_user['username']) . "<br>";
            } catch (Exception $e) {
                echo "❌ Failed to create event assignment: " . $e->getMessage() . "<br>";
            }
        }
        
        if (!$test_user) {
            echo "❌ No test users available. Create additional admin users first.<br>";
        }
    }
    
    echo "<h3>🎯 Summary</h3>";
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>✅ Link Fix Complete!</strong></p>";
    echo "<p>The View Session and View Event buttons now correctly link to:</p>";
    echo "<ul>";
    echo "<li><strong>Sessions:</strong> session_attendance.php?session_id=X (shows session details and attendance)</li>";
    echo "<li><strong>Events:</strong> event_attendance_detail.php?event_id=X (shows event details and management)</li>";
    echo "</ul>";
    echo "<p>Users will now be taken to the correct pages that display the specific session or event details instead of generic listing pages.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>❌ Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<p>";
echo "<a href='assignment_dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📊 Test Assignment Dashboard</a>";
echo "<a href='setup_rbac_system.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔧 Test RBAC System</a>";
echo "<a href='dashboard.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 Main Dashboard</a>";
echo "</p>";
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h2, h3, h4 { color: #333; }
    table { width: 100%; margin: 10px 0; }
    th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
    p { margin: 8px 0; }
    ul, ol { margin: 10px 0; padding-left: 20px; }
    li { margin: 5px 0; }
    code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
</style>
