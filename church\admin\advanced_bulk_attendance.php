<?php
require_once '../config.php';
require_once 'includes/auth_check.php';

$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    header('Location: events.php');
    exit();
}

// Get event details
$stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
$stmt->execute([$event_id]);
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    header('Location: events.php');
    exit();
}

$message = '';
$error = '';

// Handle bulk operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'multi_session_bulk') {
            $selected_sessions = $_POST['selected_sessions'] ?? [];
            $selected_attendees = $_POST['selected_attendees'] ?? [];
            $bulk_action = $_POST['bulk_action'] ?? '';
            
            if (empty($selected_sessions)) {
                $error = "Please select at least one session.";
            } elseif (empty($selected_attendees)) {
                $error = "Please select at least one attendee.";
            } else {
                $pdo->beginTransaction();
                
                $affected_count = 0;
                foreach ($selected_sessions as $session_id) {
                    foreach ($selected_attendees as $attendee_id) {
                        // Check if attendance record exists
                        $stmt = $pdo->prepare("
                            SELECT id FROM session_attendance 
                            WHERE session_id = ? AND 
                            (member_id = ? OR CONCAT('guest_', id) = ?)
                        ");
                        $stmt->execute([$session_id, $attendee_id, $attendee_id]);
                        $existing = $stmt->fetch();
                        
                        if ($existing) {
                            // Update existing record
                            $stmt = $pdo->prepare("
                                UPDATE session_attendance 
                                SET attendance_status = ?,
                                    attendance_date = CASE WHEN ? = 'attended' THEN NOW() ELSE attendance_date END
                                WHERE id = ?
                            ");
                            $stmt->execute([$bulk_action, $bulk_action, $existing['id']]);
                            $affected_count++;
                        } else {
                            // Create new record if attendee is registered for event
                            if (is_numeric($attendee_id)) {
                                // Member
                                $stmt = $pdo->prepare("
                                    INSERT INTO session_attendance (session_id, member_id, attendance_status, attendance_date)
                                    VALUES (?, ?, ?, CASE WHEN ? = 'attended' THEN NOW() ELSE NULL END)
                                ");
                                $stmt->execute([$session_id, $attendee_id, $bulk_action, $bulk_action]);
                                $affected_count++;
                            }
                        }
                    }
                }
                
                $pdo->commit();
                $message = "Successfully updated attendance for {$affected_count} session-attendee combinations.";
            }
        } elseif ($action === 'smart_event_attendance') {
            $min_sessions = (int)($_POST['min_sessions'] ?? 1);
            $attendance_threshold = (float)($_POST['attendance_threshold'] ?? 50.0);
            
            // Get all event attendees and their session attendance
            $stmt = $pdo->prepare("
                SELECT 
                    COALESCE(er.user_id, erg.id) as attendee_id,
                    COALESCE(m.full_name, erg.guest_name) as attendee_name,
                    CASE WHEN er.user_id IS NOT NULL THEN 'member' ELSE 'guest' END as attendee_type,
                    COUNT(DISTINCT sa.session_id) as sessions_attended,
                    COUNT(DISTINCT es.id) as sessions_registered,
                    ROUND((COUNT(DISTINCT sa.session_id) / NULLIF(COUNT(DISTINCT es.id), 0)) * 100, 1) as attendance_percentage
                FROM (
                    SELECT user_id, 'member' as type FROM event_rsvps WHERE event_id = ? AND status = 'attending'
                    UNION ALL
                    SELECT id, 'guest' as type FROM event_rsvps_guests WHERE event_id = ? AND status = 'attending'
                ) attendees
                LEFT JOIN event_rsvps er ON attendees.user_id = er.user_id AND attendees.type = 'member'
                LEFT JOIN event_rsvps_guests erg ON attendees.user_id = erg.id AND attendees.type = 'guest'
                LEFT JOIN members m ON er.user_id = m.id
                LEFT JOIN session_attendance sa ON (
                    (sa.member_id = er.user_id AND attendees.type = 'member') OR
                    (sa.guest_name = erg.guest_name AND attendees.type = 'guest')
                ) AND sa.attendance_status = 'attended'
                LEFT JOIN event_sessions es ON sa.session_id = es.id AND es.event_id = ?
                GROUP BY attendee_id, attendee_name, attendee_type
                HAVING sessions_attended >= ? OR attendance_percentage >= ?
            ");
            $stmt->execute([$event_id, $event_id, $event_id, $min_sessions, $attendance_threshold]);
            $qualified_attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Mark these attendees as attended for the main event
            $updated_count = 0;
            foreach ($qualified_attendees as $attendee) {
                if ($attendee['attendee_type'] === 'member') {
                    $stmt = $pdo->prepare("
                        UPDATE event_rsvps 
                        SET actually_attended = 1 
                        WHERE event_id = ? AND user_id = ?
                    ");
                    $stmt->execute([$event_id, $attendee['attendee_id']]);
                } else {
                    $stmt = $pdo->prepare("
                        UPDATE event_rsvps_guests 
                        SET actually_attended = 1 
                        WHERE event_id = ? AND id = ?
                    ");
                    $stmt->execute([$event_id, $attendee['attendee_id']]);
                }
                $updated_count++;
            }
            
            $message = "Smart attendance applied! Marked {$updated_count} attendees as attended for the main event based on session attendance.";
        }
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        $error = "Error: " . $e->getMessage();
    }
}

// Get all sessions for this event
$stmt = $pdo->prepare("
    SELECT es.*, 
           COUNT(sa.id) as registered_count,
           COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as attended_count,
           COUNT(CASE WHEN sa.attendance_status = 'no_show' THEN 1 END) as no_show_count
    FROM event_sessions es
    LEFT JOIN session_attendance sa ON es.id = sa.session_id
    WHERE es.event_id = ? AND es.status = 'active'
    GROUP BY es.id
    ORDER BY es.start_datetime
");
$stmt->execute([$event_id]);
$sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get all event attendees (members + guests)
$stmt = $pdo->prepare("
    SELECT 
        er.user_id as id,
        m.full_name as name,
        m.email,
        'member' as type,
        er.actually_attended
    FROM event_rsvps er
    JOIN members m ON er.user_id = m.id
    WHERE er.event_id = ? AND er.status = 'attending'
    
    UNION ALL
    
    SELECT 
        erg.id,
        erg.guest_name as name,
        erg.guest_email as email,
        'guest' as type,
        erg.actually_attended
    FROM event_rsvps_guests erg
    WHERE erg.event_id = ? AND erg.status = 'attending'
    
    ORDER BY name
");
$stmt->execute([$event_id, $event_id]);
$attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'Advanced Bulk Attendance Management';
include 'includes/header.php';
?>

<style>
.session-selector {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
}

.attendee-selector {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
}

.bulk-action-panel {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.session-card {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s;
}

.session-card:hover {
    background-color: #f8f9fa;
}

.session-card.selected {
    background-color: #e3f2fd;
    border-color: #2196f3;
}

.attendee-item {
    padding: 0.5rem;
    border-bottom: 1px solid #eee;
}

.attendee-item:last-child {
    border-bottom: none;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1><i class="bi bi-diagram-3"></i> Advanced Bulk Attendance</h1>
                    <p class="text-muted mb-0">
                        <strong><?php echo htmlspecialchars($event['title']); ?></strong> • 
                        <?php echo date('M j, Y', strtotime($event['event_date'])); ?>
                    </p>
                </div>
                <div>
                    <a href="attendance_inheritance_engine.php?event_id=<?php echo $event_id; ?>" class="btn btn-primary me-2">
                        <i class="bi bi-diagram-3"></i> Inheritance Engine
                    </a>
                    <a href="multi_session_dashboard.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Quick Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 class="text-primary"><?php echo count($sessions); ?></h3>
                    <p class="mb-0">Total Sessions</p>
                </div>
                <div class="stat-card">
                    <h3 class="text-success"><?php echo count($attendees); ?></h3>
                    <p class="mb-0">Event Attendees</p>
                </div>

            <!-- Smart Event Attendance Panel -->
            <div class="bulk-action-panel">
                <h4><i class="bi bi-lightbulb"></i> Smart Event Attendance</h4>
                <p class="mb-3">Automatically mark event attendance based on session participation patterns.</p>

                <form method="POST" class="row g-3">
                    <input type="hidden" name="action" value="smart_event_attendance">

                    <div class="col-md-4">
                        <label class="form-label">Minimum Sessions Required</label>
                        <input type="number" name="min_sessions" class="form-control" value="1" min="1" max="<?php echo count($sessions); ?>">
                        <small class="text-light">Mark as attended if attended at least this many sessions</small>
                    </div>

                    <div class="col-md-4">
                        <label class="form-label">Attendance Threshold (%)</label>
                        <input type="number" name="attendance_threshold" class="form-control" value="50" min="0" max="100" step="0.1">
                        <small class="text-light">Mark as attended if attended this % of registered sessions</small>
                    </div>

                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-light btn-lg w-100">
                            <i class="bi bi-magic"></i> Apply Smart Attendance
                        </button>
                    </div>
                </form>
            </div>

            <!-- Multi-Session Bulk Operations -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-check2-all"></i> Multi-Session Bulk Operations</h5>
                    <small class="text-muted">Select sessions and attendees, then apply bulk actions across multiple sessions simultaneously</small>
                </div>
                <div class="card-body">
                    <form method="POST" id="bulkForm">
                        <input type="hidden" name="action" value="multi_session_bulk">

                        <div class="row">
                            <!-- Session Selection -->
                            <div class="col-md-6">
                                <h6><i class="bi bi-calendar-event"></i> Select Sessions</h6>
                                <div class="mb-3">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllSessions()">Select All</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearSessionSelection()">Clear All</button>
                                    <span id="sessionCount" class="badge bg-primary ms-2">0 selected</span>
                                </div>

                                <div class="session-selector">
                                    <?php foreach ($sessions as $session): ?>
                                        <div class="session-card" onclick="toggleSession(<?php echo $session['id']; ?>)">
                                            <div class="form-check">
                                                <input class="form-check-input session-checkbox" type="checkbox"
                                                       name="selected_sessions[]" value="<?php echo $session['id']; ?>"
                                                       id="session_<?php echo $session['id']; ?>">
                                                <label class="form-check-label w-100" for="session_<?php echo $session['id']; ?>">
                                                    <strong><?php echo htmlspecialchars($session['session_title']); ?></strong>
                                                    <br>
                                                    <small class="text-muted">
                                                        <?php echo date('M j, g:i A', strtotime($session['start_datetime'])); ?>
                                                        • <?php echo $session['registered_count']; ?> registered
                                                        • <?php echo $session['attended_count']; ?> attended
                                                    </small>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            <!-- Attendee Selection -->
                            <div class="col-md-6">
                                <h6><i class="bi bi-people"></i> Select Attendees</h6>
                                <div class="mb-3">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllAttendees()">Select All</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearAttendeeSelection()">Clear All</button>
                                    <span id="attendeeCount" class="badge bg-success ms-2">0 selected</span>
                                </div>

                                <div class="mb-3">
                                    <input type="text" id="attendeeSearch" class="form-control" placeholder="Search attendees...">
                                </div>

                                <div class="attendee-selector">
                                    <?php foreach ($attendees as $attendee): ?>
                                        <div class="attendee-item" data-name="<?php echo strtolower($attendee['name']); ?>">
                                            <div class="form-check">
                                                <input class="form-check-input attendee-checkbox" type="checkbox"
                                                       name="selected_attendees[]" value="<?php echo $attendee['id']; ?>"
                                                       id="attendee_<?php echo $attendee['id']; ?>">
                                                <label class="form-check-label" for="attendee_<?php echo $attendee['id']; ?>">
                                                    <strong><?php echo htmlspecialchars($attendee['name']); ?></strong>
                                                    <span class="badge bg-<?php echo $attendee['type'] === 'member' ? 'primary' : 'secondary'; ?> ms-2">
                                                        <?php echo ucfirst($attendee['type']); ?>
                                                    </span>
                                                    <?php if ($attendee['actually_attended']): ?>
                                                        <span class="badge bg-success ms-1">Event Attended</span>
                                                    <?php endif; ?>
                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Bulk Actions -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6><i class="bi bi-lightning"></i> Bulk Actions</h6>
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <select name="bulk_action" class="form-select" required>
                                                    <option value="">Choose action...</option>
                                                    <option value="attended">Mark as Attended</option>
                                                    <option value="no_show">Mark as No-Show</option>
                                                    <option value="registered">Reset to Registered</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <button type="submit" class="btn btn-primary btn-lg w-100" id="bulkSubmit" disabled>
                                                    <i class="bi bi-check2-all"></i> Apply to Selected Sessions & Attendees
                                                </button>
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                This will apply the selected action to <span id="operationCount">0</span> session-attendee combinations.
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let selectedSessions = new Set();
let selectedAttendees = new Set();

function updateCounts() {
    document.getElementById('sessionCount').textContent = selectedSessions.size + ' selected';
    document.getElementById('attendeeCount').textContent = selectedAttendees.size + ' selected';

    const operationCount = selectedSessions.size * selectedAttendees.size;
    document.getElementById('operationCount').textContent = operationCount.toLocaleString();

    const bulkSubmit = document.getElementById('bulkSubmit');
    bulkSubmit.disabled = selectedSessions.size === 0 || selectedAttendees.size === 0;
}

function toggleSession(sessionId) {
    const checkbox = document.getElementById('session_' + sessionId);
    checkbox.checked = !checkbox.checked;

    if (checkbox.checked) {
        selectedSessions.add(sessionId);
        checkbox.closest('.session-card').classList.add('selected');
    } else {
        selectedSessions.delete(sessionId);
        checkbox.closest('.session-card').classList.remove('selected');
    }

    updateCounts();
}

function selectAllSessions() {
    document.querySelectorAll('.session-checkbox').forEach(checkbox => {
        checkbox.checked = true;
        selectedSessions.add(parseInt(checkbox.value));
        checkbox.closest('.session-card').classList.add('selected');
    });
    updateCounts();
}

function clearSessionSelection() {
    document.querySelectorAll('.session-checkbox').forEach(checkbox => {
        checkbox.checked = false;
        checkbox.closest('.session-card').classList.remove('selected');
    });
    selectedSessions.clear();
    updateCounts();
}

function selectAllAttendees() {
    document.querySelectorAll('.attendee-checkbox:not([style*="display: none"])').forEach(checkbox => {
        checkbox.checked = true;
        selectedAttendees.add(checkbox.value);
    });
    updateCounts();
}

function clearAttendeeSelection() {
    document.querySelectorAll('.attendee-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    selectedAttendees.clear();
    updateCounts();
}

// Attendee search functionality
document.getElementById('attendeeSearch').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    document.querySelectorAll('.attendee-item').forEach(item => {
        const name = item.dataset.name;
        if (name.includes(searchTerm)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
});

// Track checkbox changes
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('session-checkbox')) {
        const sessionId = parseInt(e.target.value);
        if (e.target.checked) {
            selectedSessions.add(sessionId);
            e.target.closest('.session-card').classList.add('selected');
        } else {
            selectedSessions.delete(sessionId);
            e.target.closest('.session-card').classList.remove('selected');
        }
        updateCounts();
    }

    if (e.target.classList.contains('attendee-checkbox')) {
        const attendeeId = e.target.value;
        if (e.target.checked) {
            selectedAttendees.add(attendeeId);
        } else {
            selectedAttendees.delete(attendeeId);
        }
        updateCounts();
    }
});

// Form validation
document.getElementById('bulkForm').addEventListener('submit', function(e) {
    if (selectedSessions.size === 0) {
        e.preventDefault();
        alert('Please select at least one session.');
        return;
    }

    if (selectedAttendees.size === 0) {
        e.preventDefault();
        alert('Please select at least one attendee.');
        return;
    }

    const action = document.querySelector('select[name="bulk_action"]').value;
    if (!action) {
        e.preventDefault();
        alert('Please select a bulk action.');
        return;
    }

    const operationCount = selectedSessions.size * selectedAttendees.size;
    if (!confirm(`This will update ${operationCount.toLocaleString()} session-attendee combinations. Continue?`)) {
        e.preventDefault();
    }
});

// Initialize
updateCounts();
</script>

<?php include 'includes/footer.php'; ?>
                <div class="stat-card">
                    <h3 class="text-info"><?php echo array_sum(array_column($sessions, 'registered_count')); ?></h3>
                    <p class="mb-0">Session Registrations</p>
                </div>
                <div class="stat-card">
                    <h3 class="text-warning"><?php echo array_sum(array_column($sessions, 'attended_count')); ?></h3>
                    <p class="mb-0">Session Attendances</p>
                </div>
            </div>
