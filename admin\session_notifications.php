<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';
require_once '../classes/SessionNotificationManager.php';
require_once '../classes/SessionNotificationLogger.php';

// Database connection
$conn = $pdo;

$message = '';
$error = '';

// Initialize managers
$sessionNotificationManager = new SessionNotificationManager($pdo);
$logger = new SessionNotificationLogger($pdo);

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'send_test_notification':
            $sessionId = (int)$_POST['session_id'];
            $notificationType = $_POST['notification_type'];
            
            try {
                switch ($notificationType) {
                    case 'new_session':
                        $result = $sessionNotificationManager->notifyNewSession($sessionId, 'all');
                        break;
                    case 'session_update':
                        $updateMessage = $_POST['update_message'] ?? 'Test update message';
                        $result = $sessionNotificationManager->sendSessionUpdate($sessionId, $updateMessage);
                        break;
                    case 'session_reminder':
                        // Get first registered user for testing
                        $stmt = $pdo->prepare("SELECT member_id FROM session_attendance WHERE session_id = ? AND member_id IS NOT NULL LIMIT 1");
                        $stmt->execute([$sessionId]);
                        $attendee = $stmt->fetch();
                        if ($attendee) {
                            $result = $sessionNotificationManager->sendSessionReminder($sessionId, $attendee['member_id']);
                        } else {
                            $result = ['success' => false, 'message' => 'No registered members found for this session'];
                        }
                        break;
                    default:
                        $result = ['success' => false, 'message' => 'Invalid notification type'];
                }
                
                if ($result['success']) {
                    $message = "Test notification sent successfully! " . (isset($result['sent']) ? "Sent to {$result['sent']} recipients." : "");
                } else {
                    $error = "Failed to send test notification: " . $result['message'];
                }
                
            } catch (Exception $e) {
                $error = "Error sending test notification: " . $e->getMessage();
            }
            break;
            
        case 'cleanup_logs':
            $daysToKeep = (int)$_POST['days_to_keep'];
            if ($daysToKeep >= 7 && $daysToKeep <= 365) {
                $deletedCount = $logger->cleanupOldLogs($daysToKeep);
                $message = "Cleaned up $deletedCount old notification log entries.";
            } else {
                $error = "Invalid days to keep. Must be between 7 and 365.";
            }
            break;
    }
}

// Get notification statistics
$recentActivity = $logger->getRecentActivity(20);
$deliveryStats = [
    'registration_confirmation' => $logger->getDeliveryRate('registration_confirmation', 30),
    'session_reminder' => $logger->getDeliveryRate('session_reminder', 30),
    'new_session' => $logger->getDeliveryRate('new_session', 30),
    'session_update' => $logger->getDeliveryRate('session_update', 30)
];

// Get active sessions for testing
$stmt = $pdo->prepare("
    SELECT es.id, es.session_title, e.title as event_title, es.start_datetime,
           COUNT(sa.id) as registered_count
    FROM event_sessions es
    JOIN events e ON es.event_id = e.id
    LEFT JOIN session_attendance sa ON es.id = sa.session_id AND sa.attendance_status = 'registered'
    WHERE es.status = 'active' AND es.start_datetime > NOW()
    GROUP BY es.id
    ORDER BY es.start_datetime ASC
    LIMIT 10
");
$stmt->execute();
$activeSessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Notifications Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        .notification-log {
            max-height: 400px;
            overflow-y: auto;
        }
        .success-rate {
            font-weight: bold;
        }
        .success-rate.high { color: #28a745; }
        .success-rate.medium { color: #ffc107; }
        .success-rate.low { color: #dc3545; }
    </style>
</head>
<body>
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="bi bi-bell"></i> Session Notifications Management</h1>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Notification Statistics -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="stats-card p-4 mb-4">
                            <h3><i class="bi bi-graph-up"></i> Notification Performance (Last 30 Days)</h3>
                            <div class="row mt-3">
                                <?php foreach ($deliveryStats as $type => $stats): ?>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <h4><?php echo $stats['total']; ?></h4>
                                        <p class="mb-1"><?php echo ucfirst(str_replace('_', ' ', $type)); ?></p>
                                        <?php 
                                        $rate = $stats['success_rate'];
                                        $rateClass = $rate >= 90 ? 'high' : ($rate >= 70 ? 'medium' : 'low');
                                        ?>
                                        <span class="success-rate <?php echo $rateClass; ?>"><?php echo $rate; ?>% success</span>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Test Notifications -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-send"></i> Test Notifications</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="send_test_notification">
                                    
                                    <div class="mb-3">
                                        <label for="session_id" class="form-label">Select Session</label>
                                        <select class="form-select" id="session_id" name="session_id" required>
                                            <option value="">Choose a session...</option>
                                            <?php foreach ($activeSessions as $session): ?>
                                            <option value="<?php echo $session['id']; ?>">
                                                <?php echo htmlspecialchars($session['event_title'] . ' - ' . $session['session_title']); ?>
                                                (<?php echo date('M j, Y g:i A', strtotime($session['start_datetime'])); ?>)
                                                - <?php echo $session['registered_count']; ?> registered
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="notification_type" class="form-label">Notification Type</label>
                                        <select class="form-select" id="notification_type" name="notification_type" required>
                                            <option value="new_session">New Session Announcement</option>
                                            <option value="session_update">Session Update</option>
                                            <option value="session_reminder">Session Reminder</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3" id="update_message_group" style="display: none;">
                                        <label for="update_message" class="form-label">Update Message</label>
                                        <textarea class="form-control" id="update_message" name="update_message" rows="3" 
                                                  placeholder="Enter the update message..."></textarea>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-send"></i> Send Test Notification
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- System Maintenance -->
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-tools"></i> System Maintenance</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="cleanup_logs">
                                    
                                    <div class="mb-3">
                                        <label for="days_to_keep" class="form-label">Clean Up Old Logs</label>
                                        <select class="form-select" id="days_to_keep" name="days_to_keep">
                                            <option value="30">Keep last 30 days</option>
                                            <option value="60">Keep last 60 days</option>
                                            <option value="90" selected>Keep last 90 days</option>
                                            <option value="180">Keep last 180 days</option>
                                            <option value="365">Keep last 365 days</option>
                                        </select>
                                        <div class="form-text">Delete notification logs older than the selected period</div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-warning" 
                                            onclick="return confirm('Are you sure you want to delete old notification logs?')">
                                        <i class="bi bi-trash"></i> Clean Up Logs
                                    </button>
                                </form>
                                
                                <hr>
                                
                                <div class="d-grid gap-2">
                                    <a href="../cron/session_reminders.php?cron_key=fac_2024_secure_cron_8x9q2p5m" 
                                       class="btn btn-outline-primary" target="_blank">
                                        <i class="bi bi-play"></i> Run Session Reminders Manually
                                    </a>
                                    <a href="session_notification_logs.php" class="btn btn-outline-info">
                                        <i class="bi bi-list"></i> View Detailed Logs
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-clock-history"></i> Recent Notification Activity</h5>
                            </div>
                            <div class="card-body">
                                <div class="notification-log">
                                    <?php if (empty($recentActivity)): ?>
                                        <p class="text-muted">No recent notification activity.</p>
                                    <?php else: ?>
                                        <?php foreach ($recentActivity as $activity): ?>
                                        <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                                            <div>
                                                <strong><?php echo htmlspecialchars($activity['session_title'] ?? 'Unknown Session'); ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo ucfirst(str_replace('_', ' ', $activity['notification_type'])); ?>
                                                    <?php if ($activity['first_name']): ?>
                                                        to <?php echo htmlspecialchars($activity['first_name'] . ' ' . $activity['last_name']); ?>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                            <div class="text-end">
                                                <?php if ($activity['success']): ?>
                                                    <span class="badge bg-success">Success</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Failed</span>
                                                <?php endif; ?>
                                                <br>
                                                <small class="text-muted"><?php echo date('M j, g:i A', strtotime($activity['created_at'])); ?></small>
                                            </div>
                                        </div>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Show/hide update message field based on notification type
        document.getElementById('notification_type').addEventListener('change', function() {
            const updateMessageGroup = document.getElementById('update_message_group');
            if (this.value === 'session_update') {
                updateMessageGroup.style.display = 'block';
                document.getElementById('update_message').required = true;
            } else {
                updateMessageGroup.style.display = 'none';
                document.getElementById('update_message').required = false;
            }
        });
    </script>
</body>
</html>
