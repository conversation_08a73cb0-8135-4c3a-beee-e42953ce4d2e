<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include smart attendance functions
require_once 'includes/smart_attendance_functions.php';

$event_id = $_GET['event_id'] ?? '';
if (empty($event_id)) {
    header("Location: event_attendance.php");
    exit();
}

$message = '';
$error = '';

// Get event details
try {
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        header("Location: event_attendance.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading event: " . $e->getMessage();
}

// Handle smart attendance rule execution
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST['action'])) {
    try {
        $pdo->beginTransaction();
        
        if ($_POST['action'] === 'apply_smart_rules') {
            $rules = $_POST['rules'] ?? [];
            $total_marked = 0;
            $rule_results = [];
            
            foreach ($rules as $rule_type => $rule_config) {
                if (!isset($rule_config['enabled']) || !$rule_config['enabled']) {
                    continue;
                }
                
                $marked_count = 0;
                
                switch ($rule_type) {
                    case 'session_threshold':
                        $threshold_type = $rule_config['threshold_type'] ?? 'percentage';
                        $threshold_value = (float)($rule_config['threshold_value'] ?? 50);
                        
                        if ($threshold_type === 'percentage') {
                            // Mark attended if attended >= X% of registered sessions
                            $marked_count = applySessionPercentageRule($pdo, $event_id, $threshold_value);
                            $rule_results[] = "Session Percentage Rule ({$threshold_value}%): {$marked_count} attendees marked";
                        } else {
                            // Mark attended if attended >= X sessions
                            $marked_count = applySessionCountRule($pdo, $event_id, (int)$threshold_value);
                            $rule_results[] = "Session Count Rule ({$threshold_value} sessions): {$marked_count} attendees marked";
                        }
                        break;
                        
                    case 'core_sessions':
                        $core_session_ids = $rule_config['session_ids'] ?? [];
                        if (!empty($core_session_ids)) {
                            $marked_count = applyCoreSessionRule($pdo, $event_id, $core_session_ids);
                            $rule_results[] = "Core Sessions Rule: {$marked_count} attendees marked";
                        }
                        break;
                        
                    case 'time_based':
                        $start_time = $rule_config['start_time'] ?? '';
                        $end_time = $rule_config['end_time'] ?? '';
                        if ($start_time && $end_time) {
                            $marked_count = applyTimeBasedRule($pdo, $event_id, $start_time, $end_time);
                            $rule_results[] = "Time-Based Rule ({$start_time} - {$end_time}): {$marked_count} attendees marked";
                        }
                        break;
                        
                    case 'reverse_propagation':
                        $marked_count = applyReversePropagationRule($pdo, $event_id);
                        $rule_results[] = "Reverse Propagation Rule: {$marked_count} session attendance records created";
                        break;
                }
                
                $total_marked += $marked_count;
            }
            
            $pdo->commit();
            $message = "Smart attendance rules applied successfully! Total: {$total_marked} updates. " . implode('. ', $rule_results);
            
        } elseif ($_POST['action'] === 'preview_rules') {
            $rules = $_POST['rules'] ?? [];
            $preview_results = [];
            
            foreach ($rules as $rule_type => $rule_config) {
                if (!isset($rule_config['enabled']) || !$rule_config['enabled']) {
                    continue;
                }
                
                switch ($rule_type) {
                    case 'session_threshold':
                        $threshold_type = $rule_config['threshold_type'] ?? 'percentage';
                        $threshold_value = (float)($rule_config['threshold_value'] ?? 50);
                        
                        if ($threshold_type === 'percentage') {
                            $count = previewSessionPercentageRule($pdo, $event_id, $threshold_value);
                            $preview_results[] = "Session Percentage Rule ({$threshold_value}%): Would mark {$count} attendees";
                        } else {
                            $count = previewSessionCountRule($pdo, $event_id, (int)$threshold_value);
                            $preview_results[] = "Session Count Rule ({$threshold_value} sessions): Would mark {$count} attendees";
                        }
                        break;
                        
                    case 'core_sessions':
                        $core_session_ids = $rule_config['session_ids'] ?? [];
                        if (!empty($core_session_ids)) {
                            $count = previewCoreSessionRule($pdo, $event_id, $core_session_ids);
                            $preview_results[] = "Core Sessions Rule: Would mark {$count} attendees";
                        }
                        break;
                        
                    case 'time_based':
                        $start_time = $rule_config['start_time'] ?? '';
                        $end_time = $rule_config['end_time'] ?? '';
                        if ($start_time && $end_time) {
                            $count = previewTimeBasedRule($pdo, $event_id, $start_time, $end_time);
                            $preview_results[] = "Time-Based Rule ({$start_time} - {$end_time}): Would mark {$count} attendees";
                        }
                        break;
                        
                    case 'reverse_propagation':
                        $count = previewReversePropagationRule($pdo, $event_id);
                        $preview_results[] = "Reverse Propagation Rule: Would create {$count} session attendance records";
                        break;
                }
            }
            
            $message = "Preview Results: " . implode('. ', $preview_results);
        }
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $error = $e->getMessage();
    }
}

// Get event sessions for rule configuration
$stmt = $pdo->prepare("
    SELECT s.*, 
           COUNT(sa.id) as registered_count,
           COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as attended_count
    FROM event_sessions s
    LEFT JOIN session_attendance sa ON s.id = sa.session_id
    WHERE s.event_id = ? AND s.status = 'active'
    GROUP BY s.id
    ORDER BY s.start_datetime
");
$stmt->execute([$event_id]);
$sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get current attendance statistics
$stmt = $pdo->prepare("
    SELECT 
        COUNT(*) as total_rsvps,
        COUNT(CASE WHEN actually_attended = 1 THEN 1 END) as event_attended,
        COUNT(CASE WHEN actually_attended = 0 THEN 1 END) as event_not_attended,
        COUNT(CASE WHEN actually_attended IS NULL THEN 1 END) as event_not_marked
    FROM (
        SELECT actually_attended FROM event_rsvps WHERE event_id = ? AND status = 'attending'
        UNION ALL
        SELECT actually_attended FROM event_rsvps_guests WHERE event_id = ? AND status = 'attending'
    ) combined_rsvps
");
$stmt->execute([$event_id, $event_id]);
$attendance_stats = $stmt->fetch(PDO::FETCH_ASSOC);

// Page title and header info
$page_title = 'Smart Attendance Rules';
$page_header = 'Smart Attendance Rules';
$page_description = 'Configure intelligent rules to automatically mark event attendance based on session participation';

// Include header
include 'includes/header.php';
?>

<style>
.rule-card {
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}
.rule-card.enabled {
    border-color: #28a745;
    background-color: #f8fff9;
}
.rule-preview {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 10px;
    margin: 10px 0;
}
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <p class="text-muted mb-0">Event: <strong><?php echo htmlspecialchars($event['title']); ?></strong></p>
                <small class="text-muted"><?php echo date('F j, Y g:i A', strtotime($event['event_date'])); ?> • <?php echo htmlspecialchars($event['location']); ?></small>
            </div>
            <div>
                <a href="event_attendance_detail.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left"></i> Back to Event Attendance
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Current Statistics -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card stats-card">
            <div class="card-body">
                <h5 class="card-title text-white"><i class="bi bi-graph-up"></i> Current Attendance Overview</h5>
                <div class="row text-center">
                    <div class="col-md-3">
                        <h3 class="text-white"><?php echo $attendance_stats['total_rsvps']; ?></h3>
                        <small>Total RSVPs</small>
                    </div>
                    <div class="col-md-3">
                        <h3 class="text-success"><?php echo $attendance_stats['event_attended']; ?></h3>
                        <small>Event Attended</small>
                    </div>
                    <div class="col-md-3">
                        <h3 class="text-danger"><?php echo $attendance_stats['event_not_attended']; ?></h3>
                        <small>Event Not Attended</small>
                    </div>
                    <div class="col-md-3">
                        <h3 class="text-warning"><?php echo $attendance_stats['event_not_marked']; ?></h3>
                        <small>Not Marked</small>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12 text-center">
                        <h5 class="text-white">Sessions: <?php echo count($sessions); ?> active sessions</h5>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Smart Rules Configuration -->
<form method="POST" id="smart-rules-form">
    <div class="row">
        <!-- Session Threshold Rule -->
        <div class="col-md-6 mb-4">
            <div class="card rule-card" id="session-threshold-card">
                <div class="card-header">
                    <div class="form-check">
                        <input class="form-check-input rule-toggle" type="checkbox"
                               name="rules[session_threshold][enabled]" value="1"
                               id="session-threshold-enabled" onchange="toggleRule('session-threshold')">
                        <label class="form-check-label fw-bold" for="session-threshold-enabled">
                            <i class="bi bi-percent"></i> Session Threshold Rule
                        </label>
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-muted small">Mark event as attended if attendee participated in a minimum number or percentage of their registered sessions.</p>

                    <div class="rule-config" id="session-threshold-config" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Threshold Type</label>
                                <select class="form-select" name="rules[session_threshold][threshold_type]" onchange="updateThresholdType()">
                                    <option value="percentage">Percentage</option>
                                    <option value="count">Session Count</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Threshold Value</label>
                                <div class="input-group">
                                    <input type="number" class="form-control"
                                           name="rules[session_threshold][threshold_value]"
                                           value="50" min="1" max="100" id="threshold-value">
                                    <span class="input-group-text" id="threshold-unit">%</span>
                                </div>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <strong>Example:</strong> If set to 50%, attendees who attended at least 50% of their registered sessions will be marked as "attended" for the overall event.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Core Sessions Rule -->
        <div class="col-md-6 mb-4">
            <div class="card rule-card" id="core-sessions-card">
                <div class="card-header">
                    <div class="form-check">
                        <input class="form-check-input rule-toggle" type="checkbox"
                               name="rules[core_sessions][enabled]" value="1"
                               id="core-sessions-enabled" onchange="toggleRule('core-sessions')">
                        <label class="form-check-label fw-bold" for="core-sessions-enabled">
                            <i class="bi bi-star"></i> Core Sessions Rule
                        </label>
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-muted small">Mark event as attended if attendee participated in all designated core/mandatory sessions.</p>

                    <div class="rule-config" id="core-sessions-config" style="display: none;">
                        <label class="form-label">Select Core Sessions</label>
                        <div class="core-sessions-list" style="max-height: 200px; overflow-y: auto;">
                            <?php foreach ($sessions as $session): ?>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                           name="rules[core_sessions][session_ids][]"
                                           value="<?php echo $session['id']; ?>"
                                           id="core_session_<?php echo $session['id']; ?>">
                                    <label class="form-check-label" for="core_session_<?php echo $session['id']; ?>">
                                        <strong><?php echo htmlspecialchars($session['session_title']); ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <?php echo date('M j, Y g:i A', strtotime($session['start_datetime'])); ?>
                                            • <?php echo $session['attended_count']; ?>/<?php echo $session['registered_count']; ?> attended
                                        </small>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <strong>Example:</strong> If "Opening Keynote" and "Closing Ceremony" are selected, only attendees who attended both will be marked as "attended" for the overall event.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Time-Based Rule -->
        <div class="col-md-6 mb-4">
            <div class="card rule-card" id="time-based-card">
                <div class="card-header">
                    <div class="form-check">
                        <input class="form-check-input rule-toggle" type="checkbox"
                               name="rules[time_based][enabled]" value="1"
                               id="time-based-enabled" onchange="toggleRule('time-based')">
                        <label class="form-check-label fw-bold" for="time-based-enabled">
                            <i class="bi bi-clock"></i> Time-Based Rule
                        </label>
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-muted small">Mark event as attended if attendee was present during core event hours (based on session attendance).</p>

                    <div class="rule-config" id="time-based-config" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Core Start Time</label>
                                <input type="time" class="form-control"
                                       name="rules[time_based][start_time]"
                                       value="09:00">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Core End Time</label>
                                <input type="time" class="form-control"
                                       name="rules[time_based][end_time]"
                                       value="17:00">
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <strong>Example:</strong> If set to 9:00 AM - 5:00 PM, attendees who attended any session during these core hours will be marked as "attended" for the overall event.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reverse Propagation Rule -->
        <div class="col-md-6 mb-4">
            <div class="card rule-card" id="reverse-propagation-card">
                <div class="card-header">
                    <div class="form-check">
                        <input class="form-check-input rule-toggle" type="checkbox"
                               name="rules[reverse_propagation][enabled]" value="1"
                               id="reverse-propagation-enabled" onchange="toggleRule('reverse-propagation')">
                        <label class="form-check-label fw-bold" for="reverse-propagation-enabled">
                            <i class="bi bi-arrow-down"></i> Reverse Propagation Rule
                        </label>
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-muted small">For attendees marked as "attended" at event level, automatically mark them as "attended" for all their registered sessions.</p>

                    <div class="rule-config" id="reverse-propagation-config" style="display: none;">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>Top-Down Attendance:</strong> This rule works in reverse - it takes event-level attendance and applies it to session-level attendance. Useful when you know someone attended the overall event but want to mark their individual sessions.
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <strong>Example:</strong> If John is marked as "attended" for the overall event, this rule will automatically mark him as "attended" for all sessions he registered for.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body text-center">
                    <div class="btn-group" role="group">
                        <button type="submit" name="action" value="preview_rules" class="btn btn-outline-info">
                            <i class="bi bi-eye"></i> Preview Results
                        </button>
                        <button type="submit" name="action" value="apply_smart_rules" class="btn btn-success"
                                onclick="return confirmApplyRules()">
                            <i class="bi bi-lightning"></i> Apply Smart Rules
                        </button>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="bi bi-info-circle"></i>
                            Preview shows what would happen without making changes. Apply executes the rules and updates attendance records.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Rule toggle functionality
function toggleRule(ruleType) {
    const checkbox = document.getElementById(ruleType + '-enabled');
    const card = document.getElementById(ruleType + '-card');
    const config = document.getElementById(ruleType + '-config');

    if (checkbox.checked) {
        card.classList.add('enabled');
        config.style.display = 'block';
    } else {
        card.classList.remove('enabled');
        config.style.display = 'none';
    }
}

// Update threshold type display
function updateThresholdType() {
    const thresholdType = document.querySelector('select[name="rules[session_threshold][threshold_type]"]').value;
    const thresholdValue = document.getElementById('threshold-value');
    const thresholdUnit = document.getElementById('threshold-unit');

    if (thresholdType === 'percentage') {
        thresholdValue.max = 100;
        thresholdValue.value = Math.min(thresholdValue.value, 100);
        thresholdUnit.textContent = '%';
    } else {
        thresholdValue.max = <?php echo count($sessions); ?>;
        thresholdValue.value = Math.min(thresholdValue.value, <?php echo count($sessions); ?>);
        thresholdUnit.textContent = 'sessions';
    }
}

// Confirmation for applying rules
function confirmApplyRules() {
    const enabledRules = document.querySelectorAll('.rule-toggle:checked');

    if (enabledRules.length === 0) {
        alert('Please enable at least one rule before applying.');
        return false;
    }

    const ruleNames = Array.from(enabledRules).map(checkbox => {
        return checkbox.closest('.card').querySelector('.form-check-label').textContent.trim();
    });

    const message = `You are about to apply the following smart attendance rules:\n\n${ruleNames.join('\n')}\n\nThis will automatically update attendance records based on session participation. This action cannot be undone.\n\nContinue?`;

    return confirm(message);
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Initialize rule states
    document.querySelectorAll('.rule-toggle').forEach(function(checkbox) {
        const ruleType = checkbox.id.replace('-enabled', '');
        toggleRule(ruleType);
    });

    // Update threshold type on page load
    updateThresholdType();
});
</script>

<?php include 'includes/footer.php'; ?>
