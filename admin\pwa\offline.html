<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Universal Event Manager</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2rem;
        }
        
        .offline-container {
            max-width: 400px;
            width: 100%;
        }
        
        .offline-icon {
            font-size: 4rem;
            margin-bottom: 2rem;
            opacity: 0.8;
        }
        
        .offline-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .offline-message {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.5;
        }
        
        .offline-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: 2px solid white;
            background: transparent;
            color: white;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: white;
            color: #667eea;
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: white;
            color: #667eea;
        }
        
        .btn-primary:hover {
            background: transparent;
            color: white;
        }
        
        .offline-features {
            margin-top: 3rem;
            text-align: left;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        
        .feature-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
        }
        
        .connection-status {
            position: fixed;
            top: 2rem;
            right: 2rem;
            padding: 0.5rem 1rem;
            background: rgba(255,255,255,0.2);
            border-radius: 20px;
            font-size: 0.9rem;
            backdrop-filter: blur(10px);
        }
        
        .status-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #dc3545;
            margin-right: 0.5rem;
            animation: pulse 2s infinite;
        }
        
        .status-dot.online {
            background: #28a745;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        @media (max-width: 480px) {
            .offline-title {
                font-size: 1.5rem;
            }
            
            .offline-message {
                font-size: 1rem;
            }
            
            .connection-status {
                top: 1rem;
                right: 1rem;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="connection-status">
        <span class="status-dot" id="statusDot"></span>
        <span id="statusText">Offline</span>
    </div>
    
    <div class="offline-container">
        <div class="offline-icon">📡</div>
        
        <h1 class="offline-title">You're Offline</h1>
        
        <p class="offline-message">
            No internet connection detected. Don't worry - you can still access some features 
            and your data will sync when you're back online.
        </p>
        
        <div class="offline-actions">
            <button class="btn btn-primary" onclick="retryConnection()">
                Try Again
            </button>
            <a href="index.php" class="btn">
                Go to Dashboard
            </a>
        </div>
        
        <div class="offline-features">
            <h3 style="margin-bottom: 1rem; opacity: 0.9;">Available Offline:</h3>
            
            <div class="feature-item">
                <div class="feature-icon">📊</div>
                <span>View cached event data</span>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">✅</div>
                <span>Record attendance (syncs later)</span>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">📱</div>
                <span>Access QR scanner</span>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">📈</div>
                <span>View analytics reports</span>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">⚙️</div>
                <span>Manage app settings</span>
            </div>
        </div>
    </div>
    
    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            
            if (navigator.onLine) {
                statusDot.classList.add('online');
                statusText.textContent = 'Back Online!';
                
                // Redirect to main app after a short delay
                setTimeout(() => {
                    window.location.href = 'index.php';
                }, 2000);
            } else {
                statusDot.classList.remove('online');
                statusText.textContent = 'Offline';
            }
        }
        
        function retryConnection() {
            const button = event.target;
            const originalText = button.textContent;
            
            button.textContent = 'Checking...';
            button.disabled = true;
            
            setTimeout(() => {
                updateConnectionStatus();
                button.textContent = originalText;
                button.disabled = false;
                
                if (!navigator.onLine) {
                    // Show temporary message
                    const message = document.createElement('div');
                    message.style.cssText = `
                        position: fixed;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        background: rgba(220, 53, 69, 0.9);
                        color: white;
                        padding: 1rem 2rem;
                        border-radius: 10px;
                        font-weight: 500;
                        z-index: 1000;
                    `;
                    message.textContent = 'Still offline - please check your connection';
                    document.body.appendChild(message);
                    
                    setTimeout(() => {
                        message.remove();
                    }, 3000);
                }
            }, 1000);
        }
        
        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial status check
        updateConnectionStatus();
        
        // Periodic connection check
        setInterval(updateConnectionStatus, 5000);
        
        // Add some interactive animations
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.offline-container');
            container.style.animation = 'fadeIn 0.5s ease';
            
            // Add fadeIn animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeIn {
                    from {
                        opacity: 0;
                        transform: translateY(20px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
            `;
            document.head.appendChild(style);
        });
        
        // Handle back button
        window.addEventListener('popstate', function(e) {
            // Try to go to main app if online
            if (navigator.onLine) {
                window.location.href = 'index.php';
            }
        });
        
        // Service worker communication
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', function(event) {
                if (event.data && event.data.type === 'CONNECTIVITY_CHANGED') {
                    updateConnectionStatus();
                }
            });
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'r' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                retryConnection();
            }
            
            if (e.key === 'Escape') {
                window.location.href = 'index.php';
            }
        });
    </script>
</body>
</html>
