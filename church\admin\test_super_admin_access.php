<?php
// Test script to verify super admin has access to all menu items
session_start();

// Set super admin session
$_SESSION['admin_id'] = 4;
$_SESSION['admin_username'] = 'admin';
$_SESSION['admin_role'] = 'super_admin';

require_once '../config.php';
require_once 'includes/rbac_access_control_granular.php';

// Include the canAccessPage function from sidebar
if (!function_exists('canAccessPage')) {
    // Helper function to check if user can access a page using granular permissions
    function canAccessPage($page) {
        global $current_user_id;

        // Super admins have access to everything - bypass all permission checks
        if (isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === 'super_admin') {
            return true;
        }

        // Map pages to granular permissions
        $page_permissions = [
            'dashboard.php' => 'dashboard.view',
            'members.php' => 'members.view',
            'add_member.php' => 'members.add',
            'edit_member.php' => 'members.edit',
            'member_skills.php' => 'members.skills',
            'family_management.php' => 'members.family_management',
            'requests.php' => 'members.requests',
            'prayer_requests.php' => 'members.requests',
            'volunteer_opportunities.php' => 'members.volunteers',
            'volunteer_applications.php' => 'members.volunteers',
            'volunteer_hours.php' => 'members.volunteers',
            'events.php' => 'events.view',
            'add_event.php' => 'events.create',
            'edit_event.php' => 'events.edit',
            'event_attendance.php' => 'events.attendance',
            'event_categories.php' => 'events.categories',
            'event_sessions.php' => 'events.sessions',
            'event_reports.php' => 'events.reports',
            'email_templates.php' => 'email.templates',
            'email_scheduler.php' => 'email.scheduler',
            'email_contacts.php' => 'email.contacts',
            'contact_groups.php' => 'email.contact_groups',
            'birthday_messages.php' => 'email.birthday_messages',
            'send_birthday_emails.php' => 'email.send_birthday_emails',
            'test_birthday_emails.php' => 'email.test_birthday_emails',
            'bulk_email.php' => 'email.bulk_send',
            'manage_user_permissions.php' => 'admin.user_permissions',
            'create_admin_users.php' => 'admin.create_users',
            'system_test_dashboard.php' => 'admin.system_testing',
            'super_admin_navigation.php' => 'admin.navigation_guide',
            'settings.php' => 'admin.settings',
        ];

        // Get the permission required for this page
        $required_permission = $page_permissions[$page] ?? null;

        if (!$required_permission) {
            // If no specific permission is mapped, allow access (backward compatibility)
            return true;
        }

        // Check if user has the required permission
        $user_id = $_SESSION['admin_id'] ?? null;
        if (!$user_id) {
            return false;
        }

        return hasUserPermission($user_id, $required_permission);
    }
}

// Include functions
if (!function_exists('get_organization_name')) {
    function get_organization_name() {
        return 'Freedom Assembly Church';
    }
}

if (!function_exists('get_site_setting')) {
    function get_site_setting($key, $default = '') {
        return $default;
    }
}

if (!function_exists('admin_url_for')) {
    function admin_url_for($page) {
        return $page;
    }
}

if (!function_exists('is_active')) {
    function is_active($page) {
        return '';
    }
}

echo "<h2>🔐 Super Admin Access Test</h2>";

// Test permission functions
echo "<h3>✅ Permission Function Tests:</h3>";

$test_permissions = [
    'dashboard.view',
    'members.view',
    'members.requests',
    'members.volunteers',
    'events.view',
    'email.templates',
    'email.birthday_messages',
    'admin.user_permissions',
    'admin.create_users'
];

echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>Permission</th><th>hasUserPermission()</th><th>Expected</th><th>Status</th></tr>";

foreach ($test_permissions as $permission) {
    $has_permission = hasUserPermission($_SESSION['admin_id'], $permission);
    $expected = true; // Super admin should have all permissions
    $status = ($has_permission === $expected) ? '✅ PASS' : '❌ FAIL';
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($permission) . "</td>";
    echo "<td>" . ($has_permission ? 'true' : 'false') . "</td>";
    echo "<td>true</td>";
    echo "<td style='color: " . ($status === '✅ PASS' ? 'green' : 'red') . ";'>" . $status . "</td>";
    echo "</tr>";
}
echo "</table>";

// Test page access function
echo "<h3>✅ Page Access Function Tests:</h3>";

$test_pages = [
    'dashboard.php',
    'members.php',
    'requests.php',
    'volunteer_opportunities.php',
    'events.php',
    'email_templates.php',
    'create_admin_users.php',
    'manage_user_permissions.php',
    'system_test_dashboard.php',
    'settings.php'
];

echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>Page</th><th>canAccessPage()</th><th>Expected</th><th>Status</th></tr>";

foreach ($test_pages as $page) {
    $can_access = canAccessPage($page);
    $expected = true; // Super admin should access all pages
    $status = ($can_access === $expected) ? '✅ PASS' : '❌ FAIL';
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($page) . "</td>";
    echo "<td>" . ($can_access ? 'true' : 'false') . "</td>";
    echo "<td>true</td>";
    echo "<td style='color: " . ($status === '✅ PASS' ? 'green' : 'red') . ";'>" . $status . "</td>";
    echo "</tr>";
}
echo "</table>";

// Test session role
echo "<h3>✅ Session Information:</h3>";
echo "<p><strong>Admin ID:</strong> " . ($_SESSION['admin_id'] ?? 'Not set') . "</p>";
echo "<p><strong>Username:</strong> " . ($_SESSION['admin_username'] ?? 'Not set') . "</p>";
echo "<p><strong>Role:</strong> " . ($_SESSION['admin_role'] ?? 'Not set') . "</p>";
echo "<p><strong>Is Super Admin:</strong> " . (($_SESSION['admin_role'] ?? '') === 'super_admin' ? '✅ Yes' : '❌ No') . "</p>";

echo "<h3>🎯 Expected Results:</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Super Admin Should:</strong></p>";
echo "<ul>";
echo "<li>✅ Have ALL permissions return <code>true</code></li>";
echo "<li>✅ Have ALL page access return <code>true</code></li>";
echo "<li>✅ See ALL menu items in the sidebar</li>";
echo "<li>✅ Bypass all permission checks</li>";
echo "<li>✅ Have unrestricted access to the system</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔧 Implementation Details:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Changes Made:</strong></p>";
echo "<ul>";
echo "<li>✅ Updated <code>hasUserPermission()</code> to return <code>true</code> for super admins</li>";
echo "<li>✅ Updated <code>canAccessPage()</code> to return <code>true</code> for super admins</li>";
echo "<li>✅ Added bypass logic: <code>if (\$_SESSION['admin_role'] === 'super_admin') return true;</code></li>";
echo "<li>✅ Added missing menu items to sidebar for comprehensive access</li>";
echo "</ul>";
echo "</div>";

echo "<p><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>→ Test in Admin Panel</a></p>";
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h2, h3 { color: #333; }
    table { width: 100%; margin: 10px 0; }
    th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
    p { margin: 8px 0; }
    ul { margin: 10px 0; padding-left: 20px; }
    li { margin: 5px 0; }
</style>
