<?php
require_once '../config.php';
require_once 'includes/auth_check.php';

$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    header('Location: events.php');
    exit();
}

// Get event details
$stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
$stmt->execute([$event_id]);
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    header('Location: events.php');
    exit();
}

$message = '';
$error = '';

// Handle cross-session operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'cross_session_bulk') {
            $selected_attendees = $_POST['selected_attendees'] ?? [];
            $session_actions = $_POST['session_actions'] ?? [];
            
            if (empty($selected_attendees)) {
                $error = "Please select at least one attendee.";
            } elseif (empty($session_actions)) {
                $error = "Please specify actions for at least one session.";
            } else {
                $pdo->beginTransaction();
                $affected_count = 0;
                
                foreach ($selected_attendees as $attendee_id) {
                    foreach ($session_actions as $session_id => $action_type) {
                        if ($action_type === 'no_change') continue;
                        
                        // Check if attendance record exists
                        $stmt = $pdo->prepare("
                            SELECT id FROM session_attendance 
                            WHERE session_id = ? AND 
                            (member_id = ? OR (guest_name IS NOT NULL AND CONCAT('guest_', id) = ?))
                        ");
                        $stmt->execute([$session_id, $attendee_id, $attendee_id]);
                        $existing = $stmt->fetch();
                        
                        if ($existing) {
                            // Update existing record
                            $stmt = $pdo->prepare("
                                UPDATE session_attendance 
                                SET attendance_status = ?,
                                    attendance_date = CASE WHEN ? = 'attended' THEN NOW() ELSE attendance_date END
                                WHERE id = ?
                            ");
                            $stmt->execute([$action_type, $action_type, $existing['id']]);
                            $affected_count++;
                        } else {
                            // Create new record if attendee is registered for event
                            if (is_numeric($attendee_id)) {
                                $stmt = $pdo->prepare("
                                    INSERT INTO session_attendance (session_id, member_id, attendance_status, attendance_date)
                                    VALUES (?, ?, ?, CASE WHEN ? = 'attended' THEN NOW() ELSE NULL END)
                                ");
                                $stmt->execute([$session_id, $attendee_id, $action_type, $action_type]);
                                $affected_count++;
                            }
                        }
                    }
                }
                
                $pdo->commit();
                $message = "Successfully updated {$affected_count} attendance records across sessions.";
            }
        } elseif ($action === 'copy_attendance') {
            $source_session = (int)$_POST['source_session'];
            $target_sessions = $_POST['target_sessions'] ?? [];
            $copy_type = $_POST['copy_type'] ?? 'attended_only';
            
            if (!$source_session || empty($target_sessions)) {
                $error = "Please select source session and at least one target session.";
            } else {
                $pdo->beginTransaction();
                $copied_count = 0;
                
                // Get attendance from source session
                $where_clause = $copy_type === 'attended_only' ? "AND attendance_status = 'attended'" : "";
                $stmt = $pdo->prepare("
                    SELECT member_id, guest_name, guest_email, attendance_status
                    FROM session_attendance 
                    WHERE session_id = ? $where_clause
                ");
                $stmt->execute([$source_session]);
                $source_attendance = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                foreach ($target_sessions as $target_session) {
                    foreach ($source_attendance as $attendance) {
                        // Check if record already exists
                        $stmt = $pdo->prepare("
                            SELECT id FROM session_attendance 
                            WHERE session_id = ? AND 
                            ((member_id = ? AND member_id IS NOT NULL) OR 
                             (guest_name = ? AND guest_name IS NOT NULL))
                        ");
                        $stmt->execute([$target_session, $attendance['member_id'], $attendance['guest_name']]);
                        $existing = $stmt->fetch();
                        
                        if (!$existing) {
                            // Create new record
                            $stmt = $pdo->prepare("
                                INSERT INTO session_attendance 
                                (session_id, member_id, guest_name, guest_email, attendance_status, attendance_date)
                                VALUES (?, ?, ?, ?, ?, CASE WHEN ? = 'attended' THEN NOW() ELSE NULL END)
                            ");
                            $stmt->execute([
                                $target_session,
                                $attendance['member_id'],
                                $attendance['guest_name'],
                                $attendance['guest_email'],
                                $attendance['attendance_status'],
                                $attendance['attendance_status']
                            ]);
                            $copied_count++;
                        }
                    }
                }
                
                $pdo->commit();
                $message = "Successfully copied {$copied_count} attendance records to target sessions.";
            }
        }
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        $error = "Error: " . $e->getMessage();
    }
}

// Get all sessions for this event
$stmt = $pdo->prepare("
    SELECT es.*, 
           COUNT(sa.id) as registered_count,
           COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as attended_count,
           COUNT(CASE WHEN sa.attendance_status = 'no_show' THEN 1 END) as no_show_count,
           ROUND((COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) / 
                  NULLIF(COUNT(sa.id), 0)) * 100, 1) as attendance_rate
    FROM event_sessions es
    LEFT JOIN session_attendance sa ON es.id = sa.session_id
    WHERE es.event_id = ? AND es.status = 'active'
    GROUP BY es.id
    ORDER BY es.start_datetime
");
$stmt->execute([$event_id]);
$sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get comprehensive attendee data with cross-session attendance
$stmt = $pdo->prepare("
    SELECT 
        attendees.id,
        attendees.name,
        attendees.email,
        attendees.type,
        attendees.event_attended,
        COUNT(DISTINCT sa.session_id) as sessions_registered,
        COUNT(DISTINCT CASE WHEN sa.attendance_status = 'attended' THEN sa.session_id END) as sessions_attended,
        COUNT(DISTINCT CASE WHEN sa.attendance_status = 'no_show' THEN sa.session_id END) as sessions_no_show,
        GROUP_CONCAT(
            CONCAT(sa.session_id, ':', sa.attendance_status) 
            ORDER BY sa.session_id SEPARATOR ','
        ) as session_attendance_map
    FROM (
        SELECT 
            er.user_id as id,
            m.full_name as name,
            m.email,
            'member' as type,
            er.actually_attended as event_attended
        FROM event_rsvps er
        JOIN members m ON er.user_id = m.id
        WHERE er.event_id = ? AND er.status = 'attending'
        
        UNION ALL
        
        SELECT 
            erg.id,
            erg.guest_name as name,
            erg.guest_email as email,
            'guest' as type,
            erg.actually_attended as event_attended
        FROM event_rsvps_guests erg
        WHERE erg.event_id = ? AND erg.status = 'attending'
    ) attendees
    LEFT JOIN session_attendance sa ON (
        (attendees.type = 'member' AND sa.member_id = attendees.id) OR
        (attendees.type = 'guest' AND sa.guest_name = attendees.name)
    )
    LEFT JOIN event_sessions es ON sa.session_id = es.id AND es.event_id = ?
    GROUP BY attendees.id, attendees.name, attendees.email, attendees.type, attendees.event_attended
    ORDER BY attendees.name
");
$stmt->execute([$event_id, $event_id, $event_id]);
$attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Process session attendance map for each attendee
foreach ($attendees as &$attendee) {
    $attendance_map = [];
    if ($attendee['session_attendance_map']) {
        $pairs = explode(',', $attendee['session_attendance_map']);
        foreach ($pairs as $pair) {
            list($session_id, $status) = explode(':', $pair);
            $attendance_map[$session_id] = $status;
        }
    }
    $attendee['attendance_map'] = $attendance_map;
}

$page_title = 'Cross-Session Attendance Dashboard';
include 'includes/header.php';
?>

<style>
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.session-header {
    writing-mode: vertical-rl;
    text-orientation: mixed;
    padding: 0.5rem;
    min-width: 120px;
    font-size: 0.8rem;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
}

.attendance-cell {
    text-align: center;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    cursor: pointer;
    transition: all 0.2s;
}

.attendance-cell:hover {
    background-color: #f8f9fa;
}

.attendance-cell.attended {
    background-color: #d4edda;
    color: #155724;
}

.attendance-cell.no_show {
    background-color: #f8d7da;
    color: #721c24;
}

.attendance-cell.registered {
    background-color: #fff3cd;
    color: #856404;
}

.attendance-cell.not_registered {
    background-color: #f8f9fa;
    color: #6c757d;
}

.attendee-row {
    transition: all 0.2s;
}

.attendee-row:hover {
    background-color: #f8f9fa;
}

.attendee-row.selected {
    background-color: #e3f2fd;
}

.cross-session-table {
    overflow-x: auto;
    max-height: 600px;
    overflow-y: auto;
}

.sticky-column {
    position: sticky;
    left: 0;
    background: white;
    z-index: 10;
    border-right: 2px solid #dee2e6;
}

.action-panel {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="dashboard-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="bi bi-grid-3x3-gap"></i> Cross-Session Attendance Dashboard</h1>
                        <p class="mb-0">
                            <strong><?php echo htmlspecialchars($event['title']); ?></strong> •
                            <?php echo date('M j, Y', strtotime($event['event_date'])); ?>
                        </p>
                    </div>
                    <div>
                        <a href="smart_attendance_engine.php?event_id=<?php echo $event_id; ?>" class="btn btn-light me-2">
                            <i class="bi bi-lightbulb"></i> Smart Engine
                        </a>
                        <a href="advanced_bulk_attendance.php?event_id=<?php echo $event_id; ?>" class="btn btn-light me-2">
                            <i class="bi bi-diagram-3"></i> Bulk Operations
                        </a>
                        <a href="multi_session_dashboard.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-light">
                            <i class="bi bi-arrow-left"></i> Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Quick Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h4 class="text-primary"><?php echo count($sessions); ?></h4>
                    <small>Sessions</small>
                </div>
                <div class="stat-card">
                    <h4 class="text-success"><?php echo count($attendees); ?></h4>
                    <small>Attendees</small>
                </div>
                <div class="stat-card">
                    <h4 class="text-info"><?php echo array_sum(array_column($sessions, 'registered_count')); ?></h4>
                    <small>Total Registrations</small>
                </div>
                <div class="stat-card">
                    <h4 class="text-warning"><?php echo array_sum(array_column($sessions, 'attended_count')); ?></h4>
                    <small>Total Attended</small>
                </div>
                <div class="stat-card">
                    <h4 class="text-secondary"><?php echo count(array_filter($attendees, function($a) { return $a['event_attended']; })); ?></h4>
                    <small>Event Attended</small>
                </div>
            </div>

            <!-- Action Panels -->
            <div class="row mb-4">
                <!-- Cross-Session Bulk Actions -->
                <div class="col-md-6">
                    <div class="action-panel">
                        <h6><i class="bi bi-check2-all"></i> Cross-Session Bulk Actions</h6>
                        <form method="POST" id="crossSessionForm">
                            <input type="hidden" name="action" value="cross_session_bulk">

                            <div class="mb-3">
                                <label class="form-label">Select attendees and set session actions below</label>
                                <div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllAttendees()">Select All</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearAttendeeSelection()">Clear All</button>
                                    <span id="selectedCount" class="badge bg-primary ms-2">0 selected</span>
                                </div>
                            </div>

                            <div class="row">
                                <?php foreach ($sessions as $session): ?>
                                    <div class="col-md-6 mb-2">
                                        <label class="form-label small"><?php echo htmlspecialchars($session['session_title']); ?></label>
                                        <select name="session_actions[<?php echo $session['id']; ?>]" class="form-select form-select-sm">
                                            <option value="no_change">No Change</option>
                                            <option value="attended">Mark Attended</option>
                                            <option value="no_show">Mark No-Show</option>
                                            <option value="registered">Mark Registered</option>
                                        </select>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <button type="submit" class="btn btn-primary" id="crossSessionSubmit" disabled>
                                <i class="bi bi-check2-all"></i> Apply Actions
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Copy Attendance -->
                <div class="col-md-6">
                    <div class="action-panel">
                        <h6><i class="bi bi-files"></i> Copy Attendance Between Sessions</h6>
                        <form method="POST">
                            <input type="hidden" name="action" value="copy_attendance">

                            <div class="mb-3">
                                <label class="form-label">Source Session</label>
                                <select name="source_session" class="form-select" required>
                                    <option value="">Choose source session...</option>
                                    <?php foreach ($sessions as $session): ?>
                                        <option value="<?php echo $session['id']; ?>">
                                            <?php echo htmlspecialchars($session['session_title']); ?>
                                            (<?php echo $session['attended_count']; ?> attended)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Target Sessions</label>
                                <div style="max-height: 120px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 0.5rem;">
                                    <?php foreach ($sessions as $session): ?>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="target_sessions[]"
                                                   value="<?php echo $session['id']; ?>" id="target_<?php echo $session['id']; ?>">
                                            <label class="form-check-label" for="target_<?php echo $session['id']; ?>">
                                                <?php echo htmlspecialchars($session['session_title']); ?>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Copy Type</label>
                                <select name="copy_type" class="form-select">
                                    <option value="attended_only">Attended Only</option>
                                    <option value="all_statuses">All Statuses</option>
                                </select>
                            </div>

                            <button type="submit" class="btn btn-secondary">
                                <i class="bi bi-files"></i> Copy Attendance
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Cross-Session Attendance Matrix -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-table"></i> Cross-Session Attendance Matrix</h5>
                    <small class="text-muted">Click cells to toggle attendance status • Green = Attended, Red = No-Show, Yellow = Registered, Gray = Not Registered</small>
                </div>
                <div class="card-body p-0">
                    <div class="cross-session-table">
                        <table class="table table-sm mb-0">
                            <thead>
                                <tr>
                                    <th class="sticky-column" style="min-width: 200px;">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAllCheckbox" onchange="toggleAllAttendees()">
                                            <label class="form-check-label" for="selectAllCheckbox">
                                                Attendee
                                            </label>
                                        </div>
                                    </th>
                                    <th class="sticky-column" style="width: 80px;">Type</th>
                                    <th class="sticky-column" style="width: 100px;">Event Status</th>
                                    <?php foreach ($sessions as $session): ?>
                                        <th class="session-header">
                                            <div title="<?php echo htmlspecialchars($session['session_title']); ?>">
                                                <?php echo htmlspecialchars(substr($session['session_title'], 0, 15)); ?>
                                                <?php if (strlen($session['session_title']) > 15): ?>...<?php endif; ?>
                                                <br>
                                                <small class="text-muted">
                                                    <?php echo date('M j', strtotime($session['start_datetime'])); ?>
                                                    <br>
                                                    <?php echo $session['attended_count']; ?>/<?php echo $session['registered_count']; ?>
                                                </small>
                                            </div>
                                        </th>
                                    <?php endforeach; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($attendees as $attendee): ?>
                                    <tr class="attendee-row" data-attendee-id="<?php echo $attendee['id']; ?>">
                                        <td class="sticky-column">
                                            <div class="form-check">
                                                <input class="form-check-input attendee-checkbox" type="checkbox"
                                                       name="selected_attendees[]" value="<?php echo $attendee['id']; ?>"
                                                       id="attendee_<?php echo $attendee['id']; ?>"
                                                       onchange="updateSelectedCount()">
                                                <label class="form-check-label" for="attendee_<?php echo $attendee['id']; ?>">
                                                    <strong><?php echo htmlspecialchars($attendee['name']); ?></strong>
                                                </label>
                                            </div>
                                        </td>
                                        <td class="sticky-column">
                                            <span class="badge bg-<?php echo $attendee['type'] === 'member' ? 'primary' : 'secondary'; ?>">
                                                <?php echo ucfirst($attendee['type']); ?>
                                            </span>
                                        </td>
                                        <td class="sticky-column">
                                            <?php if ($attendee['event_attended']): ?>
                                                <span class="badge bg-success">Attended</span>
                                            <?php else: ?>
                                                <span class="badge bg-light text-dark">Pending</span>
                                            <?php endif; ?>
                                        </td>
                                        <?php foreach ($sessions as $session): ?>
                                            <?php
                                            $status = $attendee['attendance_map'][$session['id']] ?? 'not_registered';
                                            $icon = '';
                                            $text = '';
                                            switch ($status) {
                                                case 'attended':
                                                    $icon = 'bi-check-circle-fill';
                                                    $text = '✓';
                                                    break;
                                                case 'no_show':
                                                    $icon = 'bi-x-circle-fill';
                                                    $text = '✗';
                                                    break;
                                                case 'registered':
                                                    $icon = 'bi-clock-fill';
                                                    $text = '○';
                                                    break;
                                                default:
                                                    $icon = 'bi-dash';
                                                    $text = '—';
                                            }
                                            ?>
                                            <td class="attendance-cell <?php echo $status; ?>"
                                                data-attendee="<?php echo $attendee['id']; ?>"
                                                data-session="<?php echo $session['id']; ?>"
                                                data-status="<?php echo $status; ?>"
                                                title="<?php echo ucfirst(str_replace('_', ' ', $status)); ?>">
                                                <i class="bi <?php echo $icon; ?>"></i>
                                                <?php echo $text; ?>
                                            </td>
                                        <?php endforeach; ?>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let selectedAttendees = new Set();

function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.attendee-checkbox:checked');
    selectedAttendees.clear();

    checkboxes.forEach(checkbox => {
        selectedAttendees.add(checkbox.value);
        checkbox.closest('.attendee-row').classList.add('selected');
    });

    document.querySelectorAll('.attendee-checkbox:not(:checked)').forEach(checkbox => {
        checkbox.closest('.attendee-row').classList.remove('selected');
    });

    document.getElementById('selectedCount').textContent = selectedAttendees.size + ' selected';
    document.getElementById('crossSessionSubmit').disabled = selectedAttendees.size === 0;
}

function selectAllAttendees() {
    document.querySelectorAll('.attendee-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedCount();
}

function clearAttendeeSelection() {
    document.querySelectorAll('.attendee-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedCount();
}

function toggleAllAttendees() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    document.querySelectorAll('.attendee-checkbox').forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    updateSelectedCount();
}

// Handle attendance cell clicks
document.addEventListener('click', function(e) {
    if (e.target.closest('.attendance-cell')) {
        const cell = e.target.closest('.attendance-cell');
        const currentStatus = cell.dataset.status;
        const attendeeId = cell.dataset.attendee;
        const sessionId = cell.dataset.session;

        // Cycle through statuses: not_registered -> registered -> attended -> no_show -> not_registered
        let newStatus;
        switch (currentStatus) {
            case 'not_registered':
                newStatus = 'registered';
                break;
            case 'registered':
                newStatus = 'attended';
                break;
            case 'attended':
                newStatus = 'no_show';
                break;
            case 'no_show':
                newStatus = 'not_registered';
                break;
            default:
                newStatus = 'registered';
        }

        // Update cell appearance
        cell.className = 'attendance-cell ' + newStatus;
        cell.dataset.status = newStatus;

        // Update icon and text
        let icon, text;
        switch (newStatus) {
            case 'attended':
                icon = 'bi-check-circle-fill';
                text = '✓';
                cell.title = 'Attended';
                break;
            case 'no_show':
                icon = 'bi-x-circle-fill';
                text = '✗';
                cell.title = 'No Show';
                break;
            case 'registered':
                icon = 'bi-clock-fill';
                text = '○';
                cell.title = 'Registered';
                break;
            default:
                icon = 'bi-dash';
                text = '—';
                cell.title = 'Not Registered';
        }

        const iconElement = cell.querySelector('i');
        if (iconElement) {
            iconElement.className = 'bi ' + icon;
        }

        // Update the text content (preserve the icon)
        cell.innerHTML = '<i class="bi ' + icon + '"></i>' + text;

        // TODO: Send AJAX request to update database
        updateAttendanceStatus(attendeeId, sessionId, newStatus);
    }
});

function updateAttendanceStatus(attendeeId, sessionId, status) {
    // AJAX call to update attendance status
    fetch('update_attendance_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            attendee_id: attendeeId,
            session_id: sessionId,
            status: status
        })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            console.error('Failed to update attendance:', data.error);
            // Optionally revert the UI change
        }
    })
    .catch(error => {
        console.error('Error updating attendance:', error);
    });
}

// Form validation
document.getElementById('crossSessionForm').addEventListener('submit', function(e) {
    if (selectedAttendees.size === 0) {
        e.preventDefault();
        alert('Please select at least one attendee.');
        return;
    }

    // Check if any session actions are selected
    const sessionActions = document.querySelectorAll('select[name^="session_actions"]');
    let hasActions = false;
    sessionActions.forEach(select => {
        if (select.value !== 'no_change') {
            hasActions = true;
        }
    });

    if (!hasActions) {
        e.preventDefault();
        alert('Please select at least one session action (not "No Change").');
        return;
    }

    const actionCount = selectedAttendees.size * Array.from(sessionActions).filter(s => s.value !== 'no_change').length;
    if (!confirm(`This will update ${actionCount} attendance records. Continue?`)) {
        e.preventDefault();
    }
});

// Initialize
updateSelectedCount();

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
            case 'a':
                e.preventDefault();
                selectAllAttendees();
                break;
            case 'd':
                e.preventDefault();
                clearAttendeeSelection();
                break;
        }
    }
});

// Add search functionality
function addSearchBox() {
    const header = document.querySelector('.dashboard-header');
    const searchBox = document.createElement('div');
    searchBox.className = 'mt-3';
    searchBox.innerHTML = `
        <input type="text" id="attendeeSearch" class="form-control" placeholder="Search attendees..." style="max-width: 300px;">
    `;
    header.appendChild(searchBox);

    document.getElementById('attendeeSearch').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        document.querySelectorAll('.attendee-row').forEach(row => {
            const name = row.querySelector('label').textContent.toLowerCase();
            if (name.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
}

// Add search box on load
document.addEventListener('DOMContentLoaded', addSearchBox);
</script>

<?php include 'includes/footer.php'; ?>
