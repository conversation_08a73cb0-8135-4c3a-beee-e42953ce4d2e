<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit();
}

// Include the configuration file
require_once '../config.php';
require_once '../classes/RecurringEventManager.php';

header('Content-Type: application/json');

try {
    $eventId = (int)($_GET['event_id'] ?? 0);
    
    if (!$eventId) {
        echo json_encode(['success' => false, 'message' => 'Invalid event ID']);
        exit();
    }
    
    $recurringEventManager = new RecurringEventManager($pdo);
    $instances = $recurringEventManager->getRecurringInstances($eventId);
    
    echo json_encode([
        'success' => true,
        'instances' => $instances
    ]);
    
} catch (Exception $e) {
    error_log("Error getting recurring instances: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error loading instances'
    ]);
}
?>
