<?php
/**
 * API endpoint to get sessions for an event
 * Universal endpoint that works for any organization type
 */

require_once '../config.php';
require_once '../includes/auth_check.php';

header('Content-Type: application/json');

try {
    $event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;
    
    if (!$event_id) {
        echo json_encode(['success' => false, 'error' => 'Event ID is required']);
        exit();
    }
    
    // Get sessions for the event
    $stmt = $pdo->prepare("
        SELECT 
            id,
            session_title,
            session_type,
            start_datetime,
            end_datetime,
            max_attendees,
            status
        FROM event_sessions 
        WHERE event_id = ? 
        ORDER BY start_datetime ASC
    ");
    $stmt->execute([$event_id]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get attendance counts for each session
    foreach ($sessions as &$session) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as attendance_count
            FROM session_attendance 
            WHERE session_id = ? AND attendance_status = 'attended'
        ");
        $stmt->execute([$session['id']]);
        $attendance = $stmt->fetch(PDO::FETCH_ASSOC);
        $session['current_attendance'] = $attendance['attendance_count'];
        $session['utilization_percentage'] = $session['max_attendees'] > 0 ? 
            round(($attendance['attendance_count'] / $session['max_attendees']) * 100, 1) : 0;
    }
    
    echo json_encode([
        'success' => true,
        'sessions' => $sessions,
        'total_sessions' => count($sessions)
    ]);
    
} catch (Exception $e) {
    error_log("Get sessions API error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Failed to retrieve sessions'
    ]);
}
?>
