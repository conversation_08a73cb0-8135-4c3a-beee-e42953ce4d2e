<?php
// Setup Admin Roles - Add missing column and configure super admin
require_once '../config.php';

echo "<h2>🔧 Setting Up Admin Roles</h2>";

try {
    // 1. Check role column in admins table
    echo "<h3>📋 Step 1: Checking Role Column</h3>";

    try {
        $stmt = $pdo->query("DESCRIBE admins");
        $columns = $stmt->fetchAll();
        $hasRoleColumn = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'role') {
                $hasRoleColumn = true;
                break;
            }
        }

        if ($hasRoleColumn) {
            echo "✅ Role column already exists in admins table<br>";
        } else {
            $pdo->exec("ALTER TABLE admins ADD COLUMN role VARCHAR(50) DEFAULT 'staff'");
            echo "✅ Successfully added role column to admins table<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error checking/adding role column: " . $e->getMessage() . "<br>";
        throw $e;
    }
    
    // 2. Check current admin table structure
    echo "<h3>📋 Step 2: Current Admin Table Structure</h3>";
    $stmt = $pdo->query("DESCRIBE admins");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 3. Check current admins
    echo "<h3>📋 Step 3: Current Admin Users</h3>";
    $stmt = $pdo->query("SELECT id, username, full_name, role FROM admins ORDER BY id");
    $admins = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Full Name</th><th>Current Role</th></tr>";
    foreach ($admins as $admin) {
        echo "<tr>";
        echo "<td>" . $admin['id'] . "</td>";
        echo "<td>" . htmlspecialchars($admin['username']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['full_name']) . "</td>";
        echo "<td>" . ($admin['role'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 4. Set super admin roles
    echo "<h3>👑 Step 4: Setting Super Admin Roles</h3>";
    
    // Set admin ID 4 as super_admin
    $stmt = $pdo->prepare("UPDATE admins SET role = 'super_admin' WHERE id = 4");
    $result1 = $stmt->execute();
    echo ($result1 ? "✅" : "❌") . " Set admin ID 4 as super_admin<br>";

    // Set admin ID 1 as super_admin (backup)
    $stmt = $pdo->prepare("UPDATE admins SET role = 'super_admin' WHERE id = 1");
    $result2 = $stmt->execute();
    echo ($result2 ? "✅" : "❌") . " Set admin ID 1 as super_admin<br>";

    // Set main admin user as super_admin (by username)
    $stmt = $pdo->prepare("UPDATE admins SET role = 'super_admin' WHERE username = 'admin'");
    $result3 = $stmt->execute();
    echo ($result3 ? "✅" : "❌") . " Set username 'admin' as super_admin<br>";
    
    // 5. Verify updates
    echo "<h3>📋 Step 5: Verification - Updated Admin Roles</h3>";
    $stmt = $pdo->query("SELECT id, username, full_name, role FROM admins ORDER BY id");
    $admins = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Full Name</th><th>Role</th><th>Status</th></tr>";
    foreach ($admins as $admin) {
        $status = ($admin['role'] === 'super_admin') ? '👑 Super Admin' : '👤 Regular Admin';
        $rowColor = ($admin['role'] === 'super_admin') ? 'background-color: #d4edda;' : '';
        echo "<tr style='$rowColor'>";
        echo "<td>" . $admin['id'] . "</td>";
        echo "<td>" . htmlspecialchars($admin['username']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['full_name']) . "</td>";
        echo "<td><strong>" . ($admin['role'] ?? 'NULL') . "</strong></td>";
        echo "<td>" . $status . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 6. Test the login query
    echo "<h3>🧪 Step 6: Testing Login Query</h3>";
    $stmt = $pdo->prepare("SELECT id, username, password, full_name, role FROM admins WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "✅ Login query successful<br>";
        echo "<strong>Admin Details:</strong><br>";
        echo "- ID: " . $admin['id'] . "<br>";
        echo "- Username: " . $admin['username'] . "<br>";
        echo "- Full Name: " . $admin['full_name'] . "<br>";
        echo "- Role: " . $admin['role'] . "<br>";
    } else {
        echo "❌ Admin user 'admin' not found<br>";
    }
    
    echo "<h3>🎯 Summary</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>✅ Setup Complete!</strong></p>";
    echo "<ul>";
    echo "<li>✅ Verified role column in admins table</li>";
    echo "<li>✅ Set super_admin roles for main admin users</li>";
    echo "<li>✅ Updated login.php to include role in session</li>";
    echo "<li>✅ Permission bypass functions are in place</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>📝 Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li><strong>Logout and Login Again</strong> - This will set the admin_role session variable</li>";
    echo "<li><strong>Check Dashboard</strong> - All analytics sections should now be visible</li>";
    echo "<li><strong>Check Sidebar</strong> - Events Management and all sections should appear</li>";
    echo "<li><strong>Verify Permissions</strong> - Super admin should have access to everything</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>❌ Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Stack Trace:</strong><br><pre>" . $e->getTraceAsString() . "</pre></p>";
    echo "</div>";
}

echo "<p>";
echo "<a href='logout.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🚪 Logout</a>";
echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔑 Login Again</a>";
echo "<a href='dashboard.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📊 Test Dashboard</a>";
echo "</p>";
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h2, h3 { color: #333; }
    table { width: 100%; margin: 10px 0; }
    th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
    p { margin: 8px 0; }
    ul, ol { margin: 10px 0; padding-left: 20px; }
    li { margin: 5px 0; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>
