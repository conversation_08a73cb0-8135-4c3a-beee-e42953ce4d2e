<?php
/**
 * Branding Settings - Redirect to Appearance Settings
 *
 * This page has been consolidated into the Appearance Settings page.
 * All branding functionality is now available in the comprehensive
 * Appearance & Branding section of appearance_settings.php
 */

session_start();
require_once "../config.php";

// Check if admin is logged in
if (!isset($_SESSION["admin_id"])) {
    header("Location: login.php");
    exit();
}

// Redirect to appearance settings with a message
$_SESSION['redirect_message'] = "Branding settings have been moved to the Appearance & Branding page for a better experience.";
header("Location: appearance_settings.php#branding");
exit();
?>
