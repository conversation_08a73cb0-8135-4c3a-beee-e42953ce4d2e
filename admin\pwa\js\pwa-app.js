/**
 * Universal Event Manager PWA - Main Application
 * Progressive Web App functionality for any organization type
 */

class PWAApp {
    constructor() {
        this.isOnline = navigator.onLine;
        this.installPrompt = null;
        this.swipeStartX = 0;
        this.swipeStartY = 0;
        this.pullStartY = 0;
        this.isPulling = false;
        this.syncQueue = [];
        this.orgConfig = window.ORG_CONFIG || {};
        this.userInfo = window.USER_INFO || {};
        
        this.initializeServiceWorker();
        this.setupEventListeners();
        this.checkInstallability();
        this.updateConnectionStatus();
    }
    
    static init() {
        window.pwaApp = new PWAApp();
        return window.pwaApp;
    }
    
    initializeServiceWorker() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('service-worker.js')
                .then(registration => {
                    console.log('PWA: Service Worker registered', registration);
                    
                    // Check for updates
                    registration.addEventListener('updatefound', () => {
                        const newWorker = registration.installing;
                        newWorker.addEventListener('statechange', () => {
                            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                this.showUpdateAvailable();
                            }
                        });
                    });
                })
                .catch(error => {
                    console.error('PWA: Service Worker registration failed', error);
                });
        }
    }
    
    setupEventListeners() {
        // Online/offline status
        window.addEventListener('online', () => this.handleOnline());
        window.addEventListener('offline', () => this.handleOffline());
        
        // Install prompt
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            this.installPrompt = e;
            this.showInstallBanner();
        });
        
        // App installed
        window.addEventListener('appinstalled', () => {
            console.log('PWA: App installed');
            this.hideInstallBanner();
            this.showNotification('App installed successfully!', 'success');
        });
        
        // Touch events for gestures
        document.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: true });
        document.addEventListener('touchmove', (e) => this.handleTouchMove(e), { passive: false });
        document.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: true });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));
        
        // Visibility change (app focus/blur)
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.syncData();
            }
        });
        
        // Back button handling
        window.addEventListener('popstate', (e) => this.handleBackButton(e));
    }
    
    checkInstallability() {
        // Check if app is already installed
        if (window.matchMedia('(display-mode: standalone)').matches || 
            window.navigator.standalone === true) {
            console.log('PWA: App is running in standalone mode');
            document.body.classList.add('pwa-installed');
        } else {
            // Show install banner after delay if not dismissed
            setTimeout(() => {
                if (!localStorage.getItem('pwa-install-dismissed')) {
                    this.showInstallBanner();
                }
            }, 5000);
        }
    }
    
    handleOnline() {
        this.isOnline = true;
        this.updateConnectionStatus();
        this.hideOfflineBanner();
        this.syncData();
        console.log('PWA: Back online');
    }
    
    handleOffline() {
        this.isOnline = false;
        this.updateConnectionStatus();
        this.showOfflineBanner();
        console.log('PWA: Gone offline');
    }
    
    updateConnectionStatus() {
        const statusElement = document.getElementById('connectionStatus');
        if (statusElement) {
            const icon = statusElement.querySelector('i');
            const text = statusElement.querySelector('span');
            
            if (this.isOnline) {
                icon.className = 'bi bi-wifi';
                text.textContent = 'Online';
                statusElement.className = 'connection-status online';
            } else {
                icon.className = 'bi bi-wifi-off';
                text.textContent = 'Offline';
                statusElement.className = 'connection-status offline';
            }
        }
    }
    
    showInstallBanner() {
        const banner = document.getElementById('installBanner');
        if (banner && this.installPrompt) {
            banner.style.display = 'block';
        }
    }
    
    hideInstallBanner() {
        const banner = document.getElementById('installBanner');
        if (banner) {
            banner.style.display = 'none';
        }
    }
    
    showOfflineBanner() {
        const banner = document.getElementById('offlineBanner');
        if (banner) {
            banner.style.display = 'block';
        }
    }
    
    hideOfflineBanner() {
        const banner = document.getElementById('offlineBanner');
        if (banner) {
            banner.style.display = 'none';
        }
    }
    
    async installPWA() {
        if (this.installPrompt) {
            const result = await this.installPrompt.prompt();
            console.log('PWA: Install prompt result', result);
            this.installPrompt = null;
            this.hideInstallBanner();
        }
    }
    
    dismissInstall() {
        this.hideInstallBanner();
        localStorage.setItem('pwa-install-dismissed', 'true');
    }
    
    async syncData() {
        if (!this.isOnline) {
            console.log('PWA: Cannot sync - offline');
            return;
        }
        
        const statusElement = document.getElementById('connectionStatus');
        if (statusElement) {
            statusElement.className = 'connection-status syncing';
            statusElement.querySelector('span').textContent = 'Syncing...';
        }
        
        try {
            // Sync pending requests from IndexedDB
            await this.syncPendingRequests();
            
            // Refresh current data
            await this.refreshCurrentView();
            
            console.log('PWA: Data sync completed');
            this.showNotification('Data synced successfully', 'success');
        } catch (error) {
            console.error('PWA: Sync failed', error);
            this.showNotification('Sync failed - will retry later', 'warning');
        } finally {
            this.updateConnectionStatus();
        }
    }
    
    async syncPendingRequests() {
        // This would sync with the service worker's IndexedDB
        // For now, we'll simulate the sync
        return new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    async refreshCurrentView() {
        // Refresh data for current view
        const currentAction = new URLSearchParams(window.location.search).get('action') || 'dashboard';
        
        switch (currentAction) {
            case 'dashboard':
                await this.refreshDashboard();
                break;
            case 'events':
                await this.refreshEvents();
                break;
            case 'ai':
                await this.refreshAIPredictions();
                break;
        }
    }
    
    async refreshDashboard() {
        // Refresh dashboard data
        console.log('PWA: Refreshing dashboard data');
    }
    
    async refreshEvents() {
        // Refresh events data
        console.log('PWA: Refreshing events data');
    }
    
    async refreshAIPredictions() {
        // Refresh AI predictions
        console.log('PWA: Refreshing AI predictions');
    }
    
    handleTouchStart(e) {
        this.swipeStartX = e.touches[0].clientX;
        this.swipeStartY = e.touches[0].clientY;
        
        // Check for pull-to-refresh
        if (window.scrollY === 0 && e.touches[0].clientY > 0) {
            this.pullStartY = e.touches[0].clientY;
            this.isPulling = true;
        }
    }
    
    handleTouchMove(e) {
        if (this.isPulling && window.scrollY === 0) {
            const pullDistance = e.touches[0].clientY - this.pullStartY;
            
            if (pullDistance > 0 && pullDistance < 100) {
                e.preventDefault();
                this.showPullToRefresh(pullDistance);
            } else if (pullDistance >= 100) {
                this.triggerRefresh();
                this.isPulling = false;
            }
        }
    }
    
    handleTouchEnd(e) {
        if (this.isPulling) {
            this.hidePullToRefresh();
            this.isPulling = false;
        }
        
        // Handle swipe gestures
        const swipeEndX = e.changedTouches[0].clientX;
        const swipeEndY = e.changedTouches[0].clientY;
        const deltaX = swipeEndX - this.swipeStartX;
        const deltaY = swipeEndY - this.swipeStartY;
        
        // Horizontal swipe for sidebar
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
            if (deltaX > 0 && this.swipeStartX < 50) {
                // Swipe right from left edge - open sidebar
                this.openSidebar();
            } else if (deltaX < 0 && this.swipeStartX > window.innerWidth - 50) {
                // Swipe left from right edge - close sidebar
                this.closeSidebar();
            }
        }
    }
    
    showPullToRefresh(distance) {
        // Show pull-to-refresh indicator
        console.log('PWA: Pull to refresh', distance);
    }
    
    hidePullToRefresh() {
        // Hide pull-to-refresh indicator
        console.log('PWA: Hide pull to refresh');
    }
    
    triggerRefresh() {
        console.log('PWA: Triggering refresh');
        this.syncData();
    }
    
    handleKeyboard(e) {
        // Keyboard shortcuts
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'r':
                    e.preventDefault();
                    this.syncData();
                    break;
                case 'k':
                    e.preventDefault();
                    this.showQuickActions();
                    break;
            }
        }
        
        // Escape key
        if (e.key === 'Escape') {
            this.closeSidebar();
            this.closeModals();
        }
    }
    
    handleBackButton(e) {
        // Handle browser back button
        if (document.getElementById('sidebar').classList.contains('open')) {
            e.preventDefault();
            this.closeSidebar();
        }
    }
    
    openSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = this.getOrCreateOverlay();
        
        sidebar.classList.add('open');
        overlay.classList.add('show');
    }
    
    closeSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.querySelector('.sidebar-overlay');
        
        sidebar.classList.remove('open');
        if (overlay) {
            overlay.classList.remove('show');
        }
    }
    
    getOrCreateOverlay() {
        let overlay = document.querySelector('.sidebar-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            overlay.addEventListener('click', () => this.closeSidebar());
            document.body.appendChild(overlay);
        }
        return overlay;
    }
    
    closeModals() {
        // Close any open modals
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        });
    }
    
    showNotification(message, type = 'info') {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        // Add to toast container or create one
        let container = document.querySelector('.toast-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            document.body.appendChild(container);
        }
        
        container.appendChild(toast);
        
        // Show toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove after hiding
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }
    
    showUpdateAvailable() {
        const updateBanner = document.createElement('div');
        updateBanner.className = 'alert alert-info alert-dismissible fade show position-fixed';
        updateBanner.style.cssText = 'top: 70px; left: 1rem; right: 1rem; z-index: 1050;';
        updateBanner.innerHTML = `
            <strong>Update Available!</strong> A new version of the app is ready.
            <button type="button" class="btn btn-info btn-sm ms-2" onclick="location.reload()">Update Now</button>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(updateBanner);
    }
    
    // Public methods for global access
    showQuickActions() {
        const modal = new bootstrap.Modal(document.getElementById('quickActionsModal'));
        modal.show();
    }
    
    retryConnection() {
        if (navigator.onLine) {
            this.handleOnline();
        } else {
            this.showNotification('Still offline - please check your connection', 'warning');
        }
    }
}

// Global functions for HTML onclick handlers
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    if (sidebar.classList.contains('open')) {
        window.pwaApp.closeSidebar();
    } else {
        window.pwaApp.openSidebar();
    }
}

function showQuickActions() {
    window.pwaApp.showQuickActions();
}

function showNotifications() {
    // Show notifications panel
    console.log('PWA: Show notifications');
}

function syncData() {
    window.pwaApp.syncData();
}

function installPWA() {
    window.pwaApp.installPWA();
}

function dismissInstall() {
    window.pwaApp.dismissInstall();
}

function retryConnection() {
    window.pwaApp.retryConnection();
}

// Quick action functions
function quickCheckIn() {
    window.location.href = '?action=checkin';
}

function scanQR() {
    window.location.href = '?action=scanner';
}

function viewPredictions() {
    window.location.href = '?action=ai';
}

function exportData() {
    console.log('PWA: Export data');
    window.pwaApp.showNotification('Export feature coming soon!', 'info');
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => PWAApp.init());
} else {
    PWAApp.init();
}
