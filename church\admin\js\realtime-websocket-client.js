/**
 * Real-Time WebSocket Client for Church Event Management
 * Enterprise-grade client for instant staff coordination
 */

class ChurchEventWebSocketClient {
    constructor(options = {}) {
        this.options = {
            serverUrl: options.serverUrl || 'ws://localhost:8080',
            autoReconnect: options.autoReconnect !== false,
            reconnectInterval: options.reconnectInterval || 5000,
            heartbeatInterval: options.heartbeatInterval || 30000,
            maxReconnectAttempts: options.maxReconnectAttempts || 10,
            debug: options.debug || false,
            ...options
        };
        
        this.ws = null;
        this.isConnected = false;
        this.isAuthenticated = false;
        this.reconnectAttempts = 0;
        this.heartbeatTimer = null;
        this.reconnectTimer = null;
        this.eventListeners = {};
        this.currentEventId = null;
        this.userInfo = null;
        
        // Bind methods
        this.connect = this.connect.bind(this);
        this.disconnect = this.disconnect.bind(this);
        this.send = this.send.bind(this);
        this.authenticate = this.authenticate.bind(this);
        this.joinEvent = this.joinEvent.bind(this);
        
        this.log('WebSocket client initialized');
    }
    
    // Connection Management
    connect() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.log('Already connected');
            return Promise.resolve();
        }
        
        return new Promise((resolve, reject) => {
            try {
                this.log(`Connecting to ${this.options.serverUrl}`);
                this.ws = new WebSocket(this.options.serverUrl);
                
                this.ws.onopen = (event) => {
                    this.log('WebSocket connected');
                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    this.startHeartbeat();
                    this.emit('connected', event);
                    resolve();
                };
                
                this.ws.onmessage = (event) => {
                    this.handleMessage(event);
                };
                
                this.ws.onclose = (event) => {
                    this.log(`WebSocket closed: ${event.code} - ${event.reason}`);
                    this.isConnected = false;
                    this.isAuthenticated = false;
                    this.stopHeartbeat();
                    this.emit('disconnected', event);
                    
                    if (this.options.autoReconnect && this.reconnectAttempts < this.options.maxReconnectAttempts) {
                        this.scheduleReconnect();
                    }
                };
                
                this.ws.onerror = (error) => {
                    this.log('WebSocket error:', error);
                    this.emit('error', error);
                    reject(error);
                };
                
            } catch (error) {
                this.log('Connection error:', error);
                reject(error);
            }
        });
    }
    
    disconnect() {
        this.options.autoReconnect = false;
        this.stopHeartbeat();
        this.clearReconnectTimer();
        
        if (this.ws) {
            this.ws.close(1000, 'Client disconnect');
            this.ws = null;
        }
        
        this.isConnected = false;
        this.isAuthenticated = false;
        this.log('Disconnected');
    }
    
    // Authentication
    authenticate(userId, authToken) {
        return new Promise((resolve, reject) => {
            if (!this.isConnected) {
                reject(new Error('Not connected to WebSocket server'));
                return;
            }
            
            this.userInfo = { userId, authToken };
            
            // Listen for authentication response
            const authHandler = (data) => {
                if (data.type === 'authenticated') {
                    this.isAuthenticated = true;
                    this.off('message', authHandler);
                    this.log('Authenticated successfully');
                    resolve(data);
                } else if (data.type === 'error' && data.message.includes('authentication')) {
                    this.off('message', authHandler);
                    reject(new Error(data.message));
                }
            };
            
            this.on('message', authHandler);
            
            this.send({
                type: 'authenticate',
                user_id: userId,
                auth_token: authToken
            });
        });
    }
    
    // Event Management
    joinEvent(eventId) {
        return new Promise((resolve, reject) => {
            if (!this.isAuthenticated) {
                reject(new Error('Not authenticated'));
                return;
            }
            
            this.currentEventId = eventId;
            
            // Listen for join response
            const joinHandler = (data) => {
                if (data.type === 'joined_event') {
                    this.off('message', joinHandler);
                    this.log(`Joined event: ${data.event_title}`);
                    resolve(data);
                } else if (data.type === 'error' && data.message.includes('event')) {
                    this.off('message', joinHandler);
                    reject(new Error(data.message));
                }
            };
            
            this.on('message', joinHandler);
            
            this.send({
                type: 'join_event',
                event_id: eventId
            });
        });
    }
    
    // Real-Time Actions
    sendAttendanceUpdate(attendeeId, attendeeType, action, sessionId, additionalData = {}) {
        if (!this.isAuthenticated || !this.currentEventId) {
            throw new Error('Not authenticated or not in event');
        }
        
        this.send({
            type: 'attendance_update',
            attendee_id: attendeeId,
            attendee_type: attendeeType,
            action: action,
            session_id: sessionId,
            ...additionalData
        });
    }
    
    sendSessionStatusUpdate(sessionId, statusType, statusData) {
        if (!this.isAuthenticated || !this.currentEventId) {
            throw new Error('Not authenticated or not in event');
        }
        
        this.send({
            type: 'session_status_update',
            session_id: sessionId,
            status_type: statusType,
            status_data: statusData
        });
    }
    
    sendStaffMessage(message, targetUsers = null) {
        if (!this.isAuthenticated || !this.currentEventId) {
            throw new Error('Not authenticated or not in event');
        }
        
        this.send({
            type: 'staff_message',
            message: message,
            target_users: targetUsers
        });
    }
    
    requestSync() {
        if (!this.isAuthenticated || !this.currentEventId) {
            throw new Error('Not authenticated or not in event');
        }
        
        this.send({
            type: 'request_sync'
        });
    }
    
    // Message Handling
    handleMessage(event) {
        try {
            const data = JSON.parse(event.data);
            this.log('Received message:', data);
            
            // Handle specific message types
            switch (data.type) {
                case 'welcome':
                    this.emit('welcome', data);
                    break;
                    
                case 'authenticated':
                    this.emit('authenticated', data);
                    break;
                    
                case 'joined_event':
                    this.emit('joined_event', data);
                    break;
                    
                case 'attendance_updated':
                    this.emit('attendance_updated', data);
                    break;
                    
                case 'session_status_updated':
                    this.emit('session_status_updated', data);
                    break;
                    
                case 'staff_message':
                    this.emit('staff_message', data);
                    break;
                    
                case 'staff_joined':
                    this.emit('staff_joined', data);
                    break;
                    
                case 'staff_disconnected':
                    this.emit('staff_disconnected', data);
                    break;
                    
                case 'event_status':
                    this.emit('event_status', data);
                    break;
                    
                case 'heartbeat_response':
                    // Heartbeat acknowledged
                    break;
                    
                case 'error':
                    this.emit('error', new Error(data.message));
                    break;
                    
                default:
                    this.log('Unknown message type:', data.type);
            }
            
            // Emit generic message event
            this.emit('message', data);
            
        } catch (error) {
            this.log('Error parsing message:', error);
            this.emit('error', error);
        }
    }
    
    // Utility Methods
    send(data) {
        if (!this.isConnected || !this.ws) {
            throw new Error('WebSocket not connected');
        }
        
        try {
            const message = JSON.stringify(data);
            this.ws.send(message);
            this.log('Sent message:', data);
        } catch (error) {
            this.log('Error sending message:', error);
            throw error;
        }
    }
    
    // Heartbeat Management
    startHeartbeat() {
        this.stopHeartbeat();
        this.heartbeatTimer = setInterval(() => {
            if (this.isConnected) {
                this.send({ type: 'heartbeat' });
            }
        }, this.options.heartbeatInterval);
    }
    
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }
    
    // Reconnection Management
    scheduleReconnect() {
        this.clearReconnectTimer();
        this.reconnectAttempts++;
        
        const delay = Math.min(
            this.options.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1),
            30000 // Max 30 seconds
        );
        
        this.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
        
        this.reconnectTimer = setTimeout(() => {
            this.log(`Reconnect attempt ${this.reconnectAttempts}`);
            this.connect().then(() => {
                // Re-authenticate and rejoin event if we were previously authenticated
                if (this.userInfo) {
                    return this.authenticate(this.userInfo.userId, this.userInfo.authToken);
                }
            }).then(() => {
                if (this.currentEventId) {
                    return this.joinEvent(this.currentEventId);
                }
            }).catch((error) => {
                this.log('Reconnect failed:', error);
            });
        }, delay);
    }
    
    clearReconnectTimer() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
    }
    
    // Event Emitter Methods
    on(event, callback) {
        if (!this.eventListeners[event]) {
            this.eventListeners[event] = [];
        }
        this.eventListeners[event].push(callback);
    }
    
    off(event, callback) {
        if (!this.eventListeners[event]) return;
        
        const index = this.eventListeners[event].indexOf(callback);
        if (index > -1) {
            this.eventListeners[event].splice(index, 1);
        }
    }
    
    emit(event, data) {
        if (!this.eventListeners[event]) return;
        
        this.eventListeners[event].forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                this.log('Error in event callback:', error);
            }
        });
    }
    
    // Logging
    log(...args) {
        if (this.options.debug) {
            console.log('[WebSocket Client]', ...args);
        }
    }
    
    // Status Methods
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            isAuthenticated: this.isAuthenticated,
            currentEventId: this.currentEventId,
            reconnectAttempts: this.reconnectAttempts,
            userInfo: this.userInfo
        };
    }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChurchEventWebSocketClient;
} else if (typeof window !== 'undefined') {
    window.ChurchEventWebSocketClient = ChurchEventWebSocketClient;
}
