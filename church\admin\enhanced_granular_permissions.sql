-- Enhanced Granular Permission System
-- This file adds comprehensive permissions covering ALL admin functionality

-- First, ensure we have all necessary permission categories
INSERT IGNORE INTO permission_categories (category_name, category_display_name, category_description, sort_order, icon_class) VALUES
('dashboard', 'Dashboard Access', 'Access to main dashboard and analytics', 1, 'bi-speedometer2'),
('member_management', 'Member Management', 'Manage members, profiles, families, and member data', 2, 'bi-people'),
('event_management', 'Event Management', 'Create and manage events, sessions, attendance, and categories', 3, 'bi-calendar-event'),
('email_management', 'Email & Communication', 'Send emails, manage templates, contacts, and communication', 4, 'bi-envelope'),
('sms_management', 'SMS Management', 'Send SMS messages and manage SMS templates', 5, 'bi-chat-dots'),
('donations', 'Donations & Finance', 'Manage donations, gifts, payments, and financial records', 6, 'bi-currency-dollar'),
('integrations', 'Integrations', 'Manage external integrations, APIs, and third-party services', 7, 'bi-link-45deg'),
('system_settings', 'System Settings', 'Configure system settings, appearance, security, and organization setup', 8, 'bi-gear'),
('reports', 'Reports & Analytics', 'View and export various reports, analytics, and data insights', 9, 'bi-graph-up'),
('notifications', 'Notifications', 'View and manage system notifications and alerts', 10, 'bi-bell'),
('admin_management', 'Admin Management', 'Manage admin users, roles, permissions, and RBAC system', 11, 'bi-shield-check'),
('ai_features', 'AI & Predictions', 'Access AI-powered features, predictions, and analytics', 12, 'bi-robot');

-- Enhanced Dashboard Access Permissions
INSERT IGNORE INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('dashboard.view', 'View Dashboard', 'Access to main admin dashboard', 1, 'dashboard.php', 1),
('dashboard.analytics', 'View Analytics', 'Access to dashboard analytics and charts', 1, NULL, 2),
('dashboard.realtime', 'Realtime Dashboard', 'Access to realtime event dashboard', 1, 'realtime_dashboard.php', 3),
('dashboard.super_admin', 'Super Admin Dashboard', 'Access to super admin dashboard', 1, 'super_admin_dashboard.php', 4),
('dashboard.system_test', 'System Test Dashboard', 'Access to system testing dashboard', 1, 'system_test_dashboard.php', 5);

-- Comprehensive Member Management Permissions
INSERT IGNORE INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('members.view', 'View Members', 'View member list and profiles', 2, 'members.php', 1),
('members.add', 'Add Members', 'Add new members to the system', 2, 'add_member.php', 2),
('members.edit', 'Edit Members', 'Edit existing member information', 2, 'edit_member.php', 3),
('members.delete', 'Delete Members', 'Delete members from the system', 2, NULL, 4),
('members.view_profile', 'View Member Profiles', 'View detailed member profiles', 2, 'view_member.php', 5),
('members.family_management', 'Family Management', 'Manage family relationships', 2, 'family_management.php', 6),
('members.skills', 'Member Skills', 'Manage member skills and talents', 2, 'member_skills.php', 7),
('members.requests', 'Member Requests', 'View and manage member requests', 2, 'requests.php', 8),
('members.prayer_requests', 'Prayer Requests', 'Manage prayer requests', 2, 'prayer_requests.php', 9),
('members.volunteers', 'Volunteer Management', 'Manage volunteer opportunities and applications', 2, 'volunteer_opportunities.php', 10),
('members.volunteer_applications', 'Volunteer Applications', 'View volunteer applications', 2, 'volunteer_applications.php', 11),
('members.volunteer_hours', 'Volunteer Hours', 'Track volunteer hours', 2, 'volunteer_hours.php', 12);

-- Comprehensive Event Management Permissions
INSERT IGNORE INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('events.view', 'View Events', 'View event list and details', 3, 'events.php', 1),
('events.create', 'Create Events', 'Create new events', 3, 'add_event.php', 2),
('events.edit', 'Edit Events', 'Edit existing events', 3, 'edit_event.php', 3),
('events.delete', 'Delete Events', 'Delete events', 3, NULL, 4),
('events.attendance', 'Event Attendance', 'Manage event attendance', 3, 'event_attendance.php', 5),
('events.sessions', 'Event Sessions', 'Manage event sessions', 3, 'event_sessions.php', 6),
('events.categories', 'Event Categories', 'Manage event categories', 3, 'event_categories.php', 7),
('events.reports', 'Event Reports', 'View event reports and analytics', 3, 'event_reports.php', 8),
('events.realtime', 'Realtime Event Tracking', 'Access realtime event tracking', 3, 'realtime_dashboard.php', 9);

-- Comprehensive Email & Communication Permissions
INSERT IGNORE INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('email.bulk_send', 'Send Bulk Email', 'Send bulk emails to members', 4, 'bulk_email.php', 1),
('email.scheduler', 'Email Scheduler', 'Schedule emails for future sending', 4, 'email_scheduler.php', 2),
('email.templates', 'Email Templates', 'Manage email templates', 4, 'email_templates.php', 3),
('email.contacts', 'Manage Contacts', 'Manage email contacts', 4, 'email_contacts.php', 4),
('email.contact_groups', 'Contact Groups', 'Manage contact groups', 4, 'contact_groups.php', 5),
('email.birthday_messages', 'Birthday Messages', 'Manage birthday email messages', 4, 'birthday_messages.php', 6),
('email.send_birthday_emails', 'Send Birthday Emails', 'Send bulk birthday emails', 4, 'send_birthday_emails.php', 7),
('email.test_birthday_emails', 'Test Birthday Emails', 'Test birthday email functionality', 4, 'test_birthday_emails.php', 8),
('email.automated_templates', 'Automated Templates', 'Manage automated email templates', 4, 'automated_templates.php', 9),
('email.whatsapp', 'WhatsApp Messages', 'Send WhatsApp messages', 4, 'whatsapp_messages.php', 10),
('email.analytics', 'Email Analytics', 'View email analytics and reports', 4, 'email_analytics.php', 11);

-- SMS Management Permissions
INSERT IGNORE INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('sms.single_send', 'Send Single SMS', 'Send individual SMS messages', 5, 'single_sms.php', 1),
('sms.bulk_send', 'Send Bulk SMS', 'Send bulk SMS messages', 5, 'bulk_sms.php', 2),
('sms.templates', 'SMS Templates', 'Manage SMS templates', 5, 'sms_templates.php', 3),
('sms.analytics', 'SMS Analytics', 'View SMS analytics and reports', 5, 'sms_analytics.php', 4);

-- Comprehensive Donations & Finance Permissions
INSERT IGNORE INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('donations.view', 'View Donations', 'View donation records', 6, 'donations.php', 1),
('donations.manage', 'Manage Donations', 'Add and edit donation records', 6, NULL, 2),
('donations.gifts', 'Gift Management', 'Manage birthday and special gifts', 6, 'gift_management.php', 3),
('donations.enhanced', 'Enhanced Donations', 'Access enhanced donation features', 6, 'enhanced_donate.php', 4),
('donations.payment_integration', 'Payment Integration', 'Manage payment gateway integration', 6, 'payment_integration.php', 5),
('donations.payment_tables', 'Payment Tables', 'Manage payment table structure', 6, 'payment_tables.php', 6);

-- Integrations Permissions
INSERT IGNORE INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('integrations.calendar', 'Calendar Integration', 'Manage calendar integration', 7, 'calendar_integration.php', 1),
('integrations.social_media', 'Social Media Integration', 'Manage social media integration', 7, 'social_media_integration.php', 2);

-- Comprehensive System Settings Permissions
INSERT IGNORE INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('settings.general', 'General Settings', 'Manage general system settings', 8, 'settings.php', 1),
('settings.main_settings', 'Main Settings', 'Access main settings page', 8, 'main_settings.php', 2),
('settings.organization_setup', 'Organization Setup', 'Configure universal organization setup', 8, 'universal_organization_setup.php', 3),
('settings.appearance', 'Appearance Settings', 'Manage system appearance and themes', 8, 'appearance_settings.php', 4),
('settings.branding', 'Branding Settings', 'Manage system branding and logos', 8, 'branding_settings.php', 5),
('settings.logo', 'Logo Management', 'Manage system logos', 8, 'logo_management_consolidated.php', 6),
('settings.custom_fields', 'Custom Fields', 'Manage custom fields', 8, 'custom_fields.php', 7),
('settings.security_audit', 'Security Audit', 'View security audit logs', 8, 'security_audit.php', 8),
('settings.security_settings', 'Security Settings', 'Manage security settings', 8, 'security_settings.php', 9),
('settings.backup', 'Database Backup', 'Manage database backups', 8, 'backup_management.php', 10),
('settings.profile', 'My Profile', 'Manage personal profile', 8, 'profile.php', 11);

-- Reports & Analytics Permissions
INSERT IGNORE INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('reports.analytics', 'Universal Analytics', 'Access universal analytics dashboard', 9, 'universal_analytics_dashboard.php', 1),
('reports.event_reports', 'Event Reports', 'View event-specific reports', 9, 'event_reports.php', 2),
('reports.member_reports', 'Member Reports', 'View member-related reports', 9, NULL, 3),
('reports.donation_reports', 'Donation Reports', 'View donation and financial reports', 9, NULL, 4),
('reports.export', 'Export Reports', 'Export reports to various formats', 9, NULL, 5);

-- Notifications Permissions
INSERT IGNORE INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('notifications.view', 'View Notifications', 'View system notifications', 10, 'notifications.php', 1),
('notifications.manage', 'Manage Notifications', 'Create and manage notifications', 10, NULL, 2);

-- Comprehensive Admin Management Permissions (Super Admin Only)
INSERT IGNORE INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('admin.rbac_management', 'RBAC Management', 'Manage role-based access control system', 11, 'setup_rbac_system.php', 1),
('admin.create_users', 'Create Admin Users', 'Create new admin users', 11, 'create_admin_users.php', 2),
('admin.permission_management', 'Permission Management', 'Manage individual user permissions', 11, 'manage_user_permissions.php', 3),
('admin.granular_permissions_setup', 'Granular Permissions Setup', 'Set up granular permission system', 11, 'setup_granular_permissions_system.php', 4),
('admin.initialize_rbac', 'Initialize RBAC Database', 'Initialize RBAC database tables', 11, 'initialize_rbac_database.php', 5),
('admin.super_admin_navigation', 'Super Admin Navigation', 'Access super admin navigation', 11, 'super_admin_navigation.php', 6);

-- AI & Predictions Permissions
INSERT IGNORE INTO granular_permissions (permission_key, permission_name, permission_description, category_id, page_file, sort_order) VALUES
('ai.predictions', 'AI Predictions', 'Access AI-powered predictions and insights', 12, 'universal_ai_dashboard.php', 1),
('ai.analytics', 'AI Analytics', 'View AI-powered analytics', 12, NULL, 2);
