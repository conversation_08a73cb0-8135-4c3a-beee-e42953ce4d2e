<?php
/**
 * Enhanced RSVP System
 * Comprehensive RSVP with guest registration, dietary restrictions, and special needs
 */

session_start();
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$message = '';
$error = '';

// Get user data
try {
    $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
    $stmt->execute([$userId]);
    $userData = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$userData) {
        header("Location: login.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading user data: " . $e->getMessage();
    $userData = ['first_name' => 'User'];
}

// Get site settings
$sitename = 'Organization Management';
try {
    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
    $stmt->execute();
    $siteNameResult = $stmt->fetch();
    if ($siteNameResult) {
        $sitename = $siteNameResult['setting_value'];
    }
} catch (PDOException $e) {
    // Use default if settings table doesn't exist
}

// Get event ID from URL
$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    header("Location: events.php");
    exit();
}

// Check for success message from redirect
if (isset($_GET['success']) && $_GET['success'] == '1') {
    $message = "Your RSVP has been submitted successfully!";
}
if (isset($_GET['updated']) && $_GET['updated'] == '1') {
    $message = "Your RSVP has been updated successfully!";
}

// Get event details
try {
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ? AND (status = 'published' OR (status IS NULL AND is_active = 1))");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        header("Location: events.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading event: " . $e->getMessage();
}

// Ensure enhanced RSVP columns exist
try {
    // First, check what columns exist in the table
    $stmt = $pdo->query("SHOW COLUMNS FROM event_rsvps");
    $existing_columns = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $existing_columns[] = $row['Field'];
    }
    // Debug: Log existing columns (remove in production)
    // error_log("Existing columns in event_rsvps: " . implode(', ', $existing_columns));

    // Check if we have the basic required columns
    $has_basic_columns = in_array('event_id', $existing_columns) && in_array('status', $existing_columns);
    if (!$has_basic_columns) {
        throw new Exception("Missing basic required columns in event_rsvps table");
    }

    // Add missing columns one by one to avoid conflicts
    $required_columns = [
        'user_id' => 'INT(11) DEFAULT NULL',
        'guest_count' => 'INT(11) DEFAULT 0',
        'dietary_restrictions' => 'TEXT',
        'special_needs' => 'TEXT',
        'transportation_needed' => 'TINYINT(1) DEFAULT 0',
        'can_provide_transportation' => 'TINYINT(1) DEFAULT 0',
        'vehicle_capacity' => 'INT(11) DEFAULT NULL',
        'emergency_contact_name' => 'VARCHAR(255)',
        'emergency_contact_phone' => 'VARCHAR(20)',
        'additional_info' => 'TEXT'
    ];

    foreach ($required_columns as $column => $definition) {
        if (!in_array($column, $existing_columns)) {
            try {
                $pdo->exec("ALTER TABLE event_rsvps ADD COLUMN $column $definition");
                // error_log("Successfully added column: $column");
                // Add to existing columns array so we know it's available
                $existing_columns[] = $column;
            } catch (PDOException $e) {
                error_log("Error adding column $column: " . $e->getMessage());
            }
        }
    }

    // Refresh the existing columns list after modifications
    $stmt = $pdo->query("SHOW COLUMNS FROM event_rsvps");
    $existing_columns = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $existing_columns[] = $row['Field'];
    }

    // Add index for user_id if it doesn't exist
    if (in_array('user_id', $existing_columns)) {
        try {
            $pdo->exec("ALTER TABLE event_rsvps ADD INDEX idx_user_id (user_id)");
        } catch (PDOException $e) {
            // Index might already exist, ignore error
        }
    }

    // Create event_guests table if it doesn't exist
    $pdo->exec("CREATE TABLE IF NOT EXISTS event_guests (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        event_id INT(11) NOT NULL,
        rsvp_id INT(11) NOT NULL,
        guest_name VARCHAR(255) NOT NULL,
        guest_email VARCHAR(255),
        guest_phone VARCHAR(20),
        age_group ENUM('child', 'teen', 'adult', 'senior') DEFAULT 'adult',
        relationship_to_member VARCHAR(100),
        dietary_restrictions TEXT,
        special_needs TEXT,
        emergency_contact_name VARCHAR(255),
        emergency_contact_phone VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_event_id (event_id),
        INDEX idx_rsvp_id (rsvp_id),
        FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
        FOREIGN KEY (rsvp_id) REFERENCES event_rsvps(id) ON DELETE CASCADE
    )");
} catch (PDOException $e) {
    error_log("Error adding enhanced RSVP columns: " . $e->getMessage());
}

// Get existing RSVP if any - check based on available columns
$existing_rsvp = null;
try {
    // Try different column combinations based on what exists
    if (in_array('user_id', $existing_columns)) {
        $stmt = $pdo->prepare("SELECT * FROM event_rsvps WHERE event_id = ? AND user_id = ?");
        $stmt->execute([$event_id, $userId]);
        $existing_rsvp = $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // If not found and member_id column exists, try that
    if (!$existing_rsvp && in_array('member_id', $existing_columns)) {
        $stmt = $pdo->prepare("SELECT * FROM event_rsvps WHERE event_id = ? AND member_id = ?");
        $stmt->execute([$event_id, $userId]);
        $existing_rsvp = $stmt->fetch(PDO::FETCH_ASSOC);
    }

    error_log("Existing RSVP check: " . ($existing_rsvp ? "Found RSVP ID " . $existing_rsvp['id'] : "No existing RSVP found"));
} catch (PDOException $e) {
    error_log("Error checking existing RSVP: " . $e->getMessage());
    $existing_rsvp = null;
}

// Get existing guests if any
$existing_guests = [];
if ($existing_rsvp) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM event_guests WHERE event_id = ? AND rsvp_id = ?");
        $stmt->execute([$event_id, $existing_rsvp['id']]);
        $existing_guests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $existing_guests = [];
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_rsvp'])) {
    // error_log("RSVP form submitted for event_id: $event_id, user_id: $userId");
    try {
        $pdo->beginTransaction();
        
        // Prepare RSVP data
        $rsvp_data = [
            'response' => $_POST['response'],
            'guest_count' => (int)($_POST['guests_count'] ?? 0),
            'notes' => $_POST['notes'] ?? '',
            'dietary_restrictions' => $_POST['dietary_restrictions'] ?? '',
            'special_needs' => $_POST['special_needs'] ?? '',
            'transportation_needed' => isset($_POST['transportation_needed']) ? 1 : 0,
            'can_provide_transportation' => isset($_POST['can_provide_transportation']) ? 1 : 0,
            'vehicle_capacity' => $_POST['vehicle_capacity'] ?? null,
            'emergency_contact_name' => $_POST['emergency_contact_name'] ?? '',
            'emergency_contact_phone' => $_POST['emergency_contact_phone'] ?? '',
            'additional_info' => $_POST['additional_info'] ?? ''
        ];
        
        if ($existing_rsvp) {
            // Update existing RSVP - use simple approach if possible
            if (in_array('notes', $existing_columns) && in_array('updated_at', $existing_columns)) {
                // Use simple update that matches the basic RSVP handler
                $stmt = $pdo->prepare("
                    UPDATE event_rsvps
                    SET status = ?, notes = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$rsvp_data['response'], $rsvp_data['notes'], $existing_rsvp['id']]);
                $rsvp_id = $existing_rsvp['id'];
                // error_log("Used simple RSVP update for ID: " . $rsvp_id);
            } else {
                // Build dynamic query based on existing columns
                $update_fields = ['status = ?'];
                $update_values = [$rsvp_data['response']];

                // Add guest count field (try both guest_count and guests_count)
                if (in_array('guest_count', $existing_columns)) {
                    $update_fields[] = 'guest_count = ?';
                    $update_values[] = $rsvp_data['guest_count'];
                } elseif (in_array('guests_count', $existing_columns)) {
                    $update_fields[] = 'guests_count = ?';
                    $update_values[] = $rsvp_data['guest_count'];
                }

                // Add notes if column exists
                if (in_array('notes', $existing_columns)) {
                    $update_fields[] = 'notes = ?';
                    $update_values[] = $rsvp_data['notes'];
                }

                // Add enhanced fields if they exist
                $enhanced_updates = [
                    'dietary_restrictions' => $rsvp_data['dietary_restrictions'],
                    'special_needs' => $rsvp_data['special_needs'],
                    'transportation_needed' => $rsvp_data['transportation_needed'],
                    'can_provide_transportation' => $rsvp_data['can_provide_transportation'],
                    'vehicle_capacity' => $rsvp_data['vehicle_capacity'],
                    'emergency_contact_name' => $rsvp_data['emergency_contact_name'],
                    'emergency_contact_phone' => $rsvp_data['emergency_contact_phone'],
                    'additional_info' => $rsvp_data['additional_info']
                ];

                foreach ($enhanced_updates as $field => $value) {
                    if (in_array($field, $existing_columns)) {
                        $update_fields[] = "$field = ?";
                        $update_values[] = $value;
                    }
                }

                // Add updated_at if column exists
                if (in_array('updated_at', $existing_columns)) {
                    $update_fields[] = 'updated_at = CURRENT_TIMESTAMP';
                }

                $update_values[] = $existing_rsvp['id']; // WHERE clause parameter

                $sql = "UPDATE event_rsvps SET " . implode(', ', $update_fields) . " WHERE id = ?";
                // error_log("RSVP UPDATE SQL: " . $sql);
                // error_log("RSVP UPDATE VALUES: " . print_r($update_values, true));
                $stmt = $pdo->prepare($sql);
                $stmt->execute($update_values);
                $rsvp_id = $existing_rsvp['id'];
            }
            
            // Delete existing guests to re-add them
            $stmt = $pdo->prepare("DELETE FROM event_guests WHERE event_id = ? AND rsvp_id = ?");
            $stmt->execute([$event_id, $rsvp_id]);
        } else {
            // Create new RSVP - use fallback approach for basic compatibility
            error_log("Creating new RSVP for event_id: $event_id, user_id: $userId");

            // Double-check for existing RSVP to prevent duplicates
            $stmt = $pdo->prepare("SELECT id FROM event_rsvps WHERE event_id = ? AND user_id = ?");
            $stmt->execute([$event_id, $userId]);
            $duplicate_check = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($duplicate_check) {
                error_log("Found duplicate RSVP during insert check - updating instead");
                $existing_rsvp = ['id' => $duplicate_check['id']];
                $stmt = $pdo->prepare("UPDATE event_rsvps SET status = ?, notes = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$rsvp_data['response'], $rsvp_data['notes'], $duplicate_check['id']]);
                $rsvp_id = $duplicate_check['id'];
            } else if (in_array('user_id', $existing_columns) && in_array('notes', $existing_columns)) {
                // Use the simple RSVP structure that works
                $stmt = $pdo->prepare("
                    INSERT INTO event_rsvps (event_id, user_id, status, notes, created_at, updated_at)
                    VALUES (?, ?, ?, ?, NOW(), NOW())
                ");
                $stmt->execute([$event_id, $userId, $rsvp_data['response'], $rsvp_data['notes']]);
                $rsvp_id = $pdo->lastInsertId();
                // error_log("Used simple RSVP insert - ID: $rsvp_id");
            } else {
                // Build dynamic query for whatever columns exist
                $columns = ['event_id', 'status'];
                $values = [$event_id, $rsvp_data['response']];
                $placeholders = ['?', '?'];

                // Add guest count column (try both guest_count and guests_count)
                if (in_array('guest_count', $existing_columns)) {
                    $columns[] = 'guest_count';
                    $values[] = $rsvp_data['guest_count'];
                    $placeholders[] = '?';
                } elseif (in_array('guests_count', $existing_columns)) {
                    $columns[] = 'guests_count';
                    $values[] = $rsvp_data['guest_count'];
                    $placeholders[] = '?';
                }

                // Add notes if column exists
                if (in_array('notes', $existing_columns)) {
                    $columns[] = 'notes';
                    $values[] = $rsvp_data['notes'];
                    $placeholders[] = '?';
                }

                // Add user_id if column exists
                if (in_array('user_id', $existing_columns)) {
                    $columns[] = 'user_id';
                    $values[] = $userId;
                    $placeholders[] = '?';
                }

                // Add member_id if column exists
                if (in_array('member_id', $existing_columns)) {
                    $columns[] = 'member_id';
                    $values[] = $userId;
                    $placeholders[] = '?';
                }

                // Add enhanced columns if they exist
                $enhanced_fields = [
                    'dietary_restrictions' => $rsvp_data['dietary_restrictions'],
                    'special_needs' => $rsvp_data['special_needs'],
                    'transportation_needed' => $rsvp_data['transportation_needed'],
                    'can_provide_transportation' => $rsvp_data['can_provide_transportation'],
                    'vehicle_capacity' => $rsvp_data['vehicle_capacity'],
                    'emergency_contact_name' => $rsvp_data['emergency_contact_name'],
                    'emergency_contact_phone' => $rsvp_data['emergency_contact_phone'],
                    'additional_info' => $rsvp_data['additional_info']
                ];

                foreach ($enhanced_fields as $field => $value) {
                    if (in_array($field, $existing_columns)) {
                        $columns[] = $field;
                        $values[] = $value;
                        $placeholders[] = '?';
                    }
                }

                $sql = "INSERT INTO event_rsvps (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
                // error_log("RSVP INSERT SQL: " . $sql);
                // error_log("RSVP INSERT VALUES: " . print_r($values, true));
                $stmt = $pdo->prepare($sql);
                $stmt->execute($values);
                $rsvp_id = $pdo->lastInsertId();
            }
        }
        
        // Add guests if any
        if ($rsvp_data['guest_count'] > 0 && isset($_POST['guests'])) {
            $stmt = $pdo->prepare("
                INSERT INTO event_guests 
                (event_id, rsvp_id, guest_name, guest_email, guest_phone, age_group, 
                 relationship_to_member, dietary_restrictions, special_needs, 
                 emergency_contact_name, emergency_contact_phone)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($_POST['guests'] as $guest) {
                if (!empty($guest['name'])) {
                    $stmt->execute([
                        $event_id, $rsvp_id, $guest['name'], $guest['email'] ?? '',
                        $guest['phone'] ?? '', $guest['age_group'] ?? 'adult',
                        $guest['relationship'] ?? '', $guest['dietary_restrictions'] ?? '',
                        $guest['special_needs'] ?? '', $guest['emergency_contact_name'] ?? '',
                        $guest['emergency_contact_phone'] ?? ''
                    ]);
                }
            }
        }
        
        $pdo->commit();

        // Stay on the same page to show success message
        // Redirect to self with appropriate success parameter to prevent form resubmission
        if ($existing_rsvp) {
            // error_log("Redirecting to updated success page");
            header("Location: enhanced_rsvp.php?event_id=" . $event_id . "&updated=1");
        } else {
            // error_log("Redirecting to new success page");
            header("Location: enhanced_rsvp.php?event_id=" . $event_id . "&success=1");
        }
        exit();
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        $error = "Error saving RSVP: " . $e->getMessage();
    }
}

// Get user data
$userData = $userAuth->getUserById($userId);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RSVP - <?php echo htmlspecialchars($event['title']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        /* Responsive logo adjustments */
        @media (max-width: 768px) {
            .navbar-logo {
                max-height: 32px;
                max-width: 120px;
            }

            .navbar-brand {
                font-size: 1rem;
            }
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
        }

        .navbar-nav .nav-link.active {
            color: white !important;
            font-weight: 600;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            transition: background-color 0.15s ease-in-out;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .dropdown-item i {
            width: 16px;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
    
    <div class="container mt-4">
        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Event Info -->
        <div class="card mb-4">
            <div class="card-body">
                <h2 class="mb-2"><?php echo htmlspecialchars($event['title']); ?></h2>
                <p class="text-muted mb-1">
                    <i class="bi bi-calendar"></i> <?php echo date('M j, Y g:i A', strtotime($event['event_date'])); ?>
                    <?php if ($event['location']): ?>
                        | <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($event['location']); ?>
                    <?php endif; ?>
                </p>
                <?php if ($event['description']): ?>
                    <p class="mb-0"><?php echo nl2br(htmlspecialchars($event['description'])); ?></p>
                <?php endif; ?>
            </div>
        </div>

        <!-- RSVP Form -->
        <div class="card">
            <div class="card-header">
                <h4><i class="bi bi-calendar-check"></i> 
                    <?php echo $existing_rsvp ? 'Update Your RSVP' : 'RSVP to Event'; ?>
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" id="rsvpForm">
                    <!-- Response Selection -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">Will you be attending? *</label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="response" value="attending" 
                                           id="response_attending" <?php echo ($existing_rsvp && $existing_rsvp['status'] === 'attending') ? 'checked' : ''; ?> required>
                                    <label class="form-check-label" for="response_attending">
                                        <i class="bi bi-check-circle text-success"></i> Yes, I'll be there
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="response" value="maybe" 
                                           id="response_maybe" <?php echo ($existing_rsvp && $existing_rsvp['status'] === 'maybe') ? 'checked' : ''; ?> required>
                                    <label class="form-check-label" for="response_maybe">
                                        <i class="bi bi-question-circle text-warning"></i> Maybe
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="response" value="not_attending" 
                                           id="response_not_attending" <?php echo ($existing_rsvp && $existing_rsvp['status'] === 'not_attending') ? 'checked' : ''; ?> required>
                                    <label class="form-check-label" for="response_not_attending">
                                        <i class="bi bi-x-circle text-danger"></i> No, I can't make it
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Attending Options (shown only if attending/maybe) -->
                    <div id="attendingOptions" style="display: none;">
                        <!-- Guest Information -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Number of Guests</label>
                            <div class="row align-items-center">
                                <div class="col-md-3">
                                    <input type="number" class="form-control" name="guests_count" id="guests_count"
                                           min="0" max="10" value="<?php echo $existing_rsvp['guest_count'] ?? 0; ?>">
                                </div>
                                <div class="col-md-9">
                                    <small class="text-muted">How many additional people will you bring?</small>
                                </div>
                            </div>
                        </div>

                        <!-- Guest Details -->
                        <div id="guestDetails" style="display: none;">
                            <h5 class="mb-3">Guest Information</h5>
                            <div id="guestForms"></div>
                        </div>

                        <!-- Dietary Restrictions -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Dietary Restrictions & Allergies</label>
                            <textarea class="form-control" name="dietary_restrictions" rows="3" 
                                      placeholder="Please list any dietary restrictions, food allergies, or special meal requirements..."><?php echo htmlspecialchars($existing_rsvp['dietary_restrictions'] ?? ''); ?></textarea>
                        </div>

                        <!-- Special Needs -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Accessibility & Special Needs</label>
                            <textarea class="form-control" name="special_needs" rows="3" 
                                      placeholder="Please describe any accessibility needs, mobility assistance, or other accommodations required..."><?php echo htmlspecialchars($existing_rsvp['special_needs'] ?? ''); ?></textarea>
                        </div>

                        <!-- Transportation -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Transportation</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="transportation_needed" 
                                               id="transportation_needed" <?php echo ($existing_rsvp && $existing_rsvp['transportation_needed']) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="transportation_needed">
                                            I need a ride to the event
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="can_provide_transportation" 
                                               id="can_provide_transportation" <?php echo ($existing_rsvp && $existing_rsvp['can_provide_transportation']) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="can_provide_transportation">
                                            I can provide rides to others
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div id="vehicleCapacity" class="mt-2" style="display: none;">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label class="form-label">Vehicle Capacity</label>
                                        <input type="number" class="form-control" name="vehicle_capacity" 
                                               min="1" max="20" value="<?php echo $existing_rsvp['vehicle_capacity'] ?? ''; ?>"
                                               placeholder="How many people can you transport?">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Emergency Contact -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Emergency Contact</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Contact Name</label>
                                    <input type="text" class="form-control" name="emergency_contact_name" 
                                           value="<?php echo htmlspecialchars($existing_rsvp['emergency_contact_name'] ?? ''); ?>"
                                           placeholder="Emergency contact person">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Contact Phone</label>
                                    <input type="tel" class="form-control" name="emergency_contact_phone" 
                                           value="<?php echo htmlspecialchars($existing_rsvp['emergency_contact_phone'] ?? ''); ?>"
                                           placeholder="Emergency contact phone number">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">Additional Information</label>
                        <textarea class="form-control" name="additional_info" rows="3" 
                                  placeholder="Any other information, questions, or comments..."><?php echo htmlspecialchars($existing_rsvp['additional_info'] ?? ''); ?></textarea>
                    </div>

                    <!-- Notes -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">Notes</label>
                        <textarea class="form-control" name="notes" rows="3" 
                                  placeholder="Any additional notes or comments..."><?php echo htmlspecialchars($existing_rsvp['notes'] ?? ''); ?></textarea>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="events.php" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Events
                        </a>
                        <button type="submit" name="submit_rsvp" class="btn btn-primary btn-lg">
                            <i class="bi bi-check-circle"></i> 
                            <?php echo $existing_rsvp ? 'Update RSVP' : 'Submit RSVP'; ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const responseInputs = document.querySelectorAll('input[name="response"]');
            const attendingOptions = document.getElementById('attendingOptions');
            const guestsCountInput = document.getElementById('guests_count');
            const guestDetails = document.getElementById('guestDetails');
            const guestForms = document.getElementById('guestForms');
            const canProvideTransportation = document.getElementById('can_provide_transportation');
            const vehicleCapacity = document.getElementById('vehicleCapacity');

            // Show/hide attending options based on response
            function toggleAttendingOptions() {
                const selectedResponse = document.querySelector('input[name="response"]:checked');
                if (selectedResponse && (selectedResponse.value === 'attending' || selectedResponse.value === 'maybe')) {
                    attendingOptions.style.display = 'block';
                } else {
                    attendingOptions.style.display = 'none';
                }
            }

            // Show/hide vehicle capacity based on transportation checkbox
            function toggleVehicleCapacity() {
                if (canProvideTransportation.checked) {
                    vehicleCapacity.style.display = 'block';
                } else {
                    vehicleCapacity.style.display = 'none';
                }
            }

            // Generate guest forms based on count
            function generateGuestForms() {
                const count = parseInt(guestsCountInput.value) || 0;
                guestForms.innerHTML = '';
                
                if (count > 0) {
                    guestDetails.style.display = 'block';
                    
                    for (let i = 0; i < count; i++) {
                        const guestForm = createGuestForm(i + 1);
                        guestForms.appendChild(guestForm);
                    }
                } else {
                    guestDetails.style.display = 'none';
                }
            }

            // Create individual guest form
            function createGuestForm(guestNumber) {
                const div = document.createElement('div');
                div.className = 'card mb-3';
                div.innerHTML = `
                    <div class="card-header">
                        <h6 class="mb-0">Guest ${guestNumber}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Name *</label>
                                <input type="text" class="form-control" name="guests[${guestNumber-1}][name]" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Age Group</label>
                                <select class="form-select" name="guests[${guestNumber-1}][age_group]">
                                    <option value="adult">Adult</option>
                                    <option value="teen">Teen (13-17)</option>
                                    <option value="child">Child (under 13)</option>
                                    <option value="senior">Senior (65+)</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="guests[${guestNumber-1}][email]">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Phone</label>
                                <input type="tel" class="form-control" name="guests[${guestNumber-1}][phone]">
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <label class="form-label">Relationship to You</label>
                                <input type="text" class="form-control" name="guests[${guestNumber-1}][relationship]" placeholder="e.g., spouse, child, friend">
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <label class="form-label">Dietary Restrictions</label>
                                <input type="text" class="form-control" name="guests[${guestNumber-1}][dietary_restrictions]">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Special Needs</label>
                                <input type="text" class="form-control" name="guests[${guestNumber-1}][special_needs]">
                            </div>
                        </div>
                    </div>
                `;
                return div;
            }

            // Event listeners
            responseInputs.forEach(input => {
                input.addEventListener('change', toggleAttendingOptions);
            });

            guestsCountInput.addEventListener('input', generateGuestForms);
            canProvideTransportation.addEventListener('change', toggleVehicleCapacity);

            // Initialize based on existing data
            toggleAttendingOptions();
            toggleVehicleCapacity();
            generateGuestForms();

            // Populate existing guest data if editing
            <?php if (!empty($existing_guests)): ?>
                // Set guest count
                guestsCountInput.value = <?php echo count($existing_guests); ?>;
                generateGuestForms();
                
                // Populate guest data
                <?php foreach ($existing_guests as $index => $guest): ?>
                    const guest<?php echo $index; ?>Form = guestForms.children[<?php echo $index; ?>];
                    if (guest<?php echo $index; ?>Form) {
                        guest<?php echo $index; ?>Form.querySelector('input[name="guests[<?php echo $index; ?>][name]"]').value = '<?php echo htmlspecialchars($guest['guest_name']); ?>';
                        guest<?php echo $index; ?>Form.querySelector('input[name="guests[<?php echo $index; ?>][email]"]').value = '<?php echo htmlspecialchars($guest['guest_email']); ?>';
                        guest<?php echo $index; ?>Form.querySelector('input[name="guests[<?php echo $index; ?>][phone]"]').value = '<?php echo htmlspecialchars($guest['guest_phone']); ?>';
                        guest<?php echo $index; ?>Form.querySelector('select[name="guests[<?php echo $index; ?>][age_group]"]').value = '<?php echo $guest['age_group']; ?>';
                        guest<?php echo $index; ?>Form.querySelector('input[name="guests[<?php echo $index; ?>][relationship]"]').value = '<?php echo htmlspecialchars($guest['relationship_to_member']); ?>';
                        guest<?php echo $index; ?>Form.querySelector('input[name="guests[<?php echo $index; ?>][dietary_restrictions]"]').value = '<?php echo htmlspecialchars($guest['dietary_restrictions']); ?>';
                        guest<?php echo $index; ?>Form.querySelector('input[name="guests[<?php echo $index; ?>][special_needs]"]').value = '<?php echo htmlspecialchars($guest['special_needs']); ?>';
                    }
                <?php endforeach; ?>
            <?php endif; ?>
        });
    </script>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
</body>
</html>
