<?php
/**
 * Settings Tables Consolidation Script
 * Consolidates settings, site_settings, email_settings, and appearance_settings
 * into a clean 2-table system: site_settings + appearance_settings
 */

session_start();
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/includes/route_protection.php';

// Protect this page - Super Admin only
protectSuperAdminRoute();

$message = '';
$error = '';
$consolidation_results = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['consolidate_settings'])) {
    try {
        echo '<h2>🔧 Settings Tables Consolidation</h2>';
        echo '<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">';
        
        // Step 1: Backup existing data
        echo '<h3>📋 Step 1: Creating Backup</h3>';
        $backup_timestamp = date('Y-m-d_H-i-s');
        
        // Create backup tables
        $pdo->exec("CREATE TABLE settings_backup_$backup_timestamp AS SELECT * FROM settings");
        $pdo->exec("CREATE TABLE site_settings_backup_$backup_timestamp AS SELECT * FROM site_settings");
        $pdo->exec("CREATE TABLE email_settings_backup_$backup_timestamp AS SELECT * FROM email_settings");
        $pdo->exec("CREATE TABLE appearance_settings_backup_$backup_timestamp AS SELECT * FROM appearance_settings");
        echo "✅ Backup tables created with timestamp: $backup_timestamp<br>";
        
        // Step 2: Standardize site_settings table structure
        echo '<h3>📋 Step 2: Standardizing site_settings Table</h3>';
        
        // Add missing columns to site_settings if needed
        try {
            $pdo->exec("ALTER TABLE site_settings ADD COLUMN setting_type ENUM('text', 'number', 'boolean', 'json', 'email', 'url', 'color') DEFAULT 'text'");
            echo "✅ Added setting_type column to site_settings<br>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column') === false) {
                echo "⚠️ Could not add setting_type column: " . $e->getMessage() . "<br>";
            }
        }
        
        try {
            $pdo->exec("ALTER TABLE site_settings ADD COLUMN description TEXT");
            echo "✅ Added description column to site_settings<br>";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column') === false) {
                echo "⚠️ Could not add description column: " . $e->getMessage() . "<br>";
            }
        }
        
        // Step 3: Consolidate all general settings into site_settings
        echo '<h3>📋 Step 3: Consolidating General Settings</h3>';
        
        // Get all settings from the old settings table
        $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings");
        $old_settings = $stmt->fetchAll();
        
        $consolidated_count = 0;
        foreach ($old_settings as $setting) {
            $setting_name = $setting['setting_key'];
            $setting_value = $setting['setting_value'];
            
            // Determine setting type
            $setting_type = 'text';
            if (strpos($setting_name, 'email') !== false) $setting_type = 'email';
            elseif (strpos($setting_name, 'port') !== false) $setting_type = 'number';
            elseif (strpos($setting_name, 'auth') !== false || strpos($setting_name, 'secure') !== false) $setting_type = 'boolean';
            elseif (strpos($setting_name, 'color') !== false) $setting_type = 'color';
            elseif (strpos($setting_name, 'url') !== false) $setting_type = 'url';
            
            // Insert or update in site_settings
            $stmt = $pdo->prepare("
                INSERT INTO site_settings (setting_name, setting_value, setting_type, description, created_at, updated_at) 
                VALUES (?, ?, ?, ?, NOW(), NOW())
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value), 
                setting_type = VALUES(setting_type),
                updated_at = NOW()
            ");
            $stmt->execute([$setting_name, $setting_value, $setting_type, "Migrated from settings table"]);
            $consolidated_count++;
        }
        echo "✅ Consolidated $consolidated_count settings from 'settings' table<br>";
        
        // Get all settings from email_settings table
        $stmt = $pdo->query("SELECT setting_key, setting_value FROM email_settings");
        $email_settings = $stmt->fetchAll();
        
        $email_count = 0;
        foreach ($email_settings as $setting) {
            $setting_name = 'email_' . $setting['setting_key']; // Prefix to avoid conflicts
            $setting_value = $setting['setting_value'];
            
            $setting_type = 'text';
            if (strpos($setting['setting_key'], 'email') !== false) $setting_type = 'email';
            elseif (strpos($setting['setting_key'], 'port') !== false) $setting_type = 'number';
            elseif (strpos($setting['setting_key'], 'auth') !== false || strpos($setting['setting_key'], 'secure') !== false) $setting_type = 'boolean';
            
            $stmt = $pdo->prepare("
                INSERT INTO site_settings (setting_name, setting_value, setting_type, description, created_at, updated_at) 
                VALUES (?, ?, ?, ?, NOW(), NOW())
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value), 
                setting_type = VALUES(setting_type),
                updated_at = NOW()
            ");
            $stmt->execute([$setting_name, $setting_value, $setting_type, "Migrated from email_settings table"]);
            $email_count++;
        }
        echo "✅ Consolidated $email_count email settings from 'email_settings' table<br>";
        
        // Step 4: Clean up appearance_settings table structure
        echo '<h3>📋 Step 4: Standardizing appearance_settings Table</h3>';
        
        // Ensure appearance_settings has proper structure
        try {
            $pdo->exec("ALTER TABLE appearance_settings MODIFY COLUMN setting_type ENUM('text', 'color', 'number', 'boolean', 'json', 'url', 'file') DEFAULT 'text'");
            echo "✅ Updated setting_type enum in appearance_settings<br>";
        } catch (Exception $e) {
            echo "⚠️ Could not update setting_type enum: " . $e->getMessage() . "<br>";
        }
        
        // Step 5: Remove duplicates and conflicts
        echo '<h3>📋 Step 5: Resolving Conflicts and Duplicates</h3>';
        
        // Remove appearance-related settings from site_settings (they should be in appearance_settings)
        $appearance_keys = ['primary_color', 'secondary_color', 'accent_color', 'font_family', 'logo_url', 'logo_width', 'logo_height'];
        $removed_count = 0;
        
        foreach ($appearance_keys as $key) {
            $stmt = $pdo->prepare("DELETE FROM site_settings WHERE setting_name = ?");
            $stmt->execute([$key]);
            if ($stmt->rowCount() > 0) {
                $removed_count++;
                echo "✅ Removed '$key' from site_settings (belongs in appearance_settings)<br>";
            }
        }
        
        // Step 6: Update application code references
        echo '<h3>📋 Step 6: Summary</h3>';
        
        // Get final counts
        $stmt = $pdo->query("SELECT COUNT(*) FROM site_settings");
        $site_settings_count = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM appearance_settings");
        $appearance_settings_count = $stmt->fetchColumn();
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>✅ Consolidation Complete!</h4>";
        echo "<ul>";
        echo "<li><strong>site_settings:</strong> $site_settings_count settings (organization, contact, email, system)</li>";
        echo "<li><strong>appearance_settings:</strong> $appearance_settings_count settings (colors, fonts, logos, themes)</li>";
        echo "<li><strong>Backup tables created:</strong> settings_backup_$backup_timestamp, site_settings_backup_$backup_timestamp, email_settings_backup_$backup_timestamp, appearance_settings_backup_$backup_timestamp</li>";
        echo "</ul>";
        echo "</div>";
        
        echo '<h3>📋 Next Steps</h3>';
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h5>⚠️ Important:</h5>";
        echo "<ol>";
        echo "<li><strong>Test the application</strong> to ensure all settings are loading correctly</li>";
        echo "<li><strong>Update any custom code</strong> that references the old 'settings' or 'email_settings' tables</li>";
        echo "<li><strong>Once confirmed working</strong>, you can drop the old tables: settings, email_settings</li>";
        echo "<li><strong>Keep backup tables</strong> for at least 30 days in case rollback is needed</li>";
        echo "</ol>";
        echo "</div>";
        
        echo '</div>';
        
        $message = "Settings consolidation completed successfully!";
        
    } catch (Exception $e) {
        $error = "Consolidation error: " . $e->getMessage();
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>❌ Error:</h4>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "</div>";
    }
}

$page_title = 'Consolidate Settings Tables';
$page_header = 'Settings Tables Consolidation';
$page_description = 'Consolidate multiple settings tables into a unified system';

include __DIR__ . '/includes/header.php';
?>

<style>
.consolidation-header {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    color: white;
    border-radius: 10px;
}

.consolidation-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.table-info {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="consolidation-header p-4 text-center">
            <h2 class="mb-2">
                <i class="bi bi-database-gear"></i> <?php echo $page_header; ?>
            </h2>
            <p class="mb-0"><?php echo $page_description; ?></p>
        </div>
    </div>
</div>

<!-- Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Current Status -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="consolidation-card">
            <h5><i class="bi bi-database"></i> Current Settings Tables Status</h5>
            <p class="text-muted">Review your current settings tables before consolidation:</p>
            
            <?php
            try {
                $tables_info = [];
                $tables_to_check = ['settings', 'site_settings', 'email_settings', 'appearance_settings'];
                
                foreach ($tables_to_check as $table) {
                    try {
                        $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                        $count = $stmt->fetchColumn();
                        $tables_info[$table] = $count;
                    } catch (Exception $e) {
                        $tables_info[$table] = 'Not found';
                    }
                }
                ?>
                
                <div class="row">
                    <?php foreach ($tables_info as $table => $count): ?>
                        <div class="col-md-3">
                            <div class="table-info text-center">
                                <h6><?php echo $table; ?></h6>
                                <span class="badge <?php echo is_numeric($count) ? 'bg-primary' : 'bg-secondary'; ?>">
                                    <?php echo is_numeric($count) ? $count . ' records' : $count; ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
            <?php } catch (Exception $e): ?>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> Could not check table status: <?php echo $e->getMessage(); ?>
                </div>
            <?php endtry; ?>
        </div>
    </div>
</div>

<!-- Consolidation Plan -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="consolidation-card">
            <h5><i class="bi bi-diagram-3"></i> Consolidation Plan</h5>
            <p class="text-muted">This process will consolidate your settings into 2 clean tables:</p>
            
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> What will happen:</h6>
                <ol>
                    <li><strong>Backup:</strong> Create backup copies of all existing settings tables</li>
                    <li><strong>Consolidate:</strong> Merge settings into 2 main tables:
                        <ul>
                            <li><strong>site_settings:</strong> Organization, contact, email, system settings</li>
                            <li><strong>appearance_settings:</strong> Colors, fonts, logos, themes</li>
                        </ul>
                    </li>
                    <li><strong>Clean up:</strong> Remove duplicates and resolve conflicts</li>
                    <li><strong>Standardize:</strong> Ensure consistent column structure</li>
                </ol>
            </div>
            
            <div class="alert alert-warning">
                <h6><i class="bi bi-exclamation-triangle"></i> Important Notes:</h6>
                <ul>
                    <li>This process creates backups before making changes</li>
                    <li>Test your application thoroughly after consolidation</li>
                    <li>Keep backup tables for at least 30 days</li>
                    <li>Some custom code may need updates to reference the new structure</li>
                </ul>
            </div>
            
            <form method="POST">
                <button type="submit" name="consolidate_settings" class="btn btn-danger btn-lg" 
                        onclick="return confirm('Are you sure you want to consolidate the settings tables? This will modify your database structure.')">
                    <i class="bi bi-database-gear"></i> Start Consolidation Process
                </button>
            </form>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="consolidation-card">
            <h5><i class="bi bi-shield-check"></i> Safety Features</h5>
            
            <div class="mb-3">
                <i class="bi bi-database text-primary"></i>
                <strong>Automatic Backups</strong>
                <br><small class="text-muted">All tables backed up before changes</small>
            </div>
            
            <div class="mb-3">
                <i class="bi bi-arrow-clockwise text-success"></i>
                <strong>Rollback Ready</strong>
                <br><small class="text-muted">Easy to restore from backups if needed</small>
            </div>
            
            <div class="mb-3">
                <i class="bi bi-check-circle text-info"></i>
                <strong>Conflict Resolution</strong>
                <br><small class="text-muted">Automatically handles duplicate settings</small>
            </div>
            
            <div class="mb-3">
                <i class="bi bi-gear text-warning"></i>
                <strong>Structure Standardization</strong>
                <br><small class="text-muted">Ensures consistent column types</small>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/includes/footer.php'; ?>
