<?php
// <PERSON><PERSON><PERSON> to check and fix admin3 permissions
require_once '../config.php';

echo "<h2>Fix Admin3 Permissions</h2>";

try {
    // Get admin3 user ID
    $stmt = $pdo->prepare("SELECT id FROM admins WHERE username = 'admin3'");
    $stmt->execute();
    $admin3 = $stmt->fetch();
    
    if (!$admin3) {
        echo "<p style='color: red;'>Error: admin3 user not found!</p>";
        exit;
    }
    
    $user_id = $admin3['id'];
    echo "<p>Found admin3 with User ID: " . $user_id . "</p>";
    
    // Reset admin3 password for testing
    $new_password = 'test123';
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("UPDATE admins SET password = ? WHERE username = 'admin3'");
    $stmt->execute([$hashed_password]);
    echo "<p style='color: green;'>✓ Password reset to: " . htmlspecialchars($new_password) . "</p>";
    
    // Check current permissions for admin3
    echo "<h3>Current Permissions for admin3:</h3>";
    $stmt = $pdo->prepare("
        SELECT gp.permission_key, gp.permission_name
        FROM user_individual_permissions uip
        JOIN granular_permissions gp ON uip.permission_id = gp.id
        WHERE uip.user_id = ? 
        AND uip.is_active = 1 
        AND gp.is_active = 1
        ORDER BY gp.permission_key
    ");
    $stmt->execute([$user_id]);
    $current_permissions = $stmt->fetchAll();
    
    echo "<p>Current permissions: " . count($current_permissions) . "</p>";
    if (count($current_permissions) > 0) {
        echo "<ul>";
        foreach ($current_permissions as $perm) {
            echo "<li><strong>" . htmlspecialchars($perm['permission_key']) . "</strong> - " . htmlspecialchars($perm['permission_name']) . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: orange;'>No permissions found for admin3!</p>";
    }
    
    // Add essential permissions for admin3
    $essential_permissions = [
        'dashboard.view' => 'View Dashboard',
        'members.requests' => 'Prayer Requests', 
        'members.volunteers' => 'Volunteer Management',
        'members.view' => 'View Members',
        'members.skills' => 'Member Skills',
        'events.view' => 'View Events',
        'email.contacts' => 'Email Contacts',
        'email.contact_groups' => 'Contact Groups'
    ];
    
    echo "<h3>Adding Essential Permissions:</h3>";
    
    foreach ($essential_permissions as $permission_key => $permission_name) {
        // Get permission ID
        $stmt = $pdo->prepare("SELECT id FROM granular_permissions WHERE permission_key = ?");
        $stmt->execute([$permission_key]);
        $permission = $stmt->fetch();
        
        if (!$permission) {
            echo "<p style='color: red;'>✗ Permission not found: " . htmlspecialchars($permission_key) . "</p>";
            continue;
        }
        
        $permission_id = $permission['id'];
        
        // Check if permission already exists
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM user_individual_permissions 
            WHERE user_id = ? AND permission_id = ?
        ");
        $stmt->execute([$user_id, $permission_id]);
        $exists = $stmt->fetchColumn();
        
        if ($exists > 0) {
            // Update to make sure it's active
            $stmt = $pdo->prepare("
                UPDATE user_individual_permissions 
                SET is_active = 1 
                WHERE user_id = ? AND permission_id = ?
            ");
            $stmt->execute([$user_id, $permission_id]);
            echo "<p style='color: blue;'>↻ Updated: " . htmlspecialchars($permission_key) . "</p>";
        } else {
            // Add the permission
            $stmt = $pdo->prepare("
                INSERT INTO user_individual_permissions (user_id, permission_id, is_active, granted_by)
                VALUES (?, ?, 1, 4)
            ");
            $stmt->execute([$user_id, $permission_id]);
            echo "<p style='color: green;'>✓ Added: " . htmlspecialchars($permission_key) . "</p>";
        }
    }
    
    // Show final permissions for admin3
    echo "<h3>Final Permissions for admin3:</h3>";
    $stmt = $pdo->prepare("
        SELECT gp.permission_key, gp.permission_name
        FROM user_individual_permissions uip
        JOIN granular_permissions gp ON uip.permission_id = gp.id
        WHERE uip.user_id = ? 
        AND uip.is_active = 1 
        AND gp.is_active = 1
        ORDER BY gp.permission_key
    ");
    $stmt->execute([$user_id]);
    $final_permissions = $stmt->fetchAll();
    
    echo "<p>Total permissions: " . count($final_permissions) . "</p>";
    echo "<ul>";
    foreach ($final_permissions as $perm) {
        echo "<li><strong>" . htmlspecialchars($perm['permission_key']) . "</strong> - " . htmlspecialchars($perm['permission_name']) . "</li>";
    }
    echo "</ul>";
    
    echo "<p><strong>Login Credentials:</strong></p>";
    echo "<p>Username: admin3</p>";
    echo "<p>Password: " . htmlspecialchars($new_password) . "</p>";
    echo "<p><a href='login.php'>→ Go to Login Page</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h2, h3 { color: #333; }
    p { margin: 10px 0; }
    ul { margin: 10px 0; }
    li { margin: 5px 0; }
</style>
