<?php
session_start();

// Check if user is logged in (bypass for testing)
if (!isset($_SESSION['admin_id']) && !isset($_GET['test'])) {
    header("Location: login.php");
    exit();
}

// Set admin ID for testing if not set
if (!isset($_SESSION['admin_id'])) {
    $_SESSION['admin_id'] = 1;
}

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

// Initialize notification manager if available
$sessionNotificationManager = null;
if (file_exists('../classes/SessionNotificationManager.php')) {
    try {
        require_once '../classes/SessionNotificationManager.php';
        $sessionNotificationManager = new SessionNotificationManager($pdo);
    } catch (Exception $e) {
        error_log("Failed to initialize SessionNotificationManager: " . $e->getMessage());
        $sessionNotificationManager = null;
    }
}

$message = '';
$error = '';

// Get event ID from URL
$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if ($event_id <= 0) {
    header("Location: events.php");
    exit();
}

// Get event details
try {
    $stmt = $conn->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        header("Location: events.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading event: " . $e->getMessage();
    $event = null;
}

// Create event_sessions table if it doesn't exist
try {
    $conn->exec("
        CREATE TABLE IF NOT EXISTS event_sessions (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            event_id INT(11) NOT NULL,
            session_title VARCHAR(255) NOT NULL,
            session_description TEXT,
            start_datetime DATETIME NOT NULL,
            end_datetime DATETIME NOT NULL,
            max_attendees INT(11) DEFAULT NULL,
            location VARCHAR(255),
            instructor_name VARCHAR(255),
            status ENUM('active', 'cancelled', 'completed') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_event_id (event_id),
            INDEX idx_start_datetime (start_datetime),
            INDEX idx_status (status),
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");

    // Add created_by column if it doesn't exist
    $conn->exec("
        ALTER TABLE event_sessions
        ADD COLUMN IF NOT EXISTS created_by INT(11) DEFAULT 1,
        ADD INDEX IF NOT EXISTS idx_created_by (created_by)
    ");

    // Create session attendance table
    $conn->exec("
        CREATE TABLE IF NOT EXISTS session_attendance (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            session_id INT(11) NOT NULL,
            member_id INT(11) DEFAULT NULL,
            guest_name VARCHAR(255) DEFAULT NULL,
            guest_email VARCHAR(255) DEFAULT NULL,
            attendance_status ENUM('registered', 'attended', 'no_show') DEFAULT 'registered',
            registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            attendance_date TIMESTAMP NULL,
            notes TEXT,
            INDEX idx_session_id (session_id),
            INDEX idx_member_id (member_id),
            INDEX idx_attendance_status (attendance_status),
            FOREIGN KEY (session_id) REFERENCES event_sessions(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
} catch (PDOException $e) {
    error_log("Error creating event_sessions tables: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'add_session') {
        $session_title = trim($_POST['session_title']);
        $session_description = trim($_POST['session_description']);
        $session_date = $_POST['session_date'];
        $session_time = $_POST['session_time'];
        $session_end_time = $_POST['session_end_time'] ?? '';
        $duration_minutes = (int)$_POST['duration_minutes'];
        $max_attendees = !empty($_POST['max_attendees']) ? (int)$_POST['max_attendees'] : null;
        $location = trim($_POST['location']);
        $instructor = trim($_POST['instructor']);

        if (empty($session_title) || empty($session_date) || empty($session_time)) {
            $error = "Please fill in all required fields.";
        } else {
            try {
                // Combine date and time
                $session_datetime = $session_date . ' ' . $session_time;

                // Calculate end datetime - prefer end time if provided, otherwise use duration
                if (!empty($session_end_time)) {
                    $end_datetime = $session_date . ' ' . $session_end_time;
                    // Validate that end time is after start time
                    if (strtotime($end_datetime) <= strtotime($session_datetime)) {
                        $error = "End time must be after start time.";
                        throw new Exception($error);
                    }
                } else {
                    $end_datetime = date('Y-m-d H:i:s', strtotime($session_datetime . ' +' . $duration_minutes . ' minutes'));
                }

                // Get admin ID safely
                $admin_id = isset($_SESSION['admin_id']) ? $_SESSION['admin_id'] : 1;

                $stmt = $conn->prepare("
                    INSERT INTO event_sessions (
                        event_id, session_title, session_description, start_datetime,
                        end_datetime, max_attendees, location, instructor_name,
                        created_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");

                $stmt->execute([
                    $event_id, $session_title, $session_description, $session_datetime,
                    $end_datetime, $max_attendees, $location, $instructor, $admin_id
                ]);

                $session_id = $conn->lastInsertId();

                // Send new session notifications
                // Note: notifyNewSession method not implemented yet
                /*
                if ($sessionNotificationManager) {
                    try {
                        $sessionNotificationManager->notifyNewSession($session_id, 'all');
                    } catch (Exception $e) {
                        error_log("Failed to send new session notifications: " . $e->getMessage());
                        // Don't fail the session creation if notifications fail
                    }
                }
                */

                $message = "Session added successfully!";

                // Clear form data by redirecting
                header("Location: event_sessions.php?event_id=" . $event_id . "&success=1");
                exit();

            } catch (PDOException $e) {
                $error = "Error adding session: " . $e->getMessage();
            }
        }
    } elseif ($_POST['action'] === 'edit_session') {
        $session_id = (int)$_POST['session_id'];
        $session_title = trim($_POST['session_title']);
        $session_description = trim($_POST['session_description']);
        $session_date = $_POST['session_date'];
        $session_time = $_POST['session_time'];
        $session_end_time = $_POST['session_end_time'] ?? '';
        $duration_minutes = (int)$_POST['duration_minutes'];
        $max_attendees = !empty($_POST['max_attendees']) ? (int)$_POST['max_attendees'] : null;
        $location = trim($_POST['location']);
        $instructor = trim($_POST['instructor']);
        $status = $_POST['status'];

        if (empty($session_title) || empty($session_date) || empty($session_time)) {
            $error = "Please fill in all required fields.";
        } else {
            try {
                // Combine date and time
                $session_datetime = $session_date . ' ' . $session_time;

                // Calculate end datetime - prefer end time if provided, otherwise use duration
                if (!empty($session_end_time)) {
                    $end_datetime = $session_date . ' ' . $session_end_time;
                    // Validate that end time is after start time
                    if (strtotime($end_datetime) <= strtotime($session_datetime)) {
                        $error = "End time must be after start time.";
                        throw new Exception($error);
                    }
                } else {
                    $end_datetime = date('Y-m-d H:i:s', strtotime($session_datetime . ' +' . $duration_minutes . ' minutes'));
                }

                $stmt = $conn->prepare("
                    UPDATE event_sessions SET
                        session_title = ?, session_description = ?, start_datetime = ?,
                        end_datetime = ?, max_attendees = ?, location = ?,
                        instructor_name = ?, status = ?
                    WHERE id = ? AND event_id = ?
                ");

                $stmt->execute([
                    $session_title, $session_description, $session_datetime,
                    $end_datetime, $max_attendees, $location, $instructor, $status,
                    $session_id, $event_id
                ]);

                // Send session update notifications
                // Note: sendSessionUpdate method not implemented yet
                /*
                if ($sessionNotificationManager) {
                    try {
                        $updateMessage = "Session details have been updated. Please review the new information.";
                        $sessionNotificationManager->sendSessionUpdate($session_id, $updateMessage);
                    } catch (Exception $e) {
                        error_log("Failed to send session update notifications: " . $e->getMessage());
                        // Don't fail the update if notifications fail
                    }
                }
                */

                $message = "Session updated successfully!";

                // Clear form data by redirecting
                header("Location: event_sessions.php?event_id=" . $event_id . "&updated=1");
                exit();

            } catch (PDOException $e) {
                $error = "Error updating session: " . $e->getMessage();
            }
        }
    } elseif ($_POST['action'] === 'delete_session') {
        $session_id = (int)$_POST['session_id'];

        try {
            // Send cancellation notifications before deleting
            // Note: sendSessionCancellation method not implemented yet
            /*
            if ($sessionNotificationManager) {
                try {
                    $cancellationReason = "The session has been cancelled by the administrator.";
                    $alternativeOptions = "Please check our events page for other available sessions.";
                    $sessionNotificationManager->sendSessionCancellation($session_id, $cancellationReason, $alternativeOptions);
                } catch (Exception $e) {
                    error_log("Failed to send session cancellation notifications: " . $e->getMessage());
                    // Continue with deletion even if notifications fail
                }
            }
            */

            $stmt = $conn->prepare("DELETE FROM event_sessions WHERE id = ? AND event_id = ?");
            $stmt->execute([$session_id, $event_id]);

            $message = "Session deleted successfully!";

            // Clear form data by redirecting
            header("Location: event_sessions.php?event_id=" . $event_id . "&deleted=1");
            exit();

        } catch (PDOException $e) {
            $error = "Error deleting session: " . $e->getMessage();
        }
    }
}

// Check for success messages from redirect
if (isset($_GET['success']) && $_GET['success'] == '1') {
    $message = "Session added successfully!";
} elseif (isset($_GET['updated']) && $_GET['updated'] == '1') {
    $message = "Session updated successfully!";
} elseif (isset($_GET['deleted']) && $_GET['deleted'] == '1') {
    $message = "Session deleted successfully!";
}

// Get all sessions for this event
$sessions = [];
try {
    $stmt = $conn->prepare("
        SELECT es.*,
               COUNT(sa.id) as registered_count,
               COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as attended_count
        FROM event_sessions es
        LEFT JOIN session_attendance sa ON es.id = sa.session_id
        WHERE es.event_id = ?
        GROUP BY es.id
        ORDER BY es.start_datetime ASC
    ");
    $stmt->execute([$event_id]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error loading sessions: " . $e->getMessage());
}

// Page title and header info
$page_title = 'Event Sessions - ' . ($event ? htmlspecialchars($event['title']) : 'Unknown Event');
$page_header = 'Event Sessions';
$page_description = 'Manage sessions for: ' . ($event ? htmlspecialchars($event['title']) : 'Unknown Event');

// Include header
include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="events.php" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Events
        </a>
    </div>
</div>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($event): ?>
<!-- Event Information -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-calendar-event"></i> Event Information
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <h4><?php echo htmlspecialchars($event['title']); ?></h4>
                <p class="text-muted"><?php echo htmlspecialchars($event['description']); ?></p>
            </div>
            <div class="col-md-4">
                <p><strong>Date:</strong> <?php echo date('M j, Y g:i A', strtotime($event['event_date'])); ?></p>
                <p><strong>Location:</strong> <?php echo htmlspecialchars($event['location'] ?: 'Not specified'); ?></p>
                <p><strong>Capacity:</strong> <?php echo $event['max_attendees'] ? number_format($event['max_attendees']) : 'Unlimited'; ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Sessions Management -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            Sessions for: <strong><?php echo htmlspecialchars($event['title']); ?></strong>
        </h5>
        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addSessionModal">
            <i class="bi bi-plus-circle"></i> Add Session
        </button>
    </div>
    <div class="card-body">
        <?php if (empty($sessions)): ?>
            <div class="text-center py-5">
                <i class="bi bi-collection display-1 text-muted"></i>
                <h4 class="mt-3">No Sessions Yet</h4>
                <p class="text-muted">This event doesn't have any sessions yet.</p>
                <p class="text-muted">Click "Add Session" to create the first session for this event.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Session Title</th>
                            <th>Date & Time</th>
                            <th>Duration</th>
                            <th>Location</th>
                            <th>Instructor</th>
                            <th>Capacity</th>
                            <th>Registered</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($sessions as $session): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($session['session_title']); ?></strong>
                                    <?php if (!empty($session['session_description'])): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars(substr($session['session_description'], 0, 100)); ?><?php echo strlen($session['session_description']) > 100 ? '...' : ''; ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div><?php echo date('M j, Y', strtotime($session['start_datetime'])); ?></div>
                                    <small class="text-muted"><?php echo date('g:i A', strtotime($session['start_datetime'])); ?> - <?php echo date('g:i A', strtotime($session['end_datetime'])); ?></small>
                                </td>
                                <td>
                                    <?php
                                    $duration = (strtotime($session['end_datetime']) - strtotime($session['start_datetime'])) / 60;
                                    echo $duration . ' min';
                                    ?>
                                </td>
                                <td><?php echo htmlspecialchars($session['location'] ?: 'Not specified'); ?></td>
                                <td><?php echo htmlspecialchars($session['instructor_name'] ?: 'Not specified'); ?></td>
                                <td>
                                    <?php if ($session['max_attendees']): ?>
                                        <?php echo $session['registered_count']; ?> / <?php echo $session['max_attendees']; ?>
                                        <?php if ($session['registered_count'] >= $session['max_attendees']): ?>
                                            <span class="badge bg-warning">Full</span>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <?php echo $session['registered_count']; ?> / ∞
                                    <?php endif; ?>
                                </td>
                                <td><?php echo $session['registered_count']; ?></td>
                                <td>
                                    <?php
                                    $status_class = '';
                                    switch ($session['status']) {
                                        case 'active':
                                            $status_class = 'bg-success';
                                            break;
                                        case 'cancelled':
                                            $status_class = 'bg-danger';
                                            break;
                                        case 'completed':
                                            $status_class = 'bg-secondary';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?php echo $status_class; ?>"><?php echo ucfirst($session['status']); ?></span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" onclick="editSession(<?php echo $session['id']; ?>)" title="Edit Session">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <a href="session_attendance.php?session_id=<?php echo $session['id']; ?>" class="btn btn-outline-info" title="Manage Attendance">
                                            <i class="bi bi-people"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" onclick="deleteSession(<?php echo $session['id']; ?>, '<?php echo htmlspecialchars($session['session_title']); ?>')" title="Delete Session">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php else: ?>
<div class="alert alert-warning">
    <i class="bi bi-exclamation-triangle"></i> Event not found or could not be loaded.
</div>
<?php endif; ?>

<!-- Add Session Modal -->
<div class="modal fade" id="addSessionModal" tabindex="-1" aria-labelledby="addSessionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-form">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addSessionModalLabel">
                    <i class="bi bi-plus-circle"></i> Add New Session
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addSessionForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_session">
                    <input type="hidden" name="event_id" value="<?php echo htmlspecialchars($event_id); ?>">

                    <div class="mb-3">
                        <label for="session_title" class="form-label">Session Title *</label>
                        <input type="text" class="form-control" id="session_title" name="session_title" required>
                    </div>

                    <div class="mb-3">
                        <label for="session_description" class="form-label">Description</label>
                        <textarea class="form-control" id="session_description" name="session_description" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="session_date" class="form-label">Session Date *</label>
                        <input type="date" class="form-control" id="session_date" name="session_date" required>
                    </div>

                    <!-- Time Range Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-clock"></i> Session Time Range</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="session_time" class="form-label">Start Time *</label>
                                        <input type="time" class="form-control" id="session_time" name="session_time" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="session_end_time" class="form-label">End Time *</label>
                                        <input type="time" class="form-control" id="session_end_time" name="session_end_time" required>
                                        <div class="form-text">Or use duration below</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="duration_minutes" class="form-label">Duration (minutes)</label>
                                        <input type="number" class="form-control" id="duration_minutes" name="duration_minutes" min="15" step="15" value="60">
                                        <div class="form-text">Auto-calculated from times</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Time Range Preview -->
                            <div class="alert alert-info mb-0" id="time_range_preview" style="display: none;">
                                <i class="bi bi-info-circle"></i> <strong>Session Time:</strong> <span id="time_preview_text"></span>
                            </div>

                            <!-- Time Conflict Warning -->
                            <div class="alert alert-warning mb-0" id="time_conflict_warning" style="display: none;">
                                <i class="bi bi-exclamation-triangle"></i> <strong>Warning:</strong> <span id="conflict_warning_text"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="max_attendees" class="form-label">Max Attendees</label>
                                <input type="number" class="form-control" id="max_attendees" name="max_attendees" min="1">
                                <div class="form-text">Leave empty for unlimited</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location" class="form-label">Location</label>
                                <input type="text" class="form-control" id="location" name="location">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="instructor" class="form-label">Instructor/Speaker</label>
                        <input type="text" class="form-control" id="instructor" name="instructor">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Add Session
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Session Modal -->
<div class="modal fade" id="editSessionModal" tabindex="-1" aria-labelledby="editSessionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-form">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editSessionModalLabel">
                    <i class="bi bi-pencil"></i> Edit Session
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editSessionForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit_session">
                    <input type="hidden" name="event_id" value="<?php echo htmlspecialchars($event_id); ?>">
                    <input type="hidden" name="session_id" id="edit_session_id">

                    <div class="mb-3">
                        <label for="edit_session_title" class="form-label">Session Title *</label>
                        <input type="text" class="form-control" id="edit_session_title" name="session_title" required>
                    </div>

                    <div class="mb-3">
                        <label for="edit_session_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_session_description" name="session_description" rows="3"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="edit_session_date" class="form-label">Session Date *</label>
                        <input type="date" class="form-control" id="edit_session_date" name="session_date" required>
                    </div>

                    <!-- Time Range Section -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-clock"></i> Session Time Range</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="edit_session_time" class="form-label">Start Time *</label>
                                        <input type="time" class="form-control" id="edit_session_time" name="session_time" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="edit_session_end_time" class="form-label">End Time *</label>
                                        <input type="time" class="form-control" id="edit_session_end_time" name="session_end_time" required>
                                        <div class="form-text">Or use duration below</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="edit_duration_minutes" class="form-label">Duration (minutes)</label>
                                        <input type="number" class="form-control" id="edit_duration_minutes" name="duration_minutes" min="15" step="15" value="60">
                                        <div class="form-text">Auto-calculated from times</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Time Range Preview -->
                            <div class="alert alert-info mb-0" id="edit_time_range_preview" style="display: none;">
                                <i class="bi bi-info-circle"></i> <strong>Session Time:</strong> <span id="edit_time_preview_text"></span>
                            </div>

                            <!-- Time Conflict Warning -->
                            <div class="alert alert-warning mb-0" id="edit_time_conflict_warning" style="display: none;">
                                <i class="bi bi-exclamation-triangle"></i> <strong>Warning:</strong> <span id="edit_conflict_warning_text"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_max_attendees" class="form-label">Max Attendees</label>
                                <input type="number" class="form-control" id="edit_max_attendees" name="max_attendees" min="1">
                                <div class="form-text">Leave empty for unlimited</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_location" class="form-label">Location</label>
                                <input type="text" class="form-control" id="edit_location" name="location">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_instructor" class="form-label">Instructor/Speaker</label>
                        <input type="text" class="form-control" id="edit_instructor" name="instructor">
                    </div>

                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Status</label>
                        <select class="form-select" id="edit_status" name="status" required>
                            <option value="active">Active</option>
                            <option value="cancelled">Cancelled</option>
                            <option value="completed">Completed</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> Update Session
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Session Modal -->
<div class="modal fade" id="deleteSessionModal" tabindex="-1" aria-labelledby="deleteSessionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteSessionModalLabel">
                    <i class="bi bi-exclamation-triangle text-danger"></i> Delete Session
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="deleteSessionForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="delete_session">
                    <input type="hidden" name="event_id" value="<?php echo htmlspecialchars($event_id); ?>">
                    <input type="hidden" name="session_id" id="delete_session_id">

                    <p>Are you sure you want to delete the session "<strong id="delete_session_title"></strong>"?</p>
                    <p class="text-danger"><i class="bi bi-exclamation-triangle"></i> This action cannot be undone and will also remove all attendance records for this session.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash"></i> Delete Session
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Session data for JavaScript
const sessions = <?php echo json_encode($sessions); ?>;

function editSession(sessionId) {
    const session = sessions.find(s => s.id == sessionId);
    if (!session) return;

    // Populate the edit form
    document.getElementById('edit_session_id').value = session.id;
    document.getElementById('edit_session_title').value = session.session_title;
    document.getElementById('edit_session_description').value = session.session_description || '';

    // Parse datetime
    const startDate = new Date(session.start_datetime);
    const endDate = new Date(session.end_datetime);

    document.getElementById('edit_session_date').value = startDate.toISOString().split('T')[0];
    document.getElementById('edit_session_time').value = startDate.toTimeString().slice(0, 5);
    document.getElementById('edit_session_end_time').value = endDate.toTimeString().slice(0, 5);

    // Calculate duration in minutes
    const duration = (endDate - startDate) / (1000 * 60);
    document.getElementById('edit_duration_minutes').value = duration;

    document.getElementById('edit_max_attendees').value = session.max_attendees || '';
    document.getElementById('edit_location').value = session.location || '';
    document.getElementById('edit_instructor').value = session.instructor_name || '';
    document.getElementById('edit_status').value = session.status;

    // Update time preview for edit modal
    updateTimePreview('edit');

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('editSessionModal'));
    modal.show();
}

function deleteSession(sessionId, sessionTitle) {
    document.getElementById('delete_session_id').value = sessionId;
    document.getElementById('delete_session_title').textContent = sessionTitle;

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('deleteSessionModal'));
    modal.show();
}

// Time calculation and validation functions
function updateTimePreview(prefix = '') {
    const startTimeId = prefix ? `${prefix}_session_time` : 'session_time';
    const endTimeId = prefix ? `${prefix}_session_end_time` : 'session_end_time';
    const durationId = prefix ? `${prefix}_duration_minutes` : 'duration_minutes';
    const previewId = prefix ? `${prefix}_time_range_preview` : 'time_range_preview';
    const previewTextId = prefix ? `${prefix}_time_preview_text` : 'time_preview_text';
    const conflictId = prefix ? `${prefix}_time_conflict_warning` : 'time_conflict_warning';
    const conflictTextId = prefix ? `${prefix}_conflict_warning_text` : 'conflict_warning_text';

    const startTime = document.getElementById(startTimeId)?.value;
    const endTime = document.getElementById(endTimeId)?.value;
    const durationInput = document.getElementById(durationId);
    const preview = document.getElementById(previewId);
    const previewText = document.getElementById(previewTextId);
    const conflictWarning = document.getElementById(conflictId);
    const conflictText = document.getElementById(conflictTextId);

    if (!startTime || !endTime) {
        preview?.style.setProperty('display', 'none');
        conflictWarning?.style.setProperty('display', 'none');
        return;
    }

    // Calculate duration
    const start = new Date(`2000-01-01 ${startTime}`);
    const end = new Date(`2000-01-01 ${endTime}`);

    if (end <= start) {
        conflictWarning?.style.setProperty('display', 'block');
        conflictText && (conflictText.textContent = 'End time must be after start time.');
        preview?.style.setProperty('display', 'none');
        return;
    }

    const duration = (end - start) / (1000 * 60); // minutes

    // Update duration field
    if (durationInput) {
        durationInput.value = duration;
    }

    // Format time display
    const startFormatted = formatTime(startTime);
    const endFormatted = formatTime(endTime);
    const durationFormatted = formatDuration(duration);

    // Update preview
    if (previewText) {
        previewText.textContent = `${startFormatted} - ${endFormatted} (${durationFormatted})`;
    }
    preview?.style.setProperty('display', 'block');
    conflictWarning?.style.setProperty('display', 'none');

    // Check for conflicts with existing sessions
    checkTimeConflicts(prefix);
}

function updateEndTimeFromDuration(prefix = '') {
    const startTimeId = prefix ? `${prefix}_session_time` : 'session_time';
    const endTimeId = prefix ? `${prefix}_session_end_time` : 'session_end_time';
    const durationId = prefix ? `${prefix}_duration_minutes` : 'duration_minutes';

    const startTime = document.getElementById(startTimeId)?.value;
    const duration = parseInt(document.getElementById(durationId)?.value) || 60;

    if (!startTime) return;

    const start = new Date(`2000-01-01 ${startTime}`);
    const end = new Date(start.getTime() + duration * 60000);

    const endTimeFormatted = end.toTimeString().slice(0, 5);
    const endTimeInput = document.getElementById(endTimeId);
    if (endTimeInput) {
        endTimeInput.value = endTimeFormatted;
    }

    updateTimePreview(prefix);
}

function formatTime(time24) {
    const [hours, minutes] = time24.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
}

function formatDuration(minutes) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
        return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
    }
    return `${mins}m`;
}

function checkTimeConflicts(prefix = '') {
    const sessionDateId = prefix ? `${prefix}_session_date` : 'session_date';
    const startTimeId = prefix ? `${prefix}_session_time` : 'session_time';
    const endTimeId = prefix ? `${prefix}_session_end_time` : 'session_end_time';
    const conflictId = prefix ? `${prefix}_time_conflict_warning` : 'time_conflict_warning';
    const conflictTextId = prefix ? `${prefix}_conflict_warning_text` : 'conflict_warning_text';

    const sessionDate = document.getElementById(sessionDateId)?.value;
    const startTime = document.getElementById(startTimeId)?.value;
    const endTime = document.getElementById(endTimeId)?.value;
    const conflictWarning = document.getElementById(conflictId);
    const conflictText = document.getElementById(conflictTextId);

    if (!sessionDate || !startTime || !endTime) return;

    // Check against existing sessions (simplified - you might want to make this more sophisticated)
    const currentSessionId = prefix ? document.getElementById(`${prefix}_session_id`)?.value : null;

    // This is a basic implementation - you could enhance it to check against actual session data
    // For now, just hide the conflict warning if times are valid
    if (conflictWarning && conflictText) {
        conflictWarning.style.display = 'none';
    }
}

// Set default date to event date for new sessions and add event listeners
document.addEventListener('DOMContentLoaded', function() {
    const eventDate = '<?php echo date('Y-m-d', strtotime($event['event_date'])); ?>';
    const sessionDateInput = document.getElementById('session_date');
    if (sessionDateInput && !sessionDateInput.value) {
        sessionDateInput.value = eventDate;
    }

    // Add event listeners for time calculations
    ['session_time', 'session_end_time', 'duration_minutes'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            if (id === 'duration_minutes') {
                element.addEventListener('input', () => updateEndTimeFromDuration());
            } else {
                element.addEventListener('input', () => updateTimePreview());
            }
        }
    });

    // Add event listeners for edit modal
    ['edit_session_time', 'edit_session_end_time', 'edit_duration_minutes'].forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            if (id === 'edit_duration_minutes') {
                element.addEventListener('input', () => updateEndTimeFromDuration('edit'));
            } else {
                element.addEventListener('input', () => updateTimePreview('edit'));
            }
        }
    });

    // Set default end time when start time is set
    const startTimeInput = document.getElementById('session_time');
    if (startTimeInput) {
        startTimeInput.addEventListener('change', function() {
            const endTimeInput = document.getElementById('session_end_time');
            if (this.value && !endTimeInput.value) {
                updateEndTimeFromDuration();
            }
        });
    }

    const editStartTimeInput = document.getElementById('edit_session_time');
    if (editStartTimeInput) {
        editStartTimeInput.addEventListener('change', function() {
            const endTimeInput = document.getElementById('edit_session_end_time');
            if (this.value && !endTimeInput.value) {
                updateEndTimeFromDuration('edit');
            }
        });
    }
});
</script>

<?php include 'includes/footer.php'; ?>
