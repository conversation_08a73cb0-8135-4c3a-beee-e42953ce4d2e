<?php
// Final QR Code Email Sender using your SMTP configuration
require_once '../config.php';
require_once '../vendor/autoload.php';

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

echo "<h1>📧 Sending QR Code Email to bointa@<EMAIL></h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .step{background:#f5f5f5;padding:10px;margin:10px 0;border-left:4px solid #007bff;}</style>";

$target_email = 'bointa@<EMAIL>';

try {
    echo "<div class='step'>";
    echo "<h2>Step 1: Load SMTP Settings</h2>";
    
    // Load email settings from database
    $emailSettings = [];
    $stmt = $pdo->query("SELECT * FROM email_settings");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $emailSettings[$row['setting_key']] = $row['setting_value'];
    }
    
    echo "<span class='success'>✅ SMTP Settings loaded</span><br>";
    echo "<span class='info'>📧 Host: {$emailSettings['smtp_host']}</span><br>";
    echo "<span class='info'>📧 From: {$emailSettings['sender_name']} <{$emailSettings['sender_email']}></span><br>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 2: Get QR Code Data</h2>";
    
    // Get member and QR data
    $stmt = $pdo->prepare("
        SELECT m.*, mqr.*, e.title as event_title, e.event_date, e.location as event_location
        FROM members m
        JOIN member_qr_codes mqr ON m.email = mqr.attendee_email
        JOIN events e ON mqr.event_id = e.id
        WHERE m.email = ?
        ORDER BY mqr.created_at DESC
        LIMIT 1
    ");
    $stmt->execute([$target_email]);
    $data = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$data) {
        throw new Exception("Member or QR code not found for {$target_email}");
    }
    
    echo "<span class='success'>✅ Data loaded for: {$data['full_name']}</span><br>";
    echo "<span class='info'>🎪 Event: {$data['event_title']}</span><br>";
    echo "<span class='info'>🎫 QR Token: {$data['qr_token']}</span><br>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 3: Create and Send Email</h2>";
    
    // Generate QR URL
    $base_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
    $qr_url = $base_url . "/member_checkin.php?token=" . $data['qr_token'];
    
    // Create PHPMailer instance
    $mail = new PHPMailer(true);
    
    // Server settings
    $mail->isSMTP();
    $mail->Host       = $emailSettings['smtp_host'];
    $mail->SMTPAuth   = true;
    $mail->Username   = $emailSettings['smtp_username'];
    $mail->Password   = $emailSettings['smtp_password'];
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // SSL
    $mail->Port       = $emailSettings['smtp_port'];
    
    // Recipients
    $mail->setFrom($emailSettings['sender_email'], $emailSettings['sender_name']);
    $mail->addAddress($target_email, $data['full_name']);
    $mail->addReplyTo($emailSettings['reply_to_email'], $emailSettings['sender_name']);
    
    // Content
    $mail->isHTML(true);
    $mail->Subject = 'Your QR Code for ' . $data['event_title'];
    
    // HTML email body
    $mail->Body = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>Event QR Code</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; }
            .header h1 { margin: 0; font-size: 24px; }
            .content { padding: 30px 20px; background: white; }
            .qr-section { text-align: center; margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 10px; }
            .qr-code { border: 3px solid #007bff; padding: 30px; background: white; border-radius: 10px; display: inline-block; margin: 20px 0; font-size: 18px; font-weight: bold; color: #007bff; }
            .instructions { background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3; }
            .instructions h3 { margin-top: 0; color: #1976d2; }
            .event-details { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px; }
            .btn { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
            .highlight { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>🎟️ Your Event QR Code</h1>
                <p>Freedom Assembly Church</p>
            </div>
            
            <div class='content'>
                <h2>Hello " . htmlspecialchars($data['full_name']) . "!</h2>
                <p>Thank you for registering for <strong>" . htmlspecialchars($data['event_title']) . "</strong>. Your personal QR code is ready!</p>
                
                <div class='event-details'>
                    <h3>📅 Event Details</h3>
                    <p><strong>Event:</strong> " . htmlspecialchars($data['event_title']) . "</p>
                    <p><strong>Date:</strong> " . date('F j, Y g:i A', strtotime($data['event_date'])) . "</p>
                    <p><strong>Location:</strong> " . htmlspecialchars($data['event_location']) . "</p>
                </div>
                
                <div class='qr-section'>
                    <h3>🎯 Your Personal QR Code</h3>
                    <div class='qr-code'>
                        📱 SCAN THIS QR CODE 📱<br>
                        <small style='font-size: 12px; color: #666; word-break: break-all;'>" . htmlspecialchars($data['qr_token']) . "</small>
                    </div>
                    <p><strong>QR Code ID:</strong> " . htmlspecialchars($data['qr_token']) . "</p>
                    <a href='{$qr_url}' class='btn' style='color: white;'>🔗 Open Check-in Link</a>
                </div>
                
                <div class='instructions'>
                    <h3>📱 How to Use Your QR Code</h3>
                    <ol>
                        <li><strong>Save this email</strong> or screenshot the QR code section</li>
                        <li><strong>Arrive at the event</strong> and look for check-in stations</li>
                        <li><strong>Show your QR code</strong> to staff at the entrance</li>
                        <li><strong>Get checked in instantly</strong> - no manual process needed!</li>
                    </ol>
                    
                    <p><strong>💡 Pro Tips:</strong></p>
                    <ul>
                        <li>You can show the QR code on your phone or print this email</li>
                        <li>If QR scanning doesn't work, use the check-in link above</li>
                        <li>Each QR code is unique and can only be used once</li>
                        <li>Arrive early to avoid queues at check-in</li>
                    </ul>
                </div>
                
                <div class='highlight'>
                    <p><strong>🚀 New Feature:</strong> This QR code system makes check-in super fast! Just show your code and you're in. No more waiting in long lines!</p>
                </div>
                
                <p>If you have any questions or need assistance, please contact our event team.</p>
                <p>We look forward to seeing you at the event!</p>
                
                <p><strong>Blessings,</strong><br>
                Freedom Assembly Church Event Team</p>
            </div>
            
            <div class='footer'>
                <p>This is an automated message from Freedom Assembly Church</p>
                <p>Event Management System | QR Code Check-in</p>
                <p><strong>Direct Check-in URL:</strong><br>
                <a href='{$qr_url}' style='color: #007bff; word-break: break-all;'>{$qr_url}</a></p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    // Plain text version
    $mail->AltBody = "
Hello " . $data['full_name'] . "!

Thank you for registering for " . $data['event_title'] . ". Your personal QR code is ready!

EVENT DETAILS:
Event: " . $data['event_title'] . "
Date: " . date('F j, Y g:i A', strtotime($data['event_date'])) . "
Location: " . $data['event_location'] . "

YOUR QR CODE: " . $data['qr_token'] . "

HOW TO USE:
1. Save this email or screenshot the QR code
2. Arrive at the event and look for check-in stations
3. Show your QR code to staff at the entrance
4. Get checked in instantly!

DIRECT CHECK-IN LINK:
{$qr_url}

If you have any questions, please contact our event team.

Blessings,
Freedom Assembly Church Event Team
    ";
    
    echo "<span class='info'>📧 Sending email to: {$target_email}</span><br>";
    echo "<span class='info'>📋 Subject: {$mail->Subject}</span><br>";
    
    // Send the email
    $mail->send();
    
    echo "<span class='success'>✅ Email sent successfully!</span><br>";
    
    // Update database
    $stmt = $pdo->prepare("
        UPDATE member_qr_codes 
        SET email_sent = 1, email_sent_at = NOW() 
        WHERE qr_token = ?
    ");
    $stmt->execute([$data['qr_token']]);
    
    echo "<span class='success'>✅ Database updated</span><br>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 4: Test QR Code</h2>";
    echo "<span class='info'>🔗 QR Check-in URL: <a href='{$qr_url}' target='_blank'>{$qr_url}</a></span><br>";
    echo "<span class='info'>📱 Click to test the check-in process</span><br>";
    echo "</div>";
    
    echo "<div class='step' style='background:#e8f5e8;border-color:#28a745;'>";
    echo "<h2>🎉 SUCCESS! QR Code Email Sent</h2>";
    echo "<span class='success'>✅ Email sent to {$target_email} using your SMTP server</span><br>";
    echo "<span class='success'>✅ Used: {$emailSettings['smtp_host']} via {$emailSettings['sender_email']}</span><br>";
    echo "<span class='success'>✅ Professional HTML email with QR code delivered</span><br>";
    echo "<span class='success'>✅ Database records updated</span><br>";
    echo "<br>";
    echo "<strong>📧 The member should now have received:</strong><br>";
    echo "<span class='info'>• Beautiful HTML email with church branding</span><br>";
    echo "<span class='info'>• Personal QR code for instant check-in</span><br>";
    echo "<span class='info'>• Event details and clear instructions</span><br>";
    echo "<span class='info'>• Direct check-in link as backup</span><br>";
    echo "<br>";
    echo "<strong>📱 Next Steps:</strong><br>";
    echo "<span class='info'>1. Check bointa@<EMAIL> inbox (and spam folder)</span><br>";
    echo "<span class='info'>2. Test the QR code check-in process</span><br>";
    echo "<span class='info'>3. The system is ready for production!</span><br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='step' style='background:#ffe8e8;border-color:#dc3545;'>";
    echo "<h2>❌ Email Sending Failed</h2>";
    echo "<span class='error'>Error: " . $e->getMessage() . "</span><br>";
    if (isset($mail)) {
        echo "<span class='error'>PHPMailer Error: " . $mail->ErrorInfo . "</span><br>";
    }
    echo "</div>";
}
?>

<script>
console.log('QR Code email sending completed');
</script>
