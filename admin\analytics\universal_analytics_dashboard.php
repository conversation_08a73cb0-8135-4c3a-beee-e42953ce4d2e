<?php
/**
 * Universal Analytics Dashboard
 * Comprehensive analytics platform that adapts to any organization type
 */

require_once '../config.php';
require_once '../includes/auth_check.php';
require_once '../ai/universal_prediction_engine.php';

// Get organization configuration
$org_manager = new UniversalOrganizationManager($pdo);
$organization_id = $_SESSION['organization_id'] ?? 'default_org';
$org_config = $org_manager->getOrganizationConfig($organization_id);

// If no organization config exists, redirect to setup
if (!$org_config) {
    header('Location: ../setup/universal_organization_setup.php');
    exit();
}

// Get date range from parameters
$start_date = $_GET['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
$end_date = $_GET['end_date'] ?? date('Y-m-d');
$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

// Analytics data queries
class UniversalAnalytics {
    private $pdo;
    private $org_config;
    
    public function __construct($pdo, $org_config) {
        $this->pdo = $pdo;
        $this->org_config = $org_config;
    }
    
    public function getOverviewMetrics($start_date, $end_date, $event_id = null) {
        $event_filter = $event_id ? "AND e.id = ?" : "";
        $params = [$start_date, $end_date];
        if ($event_id) $params[] = $event_id;
        
        $stmt = $this->pdo->prepare("
            SELECT 
                COUNT(DISTINCT e.id) as total_events,
                COUNT(DISTINCT es.id) as total_sessions,
                COUNT(DISTINCT ea.user_id) as total_registrations,
                COUNT(DISTINCT CASE WHEN ea.attendance_status = 'attended' THEN ea.user_id END) as total_attended,
                AVG(CASE WHEN ea.attendance_status = 'attended' THEN 1 ELSE 0 END) * 100 as avg_attendance_rate,
                COUNT(DISTINCT u.id) as unique_attendees
            FROM events e
            LEFT JOIN event_sessions es ON e.id = es.event_id
            LEFT JOIN event_attendance ea ON e.id = ea.event_id
            LEFT JOIN users u ON ea.user_id = u.id
            WHERE e.event_date BETWEEN ? AND ? $event_filter
        ");
        $stmt->execute($params);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function getAttendanceTrends($start_date, $end_date, $event_id = null) {
        $event_filter = $event_id ? "AND e.id = ?" : "";
        $params = [$start_date, $end_date];
        if ($event_id) $params[] = $event_id;
        
        $stmt = $this->pdo->prepare("
            SELECT 
                DATE(e.event_date) as date,
                COUNT(DISTINCT ea.user_id) as registrations,
                COUNT(DISTINCT CASE WHEN ea.attendance_status = 'attended' THEN ea.user_id END) as attended,
                AVG(CASE WHEN ea.attendance_status = 'attended' THEN 1 ELSE 0 END) * 100 as attendance_rate
            FROM events e
            LEFT JOIN event_attendance ea ON e.id = ea.event_id
            WHERE e.event_date BETWEEN ? AND ? $event_filter
            GROUP BY DATE(e.event_date)
            ORDER BY DATE(e.event_date)
        ");
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getPopularSessions($start_date, $end_date, $event_id = null) {
        $event_filter = $event_id ? "AND e.id = ?" : "";
        $params = [$start_date, $end_date];
        if ($event_id) $params[] = $event_id;
        
        $stmt = $this->pdo->prepare("
            SELECT 
                es.session_title,
                es.session_type,
                e.title as event_title,
                COUNT(DISTINCT sa.user_id) as attendance_count,
                es.max_attendees,
                (COUNT(DISTINCT sa.user_id) / es.max_attendees) * 100 as utilization_rate
            FROM event_sessions es
            JOIN events e ON es.event_id = e.id
            LEFT JOIN session_attendance sa ON es.id = sa.session_id AND sa.attendance_status = 'attended'
            WHERE e.event_date BETWEEN ? AND ? $event_filter
            GROUP BY es.id
            ORDER BY attendance_count DESC
            LIMIT 10
        ");
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getEngagementMetrics($start_date, $end_date, $event_id = null) {
        $event_filter = $event_id ? "AND e.id = ?" : "";
        $params = [$start_date, $end_date];
        if ($event_id) $params[] = $event_id;
        
        $stmt = $this->pdo->prepare("
            SELECT 
                u.id,
                u.name,
                u.email,
                COUNT(DISTINCT ea.event_id) as events_attended,
                COUNT(DISTINCT sa.session_id) as sessions_attended,
                AVG(CASE WHEN ea.attendance_status = 'attended' THEN 1 ELSE 0 END) * 100 as attendance_rate,
                MAX(e.event_date) as last_attendance
            FROM users u
            LEFT JOIN event_attendance ea ON u.id = ea.user_id
            LEFT JOIN session_attendance sa ON u.id = sa.user_id AND sa.attendance_status = 'attended'
            LEFT JOIN events e ON ea.event_id = e.id
            WHERE e.event_date BETWEEN ? AND ? $event_filter
            GROUP BY u.id
            HAVING events_attended > 0
            ORDER BY events_attended DESC, sessions_attended DESC
            LIMIT 20
        ");
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getCapacityAnalysis($start_date, $end_date, $event_id = null) {
        $event_filter = $event_id ? "AND e.id = ?" : "";
        $params = [$start_date, $end_date];
        if ($event_id) $params[] = $event_id;
        
        $stmt = $this->pdo->prepare("
            SELECT 
                e.title as event_title,
                e.max_attendees as event_capacity,
                COUNT(DISTINCT ea.user_id) as registrations,
                COUNT(DISTINCT CASE WHEN ea.attendance_status = 'attended' THEN ea.user_id END) as actual_attendance,
                (COUNT(DISTINCT ea.user_id) / e.max_attendees) * 100 as registration_rate,
                (COUNT(DISTINCT CASE WHEN ea.attendance_status = 'attended' THEN ea.user_id END) / e.max_attendees) * 100 as utilization_rate
            FROM events e
            LEFT JOIN event_attendance ea ON e.id = ea.event_id
            WHERE e.event_date BETWEEN ? AND ? $event_filter
            GROUP BY e.id
            ORDER BY utilization_rate DESC
        ");
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getTimeAnalysis($start_date, $end_date, $event_id = null) {
        $event_filter = $event_id ? "AND e.id = ?" : "";
        $params = [$start_date, $end_date];
        if ($event_id) $params[] = $event_id;
        
        $stmt = $this->pdo->prepare("
            SELECT 
                HOUR(es.start_datetime) as hour,
                DAYOFWEEK(e.event_date) as day_of_week,
                COUNT(DISTINCT sa.user_id) as attendance_count,
                AVG(COUNT(DISTINCT sa.user_id)) OVER (PARTITION BY HOUR(es.start_datetime)) as avg_hourly_attendance
            FROM events e
            JOIN event_sessions es ON e.id = es.event_id
            LEFT JOIN session_attendance sa ON es.id = sa.session_id AND sa.attendance_status = 'attended'
            WHERE e.event_date BETWEEN ? AND ? $event_filter
            GROUP BY HOUR(es.start_datetime), DAYOFWEEK(e.event_date), es.id
            ORDER BY hour, day_of_week
        ");
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}

$analytics = new UniversalAnalytics($pdo, $org_config);

// Get analytics data
$overview = $analytics->getOverviewMetrics($start_date, $end_date, $event_id);
$trends = $analytics->getAttendanceTrends($start_date, $end_date, $event_id);
$popular_sessions = $analytics->getPopularSessions($start_date, $end_date, $event_id);
$engagement = $analytics->getEngagementMetrics($start_date, $end_date, $event_id);
$capacity = $analytics->getCapacityAnalysis($start_date, $end_date, $event_id);
$time_analysis = $analytics->getTimeAnalysis($start_date, $end_date, $event_id);

// Get events for filter dropdown
$stmt = $pdo->query("SELECT id, title FROM events ORDER BY event_date DESC LIMIT 20");
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'Universal Analytics Dashboard';
include '../includes/header.php';
?>

<style>
:root {
    --primary-color: <?php echo $org_config['branding']['primary_color']; ?>;
    --secondary-color: <?php echo $org_config['branding']['secondary_color']; ?>;
    --accent-color: <?php echo $org_config['branding']['accent_color']; ?>;
}

.analytics-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.analytics-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    border-left: 4px solid var(--primary-color);
}

.metric-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    border-left: 4px solid var(--accent-color);
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.metric-label {
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

.table-responsive {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.progress-bar-custom {
    height: 8px;
    border-radius: 4px;
    background: var(--secondary-color);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transition: width 0.3s ease;
}

.filter-card {
    background: var(--secondary-color);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.org-badge {
    background: var(--accent-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="analytics-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="bi bi-graph-up"></i> Universal Analytics Dashboard</h1>
                        <p class="mb-0">
                            <strong><?php echo htmlspecialchars($org_config['name']); ?></strong>
                            <span class="org-badge ms-2"><?php echo ucfirst($org_config['type']); ?></span>
                        </p>
                        <small class="opacity-75">
                            Comprehensive analytics for <?php echo $org_config['terminology']['events']; ?> 
                            and <?php echo $org_config['terminology']['attendee']; ?> engagement
                        </small>
                    </div>
                    <div>
                        <button class="btn btn-light me-2" onclick="exportAnalytics()">
                            <i class="bi bi-download"></i> Export Report
                        </button>
                        <a href="../ai/universal_ai_dashboard.php" class="btn btn-outline-light">
                            <i class="bi bi-robot"></i> AI Predictions
                        </a>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="filter-card">
                <form method="GET" class="row align-items-end">
                    <div class="col-md-3">
                        <label class="form-label">Start Date</label>
                        <input type="date" class="form-control" name="start_date" value="<?php echo $start_date; ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">End Date</label>
                        <input type="date" class="form-control" name="end_date" value="<?php echo $end_date; ?>">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label"><?php echo ucfirst($org_config['terminology']['event']); ?> (Optional)</label>
                        <select class="form-select" name="event_id">
                            <option value="">All <?php echo ucfirst($org_config['terminology']['events']); ?></option>
                            <?php foreach ($events as $event): ?>
                                <option value="<?php echo $event['id']; ?>" <?php echo $event['id'] == $event_id ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($event['title']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-funnel"></i> Apply Filters
                        </button>
                    </div>
                </form>
            </div>

            <!-- Overview Metrics -->
            <div class="row">
                <div class="col-md-2">
                    <div class="metric-card">
                        <div class="metric-value"><?php echo number_format($overview['total_events'] ?? 0); ?></div>
                        <div class="metric-label"><?php echo ucfirst($org_config['terminology']['events']); ?></div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="metric-card">
                        <div class="metric-value"><?php echo number_format($overview['total_sessions'] ?? 0); ?></div>
                        <div class="metric-label"><?php echo ucfirst($org_config['terminology']['sessions']); ?></div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="metric-card">
                        <div class="metric-value"><?php echo number_format($overview['total_registrations'] ?? 0); ?></div>
                        <div class="metric-label">Registrations</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="metric-card">
                        <div class="metric-value"><?php echo number_format($overview['total_attended'] ?? 0); ?></div>
                        <div class="metric-label">Attended</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="metric-card">
                        <div class="metric-value"><?php echo number_format($overview['avg_attendance_rate'] ?? 0, 1); ?>%</div>
                        <div class="metric-label">Attendance Rate</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="metric-card">
                        <div class="metric-value"><?php echo number_format($overview['unique_attendees'] ?? 0); ?></div>
                        <div class="metric-label">Unique <?php echo ucfirst($org_config['terminology']['attendees']); ?></div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row">
                <div class="col-md-8">
                    <div class="analytics-card">
                        <h5><i class="bi bi-graph-up"></i> Attendance Trends</h5>
                        <div class="chart-container">
                            <canvas id="trendsChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="analytics-card">
                        <h5><i class="bi bi-pie-chart"></i> Capacity Utilization</h5>
                        <div class="chart-container">
                            <canvas id="capacityChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Popular Sessions -->
            <div class="analytics-card">
                <h5><i class="bi bi-star"></i> Most Popular <?php echo ucfirst($org_config['terminology']['sessions']); ?></h5>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th><?php echo ucfirst($org_config['terminology']['session']); ?> Title</th>
                                <th>Type</th>
                                <th><?php echo ucfirst($org_config['terminology']['event']); ?></th>
                                <th>Attendance</th>
                                <th>Capacity</th>
                                <th>Utilization</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($popular_sessions as $session): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($session['session_title']); ?></strong></td>
                                    <td><span class="badge bg-secondary"><?php echo htmlspecialchars($session['session_type']); ?></span></td>
                                    <td><?php echo htmlspecialchars($session['event_title']); ?></td>
                                    <td><?php echo number_format($session['attendance_count']); ?></td>
                                    <td><?php echo number_format($session['max_attendees']); ?></td>
                                    <td>
                                        <div class="progress-bar-custom">
                                            <div class="progress-fill" style="width: <?php echo min($session['utilization_rate'], 100); ?>%"></div>
                                        </div>
                                        <small><?php echo number_format($session['utilization_rate'], 1); ?>%</small>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Engagement Analysis -->
            <div class="row">
                <div class="col-md-6">
                    <div class="analytics-card">
                        <h5><i class="bi bi-people"></i> Top <?php echo ucfirst($org_config['terminology']['attendees']); ?></h5>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th><?php echo ucfirst($org_config['terminology']['events']); ?></th>
                                        <th><?php echo ucfirst($org_config['terminology']['sessions']); ?></th>
                                        <th>Rate</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($engagement, 0, 10) as $attendee): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($attendee['name']); ?></td>
                                            <td><?php echo $attendee['events_attended']; ?></td>
                                            <td><?php echo $attendee['sessions_attended']; ?></td>
                                            <td><?php echo number_format($attendee['attendance_rate'], 1); ?>%</td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="analytics-card">
                        <h5><i class="bi bi-clock"></i> Peak Times Analysis</h5>
                        <div class="chart-container">
                            <canvas id="timeChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Chart data from PHP
const trendsData = <?php echo json_encode($trends); ?>;
const capacityData = <?php echo json_encode($capacity); ?>;
const timeData = <?php echo json_encode($time_analysis); ?>;
const orgConfig = <?php echo json_encode($org_config); ?>;

// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeTrendsChart();
    initializeCapacityChart();
    initializeTimeChart();
});

function initializeTrendsChart() {
    const ctx = document.getElementById('trendsChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: trendsData.map(d => new Date(d.date).toLocaleDateString()),
            datasets: [{
                label: 'Registrations',
                data: trendsData.map(d => d.registrations),
                borderColor: orgConfig.branding.primary_color,
                backgroundColor: orgConfig.branding.primary_color + '20',
                tension: 0.4
            }, {
                label: 'Attended',
                data: trendsData.map(d => d.attended),
                borderColor: orgConfig.branding.accent_color,
                backgroundColor: orgConfig.branding.accent_color + '20',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function initializeCapacityChart() {
    const ctx = document.getElementById('capacityChart').getContext('2d');
    
    const utilizationRates = capacityData.map(d => d.utilization_rate);
    const avgUtilization = utilizationRates.reduce((a, b) => a + b, 0) / utilizationRates.length;
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Utilized', 'Available'],
            datasets: [{
                data: [avgUtilization, 100 - avgUtilization],
                backgroundColor: [
                    orgConfig.branding.accent_color,
                    '#e9ecef'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function initializeTimeChart() {
    const ctx = document.getElementById('timeChart').getContext('2d');
    
    // Process time data for hourly attendance
    const hourlyData = {};
    timeData.forEach(d => {
        if (!hourlyData[d.hour]) {
            hourlyData[d.hour] = 0;
        }
        hourlyData[d.hour] += parseInt(d.attendance_count);
    });
    
    const hours = Object.keys(hourlyData).sort((a, b) => a - b);
    const attendanceCounts = hours.map(h => hourlyData[h]);
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: hours.map(h => `${h}:00`),
            datasets: [{
                label: 'Attendance by Hour',
                data: attendanceCounts,
                backgroundColor: orgConfig.branding.primary_color + '80',
                borderColor: orgConfig.branding.primary_color,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function exportAnalytics() {
    // Create export functionality
    const exportData = {
        overview: <?php echo json_encode($overview); ?>,
        trends: trendsData,
        popular_sessions: <?php echo json_encode($popular_sessions); ?>,
        engagement: <?php echo json_encode($engagement); ?>,
        capacity: capacityData,
        organization: orgConfig,
        date_range: {
            start: '<?php echo $start_date; ?>',
            end: '<?php echo $end_date; ?>'
        }
    };
    
    // Convert to CSV or trigger download
    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(exportData, null, 2));
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", `analytics_report_${new Date().toISOString().split('T')[0]}.json`);
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
    
    // Show success message
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">Analytics report exported successfully!</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}
</script>

<?php include '../includes/footer.php'; ?>
