<?php
require_once '../config.php';

echo "Checking events table structure:\n";
echo "================================\n";

$stmt = $pdo->query('DESCRIBE events');
while($row = $stmt->fetch()) {
    echo $row['Field'] . ' - ' . $row['Type'] . "\n";
}

echo "\nChecking event_sessions table structure:\n";
echo "========================================\n";

$stmt = $pdo->query('DESCRIBE event_sessions');
while($row = $stmt->fetch()) {
    echo $row['Field'] . ' - ' . $row['Type'] . "\n";
}

echo "\nChecking members table structure:\n";
echo "=================================\n";

$stmt = $pdo->query('DESCRIBE members');
while($row = $stmt->fetch()) {
    echo $row['Field'] . ' - ' . $row['Type'] . "\n";
}

echo "\nChecking event_rsvps table structure:\n";
echo "=====================================\n";

$stmt = $pdo->query('DESCRIBE event_rsvps');
while($row = $stmt->fetch()) {
    echo $row['Field'] . ' - ' . $row['Type'] . "\n";
}
?>
