/**
 * Modal Manager for Admin Panel
 * Handles proper modal stacking, z-index management, and backdrop cleanup
 * Prevents modal overlap issues with sidebar and other admin UI elements
 */

class ModalManager {
    constructor() {
        this.activeModals = [];
        this.baseZIndex = 1050;
        this.backdropBaseZIndex = 1040;
        this.zIndexIncrement = 10;
        this.init();
    }

    init() {
        // Listen for modal events
        document.addEventListener('show.bs.modal', (e) => this.handleModalShow(e));
        document.addEventListener('shown.bs.modal', (e) => this.handleModalShown(e));
        document.addEventListener('hide.bs.modal', (e) => this.handleModalHide(e));
        document.addEventListener('hidden.bs.modal', (e) => this.handleModalHidden(e));
        
        // Clean up any existing orphaned backdrops on page load
        document.addEventListener('DOMContentLoaded', () => this.cleanupOrphanedBackdrops());
        
        // Handle page unload cleanup
        window.addEventListener('beforeunload', () => this.cleanupAllModals());

        // Handle window resize to adjust modal positions
        window.addEventListener('resize', () => this.handleWindowResize());
    }

    handleModalShow(event) {
        const modal = event.target;
        const modalId = modal.id || `modal-${Date.now()}`;

        // Clean up any orphaned backdrops before showing new modal
        this.cleanupOrphanedBackdrops();

        // Set very high z-index to ensure modal is above everything
        const modalZIndex = 9999;

        // Set modal z-index
        modal.style.zIndex = modalZIndex;

        // Adjust modal positioning based on sidebar state
        this.adjustModalPosition(modal);

        // Add modal to active list
        this.activeModals.push({
            id: modalId,
            element: modal,
            zIndex: modalZIndex,
            backdropZIndex: 0 // No backdrop
        });

        // Add stacking class for CSS targeting
        const modalIndex = this.activeModals.length;
        modal.classList.add(`modal-stack-${modalIndex}`);

        console.log(`Modal ${modalId} showing with z-index: ${modalZIndex}`);
    }

    adjustModalPosition(modal) {
        const sidebar = document.querySelector('.sidebar');
        const isMobile = window.innerWidth <= 768;

        if (isMobile) {
            // Mobile: full width
            modal.style.left = '0';
            modal.style.width = '100%';
            modal.style.paddingRight = '0';
        } else {
            // Desktop: adjust for sidebar + right margin
            let sidebarWidth = 280; // default
            let rightMargin = 30; // default desktop margin

            if (sidebar) {
                if (sidebar.classList.contains('collapsed')) {
                    sidebarWidth = 50;
                    rightMargin = 30;
                } else if (window.innerWidth <= 1366) {
                    sidebarWidth = 250; // tablet size
                    rightMargin = 20; // smaller margin for tablet
                } else {
                    sidebarWidth = 280; // desktop size
                    rightMargin = 30; // desktop margin
                }
            }

            modal.style.left = `${sidebarWidth}px`;
            modal.style.width = `calc(100% - ${sidebarWidth}px - ${rightMargin}px)`;
            modal.style.paddingRight = `${rightMargin}px`;
        }
    }

    handleModalShown(event) {
        const modal = event.target;
        
        // Find and update backdrop z-index
        setTimeout(() => {
            const modalData = this.activeModals.find(m => m.element === modal);
            if (modalData) {
                const backdrop = document.querySelector('.modal-backdrop:last-of-type');
                if (backdrop) {
                    backdrop.style.zIndex = modalData.backdropZIndex;
                    backdrop.setAttribute('data-modal-id', modalData.id);
                }
            }
        }, 50);
    }

    handleModalHide(event) {
        const modal = event.target;
        console.log(`Modal ${modal.id || 'unnamed'} hiding`);
    }

    handleModalHidden(event) {
        const modal = event.target;
        const modalId = modal.id || modal.getAttribute('data-modal-id');
        
        // Remove from active modals list
        this.activeModals = this.activeModals.filter(m => m.element !== modal);
        
        // Remove stacking classes
        modal.classList.remove(...Array.from(modal.classList).filter(cls => cls.startsWith('modal-stack-')));
        
        // Clean up associated backdrop
        this.cleanupModalBackdrop(modalId);
        
        // Recalculate z-indexes for remaining modals
        this.recalculateZIndexes();
        
        console.log(`Modal ${modalId || 'unnamed'} hidden and cleaned up`);
    }

    cleanupModalBackdrop(modalId) {
        // Remove backdrop associated with this modal
        const backdrop = document.querySelector(`[data-modal-id="${modalId}"]`);
        if (backdrop) {
            backdrop.remove();
        }
        
        // Also remove any orphaned backdrops
        this.cleanupOrphanedBackdrops();
    }

    cleanupOrphanedBackdrops() {
        const backdrops = document.querySelectorAll('.modal-backdrop');
        const activeModalElements = this.activeModals.map(m => m.element);
        
        backdrops.forEach(backdrop => {
            const modalId = backdrop.getAttribute('data-modal-id');
            const hasActiveModal = activeModalElements.some(modal => 
                modal.id === modalId || modal.classList.contains('show')
            );
            
            if (!hasActiveModal) {
                backdrop.remove();
            }
        });
        
        // Remove modal-open class if no modals are active
        if (this.activeModals.length === 0) {
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }
    }

    recalculateZIndexes() {
        this.activeModals.forEach((modalData, index) => {
            const newModalZIndex = this.baseZIndex + (index * this.zIndexIncrement);
            const newBackdropZIndex = this.backdropBaseZIndex + (index * this.zIndexIncrement);
            
            modalData.element.style.zIndex = newModalZIndex;
            modalData.zIndex = newModalZIndex;
            modalData.backdropZIndex = newBackdropZIndex;
            
            // Update backdrop z-index
            const backdrop = document.querySelector(`[data-modal-id="${modalData.id}"]`);
            if (backdrop) {
                backdrop.style.zIndex = newBackdropZIndex;
            }
            
            // Update stacking class
            modalData.element.classList.remove(...Array.from(modalData.element.classList).filter(cls => cls.startsWith('modal-stack-')));
            modalData.element.classList.add(`modal-stack-${index + 1}`);
        });
    }

    cleanupAllModals() {
        // Force close all modals and clean up
        this.activeModals.forEach(modalData => {
            try {
                const modalInstance = bootstrap.Modal.getInstance(modalData.element);
                if (modalInstance) {
                    modalInstance.hide();
                }
            } catch (e) {
                console.warn('Error closing modal:', e);
            }
        });
        
        this.cleanupOrphanedBackdrops();
        this.activeModals = [];
    }

    // Public method to get current modal count
    getActiveModalCount() {
        return this.activeModals.length;
    }

    // Public method to get highest z-index
    getHighestZIndex() {
        if (this.activeModals.length === 0) return this.baseZIndex;
        return Math.max(...this.activeModals.map(m => m.zIndex));
    }

    // Handle window resize
    handleWindowResize() {
        // Adjust all active modals when window is resized
        this.activeModals.forEach(modalData => {
            this.adjustModalPosition(modalData.element);
        });
    }

    // Public method to force cleanup (for debugging)
    forceCleanup() {
        this.cleanupOrphanedBackdrops();
        console.log('Forced modal cleanup completed');
    }
}

// Global modal backdrop cleanup function (for backward compatibility)
function cleanupModalBackdrops() {
    if (window.modalManager) {
        window.modalManager.forceCleanup();
    } else {
        // Fallback cleanup
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    }
}

// Initialize modal manager when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.modalManager = new ModalManager();
    console.log('Modal Manager initialized');
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModalManager;
}
