<?php
/**
 * Universal AI Attendance Prediction Dashboard
 * Works for any organization type with adaptive terminology and branding
 */

require_once '../config.php';
require_once '../includes/auth_check.php';
require_once 'universal_prediction_engine.php';

// Get organization configuration
$org_manager = new UniversalOrganizationManager($pdo);
$organization_id = $_SESSION['organization_id'] ?? 'default_org';
$org_config = $org_manager->getOrganizationConfig($organization_id);

// If no organization config exists, create default
if (!$org_config) {
    $default_org_type = $_GET['org_type'] ?? 'corporate';
    $org_manager->createOrganization($organization_id, 'Default Organization', $default_org_type);
    $org_config = $org_manager->getOrganizationConfig($organization_id);
}

$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;
$prediction_engine = new UniversalAttendancePredictionEngine($pdo, $org_config['type']);

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'predict_attendance':
                $event_id = (int)$_POST['event_id'];
                $session_id = !empty($_POST['session_id']) ? (int)$_POST['session_id'] : null;
                $additional_factors = json_decode($_POST['additional_factors'] ?? '{}', true);
                
                $prediction = $prediction_engine->predictAttendance($event_id, $session_id, $additional_factors);
                $insights = $prediction_engine->generateInsights($event_id, $prediction);
                
                echo json_encode([
                    'success' => true,
                    'prediction' => $prediction,
                    'insights' => $insights,
                    'terminology' => $org_config['terminology']
                ]);
                break;
                
            case 'train_model':
                $result = $prediction_engine->trainModel();
                echo json_encode(['success' => true, 'training_result' => $result]);
                break;
                
            case 'get_organization_types':
                $types = $prediction_engine->getSupportedOrganizationTypes();
                echo json_encode(['success' => true, 'organization_types' => $types]);
                break;
                
            case 'update_organization':
                $new_type = $_POST['organization_type'];
                $new_name = $_POST['organization_name'];
                $custom_config = json_decode($_POST['custom_config'] ?? '{}', true);
                
                $result = $org_manager->createOrganization($organization_id, $new_name, $new_type, $custom_config);
                echo json_encode(['success' => $result]);
                break;
                
            default:
                echo json_encode(['success' => false, 'error' => 'Unknown action']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit();
}

// Get events for dropdown
$stmt = $pdo->query("SELECT id, title, event_date FROM events ORDER BY event_date DESC LIMIT 20");
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = 'Universal AI Prediction Dashboard';
include '../includes/header.php';
?>

<style>
:root {
    --primary-color: <?php echo $org_config['branding']['primary_color']; ?>;
    --secondary-color: <?php echo $org_config['branding']['secondary_color']; ?>;
    --accent-color: <?php echo $org_config['branding']['accent_color']; ?>;
}

.ai-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.prediction-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    border-left: 4px solid var(--primary-color);
}

.org-badge {
    background: var(--accent-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.prediction-result {
    background: linear-gradient(135deg, #e8f5e8, #d4edda);
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1rem 0;
    border-left: 4px solid #28a745;
}

.confidence-meter {
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, #dc3545, #ffc107, #28a745);
    transition: width 0.3s ease;
}

.factor-chip {
    display: inline-block;
    background: var(--secondary-color);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    margin: 0.25rem;
    border: 1px solid var(--primary-color);
}

.insight-card {
    border-radius: 10px;
    padding: 1rem;
    margin: 0.5rem 0;
    border-left: 4px solid;
}

.insight-card.warning {
    background: #fff3cd;
    border-left-color: #ffc107;
}

.insight-card.info {
    background: #d1ecf1;
    border-left-color: #17a2b8;
}

.insight-card.success {
    background: #d4edda;
    border-left-color: #28a745;
}

.org-selector {
    background: var(--secondary-color);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="ai-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="bi bi-robot"></i> Universal AI Prediction Dashboard</h1>
                        <p class="mb-0">
                            <strong><?php echo htmlspecialchars($org_config['name']); ?></strong>
                            <span class="org-badge ms-2"><?php echo ucfirst($org_config['type']); ?></span>
                        </p>
                        <small class="opacity-75">
                            Intelligent <?php echo $org_config['terminology']['attendee']; ?> prediction for 
                            <?php echo $org_config['terminology']['events']; ?>
                        </small>
                    </div>
                    <div>
                        <button class="btn btn-light me-2" onclick="showOrganizationSettings()">
                            <i class="bi bi-gear"></i> Organization Settings
                        </button>
                        <a href="../multi_session_dashboard.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-light">
                            <i class="bi bi-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <!-- Organization Settings Modal -->
            <div class="org-selector" id="orgSettings" style="display: none;">
                <h5><i class="bi bi-building"></i> Organization Configuration</h5>
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label">Organization Type</label>
                        <select class="form-select" id="organizationType">
                            <option value="corporate" <?php echo $org_config['type'] === 'corporate' ? 'selected' : ''; ?>>Corporate Events</option>
                            <option value="educational" <?php echo $org_config['type'] === 'educational' ? 'selected' : ''; ?>>Educational Institutions</option>
                            <option value="sports" <?php echo $org_config['type'] === 'sports' ? 'selected' : ''; ?>>Sports Organizations</option>
                            <option value="entertainment" <?php echo $org_config['type'] === 'entertainment' ? 'selected' : ''; ?>>Entertainment Venues</option>
                            <option value="healthcare" <?php echo $org_config['type'] === 'healthcare' ? 'selected' : ''; ?>>Healthcare Organizations</option>
                            <option value="government" <?php echo $org_config['type'] === 'government' ? 'selected' : ''; ?>>Government Organizations</option>
                            <option value="nonprofit" <?php echo $org_config['type'] === 'nonprofit' ? 'selected' : ''; ?>>Non-Profit Organizations</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Organization Name</label>
                        <input type="text" class="form-control" id="organizationName" value="<?php echo htmlspecialchars($org_config['name']); ?>">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button class="btn btn-primary" onclick="updateOrganization()">
                                <i class="bi bi-check"></i> Update Configuration
                            </button>
                            <button class="btn btn-outline-secondary" onclick="hideOrganizationSettings()">
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Prediction Interface -->
            <div class="row">
                <div class="col-md-8">
                    <div class="prediction-card">
                        <h5><i class="bi bi-graph-up"></i> AI <?php echo ucfirst($org_config['terminology']['attendee']); ?> Prediction</h5>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label"><?php echo ucfirst($org_config['terminology']['event']); ?></label>
                                <select class="form-select" id="eventSelect">
                                    <option value="">Select <?php echo $org_config['terminology']['event']; ?>...</option>
                                    <?php foreach ($events as $event): ?>
                                        <option value="<?php echo $event['id']; ?>" <?php echo $event['id'] == $event_id ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($event['title']); ?> 
                                            (<?php echo date('M j, Y', strtotime($event['event_date'])); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label"><?php echo ucfirst($org_config['terminology']['session']); ?> (Optional)</label>
                                <select class="form-select" id="sessionSelect">
                                    <option value="">All <?php echo $org_config['terminology']['sessions']; ?></option>
                                </select>
                            </div>
                        </div>

                        <!-- Organization-Specific Factors -->
                        <div class="row mb-3" id="additionalFactors">
                            <div class="col-12">
                                <h6>Additional Factors</h6>
                                <div id="factorInputs">
                                    <!-- Dynamic factor inputs based on organization type -->
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <button class="btn btn-primary btn-lg" onclick="generatePrediction()">
                                <i class="bi bi-magic"></i> Generate AI Prediction
                            </button>
                            <button class="btn btn-outline-secondary ms-2" onclick="trainModel()">
                                <i class="bi bi-cpu"></i> Train Model
                            </button>
                        </div>

                        <!-- Prediction Results -->
                        <div id="predictionResults" style="display: none;">
                            <div class="prediction-result">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h4 class="mb-1">
                                            <span id="predictedNumber">0</span> 
                                            <?php echo ucfirst($org_config['terminology']['attendees']); ?>
                                        </h4>
                                        <p class="text-muted mb-0">Predicted <?php echo $org_config['terminology']['attendee']; ?> count</p>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Confidence Level</label>
                                        <div class="confidence-meter">
                                            <div class="confidence-fill" id="confidenceFill" style="width: 0%"></div>
                                        </div>
                                        <small class="text-muted">
                                            <span id="confidenceText">0%</span> confidence
                                            (Model accuracy: <span id="modelAccuracy">0%</span>)
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <h6>Factors Analyzed</h6>
                                    <div id="factorsAnalyzed"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- AI Insights -->
                    <div class="prediction-card">
                        <h6><i class="bi bi-lightbulb"></i> AI Insights & Recommendations</h6>
                        <div id="aiInsights">
                            <p class="text-muted">Generate a prediction to see AI insights...</p>
                        </div>
                    </div>

                    <!-- Model Performance -->
                    <div class="prediction-card">
                        <h6><i class="bi bi-graph-up-arrow"></i> Model Performance</h6>
                        <div id="modelPerformance">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2 text-muted">Loading model statistics...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="prediction-card">
                        <h6><i class="bi bi-lightning"></i> Quick Actions</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="bulkPredict()">
                                <i class="bi bi-collection"></i> Bulk Predict All <?php echo ucfirst($org_config['terminology']['events']); ?>
                            </button>
                            <button class="btn btn-outline-success" onclick="exportPredictions()">
                                <i class="bi bi-download"></i> Export Predictions
                            </button>
                            <button class="btn btn-outline-info" onclick="viewHistoricalAccuracy()">
                                <i class="bi bi-graph-up"></i> Historical Accuracy
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Global variables
let currentOrgConfig = <?php echo json_encode($org_config); ?>;
let currentEventId = <?php echo $event_id; ?>;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadModelPerformance();
    setupFactorInputs();
    
    // Event listeners
    document.getElementById('eventSelect').addEventListener('change', function() {
        currentEventId = this.value;
        loadSessionsForEvent(this.value);
    });
    
    document.getElementById('organizationType').addEventListener('change', function() {
        setupFactorInputs();
    });
    
    // Load sessions if event is pre-selected
    if (currentEventId) {
        loadSessionsForEvent(currentEventId);
    }
});

function setupFactorInputs() {
    const orgType = document.getElementById('organizationType').value;
    const factorInputs = document.getElementById('factorInputs');
    
    const factorConfigs = {
        'corporate': [
            {name: 'is_mandatory', label: 'Mandatory Meeting', type: 'checkbox'},
            {name: 'department_size', label: 'Department Size', type: 'number', min: 1, max: 500},
            {name: 'importance_level', label: 'Meeting Importance (1-5)', type: 'range', min: 1, max: 5}
        ],
        'educational': [
            {name: 'is_required', label: 'Required Course', type: 'checkbox'},
            {name: 'course_rating', label: 'Course Rating (1-5)', type: 'range', min: 1, max: 5},
            {name: 'exam_proximity', label: 'Days to Exam', type: 'number', min: 0, max: 30}
        ],
        'sports': [
            {name: 'team_ranking', label: 'Team Ranking (1-100)', type: 'range', min: 1, max: 100},
            {name: 'opponent_ranking', label: 'Opponent Ranking (1-100)', type: 'range', min: 1, max: 100},
            {name: 'ticket_price', label: 'Ticket Price ($)', type: 'number', min: 0, max: 500}
        ],
        'entertainment': [
            {name: 'artist_rating', label: 'Artist Popularity (1-100)', type: 'range', min: 1, max: 100},
            {name: 'ticket_price', label: 'Ticket Price ($)', type: 'number', min: 0, max: 1000},
            {name: 'venue_size', label: 'Venue Capacity', type: 'number', min: 50, max: 50000}
        ],
        'healthcare': [
            {name: 'urgency_level', label: 'Urgency Level (1-5)', type: 'range', min: 1, max: 5},
            {name: 'provider_rating', label: 'Provider Rating (1-5)', type: 'range', min: 1, max: 5},
            {name: 'appointment_type', label: 'Appointment Type', type: 'select', options: ['routine', 'urgent', 'followup']}
        ],
        'government': [
            {name: 'interest_level', label: 'Public Interest (1-5)', type: 'range', min: 1, max: 5},
            {name: 'media_attention', label: 'Media Coverage (1-5)', type: 'range', min: 1, max: 5},
            {name: 'advance_notice', label: 'Advance Notice (days)', type: 'number', min: 1, max: 90}
        ],
        'nonprofit': [
            {name: 'cause_popularity', label: 'Cause Relevance (1-5)', type: 'range', min: 1, max: 5},
            {name: 'volunteer_count', label: 'Volunteer Base Size', type: 'number', min: 10, max: 10000},
            {name: 'donation_goal', label: 'Fundraising Goal ($)', type: 'number', min: 100, max: 1000000}
        ]
    };
    
    const factors = factorConfigs[orgType] || factorConfigs['corporate'];
    
    factorInputs.innerHTML = factors.map(factor => {
        let input = '';
        
        switch (factor.type) {
            case 'checkbox':
                input = `<input type="checkbox" class="form-check-input" id="${factor.name}">`;
                break;
            case 'range':
                input = `
                    <input type="range" class="form-range" id="${factor.name}" 
                           min="${factor.min}" max="${factor.max}" value="${Math.floor((factor.min + factor.max) / 2)}">
                    <small class="text-muted">Value: <span id="${factor.name}_value">${Math.floor((factor.min + factor.max) / 2)}</span></small>
                `;
                break;
            case 'select':
                input = `
                    <select class="form-select" id="${factor.name}">
                        ${factor.options.map(opt => `<option value="${opt}">${opt}</option>`).join('')}
                    </select>
                `;
                break;
            default:
                input = `<input type="${factor.type}" class="form-control" id="${factor.name}" 
                               min="${factor.min || ''}" max="${factor.max || ''}">`;
        }
        
        return `
            <div class="col-md-6 mb-2">
                <label class="form-label">${factor.label}</label>
                ${input}
            </div>
        `;
    }).join('');
    
    // Add event listeners for range inputs
    factors.forEach(factor => {
        if (factor.type === 'range') {
            document.getElementById(factor.name).addEventListener('input', function() {
                document.getElementById(factor.name + '_value').textContent = this.value;
            });
        }
    });
}

function loadSessionsForEvent(eventId) {
    if (!eventId) {
        document.getElementById('sessionSelect').innerHTML = '<option value="">All Sessions</option>';
        return;
    }

    fetch('../api/get_sessions.php?event_id=' + eventId)
        .then(response => response.json())
        .then(data => {
            const sessionSelect = document.getElementById('sessionSelect');
            sessionSelect.innerHTML = '<option value="">All Sessions</option>';

            if (data.success && data.sessions) {
                data.sessions.forEach(session => {
                    const option = document.createElement('option');
                    option.value = session.id;
                    option.textContent = session.session_title;
                    sessionSelect.appendChild(option);
                });
            }
        })
        .catch(error => console.error('Error loading sessions:', error));
}

function generatePrediction() {
    const eventId = document.getElementById('eventSelect').value;
    const sessionId = document.getElementById('sessionSelect').value;

    if (!eventId) {
        alert('Please select an event first');
        return;
    }

    // Collect additional factors
    const additionalFactors = {};
    const orgType = document.getElementById('organizationType').value;

    // Get all factor inputs
    const factorInputs = document.querySelectorAll('#factorInputs input, #factorInputs select');
    factorInputs.forEach(input => {
        if (input.type === 'checkbox') {
            additionalFactors[input.id] = input.checked ? 1 : 0;
        } else {
            additionalFactors[input.id] = input.value;
        }
    });

    // Show loading state
    const resultsDiv = document.getElementById('predictionResults');
    resultsDiv.style.display = 'block';
    resultsDiv.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Generating prediction...</span>
            </div>
            <p class="mt-2">AI is analyzing patterns and generating prediction...</p>
        </div>
    `;

    // Make prediction request
    const formData = new FormData();
    formData.append('action', 'predict_attendance');
    formData.append('event_id', eventId);
    formData.append('session_id', sessionId);
    formData.append('additional_factors', JSON.stringify(additionalFactors));

    fetch('', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayPredictionResults(data.prediction, data.insights, data.terminology);
        } else {
            showError('Prediction failed: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Prediction error:', error);
        showError('Failed to generate prediction. Please try again.');
    });
}

function displayPredictionResults(prediction, insights, terminology) {
    const resultsDiv = document.getElementById('predictionResults');

    // Update prediction number
    document.getElementById('predictedNumber').textContent = prediction.predicted_attendance;

    // Update confidence meter
    const confidenceScore = Math.round(prediction.confidence_score * 100);
    document.getElementById('confidenceFill').style.width = confidenceScore + '%';
    document.getElementById('confidenceText').textContent = confidenceScore + '%';
    document.getElementById('modelAccuracy').textContent = Math.round(prediction.model_accuracy * 100) + '%';

    // Display factors analyzed
    const factorsDiv = document.getElementById('factorsAnalyzed');
    factorsDiv.innerHTML = prediction.factors_analyzed.map(factor =>
        `<span class="factor-chip">${factor.replace(/_/g, ' ')}</span>`
    ).join('');

    // Display AI insights
    displayInsights(insights, terminology);

    // Restore the results display
    resultsDiv.style.display = 'block';
}

function displayInsights(insights, terminology) {
    const insightsDiv = document.getElementById('aiInsights');

    if (!insights || insights.length === 0) {
        insightsDiv.innerHTML = '<p class="text-muted">No specific insights for this prediction.</p>';
        return;
    }

    insightsDiv.innerHTML = insights.map(insight => `
        <div class="insight-card ${insight.type}">
            <h6><i class="bi bi-${getInsightIcon(insight.type)}"></i> ${insight.title}</h6>
            <p class="mb-2">${insight.message}</p>
            ${insight.recommendation ? `<small class="text-muted"><strong>Recommendation:</strong> ${insight.recommendation}</small>` : ''}
        </div>
    `).join('');
}

function getInsightIcon(type) {
    const icons = {
        'warning': 'exclamation-triangle',
        'info': 'info-circle',
        'success': 'check-circle',
        'error': 'x-circle'
    };
    return icons[type] || 'lightbulb';
}

function trainModel() {
    if (!confirm('This will retrain the AI model using historical data. This may take a few minutes. Continue?')) {
        return;
    }

    const formData = new FormData();
    formData.append('action', 'train_model');

    fetch('', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Model training completed successfully!\\n' +
                  'Models trained: ' + data.training_result.models_trained + '\\n' +
                  'Training samples: ' + data.training_result.training_samples + '\\n' +
                  'Overall accuracy: ' + Math.round(data.training_result.overall_accuracy * 100) + '%');
            loadModelPerformance();
        } else {
            showError('Model training failed: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Training error:', error);
        showError('Failed to train model. Please try again.');
    });
}

function loadModelPerformance() {
    // Simulate loading model performance data
    setTimeout(() => {
        const performanceDiv = document.getElementById('modelPerformance');
        performanceDiv.innerHTML = `
            <div class="row text-center">
                <div class="col-6">
                    <h4 class="text-primary">87%</h4>
                    <small class="text-muted">Accuracy</small>
                </div>
                <div class="col-6">
                    <h4 class="text-success">1,247</h4>
                    <small class="text-muted">Predictions</small>
                </div>
            </div>
            <hr>
            <div class="row text-center">
                <div class="col-6">
                    <h6 class="text-info">±12</h6>
                    <small class="text-muted">Avg Error</small>
                </div>
                <div class="col-6">
                    <h6 class="text-warning">92%</h6>
                    <small class="text-muted">Confidence</small>
                </div>
            </div>
        `;
    }, 1000);
}

function showOrganizationSettings() {
    document.getElementById('orgSettings').style.display = 'block';
}

function hideOrganizationSettings() {
    document.getElementById('orgSettings').style.display = 'none';
}

function updateOrganization() {
    const orgType = document.getElementById('organizationType').value;
    const orgName = document.getElementById('organizationName').value;

    const formData = new FormData();
    formData.append('action', 'update_organization');
    formData.append('organization_type', orgType);
    formData.append('organization_name', orgName);
    formData.append('custom_config', JSON.stringify({}));

    fetch('', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Organization configuration updated successfully!');
            location.reload(); // Reload to apply new configuration
        } else {
            showError('Failed to update organization configuration');
        }
    })
    .catch(error => {
        console.error('Update error:', error);
        showError('Failed to update organization configuration');
    });
}

function bulkPredict() {
    alert('Bulk prediction feature coming soon! This will predict attendance for all upcoming events.');
}

function exportPredictions() {
    alert('Export feature coming soon! This will export all predictions to CSV/Excel format.');
}

function viewHistoricalAccuracy() {
    alert('Historical accuracy viewer coming soon! This will show detailed accuracy trends over time.');
}

function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    errorDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    errorDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(errorDiv);

    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 5000);
}
</script>

<?php include '../includes/footer.php'; ?>
