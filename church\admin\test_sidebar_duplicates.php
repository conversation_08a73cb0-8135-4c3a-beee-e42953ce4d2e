<?php
// Test Sidebar Duplicates Fix
echo "<h2>🔧 Testing Sidebar Duplicate Links Fix</h2>";

try {
    // Include required files
    require_once '../config.php';
    
    echo "<h3>📋 Step 1: Analyzing Sidebar Structure</h3>";
    
    // Read the sidebar file content
    $sidebar_content = file_get_contents('includes/sidebar_rbac.php');
    
    // Extract all menu items
    preg_match_all('/canAccessPage\([\'"]([^\'"]+)[\'"]\).*?<span class="menu-text">([^<]+)<\/span>/s', $sidebar_content, $matches, PREG_SET_ORDER);
    
    echo "<h4>All Menu Items Found:</h4>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>Page</th><th>Menu Text</th><th>Count</th><th>Status</th></tr>";
    
    // Count occurrences of each page
    $page_counts = [];
    $menu_items = [];
    
    foreach ($matches as $match) {
        $page = $match[1];
        $menu_text = trim($match[2]);
        
        if (!isset($page_counts[$page])) {
            $page_counts[$page] = 0;
            $menu_items[$page] = [];
        }
        $page_counts[$page]++;
        $menu_items[$page][] = $menu_text;
    }
    
    // Display results
    foreach ($page_counts as $page => $count) {
        $status = $count > 1 ? '❌ DUPLICATE' : '✅ UNIQUE';
        $row_color = $count > 1 ? 'background-color: #f8d7da;' : 'background-color: #d4edda;';
        $menu_texts = implode(', ', array_unique($menu_items[$page]));
        
        echo "<tr style='$row_color'>";
        echo "<td><code>$page</code></td>";
        echo "<td>$menu_texts</td>";
        echo "<td>$count</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>📋 Step 2: Duplicate Analysis</h3>";
    
    $duplicates_found = array_filter($page_counts, function($count) { return $count > 1; });
    
    if (empty($duplicates_found)) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>✅ No Duplicates Found!</strong></p>";
        echo "<p>All menu items are unique. The sidebar duplicate issue has been resolved.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>❌ Duplicates Still Found:</strong></p>";
        echo "<ul>";
        foreach ($duplicates_found as $page => $count) {
            echo "<li><strong>$page:</strong> appears $count times</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<h3>📋 Step 3: Events Management Section Analysis</h3>";
    
    // Specifically check the Events Management section
    $events_section_pattern = '/<!-- Events Management Section -->.*?<!-- [^>]+ Section -->/s';
    preg_match($events_section_pattern, $sidebar_content, $events_section);
    
    if (!empty($events_section)) {
        $events_content = $events_section[0];
        
        // Count specific event-related pages in this section
        $event_pages = [
            'events.php' => 'Events',
            'event_attendance.php' => 'Event Attendance', 
            'event_categories.php' => 'Event Categories',
            'event_reports.php' => 'Event Reports'
        ];
        
        echo "<h4>Events Management Section Items:</h4>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>Page</th><th>Expected Menu Text</th><th>Count in Section</th><th>Status</th></tr>";
        
        foreach ($event_pages as $page => $expected_text) {
            $count = preg_match_all('/canAccessPage\([\'"]' . preg_quote($page, '/') . '[\'"]\)/', $events_content);
            $status = $count > 1 ? '❌ DUPLICATE' : ($count == 1 ? '✅ CORRECT' : '⚠️ MISSING');
            $row_color = $count > 1 ? 'background-color: #f8d7da;' : ($count == 1 ? 'background-color: #d4edda;' : 'background-color: #fff3cd;');
            
            echo "<tr style='$row_color'>";
            echo "<td><code>$page</code></td>";
            echo "<td>$expected_text</td>";
            echo "<td>$count</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>📋 Step 4: Section Structure Overview</h3>";
    
    // Extract all section headers
    preg_match_all('/<!-- ([^>]+) Section -->/', $sidebar_content, $section_matches);
    
    echo "<h4>Sidebar Sections:</h4>";
    echo "<ol>";
    foreach ($section_matches[1] as $section) {
        echo "<li><strong>$section Section</strong></li>";
    }
    echo "</ol>";
    
    echo "<h3>📋 Step 5: Fix Verification</h3>";
    
    // Check if the specific duplicates mentioned in the issue are fixed
    $specific_checks = [
        'event_categories.php' => 'Event Categories',
        'event_reports.php' => 'Event Reports'
    ];
    
    echo "<h4>Specific Duplicate Checks:</h4>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>Page</th><th>Menu Text</th><th>Total Count</th><th>Fix Status</th></tr>";
    
    foreach ($specific_checks as $page => $menu_text) {
        $count = $page_counts[$page] ?? 0;
        $status = $count <= 1 ? '✅ FIXED' : '❌ STILL DUPLICATE';
        $row_color = $count <= 1 ? 'background-color: #d4edda;' : 'background-color: #f8d7da;';
        
        echo "<tr style='$row_color'>";
        echo "<td><code>$page</code></td>";
        echo "<td>$menu_text</td>";
        echo "<td>$count</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🎯 Summary</h3>";
    
    $total_duplicates = count($duplicates_found);
    
    if ($total_duplicates == 0) {
        echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>✅ Sidebar Duplicates Successfully Fixed!</strong></p>";
        echo "<ul>";
        echo "<li>✅ <strong>No duplicate menu items</strong> found in the sidebar</li>";
        echo "<li>✅ <strong>Event Categories</strong> appears only once</li>";
        echo "<li>✅ <strong>Event Reports</strong> appears only once</li>";
        echo "<li>✅ <strong>Clean sidebar structure</strong> with proper organization</li>";
        echo "<li>✅ <strong>All sections</strong> have unique menu items</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>⚠️ Some duplicates still exist:</strong></p>";
        echo "<ul>";
        foreach ($duplicates_found as $page => $count) {
            echo "<li><strong>$page:</strong> appears $count times (should be 1)</li>";
        }
        echo "</ul>";
        echo "<p>Additional cleanup may be needed.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>❌ Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<p>";
echo "<a href='dashboard.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🏠 Dashboard</a>";
echo "<a href='event_reports.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📊 Event Reports</a>";
echo "<a href='event_categories.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📂 Event Categories</a>";
echo "</p>";
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h2, h3, h4 { color: #333; }
    table { width: 100%; margin: 10px 0; }
    th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
    p { margin: 8px 0; }
    ul, ol { margin: 10px 0; padding-left: 20px; }
    li { margin: 5px 0; }
    code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
</style>
