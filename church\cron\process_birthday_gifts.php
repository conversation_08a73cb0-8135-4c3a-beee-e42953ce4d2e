<?php
/**
 * Birthday Gift Processing Cron Job
 *
 * This script should be run daily via cron to process scheduled birthday gifts
 * and send gift reminders
 *
 * Dynamic Cron Job Command (replace YOUR_DOMAIN.COM/YOUR_PATH):
 * wget -q -O /dev/null "https://YOUR_DOMAIN.COM/YOUR_PATH/cron/process_birthday_gifts.php?cron_key=fac_2024_secure_cron_8x9q2p5m"
 */

// Set script execution time limit to 5 minutes
set_time_limit(300);

// Define your secret key here - DO NOT SHARE THIS KEY
define('CRON_KEY', 'fac_2024_secure_cron_8x9q2p5m');

// Security check to allow for CLI execution and web requests
if (php_sapi_name() !== 'cli') {
    // Only check for cron_key when running via web request
    if (!isset($_GET['cron_key']) || $_GET['cron_key'] !== CRON_KEY) {
        header('HTTP/1.0 403 Forbidden');
        exit('Access Denied');
    }
}

// Set the working directory to the church directory
chdir(dirname(__DIR__));

require_once 'config.php';
require_once 'includes/BirthdayGiftCoordinator.php';

// Log file for cron job output
$log_file = __DIR__ . '/../logs/birthday_gifts_cron.log';

// Ensure logs directory exists
$logs_dir = dirname($log_file);
if (!is_dir($logs_dir)) {
    mkdir($logs_dir, 0755, true);
}

function logMessage($message) {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($log_file, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

try {
    logMessage("Starting birthday gift processing cron job");
    
    // Initialize the coordinator
    $coordinator = new BirthdayGiftCoordinator($pdo);
    
    // Ensure reminder table exists
    $coordinator->createReminderTable();
    
    // Process scheduled gifts
    logMessage("Processing scheduled birthday gifts...");
    $gift_results = $coordinator->processScheduledGifts();
    
    logMessage("Gift processing results:");
    logMessage("- Processed: {$gift_results['processed']} gifts");
    logMessage("- Delivered: {$gift_results['delivered']} gifts");
    logMessage("- Failed: {$gift_results['failed']} gifts");
    
    if (!empty($gift_results['errors'])) {
        logMessage("Errors encountered:");
        foreach ($gift_results['errors'] as $error) {
            logMessage("- $error");
        }
    }
    
    // Create birthday gift reminders (7 days before birthday)
    logMessage("Creating birthday gift reminders...");
    $reminder_results = $coordinator->createBirthdayGiftReminders(7);
    
    if ($reminder_results['success']) {
        logMessage("Created {$reminder_results['reminders_created']} new gift reminders");
    } else {
        logMessage("Error creating reminders: " . $reminder_results['error']);
    }
    
    // Get organization settings for admin notification
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('organization_name', 'admin_email')");
    $stmt->execute();
    $settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
    
    $organization_name = $settings['organization_name'] ?? get_organization_name();
    $admin_email = $settings['admin_email'] ?? null;
    
    // Send daily summary email to admin if configured
    if ($admin_email && ($gift_results['processed'] > 0 || !empty($gift_results['errors']))) {
        require_once 'includes/email_functions.php';
        
        $subject = "Daily Birthday Gift Processing Summary - $organization_name";
        $body = "
        <h3>Birthday Gift Processing Summary</h3>
        <p><strong>Date:</strong> " . date('F j, Y') . "</p>
        
        <h4>Gift Delivery Results:</h4>
        <ul>
            <li>Gifts Processed: {$gift_results['processed']}</li>
            <li>Successfully Delivered: {$gift_results['delivered']}</li>
            <li>Failed Deliveries: {$gift_results['failed']}</li>
        </ul>";
        
        if (!empty($gift_results['errors'])) {
            $body .= "<h4>Errors:</h4><ul>";
            foreach ($gift_results['errors'] as $error) {
                $body .= "<li>" . htmlspecialchars($error) . "</li>";
            }
            $body .= "</ul>";
        }
        
        $body .= "
        <h4>Gift Reminders:</h4>
        <ul>
            <li>New Reminders Created: {$reminder_results['reminders_created']}</li>
        </ul>
        
        <p>This is an automated summary from the birthday gift processing system.</p>";
        
        $email_result = sendEmailWithPHPMailer(
            $admin_email,
            $subject,
            $body,
            $organization_name,
            $settings['sender_email'] ?? '<EMAIL>',
            true
        );
        
        if ($email_result['success']) {
            logMessage("Daily summary email sent to admin");
        } else {
            logMessage("Failed to send daily summary email: " . $email_result['message']);
        }
    }
    
    logMessage("Birthday gift processing cron job completed successfully");
    
    // Output summary for cron log
    echo "Birthday Gift Processing Summary:\n";
    echo "Processed: {$gift_results['processed']} gifts\n";
    echo "Delivered: {$gift_results['delivered']} gifts\n";
    echo "Failed: {$gift_results['failed']} gifts\n";
    echo "Reminders Created: {$reminder_results['reminders_created']}\n";
    
    if (!empty($gift_results['errors'])) {
        echo "Errors: " . count($gift_results['errors']) . "\n";
    }
    
} catch (Exception $e) {
    $error_message = "Fatal error in birthday gift processing cron job: " . $e->getMessage();
    logMessage($error_message);
    error_log($error_message);
    
    // Output error for cron
    echo "ERROR: " . $e->getMessage() . "\n";
    exit(1);
}

?>
