<?php

/**
 * Late Registration Helper Functions
 * Functions to check and manage late registration for events
 */

/**
 * Check if late registration is allowed for an event
 * @param PDO $pdo Database connection
 * @param int $eventId Event ID
 * @return array Result with allowed status and message
 */
function checkLateRegistrationAllowed($pdo, $eventId) {
    try {
        $stmt = $pdo->prepare("
            SELECT 
                allow_late_registration, 
                late_registration_cutoff_hours, 
                event_date,
                status,
                title
            FROM events 
            WHERE id = ?
        ");
        $stmt->execute([$eventId]);
        $event = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$event) {
            return [
                'allowed' => false,
                'message' => 'Event not found',
                'reason' => 'not_found'
            ];
        }
        
        // Check if event is published
        if ($event['status'] !== 'published') {
            return [
                'allowed' => false,
                'message' => 'Event is not available for registration',
                'reason' => 'not_published'
            ];
        }
        
        // If late registration is disabled, return false
        if (!$event['allow_late_registration']) {
            return [
                'allowed' => false,
                'message' => 'Late registration is not allowed for this event',
                'reason' => 'disabled'
            ];
        }
        
        $eventTime = new DateTime($event['event_date']);
        $now = new DateTime();
        
        // If event hasn't started yet, registration is allowed
        if ($now <= $eventTime) {
            return [
                'allowed' => true,
                'message' => 'Registration is open',
                'reason' => 'before_event'
            ];
        }
        
        // Event has started - check cutoff hours
        $cutoffHours = (int)$event['late_registration_cutoff_hours'];
        
        // If no cutoff hours set (0), allow registration even after event starts
        if ($cutoffHours == 0) {
            return [
                'allowed' => true,
                'message' => 'Late registration is allowed',
                'reason' => 'no_cutoff'
            ];
        }
        
        // Check if we're within the cutoff period
        $cutoffTime = clone $eventTime;
        $cutoffTime->sub(new DateInterval('PT' . $cutoffHours . 'H'));
        
        if ($now <= $cutoffTime) {
            return [
                'allowed' => true,
                'message' => 'Registration is open',
                'reason' => 'within_cutoff'
            ];
        } else {
            $hoursAfterCutoff = ($now->getTimestamp() - $cutoffTime->getTimestamp()) / 3600;
            return [
                'allowed' => false,
                'message' => sprintf(
                    'Registration closed %d hours before the event. Cutoff was %s.',
                    $cutoffHours,
                    $cutoffTime->format('M j, Y g:i A')
                ),
                'reason' => 'past_cutoff',
                'cutoff_time' => $cutoffTime->format('Y-m-d H:i:s'),
                'hours_after_cutoff' => round($hoursAfterCutoff, 1)
            ];
        }
        
    } catch (Exception $e) {
        error_log("Error checking late registration: " . $e->getMessage());
        return [
            'allowed' => false,
            'message' => 'Unable to check registration status',
            'reason' => 'error'
        ];
    }
}

/**
 * Get late registration status message for display
 * @param PDO $pdo Database connection
 * @param int $eventId Event ID
 * @return string HTML message to display
 */
function getLateRegistrationMessage($pdo, $eventId) {
    $status = checkLateRegistrationAllowed($pdo, $eventId);
    
    if ($status['allowed']) {
        switch ($status['reason']) {
            case 'before_event':
                return '<div class="alert alert-success"><i class="bi bi-check-circle"></i> ' . $status['message'] . '</div>';
            case 'no_cutoff':
            case 'within_cutoff':
                return '<div class="alert alert-info"><i class="bi bi-info-circle"></i> ' . $status['message'] . '</div>';
        }
    } else {
        switch ($status['reason']) {
            case 'not_found':
            case 'not_published':
                return '<div class="alert alert-danger"><i class="bi bi-exclamation-triangle"></i> ' . $status['message'] . '</div>';
            case 'disabled':
            case 'past_cutoff':
                return '<div class="alert alert-warning"><i class="bi bi-clock"></i> ' . $status['message'] . '</div>';
            default:
                return '<div class="alert alert-danger"><i class="bi bi-exclamation-triangle"></i> ' . $status['message'] . '</div>';
        }
    }
    
    return '';
}

/**
 * Get registration button HTML based on late registration status
 * @param PDO $pdo Database connection
 * @param int $eventId Event ID
 * @param string $buttonText Text for the button
 * @param string $buttonClass CSS classes for the button
 * @return string HTML for registration button or disabled message
 */
function getRegistrationButton($pdo, $eventId, $buttonText = 'Register', $buttonClass = 'btn btn-primary') {
    $status = checkLateRegistrationAllowed($pdo, $eventId);
    
    if ($status['allowed']) {
        return sprintf(
            '<button type="submit" class="%s">%s</button>',
            $buttonClass,
            $buttonText
        );
    } else {
        return sprintf(
            '<button type="button" class="btn btn-secondary" disabled title="%s">Registration Closed</button>',
            htmlspecialchars($status['message'])
        );
    }
}

/**
 * Check if a user can register for an event (includes late registration check)
 * @param PDO $pdo Database connection
 * @param int $eventId Event ID
 * @param int $userId User ID (optional, for additional checks)
 * @return array Result with registration status
 */
function canUserRegister($pdo, $eventId, $userId = null) {
    // First check late registration
    $lateRegStatus = checkLateRegistrationAllowed($pdo, $eventId);
    
    if (!$lateRegStatus['allowed']) {
        return $lateRegStatus;
    }
    
    // If user ID provided, check if already registered
    if ($userId) {
        try {
            $stmt = $pdo->prepare("
                SELECT status FROM event_rsvps 
                WHERE event_id = ? AND member_id = ?
            ");
            $stmt->execute([$eventId, $userId]);
            $existing = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($existing) {
                return [
                    'allowed' => false,
                    'message' => 'You are already registered for this event',
                    'reason' => 'already_registered',
                    'current_status' => $existing['status']
                ];
            }
        } catch (Exception $e) {
            error_log("Error checking user registration: " . $e->getMessage());
        }
    }
    
    // Check event capacity
    try {
        $stmt = $pdo->prepare("
            SELECT 
                max_attendees,
                (SELECT COUNT(*) FROM event_rsvps WHERE event_id = ? AND status = 'attending') +
                (SELECT COUNT(*) FROM event_rsvps_guests WHERE event_id = ? AND status = 'attending') as current_attendees
            FROM events 
            WHERE id = ?
        ");
        $stmt->execute([$eventId, $eventId, $eventId]);
        $capacity = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($capacity && $capacity['max_attendees'] && $capacity['current_attendees'] >= $capacity['max_attendees']) {
            return [
                'allowed' => false,
                'message' => 'Event is at full capacity',
                'reason' => 'full_capacity',
                'max_attendees' => $capacity['max_attendees'],
                'current_attendees' => $capacity['current_attendees']
            ];
        }
    } catch (Exception $e) {
        error_log("Error checking event capacity: " . $e->getMessage());
    }
    
    return [
        'allowed' => true,
        'message' => 'Registration is available',
        'reason' => 'available'
    ];
}
?>
