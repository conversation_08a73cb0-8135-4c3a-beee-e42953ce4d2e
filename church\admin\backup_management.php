<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';
require_once '../classes/DatabaseBackupManager.php';
require_once 'includes/pagination.php';

// Helper function to format file sizes
function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}

// Initialize backup manager
$backupManager = new DatabaseBackupManager($pdo);

$message = '';
$message_type = 'info';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $adminId = $_SESSION['admin_id'];
    
    switch ($action) {
        case 'create_config':
            // Process checkbox values properly
            $formData = $_POST;
            $formData['is_active'] = isset($_POST['is_active']) ? 1 : 0;
            $formData['email_notifications'] = isset($_POST['email_notifications']) ? 1 : 0;

            $result = $backupManager->createBackupConfiguration($formData, $adminId);
            $message = $result['message'];
            $message_type = $result['success'] ? 'success' : 'danger';
            break;

        case 'update_config':
            $configId = (int)$_POST['config_id'];
            // Process checkbox values properly
            $formData = $_POST;
            $formData['is_active'] = isset($_POST['is_active']) ? 1 : 0;
            $formData['email_notifications'] = isset($_POST['email_notifications']) ? 1 : 0;

            $result = $backupManager->updateBackupConfiguration($configId, $formData);
            $message = $result['message'];
            $message_type = $result['success'] ? 'success' : 'danger';
            break;
            
        case 'delete_config':
            $configId = (int)$_POST['config_id'];
            $result = $backupManager->deleteBackupConfiguration($configId);
            $message = $result['message'];
            $message_type = $result['success'] ? 'success' : 'danger';
            break;
            
        case 'manual_backup':
            $configId = isset($_POST['config_id']) ? (int)$_POST['config_id'] : null;
            $result = $backupManager->createBackup($configId, true, $adminId);
            $message = $result['message'];
            $message_type = $result['success'] ? 'success' : 'danger';
            break;

        case 'delete_backup':
            $backupId = (int)$_POST['backup_id'];
            $result = $backupManager->deleteBackup($backupId);
            $message = $result['message'];
            $message_type = $result['success'] ? 'success' : 'danger';
            break;

        case 'delete_backups':
            $backupIds = explode(',', $_POST['backup_ids']);
            $backupIds = array_map('intval', $backupIds);
            $result = $backupManager->deleteMultipleBackups($backupIds);
            $message = $result['message'];
            $message_type = $result['success'] ? 'success' : 'danger';
            break;
    }
    
    // Handle AJAX requests
    if (isset($_POST['ajax'])) {
        header('Content-Type: application/json');
        echo json_encode(['success' => $result['success'] ?? false, 'message' => $message]);
        exit;
    }

    // Redirect back to the same page with pagination parameters preserved
    $redirect_params = [];
    if (isset($_POST['backup_page'])) {
        $redirect_params['backup_page'] = $_POST['backup_page'];
    }
    if (isset($_POST['backup_limit'])) {
        $redirect_params['backup_limit'] = $_POST['backup_limit'];
    }

    $redirect_url = 'backup_management.php';
    if (!empty($redirect_params)) {
        $redirect_url .= '?' . http_build_query($redirect_params);
    }

    header("Location: $redirect_url");
    exit;
}

// Handle download requests
if (isset($_GET['download'])) {
    $historyId = (int)$_GET['download'];
    $result = $backupManager->downloadBackup($historyId);

    if ($result['success']) {
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $result['filename'] . '"');
        header('Content-Length: ' . $result['size']);
        readfile($result['filepath']);
        exit;
    } else {
        $message = $result['message'];
        $message_type = 'danger';

        // Redirect back with pagination parameters preserved
        $redirect_params = [];
        if (isset($_GET['backup_page'])) {
            $redirect_params['backup_page'] = $_GET['backup_page'];
        }
        if (isset($_GET['backup_limit'])) {
            $redirect_params['backup_limit'] = $_GET['backup_limit'];
        }

        $redirect_url = 'backup_management.php';
        if (!empty($redirect_params)) {
            $redirect_url .= '?' . http_build_query($redirect_params);
        }

        header("Location: $redirect_url");
        exit;
    }
}

// Pagination parameters for backup history
$backup_page = isset($_GET['backup_page']) ? max(1, (int)$_GET['backup_page']) : 1;
$backup_limit = isset($_GET['backup_limit']) ? max(10, min(100, (int)$_GET['backup_limit'])) : 20;
$backup_offset = ($backup_page - 1) * $backup_limit;

// Get backup configurations and history
$configurations = $backupManager->getAllBackupConfigurations();
$backupHistoryData = $backupManager->getBackupHistoryPaginated($backup_limit, $backup_offset);
$backupHistory = $backupHistoryData['records'];
$totalBackupRecords = $backupHistoryData['total'];
$totalBackupPages = ceil($totalBackupRecords / $backup_limit);

// Page title and header info
$page_title = 'Database Backup Management';
$page_header = 'Database Backup Management';
$page_description = 'Configure and manage automated database backups';

// Include header
include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#backupConfigModal">
            <i class="bi bi-plus-circle"></i> New Backup Configuration
        </button>
        <button type="button" class="btn btn-success" id="manualBackupBtn">
            <i class="bi bi-download"></i> Manual Backup
        </button>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-<?= $message_type ?> alert-dismissible fade show" role="alert">
        <i class="bi bi-<?= $message_type === 'success' ? 'check-circle' : 'exclamation-triangle' ?>"></i>
        <?= htmlspecialchars($message) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Backup Configurations -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i> Backup Configurations
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($configurations)): ?>
                    <div class="text-center py-4">
                        <i class="bi bi-inbox display-4 text-muted"></i>
                        <p class="text-muted mt-2">No backup configurations found.</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#backupConfigModal">
                            <i class="bi bi-plus-circle"></i> Create First Configuration
                        </button>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Format</th>
                                    <th>Schedule</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($configurations as $config): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($config['name']) ?></strong>
                                            <?php if ($config['description']): ?>
                                                <br><small class="text-muted"><?= htmlspecialchars($config['description']) ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?= ucfirst($config['backup_type']) ?></span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?= strtoupper($config['format']) ?></span>
                                        </td>
                                        <td>
                                            <?php if ($config['schedule_type'] === 'manual'): ?>
                                                <span class="text-muted">Manual</span>
                                            <?php else: ?>
                                                <?= ucfirst($config['schedule_type']) ?> at <?= date('g:i A', strtotime($config['schedule_time'])) ?>
                                                <?php if ($config['schedule_type'] === 'weekly'): ?>
                                                    <br><small class="text-muted">on <?= ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][$config['schedule_day_of_week']] ?></small>
                                                <?php elseif ($config['schedule_type'] === 'monthly'): ?>
                                                    <br><small class="text-muted">on day <?= $config['schedule_day_of_month'] ?></small>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($config['is_active']): ?>
                                                <span class="badge bg-success">Active</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">Inactive</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-success run-backup-btn" 
                                                        data-config-id="<?= $config['id'] ?>"
                                                        data-config-name="<?= htmlspecialchars($config['name']) ?>">
                                                    <i class="bi bi-play"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-primary edit-config-btn"
                                                        data-config='<?= htmlspecialchars(json_encode($config), ENT_QUOTES, 'UTF-8') ?>'>
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger delete-config-btn"
                                                        data-config-id="<?= $config['id'] ?>"
                                                        data-config-name="<?= htmlspecialchars($config['name']) ?>">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i> Backup Information
                </h5>
            </div>
            <div class="card-body">
                <h6>Backup Types</h6>
                <ul class="list-unstyled">
                    <li><strong>Full:</strong> Complete database structure and data</li>
                    <li><strong>Structure Only:</strong> Database schema without data</li>
                    <li><strong>Data Only:</strong> Data without table structure</li>
                </ul>
                
                <h6 class="mt-3">Formats</h6>
                <ul class="list-unstyled">
                    <li><strong>SQL:</strong> Standard MySQL dump format</li>
                    <li><strong>JSON:</strong> Portable JSON format</li>
                </ul>
                
                <h6 class="mt-3">Schedule Types</h6>
                <ul class="list-unstyled">
                    <li><strong>Manual:</strong> Run on demand only</li>
                    <li><strong>Daily:</strong> Run every day at specified time</li>
                    <li><strong>Weekly:</strong> Run weekly on specified day</li>
                    <li><strong>Monthly:</strong> Run monthly on specified date</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Backup History -->
<div class="card mt-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-clock-history"></i> Recent Backup History
        </h5>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-danger btn-sm" id="deleteSelectedBtn" style="display: none;">
                <i class="bi bi-trash me-1"></i>Delete Selected
            </button>
            <div class="btn-group" role="group">
                <input type="checkbox" class="btn-check" id="selectAllBackups" autocomplete="off">
                <label class="btn btn-outline-secondary btn-sm" for="selectAllBackups">
                    <i class="bi bi-check-all"></i>
                </label>
            </div>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($backupHistory)): ?>
            <div class="text-center py-4">
                <i class="bi bi-clock-history display-4 text-muted"></i>
                <p class="text-muted mt-2">No backup history found.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="40">
                                <input type="checkbox" class="form-check-input" id="selectAllTable">
                            </th>
                            <th>Backup Name</th>
                            <th>Configuration</th>
                            <th>Type</th>
                            <th>Size</th>
                            <th>Status</th>
                            <th>Started</th>
                            <th width="120">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($backupHistory as $backup): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input backup-checkbox" value="<?= $backup['id'] ?>">
                                </td>
                                <td>
                                    <strong><?= htmlspecialchars($backup['backup_name']) ?></strong>
                                    <?php if ($backup['created_by_name']): ?>
                                        <br><small class="text-muted">by <?= htmlspecialchars($backup['created_by_name']) ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($backup['config_name']): ?>
                                        <?= htmlspecialchars($backup['config_name']) ?>
                                    <?php else: ?>
                                        <span class="text-muted">Manual</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?= ucfirst($backup['backup_type']) ?></span>
                                    <span class="badge bg-secondary"><?= strtoupper($backup['format']) ?></span>
                                </td>
                                <td>
                                    <?php if ($backup['file_size']): ?>
                                        <?= formatBytes($backup['file_size']) ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $statusColors = [
                                        'completed' => 'success',
                                        'running' => 'primary',
                                        'failed' => 'danger',
                                        'pending' => 'warning'
                                    ];
                                    $statusColor = $statusColors[$backup['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?= $statusColor ?>"><?= ucfirst($backup['status']) ?></span>
                                    <?php if ($backup['error_message']): ?>
                                        <br><small class="text-danger"><?= htmlspecialchars($backup['error_message']) ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?= date('M j, Y g:i A', strtotime($backup['started_at'])) ?>
                                    <?php if ($backup['completed_at']): ?>
                                        <br><small class="text-muted">Completed: <?= date('g:i A', strtotime($backup['completed_at'])) ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <?php if ($backup['status'] === 'completed'): ?>
                                            <?php
                                            $download_params = ['download' => $backup['id']];
                                            if ($backup_page > 1) $download_params['backup_page'] = $backup_page;
                                            if ($backup_limit != 20) $download_params['backup_limit'] = $backup_limit;
                                            ?>
                                            <a href="?<?= http_build_query($download_params) ?>" class="btn btn-outline-primary btn-sm" title="Download">
                                                <i class="bi bi-download"></i>
                                            </a>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-outline-danger btn-sm delete-backup-btn"
                                                data-backup-id="<?= $backup['id'] ?>"
                                                data-backup-name="<?= htmlspecialchars($backup['backup_name']) ?>"
                                                title="Delete">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalBackupRecords > 0): ?>
                <?php
                echo generate_pagination(
                    $backup_page,
                    $totalBackupPages,
                    $totalBackupRecords,
                    $backup_limit,
                    'backup_management.php',
                    ['backup_limit' => $backup_limit],
                    'backup_page'
                );
                ?>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Backup Configuration Modal - Redesigned -->
<style>
.backup-config-modal .modal-dialog {
    max-width: 800px;
}

.backup-config-modal .modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.backup-config-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    border-bottom: none;
    padding: 1.5rem;
}

.backup-config-modal .modal-body {
    padding: 0;
    max-height: 70vh;
    overflow-y: auto;
}

.backup-config-modal .modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 15px 15px;
    padding: 1rem 1.5rem;
}

.config-tabs {
    border-bottom: 1px solid #e9ecef;
}

.config-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 1rem 1.5rem;
}

.config-tabs .nav-link.active {
    color: #667eea;
    border-bottom: 2px solid #667eea;
    background: none;
}

.tab-content {
    padding: 1.5rem;
}

.form-section {
    margin-bottom: 1.5rem;
}

.form-section h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}
</style>

<div class="modal fade backup-config-modal" id="backupConfigModal" tabindex="-1" aria-labelledby="backupConfigModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="backupConfigModalLabel">
                    <i class="bi bi-gear-fill me-2"></i>Backup Configuration
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs config-tabs" id="configTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic-config" type="button" role="tab">
                        <i class="bi bi-info-circle me-1"></i>Basic Settings
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="schedule-tab" data-bs-toggle="tab" data-bs-target="#schedule-config" type="button" role="tab">
                        <i class="bi bi-clock me-1"></i>Schedule
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications-config" type="button" role="tab">
                        <i class="bi bi-bell me-1"></i>Notifications
                    </button>
                </li>
            </ul>

            <form id="backupConfigForm" method="POST">
                <input type="hidden" id="configAction" name="action" value="create_config">
                <input type="hidden" id="configId" name="config_id">

                <div class="modal-body">
                    <div class="tab-content" id="configTabContent">
                        <!-- Basic Settings Tab -->
                        <div class="tab-pane fade show active" id="basic-config" role="tabpanel">
                            <div class="form-section">
                                <h6><i class="bi bi-info-circle me-2"></i>Basic Information</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="configName" class="form-label">Configuration Name</label>
                                        <input type="text" class="form-control" id="configName" name="name" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="configDescription" class="form-label">Description</label>
                                        <input type="text" class="form-control" id="configDescription" name="description">
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h6><i class="bi bi-gear me-2"></i>Backup Settings</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="backupType" class="form-label">Backup Type</label>
                                        <select class="form-select" id="backupType" name="backup_type" required>
                                            <option value="full">Full (Structure + Data)</option>
                                            <option value="structure_only">Structure Only</option>
                                            <option value="data_only">Data Only</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="backupFormat" class="form-label">Format</label>
                                        <select class="form-select" id="backupFormat" name="format" required>
                                            <option value="sql">SQL</option>
                                            <option value="json">JSON</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <label for="retentionDays" class="form-label">Retention (Days)</label>
                                        <input type="number" class="form-control" id="retentionDays" name="retention_days" min="1" max="365" value="30">
                                        <small class="text-muted">How long to keep backup files</small>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mt-4">
                                            <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                                            <label class="form-check-label" for="isActive">
                                                Active Configuration
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Schedule Tab -->
                        <div class="tab-pane fade" id="schedule-config" role="tabpanel">
                            <div class="form-section">
                                <h6><i class="bi bi-clock me-2"></i>Schedule Configuration</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="scheduleType" class="form-label">Schedule Type</label>
                                        <select class="form-select" id="scheduleType" name="schedule_type" required>
                                            <option value="manual">Manual</option>
                                            <option value="daily">Daily</option>
                                            <option value="weekly">Weekly</option>
                                            <option value="monthly">Monthly</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="scheduleTime" class="form-label">Schedule Time</label>
                                        <input type="time" class="form-control" id="scheduleTime" name="schedule_time" value="02:00">
                                    </div>
                                </div>

                                <div class="row mt-3" id="scheduleOptionsRow" style="display: none;">
                                    <div class="col-md-6" id="weeklyOptions" style="display: none;">
                                        <label for="scheduleDayOfWeek" class="form-label">Day of Week</label>
                                        <select class="form-select" id="scheduleDayOfWeek" name="schedule_day_of_week">
                                            <option value="0">Sunday</option>
                                            <option value="1">Monday</option>
                                            <option value="2">Tuesday</option>
                                            <option value="3">Wednesday</option>
                                            <option value="4">Thursday</option>
                                            <option value="5">Friday</option>
                                            <option value="6">Saturday</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6" id="monthlyOptions" style="display: none;">
                                        <label for="scheduleDayOfMonth" class="form-label">Day of Month</label>
                                        <input type="number" class="form-control" id="scheduleDayOfMonth" name="schedule_day_of_month" min="1" max="31" value="1">
                                    </div>
                                </div>

                                <div class="alert alert-info mt-3" id="scheduleInfo">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <span id="scheduleInfoText">Select a schedule type to see when backups will run.</span>
                                </div>
                            </div>
                        </div>

                        <!-- Notifications Tab -->
                        <div class="tab-pane fade" id="notifications-config" role="tabpanel">
                            <div class="form-section">
                                <h6><i class="bi bi-bell me-2"></i>Email Notifications</h6>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="emailNotifications" name="email_notifications">
                                    <label class="form-check-label" for="emailNotifications">
                                        Enable Email Notifications
                                    </label>
                                </div>
                                <div id="emailOptionsDiv" class="mt-3" style="display: none;">
                                    <label for="notificationEmails" class="form-label">Notification Emails</label>
                                    <textarea class="form-control" id="notificationEmails" name="notification_emails" rows="3" placeholder="Enter email addresses separated by commas"></textarea>
                                    <small class="text-muted">Separate multiple emails with commas (e.g., <EMAIL>, <EMAIL>)</small>
                                </div>

                                <div class="alert alert-warning mt-3">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    <strong>Note:</strong> Email notifications will be sent for backup completion, failures, and important status updates.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-primary" id="saveConfigBtn">
                        <i class="bi bi-check-circle me-1"></i>Save Configuration
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Schedule type change handler
    document.getElementById('scheduleType').addEventListener('change', function() {
        const scheduleType = this.value;
        const optionsRow = document.getElementById('scheduleOptionsRow');
        const weeklyOptions = document.getElementById('weeklyOptions');
        const monthlyOptions = document.getElementById('monthlyOptions');

        if (scheduleType === 'weekly') {
            optionsRow.style.display = 'block';
            weeklyOptions.style.display = 'block';
            monthlyOptions.style.display = 'none';
        } else if (scheduleType === 'monthly') {
            optionsRow.style.display = 'block';
            weeklyOptions.style.display = 'none';
            monthlyOptions.style.display = 'block';
        } else {
            optionsRow.style.display = 'none';
            weeklyOptions.style.display = 'none';
            monthlyOptions.style.display = 'none';
        }
    });

    // Email notifications toggle
    document.getElementById('emailNotifications').addEventListener('change', function() {
        const emailOptionsDiv = document.getElementById('emailOptionsDiv');
        emailOptionsDiv.style.display = this.checked ? 'block' : 'none';
    });

    // Edit configuration
    document.querySelectorAll('.edit-config-btn').forEach(button => {
        button.addEventListener('click', function() {
            const config = JSON.parse(this.dataset.config);

            // Populate form
            document.getElementById('configAction').value = 'update_config';
            document.getElementById('configId').value = config.id;
            document.getElementById('configName').value = config.name;
            document.getElementById('configDescription').value = config.description || '';
            document.getElementById('backupType').value = config.backup_type;
            document.getElementById('backupFormat').value = config.format;
            document.getElementById('scheduleType').value = config.schedule_type;
            document.getElementById('scheduleTime').value = config.schedule_time;
            document.getElementById('scheduleDayOfWeek').value = config.schedule_day_of_week;
            document.getElementById('scheduleDayOfMonth').value = config.schedule_day_of_month;
            document.getElementById('retentionDays').value = config.retention_days;
            document.getElementById('isActive').checked = config.is_active == 1;
            document.getElementById('emailNotifications').checked = config.email_notifications == 1;
            document.getElementById('notificationEmails').value = config.notification_emails || '';

            // Trigger change events
            document.getElementById('scheduleType').dispatchEvent(new Event('change'));
            document.getElementById('emailNotifications').dispatchEvent(new Event('change'));

            // Update modal title
            document.getElementById('backupConfigModalLabel').textContent = 'Edit Backup Configuration';
            document.getElementById('saveConfigBtn').textContent = 'Update Configuration';

            // Show modal
            new bootstrap.Modal(document.getElementById('backupConfigModal')).show();
        });
    });

    // Reset modal when closed
    document.getElementById('backupConfigModal').addEventListener('hidden.bs.modal', function() {
        document.getElementById('backupConfigForm').reset();
        document.getElementById('configAction').value = 'create_config';
        document.getElementById('configId').value = '';
        document.getElementById('backupConfigModalLabel').textContent = 'Backup Configuration';
        document.getElementById('saveConfigBtn').textContent = 'Save Configuration';
        document.getElementById('scheduleOptionsRow').style.display = 'none';
        document.getElementById('emailOptionsDiv').style.display = 'none';
    });

    // Manual backup
    document.getElementById('manualBackupBtn').addEventListener('click', function() {
        if (confirm('Create a manual backup now?')) {
            const formData = new FormData();
            formData.append('action', 'manual_backup');
            formData.append('ajax', '1');

            fetch('backup_management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Backup created successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while creating backup.');
            });
        }
    });

    // Run backup from configuration
    document.querySelectorAll('.run-backup-btn').forEach(button => {
        button.addEventListener('click', function() {
            const configId = this.dataset.configId;
            const configName = this.dataset.configName;

            if (confirm(`Run backup for configuration "${configName}"?`)) {
                const formData = new FormData();
                formData.append('action', 'manual_backup');
                formData.append('config_id', configId);
                formData.append('ajax', '1');

                fetch('backup_management.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Backup started successfully!');
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while starting backup.');
                });
            }
        });
    });

    // Delete configuration
    document.querySelectorAll('.delete-config-btn').forEach(button => {
        button.addEventListener('click', function() {
            const configId = this.dataset.configId;
            const configName = this.dataset.configName;

            if (confirm(`Delete backup configuration "${configName}"? This will also delete all associated backup history.`)) {
                const formData = new FormData();
                formData.append('action', 'delete_config');
                formData.append('config_id', configId);
                formData.append('ajax', '1');

                fetch('backup_management.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Configuration deleted successfully!');
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting configuration.');
                });
            }
        });
    });

    // Checkbox functionality for backup history
    const selectAllTable = document.getElementById('selectAllTable');
    const selectAllBackups = document.getElementById('selectAllBackups');
    const backupCheckboxes = document.querySelectorAll('.backup-checkbox');
    const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');

    // Select all functionality
    if (selectAllTable) {
        selectAllTable.addEventListener('change', function() {
            backupCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateDeleteButtonVisibility();
        });
    }

    if (selectAllBackups) {
        selectAllBackups.addEventListener('change', function() {
            backupCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            if (selectAllTable) selectAllTable.checked = this.checked;
            updateDeleteButtonVisibility();
        });
    }

    // Individual checkbox change
    backupCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateDeleteButtonVisibility();
            updateSelectAllState();
        });
    });

    function updateDeleteButtonVisibility() {
        const checkedBoxes = document.querySelectorAll('.backup-checkbox:checked');
        if (deleteSelectedBtn) {
            deleteSelectedBtn.style.display = checkedBoxes.length > 0 ? 'inline-block' : 'none';
        }
    }

    function updateSelectAllState() {
        const checkedBoxes = document.querySelectorAll('.backup-checkbox:checked');
        const allChecked = checkedBoxes.length === backupCheckboxes.length;
        const someChecked = checkedBoxes.length > 0;

        if (selectAllTable) {
            selectAllTable.checked = allChecked;
            selectAllTable.indeterminate = someChecked && !allChecked;
        }
        if (selectAllBackups) {
            selectAllBackups.checked = allChecked;
        }
    }

    // Delete selected backups
    if (deleteSelectedBtn) {
        deleteSelectedBtn.addEventListener('click', function() {
            const checkedBoxes = document.querySelectorAll('.backup-checkbox:checked');
            const backupIds = Array.from(checkedBoxes).map(cb => cb.value);

            if (backupIds.length === 0) return;

            if (confirm(`Are you sure you want to delete ${backupIds.length} backup(s)? This action cannot be undone.`)) {
                // Create form and submit
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_backups">
                    <input type="hidden" name="backup_ids" value="${backupIds.join(',')}">
                    <input type="hidden" name="backup_page" value="<?= $backup_page ?>">
                    <input type="hidden" name="backup_limit" value="<?= $backup_limit ?>">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        });
    }

    // Individual delete buttons
    document.querySelectorAll('.delete-backup-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const backupId = this.dataset.backupId;
            const backupName = this.dataset.backupName;

            if (confirm(`Are you sure you want to delete "${backupName}"? This action cannot be undone.`)) {
                // Create form and submit
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_backup">
                    <input type="hidden" name="backup_id" value="${backupId}">
                    <input type="hidden" name="backup_page" value="<?= $backup_page ?>">
                    <input type="hidden" name="backup_limit" value="<?= $backup_limit ?>">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        });
    });

    // Tab functionality for modal
    const configTabs = document.querySelectorAll('#configTabs button[data-bs-toggle="tab"]');
    configTabs.forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(event) {
            // Update schedule info when switching to schedule tab
            if (event.target.id === 'schedule-tab') {
                updateScheduleInfo();
            }
        });
    });

    // Update schedule info display
    function updateScheduleInfo() {
        const scheduleType = document.getElementById('scheduleType').value;
        const scheduleTime = document.getElementById('scheduleTime').value;
        const infoText = document.getElementById('scheduleInfoText');

        if (!infoText) return;

        let message = '';
        switch (scheduleType) {
            case 'manual':
                message = 'Backups will only run when manually triggered.';
                break;
            case 'daily':
                message = `Backups will run daily at ${scheduleTime}.`;
                break;
            case 'weekly':
                const dayOfWeek = document.getElementById('scheduleDayOfWeek').value;
                const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                message = `Backups will run weekly on ${days[dayOfWeek]} at ${scheduleTime}.`;
                break;
            case 'monthly':
                const dayOfMonth = document.getElementById('scheduleDayOfMonth').value;
                message = `Backups will run monthly on the ${dayOfMonth}${getOrdinalSuffix(dayOfMonth)} at ${scheduleTime}.`;
                break;
        }
        infoText.textContent = message;
    }

    function getOrdinalSuffix(day) {
        if (day >= 11 && day <= 13) return 'th';
        switch (day % 10) {
            case 1: return 'st';
            case 2: return 'nd';
            case 3: return 'rd';
            default: return 'th';
        }
    }

    // Update schedule info when schedule settings change
    document.getElementById('scheduleType').addEventListener('change', updateScheduleInfo);
    document.getElementById('scheduleTime').addEventListener('change', updateScheduleInfo);
    document.getElementById('scheduleDayOfWeek').addEventListener('change', updateScheduleInfo);
    document.getElementById('scheduleDayOfMonth').addEventListener('change', updateScheduleInfo);
});

// Pagination functions for backup history
function changePaginationLimit(limit) {
    const url = new URL(window.location);
    url.searchParams.set('backup_limit', limit);
    url.searchParams.set('backup_page', '1'); // Reset to first page
    window.location.href = url.toString();
}

function goToPage(page) {
    const totalPages = <?= $totalBackupPages ?>;
    if (page < 1 || page > totalPages) {
        alert('Please enter a valid page number between 1 and ' + totalPages);
        return;
    }

    const url = new URL(window.location);
    url.searchParams.set('backup_page', page);
    window.location.href = url.toString();
}
</script>

<?php
include 'includes/footer.php';
?>
