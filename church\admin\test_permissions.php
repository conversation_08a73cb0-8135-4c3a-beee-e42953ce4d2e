<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include granular permission checking
require_once 'includes/rbac_access_control_granular.php';

$user_id = $_SESSION['admin_id'];

// Get user info
$stmt = $pdo->prepare("SELECT username, email FROM admin_users WHERE id = ?");
$stmt->execute([$user_id]);
$user = $stmt->fetch();

// Get all user permissions
$stmt = $pdo->prepare("
    SELECT gp.permission_key, gp.permission_name, gp.description, pc.category_name
    FROM user_individual_permissions uip
    JOIN granular_permissions gp ON uip.permission_id = gp.id
    JOIN permission_categories pc ON gp.category_id = pc.id
    WHERE uip.user_id = ? 
    AND uip.is_active = 1 
    AND gp.is_active = 1
    ORDER BY pc.sort_order, gp.permission_name
");
$stmt->execute([$user_id]);
$permissions = $stmt->fetchAll();

// Group permissions by category
$grouped_permissions = [];
foreach ($permissions as $permission) {
    $grouped_permissions[$permission['category_name']][] = $permission;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Permission Test - <?= htmlspecialchars($user['username']) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-shield-check me-2"></i>
                            Permission Test for: <?= htmlspecialchars($user['username']) ?>
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>User Information</h5>
                                <p><strong>Username:</strong> <?= htmlspecialchars($user['username']) ?></p>
                                <p><strong>Email:</strong> <?= htmlspecialchars($user['email']) ?></p>
                                <p><strong>User ID:</strong> <?= $user_id ?></p>
                                <p><strong>Total Permissions:</strong> <?= count($permissions) ?></p>
                            </div>
                            <div class="col-md-6">
                                <h5>Permission Tests</h5>
                                <div class="list-group">
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        Dashboard View
                                        <?php if (hasUserPermission($user_id, 'dashboard.view')): ?>
                                            <span class="badge bg-success">✓ Allowed</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">✗ Denied</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        Dashboard Analytics
                                        <?php if (hasUserPermission($user_id, 'dashboard.analytics')): ?>
                                            <span class="badge bg-success">✓ Allowed</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">✗ Denied</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        View Members
                                        <?php if (hasUserPermission($user_id, 'members.view')): ?>
                                            <span class="badge bg-success">✓ Allowed</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">✗ Denied</span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                        Email Templates
                                        <?php if (hasUserPermission($user_id, 'email.templates')): ?>
                                            <span class="badge bg-success">✓ Allowed</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">✗ Denied</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h5>All Assigned Permissions</h5>
                        <?php if (empty($grouped_permissions)): ?>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                No permissions assigned to this user.
                            </div>
                        <?php else: ?>
                            <?php foreach ($grouped_permissions as $category => $category_permissions): ?>
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h6 class="mb-0"><?= htmlspecialchars($category) ?> (<?= count($category_permissions) ?> permissions)</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <?php foreach ($category_permissions as $permission): ?>
                                                <div class="col-md-6 mb-2">
                                                    <div class="d-flex align-items-center">
                                                        <i class="bi bi-check-circle text-success me-2"></i>
                                                        <div>
                                                            <strong><?= htmlspecialchars($permission['permission_name']) ?></strong>
                                                            <br>
                                                            <small class="text-muted"><?= htmlspecialchars($permission['permission_key']) ?></small>
                                                            <?php if ($permission['description']): ?>
                                                                <br>
                                                                <small><?= htmlspecialchars($permission['description']) ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>

                        <div class="mt-4">
                            <a href="dashboard.php" class="btn btn-primary">
                                <i class="bi bi-house me-2"></i>Go to Dashboard
                            </a>
                            <a href="manage_user_permissions.php" class="btn btn-secondary">
                                <i class="bi bi-gear me-2"></i>Manage Permissions
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
