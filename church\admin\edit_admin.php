<?php
/**
 * Edit Admin User
 * Interface for editing admin user details
 */

session_start();
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/includes/route_protection.php';

// Protect this page - Super Admin only
protectSuperAdminRoute();

$message = '';
$error = '';
$admin_user = null;

// Get admin ID from URL
$admin_id = $_GET['id'] ?? '';

if (empty($admin_id) || !is_numeric($admin_id)) {
    header('Location: create_admin_users.php?error=invalid_id');
    exit;
}

// Get admin user details
try {
    $stmt = $pdo->prepare("SELECT * FROM admins WHERE id = ?");
    $stmt->execute([$admin_id]);
    $admin_user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin_user) {
        header('Location: create_admin_users.php?error=user_not_found');
        exit;
    }
} catch (PDOException $e) {
    header('Location: create_admin_users.php?error=database_error');
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $username = trim($_POST['username']);
        $email = trim($_POST['email']);
        $full_name = trim($_POST['full_name']);
        $role = $_POST['role'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        
        // Validate input
        if (empty($username) || empty($email) || empty($role)) {
            throw new Exception('Username, email, and role are required.');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Please enter a valid email address.');
        }
        
        // Check if username or email already exists (excluding current user)
        $stmt = $pdo->prepare("SELECT id FROM admins WHERE (username = ? OR email = ?) AND id != ?");
        $stmt->execute([$username, $email, $admin_id]);
        if ($stmt->fetch()) {
            throw new Exception('Username or email already exists for another user.');
        }
        
        // Update admin user
        if (!empty($new_password)) {
            // Update with new password
            if (strlen($new_password) < 6) {
                throw new Exception('Password must be at least 6 characters long.');
            }
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("
                UPDATE admins 
                SET username = ?, email = ?, full_name = ?, role = ?, password = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$username, $email, $full_name, $role, $hashed_password, $admin_id]);
        } else {
            // Update without changing password
            $stmt = $pdo->prepare("
                UPDATE admins 
                SET username = ?, email = ?, full_name = ?, role = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$username, $email, $full_name, $role, $admin_id]);
        }
        
        $message = "Admin user updated successfully!";
        
        // Refresh admin user data
        $stmt = $pdo->prepare("SELECT * FROM admins WHERE id = ?");
        $stmt->execute([$admin_id]);
        $admin_user = $stmt->fetch(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

$page_title = 'Edit Admin User';
$page_header = 'Edit Admin User';
$page_description = 'Modify admin user details and permissions';

include __DIR__ . '/includes/header.php';
?>

<style>
.edit-header {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
    color: white;
    border-radius: 10px;
}

.edit-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.user-info {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="edit-header p-4 text-center">
            <h2 class="mb-2">
                <i class="bi bi-person-gear"></i> <?php echo $page_header; ?>
            </h2>
            <p class="mb-0"><?php echo $page_description; ?></p>
        </div>
    </div>
</div>

<!-- Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Edit Form -->
<div class="row">
    <div class="col-md-8">
        <div class="edit-card">
            <h5><i class="bi bi-pencil-square"></i> Edit User Details</h5>
            
            <form method="POST">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username *</label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="<?php echo htmlspecialchars($admin_user['username']); ?>" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email *</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($admin_user['email']); ?>" required>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="full_name" class="form-label">Full Name</label>
                    <input type="text" class="form-control" id="full_name" name="full_name" 
                           value="<?php echo htmlspecialchars($admin_user['full_name'] ?? ''); ?>">
                </div>
                
                <div class="mb-3">
                    <label for="role" class="form-label">Role Type *</label>
                    <select class="form-select" id="role" name="role" required>
                        <option value="">Select role...</option>
                        <option value="super_admin" <?php echo ($admin_user['role'] ?? '') === 'super_admin' ? 'selected' : ''; ?>>Super Admin</option>
                        <option value="admin" <?php echo ($admin_user['role'] ?? '') === 'admin' ? 'selected' : ''; ?>>Admin</option>
                        <option value="limited_admin" <?php echo ($admin_user['role'] ?? '') === 'limited_admin' ? 'selected' : ''; ?>>Limited Admin</option>
                        <option value="coordinator" <?php echo ($admin_user['role'] ?? '') === 'coordinator' ? 'selected' : ''; ?>>Event Coordinator</option>
                        <option value="organizer" <?php echo ($admin_user['role'] ?? '') === 'organizer' ? 'selected' : ''; ?>>Event Organizer</option>
                        <option value="session_moderator" <?php echo ($admin_user['role'] ?? '') === 'session_moderator' ? 'selected' : ''; ?>>Session Moderator</option>
                        <option value="staff" <?php echo ($admin_user['role'] ?? '') === 'staff' ? 'selected' : ''; ?>>Staff</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="new_password" class="form-label">New Password</label>
                    <input type="password" class="form-control" id="new_password" name="new_password" minlength="6">
                    <div class="form-text">Leave blank to keep current password. Minimum 6 characters if changing.</div>
                </div>
                
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Update User
                    </button>
                    <a href="create_admin_users.php" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Admin Users
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="edit-card">
            <h5><i class="bi bi-info-circle"></i> User Information</h5>
            
            <div class="user-info">
                <div class="mb-2">
                    <strong>User ID:</strong> <?php echo htmlspecialchars($admin_user['id']); ?>
                </div>
                <div class="mb-2">
                    <strong>Current Username:</strong> <?php echo htmlspecialchars($admin_user['username']); ?>
                </div>
                <div class="mb-2">
                    <strong>Current Email:</strong> <?php echo htmlspecialchars($admin_user['email']); ?>
                </div>
                <div class="mb-2">
                    <strong>Current Role:</strong> 
                    <span class="badge bg-primary"><?php echo htmlspecialchars($admin_user['role'] ?? 'Not Set'); ?></span>
                </div>
                <div class="mb-2">
                    <strong>Created:</strong> <?php echo date('M j, Y g:i A', strtotime($admin_user['created_at'])); ?>
                </div>
                <?php if ($admin_user['updated_at']): ?>
                <div class="mb-0">
                    <strong>Last Updated:</strong> <?php echo date('M j, Y g:i A', strtotime($admin_user['updated_at'])); ?>
                </div>
                <?php endif; ?>
            </div>
            
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle"></i>
                <strong>Warning:</strong> Changing the role will affect the user's access permissions immediately.
            </div>
            
            <div class="d-grid gap-2">
                <a href="setup_rbac_system.php" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-shield-plus"></i> Manage RBAC Assignments
                </a>
                <a href="manage_user_permissions.php" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-key"></i> Manage Permissions
                </a>
            </div>
        </div>
    </div>
</div>

<?php include __DIR__ . '/includes/footer.php'; ?>
