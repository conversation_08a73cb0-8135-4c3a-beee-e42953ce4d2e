<?php
// Test script to send QR code email to specific member
require_once '../config.php';

echo "<h1>📧 Test QR Code Email Sender</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} .step{background:#f5f5f5;padding:10px;margin:10px 0;border-left:4px solid #007bff;}</style>";

$target_email = 'bointa@<EMAIL>';
$message = '';
$error = '';

try {
    echo "<div class='step'>";
    echo "<h2>Step 1: Find Member</h2>";
    
    // Find the member by email
    $stmt = $pdo->prepare("SELECT * FROM members WHERE email = ? AND is_active = 1");
    $stmt->execute([$target_email]);
    $member = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$member) {
        echo "<span class='info'>ℹ️ Member not found. Creating new member...</span><br>";

        // Check members table structure first
        $stmt = $pdo->query("DESCRIBE members");
        $member_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<span class='info'>📋 Members table columns: " . implode(', ', $member_columns) . "</span><br>";

        // Create the member based on available columns
        if (in_array('phone', $member_columns)) {
            $stmt = $pdo->prepare("
                INSERT INTO members (full_name, email, phone, is_active, created_at)
                VALUES (?, ?, ?, 1, NOW())
            ");
            $stmt->execute(['Bointa Test User', $target_email, '555-0123']);
        } else {
            $stmt = $pdo->prepare("
                INSERT INTO members (full_name, email, is_active, created_at)
                VALUES (?, ?, 1, NOW())
            ");
            $stmt->execute(['Bointa Test User', $target_email]);
        }
        $member_id = $pdo->lastInsertId();

        // Get the created member
        $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
        $stmt->execute([$member_id]);
        $member = $stmt->fetch(PDO::FETCH_ASSOC);

        echo "<span class='success'>✅ Created new member: {$member['full_name']} (ID: {$member['id']})</span><br>";
    }
    
    echo "<span class='success'>✅ Found member: {$member['full_name']} (ID: {$member['id']})</span><br>";
    echo "<span class='info'>📧 Email: {$member['email']}</span><br>";
    if (isset($member['phone']) && $member['phone']) {
        echo "<span class='info'>📱 Phone: {$member['phone']}</span><br>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 2: Find/Create Test Event</h2>";
    
    // Get or create a test event
    $stmt = $pdo->query("SELECT * FROM events WHERE title LIKE '%Test%' OR title LIKE '%Evening Service%' ORDER BY id DESC LIMIT 1");
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        // Create a test event
        $stmt = $pdo->prepare("
            INSERT INTO events (title, description, event_date, location, created_at)
            VALUES ('Test QR Email Event', 'Test event for QR code email system', '2025-08-15 10:00:00', 'Main Church Hall', NOW())
        ");
        $stmt->execute();
        $event_id = $pdo->lastInsertId();
        
        // Get the created event
        $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
        $stmt->execute([$event_id]);
        $event = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<span class='success'>✅ Created test event: {$event['title']} (ID: {$event['id']})</span><br>";
    } else {
        echo "<span class='success'>✅ Using existing event: {$event['title']} (ID: {$event['id']})</span><br>";
    }
    
    echo "<span class='info'>📅 Date: " . date('F j, Y g:i A', strtotime($event['event_date'])) . "</span><br>";
    echo "<span class='info'>📍 Location: {$event['location']}</span><br>";
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 3: Create/Update RSVP</h2>";
    
    // Create or update RSVP for the member
    $stmt = $pdo->prepare("
        SELECT * FROM event_rsvps 
        WHERE event_id = ? AND user_id = ?
    ");
    $stmt->execute([$event['id'], $member['id']]);
    $existing_rsvp = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$existing_rsvp) {
        $stmt = $pdo->prepare("
            INSERT INTO event_rsvps (event_id, user_id, status, created_at)
            VALUES (?, ?, 'attending', NOW())
        ");
        $stmt->execute([$event['id'], $member['id']]);
        echo "<span class='success'>✅ Created RSVP for member</span><br>";
    } else {
        echo "<span class='info'>ℹ️ RSVP already exists</span><br>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 4: Generate QR Code</h2>";
    
    // Check if QR code already exists
    $stmt = $pdo->prepare("
        SELECT * FROM member_qr_codes 
        WHERE event_id = ? AND member_id = ?
    ");
    $stmt->execute([$event['id'], $member['id']]);
    $existing_qr = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existing_qr) {
        $qr_token = $existing_qr['qr_token'];
        echo "<span class='info'>ℹ️ Using existing QR code: {$qr_token}</span><br>";
    } else {
        // Generate new QR code
        $qr_token = 'QR_' . $event['id'] . '_' . bin2hex(random_bytes(16));
        
        $stmt = $pdo->prepare("
            INSERT INTO member_qr_codes 
            (event_id, member_id, qr_token, attendee_name, attendee_email, attendee_type)
            VALUES (?, ?, ?, ?, ?, 'member')
        ");
        $stmt->execute([
            $event['id'],
            $member['id'],
            $qr_token,
            $member['full_name'],
            $member['email']
        ]);
        
        echo "<span class='success'>✅ Generated new QR code: {$qr_token}</span><br>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 5: Send QR Code Email</h2>";
    
    // Generate QR code URL
    $base_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
    $qr_url = $base_url . "/member_checkin.php?token=" . $qr_token;
    
    // Email content
    $subject = "Your QR Code for " . $event['title'];
    $email_body = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>Event QR Code</title>
        <style>
            body { 
                font-family: Arial, sans-serif; 
                margin: 0; 
                padding: 0; 
                background-color: #f4f4f4; 
            }
            .container { 
                max-width: 600px; 
                margin: 0 auto; 
                background-color: white;
                border-radius: 10px;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }
            .header { 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white; 
                padding: 30px 20px; 
                text-align: center; 
            }
            .header h1 {
                margin: 0;
                font-size: 24px;
            }
            .content { 
                padding: 30px 20px; 
                background: white; 
            }
            .qr-section { 
                text-align: center; 
                margin: 30px 0; 
                padding: 20px;
                background: #f8f9fa;
                border-radius: 10px;
            }
            .qr-code { 
                border: 3px solid #007bff; 
                padding: 20px; 
                background: white; 
                border-radius: 10px;
                display: inline-block;
                margin: 20px 0;
            }
            .instructions { 
                background: #e3f2fd; 
                padding: 20px; 
                border-radius: 8px; 
                margin: 20px 0;
                border-left: 4px solid #2196f3;
            }
            .instructions h3 {
                margin-top: 0;
                color: #1976d2;
            }
            .event-details {
                background: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
            }
            .footer {
                background: #f8f9fa;
                padding: 20px;
                text-align: center;
                color: #666;
                font-size: 14px;
            }
            .btn {
                display: inline-block;
                padding: 12px 24px;
                background: #007bff;
                color: white;
                text-decoration: none;
                border-radius: 5px;
                margin: 10px 0;
            }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>🎟️ Your Event QR Code</h1>
                <p>Freedom Assembly Church</p>
            </div>
            
            <div class='content'>
                <h2>Hello " . htmlspecialchars($member['full_name']) . "!</h2>
                <p>Thank you for registering for <strong>" . htmlspecialchars($event['title']) . "</strong>. Your personal QR code is ready!</p>
                
                <div class='event-details'>
                    <h3>📅 Event Details</h3>
                    <p><strong>Event:</strong> " . htmlspecialchars($event['title']) . "</p>
                    <p><strong>Date:</strong> " . date('F j, Y g:i A', strtotime($event['event_date'])) . "</p>
                    <p><strong>Location:</strong> " . htmlspecialchars($event['location']) . "</p>
                </div>
                
                <div class='qr-section'>
                    <h3>🎯 Your Personal QR Code</h3>
                    <div class='qr-code'>
                        <div id='qr-code-container'></div>
                    </div>
                    <p><strong>QR Code ID:</strong> " . htmlspecialchars($qr_token) . "</p>
                    <a href='{$qr_url}' class='btn'>🔗 Open Check-in Link</a>
                </div>
                
                <div class='instructions'>
                    <h3>📱 How to Use Your QR Code</h3>
                    <ol>
                        <li><strong>Save this email</strong> or screenshot the QR code</li>
                        <li><strong>Arrive at the event</strong> and look for check-in stations</li>
                        <li><strong>Show your QR code</strong> to staff at the entrance</li>
                        <li><strong>Get checked in instantly</strong> - no manual process needed!</li>
                    </ol>
                    
                    <p><strong>💡 Pro Tips:</strong></p>
                    <ul>
                        <li>You can show the QR code on your phone or print this email</li>
                        <li>The QR code works even if you're offline</li>
                        <li>Each QR code is unique and can only be used once</li>
                    </ul>
                </div>
                
                <p>If you have any questions or need assistance, please contact our event team.</p>
                <p>We look forward to seeing you at the event!</p>
                
                <p><strong>Blessings,</strong><br>
                Freedom Assembly Church Event Team</p>
            </div>
            
            <div class='footer'>
                <p>This is an automated message from Freedom Assembly Church</p>
                <p>Event Management System | QR Code Check-in</p>
            </div>
        </div>
        
        <script src='https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js'></script>
        <script>
            // Generate QR code when page loads
            document.addEventListener('DOMContentLoaded', function() {
                QRCode.toCanvas(document.getElementById('qr-code-container'), '{$qr_url}', {
                    width: 200,
                    height: 200,
                    margin: 2,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    }
                }, function(error) {
                    if (error) {
                        document.getElementById('qr-code-container').innerHTML = '<p>QR Code: {$qr_token}</p>';
                    }
                });
            });
        </script>
    </body>
    </html>
    ";
    
    // Email headers
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: Freedom Assembly Church <<EMAIL>>" . "\r\n";
    $headers .= "Reply-To: <EMAIL>" . "\r\n";
    
    echo "<span class='info'>📧 Sending email to: {$target_email}</span><br>";
    echo "<span class='info'>📋 Subject: {$subject}</span><br>";
    echo "<span class='info'>🔗 QR Check-in URL: <a href='{$qr_url}' target='_blank'>{$qr_url}</a></span><br>";
    
    // Send the email
    $email_sent = mail($target_email, $subject, $email_body, $headers);
    
    if ($email_sent) {
        echo "<span class='success'>✅ Email sent successfully!</span><br>";
        
        // Update database to mark email as sent
        $stmt = $pdo->prepare("
            UPDATE member_qr_codes 
            SET email_sent = 1, email_sent_at = NOW() 
            WHERE qr_token = ?
        ");
        $stmt->execute([$qr_token]);
        
        echo "<span class='success'>✅ Database updated - email marked as sent</span><br>";
    } else {
        echo "<span class='error'>❌ Failed to send email</span><br>";
        echo "<span class='error'>This might be due to server email configuration</span><br>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>Step 6: Test Information</h2>";
    echo "<span class='info'>🎯 <strong>Test Details:</strong></span><br>";
    echo "<span class='info'>• Member: {$member['full_name']} ({$target_email})</span><br>";
    echo "<span class='info'>• Event: {$event['title']}</span><br>";
    echo "<span class='info'>• QR Token: {$qr_token}</span><br>";
    echo "<span class='info'>• Check-in URL: <a href='{$qr_url}' target='_blank'>{$qr_url}</a></span><br>";
    echo "<br>";
    echo "<span class='info'>📱 <strong>Testing Instructions:</strong></span><br>";
    echo "<span class='info'>1. Check {$target_email} for the QR code email</span><br>";
    echo "<span class='info'>2. Click the QR code or check-in link in the email</span><br>";
    echo "<span class='info'>3. Verify the check-in process works</span><br>";
    echo "<span class='info'>4. Test the mobile scanner interface</span><br>";
    echo "</div>";
    
    echo "<div class='step' style='background:#e8f5e8;border-color:#28a745;'>";
    echo "<h2>🎉 Test Email Sent Successfully!</h2>";
    echo "<span class='success'>✅ QR code email sent to {$target_email}</span><br>";
    echo "<span class='success'>✅ Member can now test the complete check-in process</span><br>";
    echo "<span class='success'>✅ All database records updated properly</span><br>";
    echo "<br>";
    echo "<strong>📧 The member should receive a professional email with:</strong><br>";
    echo "<span class='info'>• Personal QR code for instant check-in</span><br>";
    echo "<span class='info'>• Event details and instructions</span><br>";
    echo "<span class='info'>• Direct check-in link as backup</span><br>";
    echo "<span class='info'>• Professional church branding</span><br>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='step' style='background:#ffe8e8;border-color:#dc3545;'>";
    echo "<h2>❌ Test Failed</h2>";
    echo "<span class='error'>Error: " . $e->getMessage() . "</span><br>";
    echo "<span class='error'>File: " . $e->getFile() . " Line: " . $e->getLine() . "</span>";
    echo "</div>";
}
?>

<script>
// Auto-refresh to show updates
setTimeout(function() {
    console.log('Email test completed');
}, 5000);
</script>
