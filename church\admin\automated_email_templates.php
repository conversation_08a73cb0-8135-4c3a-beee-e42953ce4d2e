<?php
session_start();

// Check for flash messages
$flash_message = '';
$flash_type = '';
if (isset($_SESSION['flash_message'])) {
    $flash_message = $_SESSION['flash_message'];
    $flash_type = $_SESSION['flash_type'] ?? 'success';
    unset($_SESSION['flash_message']);
    unset($_SESSION['flash_type']);
}

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$error = '';

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {
        // Start transaction
        $conn->beginTransaction();
        
        // Check if the email_type enum includes 'notification'
        $stmt = $conn->prepare("SHOW COLUMNS FROM automated_emails_settings LIKE 'email_type'");
        $stmt->execute();
        $column = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // If 'notification' is not in the enum, add it
        if ($column && strpos($column['Type'], 'notification') === false) {
            $stmt = $conn->prepare("ALTER TABLE `automated_emails_settings` 
                                   MODIFY COLUMN `email_type` enum('birthday','reminder','welcome','notification') NOT NULL");
            $stmt->execute();
            error_log("Added 'notification' to email_type enum in automated_emails_settings table");
        }
        
        // Process Birthday Templates
        if (isset($_POST['birthday_templates']) && is_array($_POST['birthday_templates'])) {
            $birthday_templates = $_POST['birthday_templates'];
            
            // We only need the template IDs, not the content
            $template_ids = implode(',', array_map('intval', $birthday_templates));
            $send_time = $_POST['birthday_send_time'] ?? '07:00:00';
            
            // Debug log
            error_log("Processing birthday templates: " . $template_ids);
            error_log("Birthday send time: " . $send_time);
            
            // Check if birthday setting already exists
            $stmt = $conn->prepare("SELECT * FROM automated_emails_settings WHERE email_type = 'birthday'");
            $stmt->execute();
            $exists = $stmt->fetch();
            
            if ($exists) {
                // Update existing
                error_log("Updating existing birthday setting with ID: " . $exists['id']);
                $stmt = $conn->prepare("UPDATE automated_emails_settings SET template_ids = ?, send_time = ? WHERE email_type = 'birthday'");
                $stmt->execute([$template_ids, $send_time]);
            } else {
                // Insert new
                error_log("Inserting new birthday setting");
                $stmt = $conn->prepare("INSERT INTO automated_emails_settings (email_type, template_ids, send_time) VALUES ('birthday', ?, ?)");
                $stmt->execute([$template_ids, $send_time]);
            }
        }
        
        // Process Notification Templates
        if (isset($_POST['notification_templates']) && is_array($_POST['notification_templates'])) {
            $notification_templates = $_POST['notification_templates'];
            $template_ids = implode(',', array_map('intval', $notification_templates));
            $send_time = $_POST['notification_send_time'] ?? '07:00:00';
            $days_before = max(1, min(7, intval($_POST['notification_days_before'] ?? 3))); // Between 1 and 7 days
            
            // Debug log
            error_log("Processing notification templates: " . $template_ids);
            error_log("Notification send time: " . $send_time);
            error_log("Notification days before: " . $days_before);
            
            // Check if notification setting already exists
            $stmt = $conn->prepare("SELECT * FROM automated_emails_settings WHERE email_type = 'notification'");
            $stmt->execute();
            $exists = $stmt->fetch();
            
            if ($exists) {
                // Update existing
                error_log("Updating existing notification setting with ID: " . $exists['id']);
                $stmt = $conn->prepare("UPDATE automated_emails_settings SET template_ids = ?, send_time = ?, days_before = ? WHERE email_type = 'notification'");
                $stmt->execute([$template_ids, $send_time, $days_before]);
            } else {
                // Insert new
                error_log("Inserting new notification setting");
                $stmt = $conn->prepare("INSERT INTO automated_emails_settings (email_type, template_ids, send_time, days_before) VALUES ('notification', ?, ?, ?)");
                $stmt->execute([$template_ids, $send_time, $days_before]);
            }
        }
        
        // Process Reminder Templates
        if (isset($_POST['reminder_templates']) && is_array($_POST['reminder_templates'])) {
            $reminder_templates = $_POST['reminder_templates'];
            
            // We only need the template IDs, not the content
            $template_ids = implode(',', array_map('intval', $reminder_templates));
            $send_time = $_POST['reminder_send_time'] ?? '07:00:00';
            $days_before = max(1, min(7, intval($_POST['days_before'] ?? 3))); // Between 1 and 7 days
            
            // Debug log
            error_log("Processing reminder templates: " . $template_ids);
            error_log("Reminder send time: " . $send_time);
            error_log("Reminder days before: " . $days_before);
            
            // Check if reminder setting already exists
            $stmt = $conn->prepare("SELECT * FROM automated_emails_settings WHERE email_type = 'reminder'");
            $stmt->execute();
            $exists = $stmt->fetch();
            
            if ($exists) {
                // Update existing
                error_log("Updating existing reminder setting with ID: " . $exists['id']);
                $stmt = $conn->prepare("UPDATE automated_emails_settings SET template_ids = ?, send_time = ?, days_before = ? WHERE email_type = 'reminder'");
                $stmt->execute([$template_ids, $send_time, $days_before]);
            } else {
                // Insert new
                error_log("Inserting new reminder setting");
                $stmt = $conn->prepare("INSERT INTO automated_emails_settings (email_type, template_ids, send_time, days_before) VALUES ('reminder', ?, ?, ?)");
                $stmt->execute([$template_ids, $send_time, $days_before]);
            }
        }
        
        // Process Welcome Templates
        if (isset($_POST['welcome_templates']) && is_array($_POST['welcome_templates'])) {
            $welcome_templates = $_POST['welcome_templates'];
            
            // We only need the template IDs, not the content
            $template_ids = implode(',', array_map('intval', $welcome_templates));
            $send_time = $_POST['welcome_send_time'] ?? '07:00:00';
            
            // Debug log
            error_log("Processing welcome templates: " . $template_ids);
            error_log("Welcome send time: " . $send_time);
            
            // Check if welcome setting already exists
            $stmt = $conn->prepare("SELECT * FROM automated_emails_settings WHERE email_type = 'welcome'");
            $stmt->execute();
            $exists = $stmt->fetch();
            
            if ($exists) {
                // Update existing
                error_log("Updating existing welcome setting with ID: " . $exists['id']);
                $stmt = $conn->prepare("UPDATE automated_emails_settings SET template_ids = ?, send_time = ? WHERE email_type = 'welcome'");
                $stmt->execute([$template_ids, $send_time]);
            } else {
                // Insert new
                error_log("Inserting new welcome setting");
                $stmt = $conn->prepare("INSERT INTO automated_emails_settings (email_type, template_ids, send_time) VALUES ('welcome', ?, ?)");
                $stmt->execute([$template_ids, $send_time]);
            }
        }
        
        // Commit transaction
        $conn->commit();
        
        $_SESSION['flash_message'] = "Automated email settings saved successfully!";
        $_SESSION['flash_type'] = 'success';
        header("Location: automated_email_templates.php");
        exit();
        
    } catch (PDOException $e) {
        // Rollback transaction on error
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        $error = "Error updating settings: " . $e->getMessage();
        error_log("Error updating settings: " . $e->getMessage());
    }
}

// Load existing settings
$settings = [
    'birthday' => null,
    'reminder' => null,
    'notification' => null,
    'welcome' => null
];

try {
    $stmt = $conn->prepare("SELECT * FROM automated_emails_settings");
    $stmt->execute();
    $all_settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Debug log
    error_log("Loaded " . count($all_settings) . " settings from automated_emails_settings table");
    
    foreach ($all_settings as $setting) {
        $type = $setting['email_type'];
        $settings[$type] = $setting;
        // Debug log
        error_log("Loaded setting for type: " . $type . ", template_ids: " . $setting['template_ids']);
    }

    // After loading settings, check if notification settings exist
    try {
        // Check if notification settings exist
        $stmt = $conn->prepare("SELECT COUNT(*) FROM automated_emails_settings WHERE email_type = 'notification'");
        $stmt->execute();
        $notificationSettingsExist = (int)$stmt->fetchColumn();
        
        error_log("Notification settings exist: " . ($notificationSettingsExist ? 'Yes' : 'No'));
        
        // If notification settings don't exist, try to add them
        if (!$notificationSettingsExist) {
            error_log("Notification settings don't exist, trying to add them");
            
            // First check if the email_type enum includes 'notification'
            $stmt = $conn->prepare("SHOW COLUMNS FROM automated_emails_settings LIKE 'email_type'");
            $stmt->execute();
            $column = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // If 'notification' is not in the enum, add it
            if ($column && strpos($column['Type'], 'notification') === false) {
                error_log("Adding 'notification' to email_type enum");
                $stmt = $conn->prepare("ALTER TABLE `automated_emails_settings` 
                                       MODIFY COLUMN `email_type` enum('birthday','reminder','welcome','notification') NOT NULL");
                $stmt->execute();
            }
            
            // Now insert default notification settings
            error_log("Inserting default notification settings");
            $stmt = $conn->prepare("INSERT INTO automated_emails_settings (email_type, template_ids, send_time, days_before) 
                                   VALUES ('notification', '', '07:00:00', 3)");
            $stmt->execute();
            
            // Reload the settings
            $stmt = $conn->prepare("SELECT * FROM automated_emails_settings WHERE email_type = 'notification'");
            $stmt->execute();
            $settings['notification'] = $stmt->fetch(PDO::FETCH_ASSOC);
            
            error_log("Notification settings after insert: " . print_r($settings['notification'], true));
        }
    } catch (PDOException $e) {
        error_log("Error checking/adding notification settings: " . $e->getMessage());
    }
} catch (PDOException $e) {
    $error = "Error loading settings: " . $e->getMessage();
    error_log("Error loading settings: " . $e->getMessage());
}

// Load all email templates
$templates = [
    'birthday' => [],
    'reminder' => [],
    'notification' => [],
    'welcome' => []
];

try {
    // Get birthday templates (is_birthday_template = 1)
    $stmt = $conn->prepare("SELECT id, template_name, subject FROM email_templates WHERE is_birthday_template = 1 ORDER BY template_name");
    $stmt->execute();
    $templates['birthday'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get reminder templates (birthday reminder related)
    $stmt = $conn->prepare("SELECT id, template_name, subject FROM email_templates WHERE template_name LIKE '%reminder%' ORDER BY template_name");
    $stmt->execute();
    $templates['reminder'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get notification templates (birthday notification related)
    $stmt = $conn->prepare("SELECT id, template_name, subject FROM email_templates WHERE template_name LIKE '%notification%' ORDER BY template_name");
    $stmt->execute();
    $templates['notification'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get welcome templates
    $stmt = $conn->prepare("SELECT id, template_name, subject FROM email_templates WHERE template_name LIKE '%welcome%' ORDER BY template_name");
    $stmt->execute();
    $templates['welcome'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error = "Error loading templates: " . $e->getMessage();
}

// Page title and header info
$page_title = "Automated Email Templates";
$page_header = "Automated Email Templates";
$page_description = "Configure templates and timing for automated emails sent by the system.";

// Include header
include_once 'includes/header.php';
?>

                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    <strong>Note:</strong> This page manages email templates and their automation settings.
                    For general system notification settings, visit <a href="universal_organization_setup.php" class="alert-link">Organization Setup → Email</a>.
                </div>
            
            <?php if (!empty($flash_message)): ?>
                <div class="alert alert-<?php echo $flash_type; ?> alert-dismissible fade show" role="alert">
                    <?php echo $flash_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">Configure Automated Emails</h5>
                        </div>
                        <div class="card-body">
                            <form action="automated_email_templates.php" method="post">
                                <div class="row">
                                    <div class="col-md-12">
                                        <ul class="nav nav-tabs" id="emailTabs" role="tablist">
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link active" id="birthday-tab" data-bs-toggle="tab" data-bs-target="#birthday" type="button" role="tab" aria-controls="birthday" aria-selected="true">Birthday Emails</button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="reminder-tab" data-bs-toggle="tab" data-bs-target="#reminder" type="button" role="tab" aria-controls="reminder" aria-selected="false">Birthday Reminders</button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="notification-tab" data-bs-toggle="tab" data-bs-target="#notification" type="button" role="tab" aria-controls="notification" aria-selected="false">Birthday Notifications</button>
                                            </li>
                                            <li class="nav-item" role="presentation">
                                                <button class="nav-link" id="welcome-tab" data-bs-toggle="tab" data-bs-target="#welcome" type="button" role="tab" aria-controls="welcome" aria-selected="false">Welcome Emails</button>
                                            </li>
                                        </ul>
                                        
                                        <div class="tab-content p-3 border border-top-0 rounded-bottom" id="emailTabContent">
                                            <!-- Birthday Templates Tab -->
                                            <div class="tab-pane fade show active" id="birthday" role="tabpanel" aria-labelledby="birthday-tab">
                                                <div class="alert alert-info">
                                                    <i class="bi bi-info-circle"></i> Birthday emails are sent to members on their birthday. The system will randomly select one of the templates you select below.
                                                </div>
                                                
                                                <div class="row mb-3">
                                                    <div class="col-md-12">
                                                        <label for="birthdaySendTime" class="form-label">Send Time</label>
                                                        <input type="time" class="form-control" id="birthdaySendTime" name="birthday_send_time" value="<?php echo $settings['birthday']['send_time'] ?? '07:00:00'; ?>">
                                                        <div class="form-text">The time when birthday emails should be sent.</div>
                                                    </div>
                                                </div>
                                                
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <h5>Select Birthday Templates</h5>
                                                        <p>Choose multiple templates for auto-rotation:</p>
                                                        
                                                        <div class="table-responsive">
                                                            <table class="table table-hover">
                                                                <thead class="table-light">
                                                                    <tr>
                                                                        <th width="5%">Select</th>
                                                                        <th width="25%">Template Name</th>
                                                                        <th width="40%">Subject</th>
                                                                        <th width="30%">Actions</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <?php 
                                                                    $selected_birthday_ids = !empty($settings['birthday']['template_ids']) ? explode(',', $settings['birthday']['template_ids']) : [];
                                                                    
                                                                    foreach ($templates['birthday'] as $template): 
                                                                    ?>
                                                                    <tr>
                                                                        <td>
                                                                            <div class="form-check">
                                                                                <input class="form-check-input" type="checkbox" name="birthday_templates[]" id="birthday_template_<?php echo $template['id']; ?>" value="<?php echo $template['id']; ?>" <?php echo in_array($template['id'], $selected_birthday_ids) ? 'checked' : ''; ?>>
                                                                            </div>
                                                                        </td>
                                                                        <td><?php echo htmlspecialchars($template['template_name']); ?></td>
                                                                        <td><?php echo htmlspecialchars($template['subject']); ?></td>
                                                                        <td>
                                                                            <button type="button" class="btn btn-sm btn-primary preview-btn" data-template-id="<?php echo $template['id']; ?>">
                                                                                <i class="bi bi-eye"></i> Preview
                                                                            </button>
                                                                            <a href="email_templates.php?edit_id=<?php echo $template['id']; ?>" class="btn btn-sm btn-secondary">
                                                                                <i class="bi bi-pencil"></i> Edit
                                                                            </a>
                                                                        </td>
                                                                    </tr>
                                                                    <?php endforeach; ?>
                                                                    
                                                                    <?php if (empty($templates['birthday'])): ?>
                                                                    <tr>
                                                                        <td colspan="4" class="text-center">No birthday templates found. <a href="email_templates.php">Create one</a>.</td>
                                                                    </tr>
                                                                    <?php endif; ?>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                        <a href="email_templates.php" class="btn btn-success">
                                                            <i class="bi bi-plus-circle"></i> Create New Birthday Template
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Reminder Templates Tab -->
                                            <div class="tab-pane fade" id="reminder" role="tabpanel" aria-labelledby="reminder-tab">
                                                <div class="alert alert-info">
                                                    <i class="bi bi-info-circle"></i> Birthday reminder emails are sent to members before their birthday. The system will randomly select one of the templates you select below.
                                                </div>
                                                
                                                <div class="row mb-3">
                                                    <div class="col-md-6">
                                                        <label for="reminderSendTime" class="form-label">Send Time</label>
                                                        <input type="time" class="form-control" id="reminderSendTime" name="reminder_send_time" value="<?php echo $settings['reminder']['send_time'] ?? '07:00:00'; ?>">
                                                        <div class="form-text">The time when reminder emails should be sent.</div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label for="daysBefore" class="form-label">Days Before Birthday</label>
                                                        <input type="number" class="form-control" id="daysBefore" name="days_before" min="1" max="7" value="<?php echo $settings['reminder']['days_before'] ?? 3; ?>">
                                                        <div class="form-text">Number of days before birthday to send the reminder (1-7 days).</div>
                                                    </div>
                                                </div>
                                                
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <h5>Select Reminder Templates</h5>
                                                        <p>Choose multiple templates for auto-rotation:</p>
                                                        
                                                        <div class="table-responsive">
                                                            <table class="table table-hover">
                                                                <thead class="table-light">
                                                                    <tr>
                                                                        <th width="5%">Select</th>
                                                                        <th width="25%">Template Name</th>
                                                                        <th width="40%">Subject</th>
                                                                        <th width="30%">Actions</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <?php 
                                                                    $selected_reminder_ids = !empty($settings['reminder']['template_ids']) ? explode(',', $settings['reminder']['template_ids']) : [];
                                                                    
                                                                    foreach ($templates['reminder'] as $template): 
                                                                    ?>
                                                                    <tr>
                                                                        <td>
                                                                            <div class="form-check">
                                                                                <input class="form-check-input" type="checkbox" name="reminder_templates[]" id="reminder_template_<?php echo $template['id']; ?>" value="<?php echo $template['id']; ?>" <?php echo in_array($template['id'], $selected_reminder_ids) ? 'checked' : ''; ?>>
                                                                            </div>
                                                                        </td>
                                                                        <td><?php echo htmlspecialchars($template['template_name']); ?></td>
                                                                        <td><?php echo htmlspecialchars($template['subject']); ?></td>
                                                                        <td>
                                                                            <button type="button" class="btn btn-sm btn-primary preview-btn" data-template-id="<?php echo $template['id']; ?>">
                                                                                <i class="bi bi-eye"></i> Preview
                                                                            </button>
                                                                            <a href="email_templates.php?edit_id=<?php echo $template['id']; ?>" class="btn btn-sm btn-secondary">
                                                                                <i class="bi bi-pencil"></i> Edit
                                                                            </a>
                                                                        </td>
                                                                    </tr>
                                                                    <?php endforeach; ?>
                                                                    
                                                                    <?php if (empty($templates['reminder'])): ?>
                                                                    <tr>
                                                                        <td colspan="4" class="text-center">No reminder templates found. <a href="email_templates.php">Create one</a>.</td>
                                                                    </tr>
                                                                    <?php endif; ?>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                        <a href="email_templates.php" class="btn btn-success">
                                                            <i class="bi bi-plus-circle"></i> Create New Reminder Template
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Birthday Notifications Tab -->
                                            <div class="tab-pane fade" id="notification" role="tabpanel" aria-labelledby="notification-tab">
                                                <div class="alert alert-info">
                                                    <i class="bi bi-info-circle"></i> Birthday notifications are sent to all members to inform them about other members' upcoming birthdays. The system will randomly select one of the templates you select below.
                                                </div>
                                                
                                                <div class="row mb-3">
                                                    <div class="col-md-6">
                                                        <label for="notificationSendTime" class="form-label">Send Time</label>
                                                        <input type="time" class="form-control" id="notificationSendTime" name="notification_send_time" value="<?php echo $settings['notification']['send_time'] ?? '07:00:00'; ?>">
                                                        <div class="form-text">The time when birthday notifications should be sent to other members.</div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label for="notificationDaysBefore" class="form-label">Days Before Birthday</label>
                                                        <input type="number" class="form-control" id="notificationDaysBefore" name="notification_days_before" min="1" max="7" value="<?php echo $settings['notification']['days_before'] ?? 3; ?>">
                                                        <div class="form-text">Number of days before birthday to send notifications to other members (1-7 days).</div>
                                                    </div>
                                                </div>
                                                
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <h5>Select Notification Templates</h5>
                                                        <p>Choose multiple templates for auto-rotation:</p>
                                                        
                                                        <div class="table-responsive">
                                                            <table class="table table-hover">
                                                                <thead class="table-light">
                                                                    <tr>
                                                                        <th width="5%">Select</th>
                                                                        <th width="25%">Template Name</th>
                                                                        <th width="40%">Subject</th>
                                                                        <th width="30%">Actions</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <?php 
                                                                    $selected_notification_ids = !empty($settings['notification']['template_ids']) ? explode(',', $settings['notification']['template_ids']) : [];
                                                                    
                                                                    // Debug log
                                                                    error_log("Selected notification IDs: " . (!empty($settings['notification']['template_ids']) ? $settings['notification']['template_ids'] : "none"));
                                                                    error_log("Notification settings: " . print_r($settings['notification'], true));
                                                                    
                                                                    foreach ($templates['notification'] as $template): 
                                                                    ?>
                                                                    <tr>
                                                                        <td>
                                                                            <div class="form-check">
                                                                                <input class="form-check-input" type="checkbox" name="notification_templates[]" id="notification_template_<?php echo $template['id']; ?>" value="<?php echo $template['id']; ?>" <?php echo in_array($template['id'], $selected_notification_ids) ? 'checked' : ''; ?>>
                                                                            </div>
                                                                        </td>
                                                                        <td><?php echo htmlspecialchars($template['template_name']); ?></td>
                                                                        <td><?php echo htmlspecialchars($template['subject']); ?></td>
                                                                        <td>
                                                                            <button type="button" class="btn btn-sm btn-primary preview-btn" data-template-id="<?php echo $template['id']; ?>">
                                                                                <i class="bi bi-eye"></i> Preview
                                                                            </button>
                                                                            <a href="email_templates.php?edit_id=<?php echo $template['id']; ?>" class="btn btn-sm btn-secondary">
                                                                                <i class="bi bi-pencil"></i> Edit
                                                                            </a>
                                                                        </td>
                                                                    </tr>
                                                                    <?php endforeach; ?>
                                                                    
                                                                    <?php if (empty($templates['notification'])): ?>
                                                                    <tr>
                                                                        <td colspan="4" class="text-center">No notification templates found. <a href="email_templates.php">Create one</a>.</td>
                                                                    </tr>
                                                                    <?php endif; ?>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                        <a href="email_templates.php" class="btn btn-success">
                                                            <i class="bi bi-plus-circle"></i> Create New Notification Template
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Welcome Templates Tab -->
                                            <div class="tab-pane fade" id="welcome" role="tabpanel" aria-labelledby="welcome-tab">
                                                <div class="alert alert-info">
                                                    <i class="bi bi-info-circle"></i> Welcome emails are sent to new members when they are added to the system. The system will randomly select one of the templates you select below.
                                                </div>
                                                
                                                <div class="row mb-3">
                                                    <div class="col-md-12">
                                                        <label for="welcomeSendTime" class="form-label">Send Time</label>
                                                        <input type="time" class="form-control" id="welcomeSendTime" name="welcome_send_time" value="<?php echo $settings['welcome']['send_time'] ?? '07:00:00'; ?>">
                                                        <div class="form-text">The time when welcome emails should be sent (for scheduled welcome emails).</div>
                                                    </div>
                                                </div>
                                                
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <h5>Select Welcome Templates</h5>
                                                        <p>Choose multiple templates for auto-rotation:</p>
                                                        
                                                        <div class="table-responsive">
                                                            <table class="table table-hover">
                                                                <thead class="table-light">
                                                                    <tr>
                                                                        <th width="5%">Select</th>
                                                                        <th width="25%">Template Name</th>
                                                                        <th width="40%">Subject</th>
                                                                        <th width="30%">Actions</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <?php 
                                                                    $selected_welcome_ids = !empty($settings['welcome']['template_ids']) ? explode(',', $settings['welcome']['template_ids']) : [];
                                                                    
                                                                    foreach ($templates['welcome'] as $template): 
                                                                    ?>
                                                                    <tr>
                                                                        <td>
                                                                            <div class="form-check">
                                                                                <input class="form-check-input" type="checkbox" name="welcome_templates[]" id="welcome_template_<?php echo $template['id']; ?>" value="<?php echo $template['id']; ?>" <?php echo in_array($template['id'], $selected_welcome_ids) ? 'checked' : ''; ?>>
                                                                            </div>
                                                                        </td>
                                                                        <td><?php echo htmlspecialchars($template['template_name']); ?></td>
                                                                        <td><?php echo htmlspecialchars($template['subject']); ?></td>
                                                                        <td>
                                                                            <button type="button" class="btn btn-sm btn-primary preview-btn" data-template-id="<?php echo $template['id']; ?>">
                                                                                <i class="bi bi-eye"></i> Preview
                                                                            </button>
                                                                            <a href="email_templates.php?edit_id=<?php echo $template['id']; ?>" class="btn btn-sm btn-secondary">
                                                                                <i class="bi bi-pencil"></i> Edit
                                                                            </a>
                                                                        </td>
                                                                    </tr>
                                                                    <?php endforeach; ?>
                                                                    
                                                                    <?php if (empty($templates['welcome'])): ?>
                                                                    <tr>
                                                                        <td colspan="4" class="text-center">No welcome templates found. <a href="email_templates.php">Create one</a>.</td>
                                                                    </tr>
                                                                    <?php endif; ?>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                        <a href="email_templates.php" class="btn btn-success">
                                                            <i class="bi bi-plus-circle"></i> Create New Welcome Template
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row mt-4">
                                    <div class="col-md-12 text-center">
                                        <button type="submit" class="btn btn-primary btn-lg">
                                            <i class="bi bi-save"></i> Save Settings
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>


<!-- Template Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">Template Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <h6>Subject:</h6>
                    <div id="previewSubject" class="p-2 border rounded"></div>
                </div>
                <div>
                    <h6>Content:</h6>
                    <div id="previewContent" class="p-2 border rounded bg-light"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Custom JavaScript for page functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle template preview
    const previewBtns = document.querySelectorAll('.preview-btn');
    previewBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const templateId = this.getAttribute('data-template-id');
            previewTemplate(templateId);
        });
    });
    
    function previewTemplate(templateId) {
        fetch('ajax/get_template_preview.php?id=' + templateId)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('previewSubject').textContent = data.preview.subject;
                    document.getElementById('previewContent').innerHTML = data.preview.content;
                    const previewModal = new bootstrap.Modal(document.getElementById('previewModal'));
                    previewModal.show();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while loading the preview.');
            });
    }
});
</script>

<?php include_once 'includes/footer.php'; ?> 