<?php
// Debug script to check login credentials and permissions
require_once '../config.php';
require_once 'includes/rbac_access_control_granular.php';

echo "<h2>Debug Login and Permissions</h2>";

// Check database connection and tables
echo "<h3>Database Connection Test:</h3>";
try {
    $stmt = $pdo->query("SELECT DATABASE() as current_db");
    $db_info = $stmt->fetch();
    echo "<p>Connected to database: " . htmlspecialchars($db_info['current_db']) . "</p>";

    // Check if admin_users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'admin_users'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ admin_users table exists</p>";
    } else {
        echo "<p style='color: red;'>✗ admin_users table does not exist</p>";

        // Show all tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll();
        echo "<p>Available tables:</p><ul>";
        foreach ($tables as $table) {
            $table_name = array_values($table)[0];
            echo "<li>" . htmlspecialchars($table_name) . "</li>";
        }
        echo "</ul>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Database connection error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Check admin users
echo "<h3>Admin Users in Database:</h3>";
try {
    $stmt = $pdo->query("SELECT id, username, email, created_at FROM admins ORDER BY id");
    $users = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Created</th><th>Test Login</th></tr>";
    
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($user['id']) . "</td>";
        echo "<td>" . htmlspecialchars($user['username']) . "</td>";
        echo "<td>" . htmlspecialchars($user['email']) . "</td>";
        echo "<td>" . htmlspecialchars($user['created_at']) . "</td>";
        
        // Test common passwords
        $test_passwords = ['password', 'admin123', $user['username'], $user['username'] . '123'];
        $login_success = false;
        
        foreach ($test_passwords as $test_pass) {
            $stmt_login = $pdo->prepare("SELECT id, password FROM admins WHERE username = ?");
            $stmt_login->execute([$user['username']]);
            $login_user = $stmt_login->fetch();
            
            if ($login_user && password_verify($test_pass, $login_user['password'])) {
                echo "<td style='color: green;'>✓ Password: " . htmlspecialchars($test_pass) . "</td>";
                $login_success = true;
                break;
            }
        }
        
        if (!$login_success) {
            echo "<td style='color: red;'>✗ Password not found</td>";
        }
        
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}

// Check permissions for admin67
echo "<h3>Permissions for admin67:</h3>";
try {
    $stmt = $pdo->prepare("SELECT id FROM admins WHERE username = 'admin67'");
    $stmt->execute();
    $admin67 = $stmt->fetch();
    
    if ($admin67) {
        $user_id = $admin67['id'];
        echo "<p>User ID: " . $user_id . "</p>";
        
        // Get all permissions for admin67
        $stmt = $pdo->prepare("
            SELECT gp.permission_key, gp.permission_name
            FROM user_individual_permissions uip
            JOIN granular_permissions gp ON uip.permission_id = gp.id
            WHERE uip.user_id = ?
            AND uip.is_active = 1
            AND gp.is_active = 1
            ORDER BY gp.permission_key
        ");
        $stmt->execute([$user_id]);
        $permissions = $stmt->fetchAll();
        
        echo "<p>Total permissions: " . count($permissions) . "</p>";
        
        if (count($permissions) > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Permission Key</th><th>Permission Name</th></tr>";

            foreach ($permissions as $perm) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($perm['permission_key']) . "</td>";
                echo "<td>" . htmlspecialchars($perm['permission_name']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Test specific permissions
        echo "<h4>Permission Tests for admin67:</h4>";
        $test_permissions = [
            'dashboard.view',
            'dashboard.analytics', 
            'members.view',
            'members.skills',
            'requests.view',
            'email.templates',
            'email.birthday_messages'
        ];
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Permission</th><th>Has Permission</th></tr>";
        
        foreach ($test_permissions as $perm) {
            $has_perm = hasUserPermission($user_id, $perm);
            echo "<tr>";
            echo "<td>" . htmlspecialchars($perm) . "</td>";
            echo "<td style='color: " . ($has_perm ? 'green' : 'red') . ";'>" . ($has_perm ? '✓ Yes' : '✗ No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p>admin67 user not found!</p>";
    }
    
} catch (Exception $e) {
    echo "Error checking admin67 permissions: " . $e->getMessage();
}

// Check granular permissions table
echo "<h3>Granular Permissions Available:</h3>";
try {
    $stmt = $pdo->query("
        SELECT gp.permission_key, gp.permission_name, pc.category_name
        FROM granular_permissions gp
        JOIN permission_categories pc ON gp.category_id = pc.id
        WHERE gp.is_active = 1
        ORDER BY pc.sort_order, gp.permission_key
    ");
    $all_permissions = $stmt->fetchAll();
    
    echo "<p>Total available permissions: " . count($all_permissions) . "</p>";
    
    if (count($all_permissions) > 0) {
        $current_category = '';
        foreach ($all_permissions as $perm) {
            if ($current_category != $perm['category_name']) {
                if ($current_category != '') echo "</ul>";
                echo "<h4>" . htmlspecialchars($perm['category_name']) . "</h4><ul>";
                $current_category = $perm['category_name'];
            }
            echo "<li><strong>" . htmlspecialchars($perm['permission_key']) . "</strong> - " . htmlspecialchars($perm['permission_name']) . "</li>";
        }
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "Error checking available permissions: " . $e->getMessage();
}

?>
<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { width: 100%; }
    th, td { padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>
