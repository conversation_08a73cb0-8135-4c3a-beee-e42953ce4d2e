<?php
/**
 * My Sessions Interface
 * 
 * Shows all sessions the user is registered for across all events
 */

session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$message = '';
$error = '';

// Handle session unregistration
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'unregister_session') {
        $session_id = (int)$_POST['session_id'];
        
        try {
            $stmt = $pdo->prepare("
                DELETE FROM session_attendance 
                WHERE session_id = ? AND member_id = ?
            ");
            $stmt->execute([$session_id, $userId]);
            
            $message = "Successfully unregistered from the session.";
            
            // Redirect to prevent form resubmission
            header("Location: my_sessions.php?unregistered=1");
            exit();
            
        } catch (PDOException $e) {
            $error = "Error unregistering from session: " . $e->getMessage();
        }
    }
}

// Check for success messages from redirect
if (isset($_GET['unregistered']) && $_GET['unregistered'] == '1') {
    $message = "Successfully unregistered from the session.";
}

// Get all sessions the user is registered for
$registeredSessions = [];
try {
    $stmt = $pdo->prepare("
        SELECT es.*, e.title as event_title, e.event_date as event_date, e.location as event_location,
               sa.attendance_status, sa.registration_date, sa.attendance_date, sa.notes,
               COUNT(sa2.id) as total_registered
        FROM session_attendance sa
        JOIN event_sessions es ON sa.session_id = es.id
        JOIN events e ON es.event_id = e.id
        LEFT JOIN session_attendance sa2 ON es.id = sa2.session_id
        WHERE sa.member_id = ?
        GROUP BY es.id, sa.id
        ORDER BY es.start_datetime ASC
    ");
    $stmt->execute([$userId]);
    $registeredSessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error loading user sessions: " . $e->getMessage());
}

// Separate upcoming and past sessions
$upcomingSessions = [];
$pastSessions = [];
$currentTime = time();

foreach ($registeredSessions as $session) {
    if (strtotime($session['start_datetime']) >= $currentTime) {
        $upcomingSessions[] = $session;
    } else {
        $pastSessions[] = $session;
    }
}

// Get site settings for branding
$sitename = get_organization_name() . ' - My Sessions';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$site_name_result = $stmt->fetch(PDO::FETCH_ASSOC);
if ($site_name_result) {
    $sitename = $site_name_result['setting_value'] . ' - My Sessions';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Sessions - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="../css/promotional-materials.css">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        .session-details {
            font-size: 0.9rem;
        }

        .session-details i {
            width: 16px;
        }

        .border-primary {
            border-color: #0d6efd !important;
            border-width: 2px !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>
?>

<div class="container mt-4">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">
            <i class="bi bi-collection"></i> My Sessions
        </h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <a href="dashboard.php" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Dashboard
            </a>
            <a href="events.php" class="btn btn-outline-primary ms-2">
                <i class="bi bi-calendar-event"></i> Browse Events
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary"><?php echo count($registeredSessions); ?></h3>
                    <p class="card-text">Total Sessions</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-info"><?php echo count($upcomingSessions); ?></h3>
                    <p class="card-text">Upcoming</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success">
                        <?php 
                        $attendedCount = count(array_filter($pastSessions, function($session) {
                            return $session['attendance_status'] === 'attended';
                        }));
                        echo $attendedCount;
                        ?>
                    </h3>
                    <p class="card-text">Attended</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-secondary"><?php echo count($pastSessions); ?></h3>
                    <p class="card-text">Past Sessions</p>
                </div>
            </div>
        </div>
    </div>

    <?php if (empty($registeredSessions)): ?>
        <!-- No Sessions -->
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="bi bi-collection display-1 text-muted"></i>
                <h4 class="mt-3">No Session Registrations</h4>
                <p class="text-muted">You haven't registered for any sessions yet.</p>
                <p class="text-muted">Browse events and register for sessions that interest you!</p>
                <a href="events.php" class="btn btn-primary mt-3">
                    <i class="bi bi-calendar-event"></i> Browse Events
                </a>
            </div>
        </div>
    <?php else: ?>
        <!-- Upcoming Sessions -->
        <?php if (!empty($upcomingSessions)): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-calendar-plus"></i> Upcoming Sessions
                        <span class="badge bg-primary ms-2"><?php echo count($upcomingSessions); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($upcomingSessions as $session): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card h-100 border-primary">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0"><?php echo htmlspecialchars($session['session_title']); ?></h6>
                                        <span class="badge bg-primary">Upcoming</span>
                                    </div>
                                    <div class="card-body">
                                        <p class="text-muted mb-2">
                                            <strong>Event:</strong> <?php echo htmlspecialchars($session['event_title']); ?>
                                        </p>
                                        
                                        <div class="session-details">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="bi bi-calendar3 text-primary me-2"></i>
                                                <span><?php echo date('M j, Y', strtotime($session['start_datetime'])); ?></span>
                                            </div>
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="bi bi-clock text-primary me-2"></i>
                                                <span>
                                                    <?php echo date('g:i A', strtotime($session['start_datetime'])); ?> - 
                                                    <?php echo date('g:i A', strtotime($session['end_datetime'])); ?>
                                                </span>
                                            </div>
                                            <?php if (!empty($session['location'])): ?>
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bi bi-geo-alt text-primary me-2"></i>
                                                    <span><?php echo htmlspecialchars($session['location']); ?></span>
                                                </div>
                                            <?php endif; ?>
                                            <?php if (!empty($session['instructor_name'])): ?>
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bi bi-person text-primary me-2"></i>
                                                    <span><?php echo htmlspecialchars($session['instructor_name']); ?></span>
                                                </div>
                                            <?php endif; ?>
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="bi bi-people text-primary me-2"></i>
                                                <span><?php echo $session['total_registered']; ?> registered</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                Registered: <?php echo date('M j', strtotime($session['registration_date'])); ?>
                                            </small>
                                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="unregisterSession(<?php echo $session['id']; ?>, '<?php echo htmlspecialchars($session['session_title'], ENT_QUOTES); ?>')">
                                                <i class="bi bi-x-circle"></i> Unregister
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Past Sessions -->
        <?php if (!empty($pastSessions)): ?>
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-clock-history"></i> Past Sessions
                        <span class="badge bg-secondary ms-2"><?php echo count($pastSessions); ?></span>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Session</th>
                                    <th>Event</th>
                                    <th>Date & Time</th>
                                    <th>Attendance</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pastSessions as $session): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($session['session_title']); ?></strong>
                                            <?php if (!empty($session['instructor_name'])): ?>
                                                <br><small class="text-muted">with <?php echo htmlspecialchars($session['instructor_name']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($session['event_title']); ?></td>
                                        <td>
                                            <?php echo date('M j, Y', strtotime($session['start_datetime'])); ?><br>
                                            <small class="text-muted"><?php echo date('g:i A', strtotime($session['start_datetime'])); ?></small>
                                        </td>
                                        <td>
                                            <?php
                                            $status_class = '';
                                            $status_icon = '';
                                            switch ($session['attendance_status']) {
                                                case 'attended':
                                                    $status_class = 'bg-success';
                                                    $status_icon = 'bi-check-circle';
                                                    break;
                                                case 'no_show':
                                                    $status_class = 'bg-warning';
                                                    $status_icon = 'bi-x-circle';
                                                    break;
                                                default:
                                                    $status_class = 'bg-secondary';
                                                    $status_icon = 'bi-clock';
                                            }
                                            ?>
                                            <span class="badge <?php echo $status_class; ?>">
                                                <i class="bi <?php echo $status_icon; ?>"></i> 
                                                <?php echo ucfirst(str_replace('_', ' ', $session['attendance_status'])); ?>
                                            </span>
                                            <?php if ($session['attendance_date']): ?>
                                                <br><small class="text-muted"><?php echo date('M j, g:i A', strtotime($session['attendance_date'])); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($session['notes'] ?: '-'); ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<!-- Unregister Session Modal -->
<div class="modal fade" id="unregisterSessionModal" tabindex="-1" aria-labelledby="unregisterSessionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="unregisterSessionModalLabel">
                    <i class="bi bi-exclamation-triangle text-warning"></i> Unregister from Session
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="unregisterSessionForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="unregister_session">
                    <input type="hidden" name="session_id" id="unregister_session_id">

                    <p>Are you sure you want to unregister from the session "<strong id="unregister_session_title"></strong>"?</p>

                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Please Note:</strong>
                        <ul class="mb-0 mt-2">
                            <li>This will remove your registration from the session</li>
                            <li>Your spot may be given to someone on the waitlist</li>
                            <li>You can register again if spots are available</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-x-circle"></i> Unregister
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.session-details {
    font-size: 0.9rem;
}

.session-details i {
    width: 16px;
}

.border-primary {
    border-color: #0d6efd !important;
    border-width: 2px !important;
}
</style>

<script>
function unregisterSession(sessionId, sessionTitle) {
    document.getElementById('unregister_session_id').value = sessionId;
    document.getElementById('unregister_session_title').textContent = sessionTitle;

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('unregisterSessionModal'));
    modal.show();
}

// Auto-dismiss alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert-dismissible');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
});
</script>

<!-- Footer -->
<?php include 'includes/footer.php'; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
