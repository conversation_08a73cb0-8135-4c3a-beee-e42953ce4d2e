<?php
/**
 * Comprehensive WebSocket System Test Suite
 * Tests all aspects of the real-time WebSocket implementation
 */

require_once '../config.php';
require_once '../includes/auth_check.php';

$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    header('Location: ../events.php');
    exit();
}

// Get event details
$stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
$stmt->execute([$event_id]);
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    header('Location: ../events.php');
    exit();
}

$test_results = [];
$test_passed = 0;
$test_failed = 0;

/**
 * Comprehensive Test Suite for WebSocket System
 */
function runWebSocketTests($pdo, $event_id) {
    global $test_results, $test_passed, $test_failed;
    
    // Test 1: Database structure validation
    $test_results[] = testDatabaseStructure($pdo);
    
    // Test 2: WebSocket server configuration
    $test_results[] = testServerConfiguration($pdo);
    
    // Test 3: Client library functionality
    $test_results[] = testClientLibrary();
    
    // Test 4: Real-time activity logging
    $test_results[] = testActivityLogging($pdo, $event_id);
    
    // Test 5: Connection management
    $test_results[] = testConnectionManagement($pdo);
    
    // Test 6: Message broadcasting simulation
    $test_results[] = testMessageBroadcasting($pdo, $event_id);
    
    // Test 7: Integration with existing systems
    $test_results[] = testSystemIntegration($pdo, $event_id);
    
    // Count results
    foreach ($test_results as $result) {
        if ($result['status'] === 'PASS') {
            $test_passed++;
        } else {
            $test_failed++;
        }
    }
}

function testDatabaseStructure($pdo) {
    try {
        $required_tables = [
            'realtime_activity_log',
            'websocket_connections',
            'realtime_session_status',
            'realtime_notifications',
            'websocket_server_config'
        ];
        
        $existing_tables = [];
        foreach ($required_tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $existing_tables[] = $table;
            }
        }
        
        $missing_tables = array_diff($required_tables, $existing_tables);
        
        if (empty($missing_tables)) {
            // Test table structures
            $structure_tests = [];
            
            // Test realtime_activity_log structure
            $stmt = $pdo->query("DESCRIBE realtime_activity_log");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            $structure_tests['activity_log'] = in_array('action_data', $columns) && in_array('event_id', $columns);
            
            // Test websocket_connections structure
            $stmt = $pdo->query("DESCRIBE websocket_connections");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            $structure_tests['connections'] = in_array('connection_id', $columns) && in_array('last_heartbeat', $columns);
            
            $all_structures_valid = !in_array(false, $structure_tests);
            
            return [
                'test' => 'Database Structure Validation',
                'status' => $all_structures_valid ? 'PASS' : 'FAIL',
                'message' => $all_structures_valid ? 'All required tables and columns exist' : 'Some table structures are invalid',
                'details' => [
                    'existing_tables' => $existing_tables,
                    'structure_tests' => $structure_tests
                ]
            ];
        } else {
            return [
                'test' => 'Database Structure Validation',
                'status' => 'FAIL',
                'message' => 'Missing required tables: ' . implode(', ', $missing_tables),
                'details' => ['missing_tables' => $missing_tables]
            ];
        }
        
    } catch (Exception $e) {
        return [
            'test' => 'Database Structure Validation',
            'status' => 'FAIL',
            'message' => 'Database error: ' . $e->getMessage(),
            'details' => []
        ];
    }
}

function testServerConfiguration($pdo) {
    try {
        // Check server configuration
        $stmt = $pdo->query("SELECT * FROM websocket_server_config");
        $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $required_configs = ['server_port', 'max_connections', 'heartbeat_interval'];
        $existing_configs = array_column($configs, 'config_key');
        
        $missing_configs = array_diff($required_configs, $existing_configs);
        
        // Test if server files exist
        $server_files = [
            '../websocket/realtime_server.php',
            '../js/realtime-websocket-client.js'
        ];
        
        $existing_files = [];
        foreach ($server_files as $file) {
            if (file_exists($file)) {
                $existing_files[] = $file;
            }
        }
        
        $all_configs_exist = empty($missing_configs);
        $all_files_exist = count($existing_files) === count($server_files);
        
        return [
            'test' => 'WebSocket Server Configuration',
            'status' => ($all_configs_exist && $all_files_exist) ? 'PASS' : 'FAIL',
            'message' => $all_configs_exist && $all_files_exist ? 'Server configuration and files are ready' : 'Missing configuration or files',
            'details' => [
                'configs' => $configs,
                'missing_configs' => $missing_configs,
                'existing_files' => $existing_files,
                'server_files' => $server_files
            ]
        ];
        
    } catch (Exception $e) {
        return [
            'test' => 'WebSocket Server Configuration',
            'status' => 'FAIL',
            'message' => 'Configuration test error: ' . $e->getMessage(),
            'details' => []
        ];
    }
}

function testClientLibrary() {
    try {
        $client_file = '../js/realtime-websocket-client.js';
        
        if (!file_exists($client_file)) {
            return [
                'test' => 'Client Library Functionality',
                'status' => 'FAIL',
                'message' => 'Client library file not found',
                'details' => []
            ];
        }
        
        $client_content = file_get_contents($client_file);
        
        // Check for required methods
        $required_methods = [
            'connect',
            'disconnect',
            'authenticate',
            'joinEvent',
            'sendAttendanceUpdate',
            'handleMessage'
        ];
        
        $methods_found = [];
        foreach ($required_methods as $method) {
            if (strpos($client_content, $method) !== false) {
                $methods_found[] = $method;
            }
        }
        
        $missing_methods = array_diff($required_methods, $methods_found);
        
        // Check for class definition
        $has_class = strpos($client_content, 'class ChurchEventWebSocketClient') !== false;
        
        $all_methods_exist = empty($missing_methods);
        
        return [
            'test' => 'Client Library Functionality',
            'status' => ($all_methods_exist && $has_class) ? 'PASS' : 'FAIL',
            'message' => $all_methods_exist && $has_class ? 'Client library has all required methods' : 'Client library is incomplete',
            'details' => [
                'has_class' => $has_class,
                'methods_found' => $methods_found,
                'missing_methods' => $missing_methods
            ]
        ];
        
    } catch (Exception $e) {
        return [
            'test' => 'Client Library Functionality',
            'status' => 'FAIL',
            'message' => 'Client library test error: ' . $e->getMessage(),
            'details' => []
        ];
    }
}

function testActivityLogging($pdo, $event_id) {
    try {
        // Test creating activity log entries
        $test_activities = [
            [
                'event_id' => $event_id,
                'user_id' => 1,
                'action_type' => 'attendance_update',
                'action_data' => json_encode(['test' => 'data'])
            ],
            [
                'event_id' => $event_id,
                'user_id' => 1,
                'action_type' => 'session_status_update',
                'action_data' => json_encode(['status' => 'active'])
            ]
        ];
        
        $created_activities = 0;
        foreach ($test_activities as $activity) {
            $stmt = $pdo->prepare("
                INSERT INTO realtime_activity_log (event_id, user_id, action_type, action_data)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([
                $activity['event_id'],
                $activity['user_id'],
                $activity['action_type'],
                $activity['action_data']
            ]);
            $created_activities++;
        }
        
        // Test retrieving activities
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM realtime_activity_log 
            WHERE event_id = ? AND action_type IN ('attendance_update', 'session_status_update')
        ");
        $stmt->execute([$event_id]);
        $activity_count = $stmt->fetchColumn();
        
        return [
            'test' => 'Real-Time Activity Logging',
            'status' => ($created_activities > 0 && $activity_count >= $created_activities) ? 'PASS' : 'FAIL',
            'message' => "Created $created_activities activities, found $activity_count in database",
            'details' => [
                'created_activities' => $created_activities,
                'found_activities' => $activity_count
            ]
        ];
        
    } catch (Exception $e) {
        return [
            'test' => 'Real-Time Activity Logging',
            'status' => 'FAIL',
            'message' => 'Activity logging error: ' . $e->getMessage(),
            'details' => []
        ];
    }
}

function testConnectionManagement($pdo) {
    try {
        // Test creating connection records
        $test_connections = [
            [
                'connection_id' => 'test_conn_1',
                'user_id' => 1,
                'user_name' => 'Test User 1',
                'user_type' => 'admin'
            ],
            [
                'connection_id' => 'test_conn_2',
                'user_id' => 2,
                'user_name' => 'Test User 2',
                'user_type' => 'staff'
            ]
        ];
        
        $created_connections = 0;
        foreach ($test_connections as $conn) {
            $stmt = $pdo->prepare("
                INSERT INTO websocket_connections (connection_id, user_id, user_name, user_type)
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE last_heartbeat = NOW()
            ");
            $stmt->execute([
                $conn['connection_id'],
                $conn['user_id'],
                $conn['user_name'],
                $conn['user_type']
            ]);
            $created_connections++;
        }
        
        // Test retrieving active connections
        $stmt = $pdo->query("SELECT COUNT(*) FROM websocket_connections WHERE is_active = 1");
        $active_connections = $stmt->fetchColumn();
        
        // Test cleanup procedure
        $stmt = $pdo->query("CALL CleanupOldConnections()");
        
        return [
            'test' => 'Connection Management',
            'status' => ($created_connections > 0 && $active_connections >= 0) ? 'PASS' : 'FAIL',
            'message' => "Created $created_connections connections, $active_connections active",
            'details' => [
                'created_connections' => $created_connections,
                'active_connections' => $active_connections
            ]
        ];
        
    } catch (Exception $e) {
        return [
            'test' => 'Connection Management',
            'status' => 'FAIL',
            'message' => 'Connection management error: ' . $e->getMessage(),
            'details' => []
        ];
    }
}

function testMessageBroadcasting($pdo, $event_id) {
    try {
        // Test creating notifications for broadcasting
        $test_notifications = [
            [
                'event_id' => $event_id,
                'notification_type' => 'attendance_conflict',
                'title' => 'Test Attendance Conflict',
                'message' => 'Test message for attendance conflict',
                'created_by' => 1,
                'priority' => 'high'
            ],
            [
                'event_id' => $event_id,
                'notification_type' => 'capacity_alert',
                'title' => 'Test Capacity Alert',
                'message' => 'Test message for capacity alert',
                'created_by' => 1,
                'priority' => 'medium'
            ]
        ];
        
        $created_notifications = 0;
        foreach ($test_notifications as $notification) {
            $stmt = $pdo->prepare("
                INSERT INTO realtime_notifications 
                (event_id, notification_type, title, message, created_by, priority)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $notification['event_id'],
                $notification['notification_type'],
                $notification['title'],
                $notification['message'],
                $notification['created_by'],
                $notification['priority']
            ]);
            $created_notifications++;
        }
        
        // Test retrieving notifications
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM realtime_notifications 
            WHERE event_id = ? AND is_read = 0
        ");
        $stmt->execute([$event_id]);
        $unread_notifications = $stmt->fetchColumn();
        
        return [
            'test' => 'Message Broadcasting Simulation',
            'status' => ($created_notifications > 0 && $unread_notifications >= $created_notifications) ? 'PASS' : 'FAIL',
            'message' => "Created $created_notifications notifications, $unread_notifications unread",
            'details' => [
                'created_notifications' => $created_notifications,
                'unread_notifications' => $unread_notifications
            ]
        ];
        
    } catch (Exception $e) {
        return [
            'test' => 'Message Broadcasting Simulation',
            'status' => 'FAIL',
            'message' => 'Broadcasting test error: ' . $e->getMessage(),
            'details' => []
        ];
    }
}

function testSystemIntegration($pdo, $event_id) {
    try {
        // Test integration with existing dashboard
        $dashboard_file = '../realtime_dashboard.php';
        $dashboard_exists = file_exists($dashboard_file);
        
        if ($dashboard_exists) {
            $dashboard_content = file_get_contents($dashboard_file);
            $has_websocket_integration = strpos($dashboard_content, 'realtime-websocket-client.js') !== false;
            $has_enhanced_class = strpos($dashboard_content, 'EnhancedRealtimeDashboard') !== false;
        } else {
            $has_websocket_integration = false;
            $has_enhanced_class = false;
        }
        
        // Test if inheritance engine exists and can be integrated
        $inheritance_file = '../attendance_inheritance_engine.php';
        $inheritance_exists = file_exists($inheritance_file);
        
        // Test if mobile manager exists
        $mobile_file = '../mobile_session_manager.php';
        $mobile_exists = file_exists($mobile_file);
        
        $integration_score = 0;
        if ($dashboard_exists && $has_websocket_integration) $integration_score++;
        if ($has_enhanced_class) $integration_score++;
        if ($inheritance_exists) $integration_score++;
        if ($mobile_exists) $integration_score++;
        
        return [
            'test' => 'System Integration',
            'status' => ($integration_score >= 3) ? 'PASS' : 'FAIL',
            'message' => "Integration score: $integration_score/4 components ready",
            'details' => [
                'dashboard_exists' => $dashboard_exists,
                'has_websocket_integration' => $has_websocket_integration,
                'has_enhanced_class' => $has_enhanced_class,
                'inheritance_exists' => $inheritance_exists,
                'mobile_exists' => $mobile_exists,
                'integration_score' => $integration_score
            ]
        ];
        
    } catch (Exception $e) {
        return [
            'test' => 'System Integration',
            'status' => 'FAIL',
            'message' => 'Integration test error: ' . $e->getMessage(),
            'details' => []
        ];
    }
}

// Run tests if requested
if (isset($_GET['run_tests']) && $_GET['run_tests'] === '1') {
    runWebSocketTests($pdo, $event_id);
}

$page_title = 'WebSocket System Test Suite';
include '../includes/header.php';
?>

<style>
.websocket-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.test-result {
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.test-result.PASS {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
}

.test-result.FAIL {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
}

.websocket-demo {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.connection-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.connection-indicator.connected {
    background: #28a745;
    animation: pulse 2s infinite;
}

.connection-indicator.disconnected {
    background: #dc3545;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="websocket-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="bi bi-broadcast"></i> WebSocket System Test Suite</h1>
                        <p class="mb-0">
                            <strong><?php echo htmlspecialchars($event['title']); ?></strong> •
                            Comprehensive testing of real-time WebSocket functionality
                        </p>
                    </div>
                    <div>
                        <a href="../realtime_dashboard.php?event_id=<?php echo $event_id; ?>" class="btn btn-light me-2">
                            <i class="bi bi-speedometer2"></i> Real-Time Dashboard
                        </a>
                        <a href="../multi_session_dashboard.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-light">
                            <i class="bi bi-arrow-left"></i> Main Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <?php if (empty($test_results)): ?>
                <!-- Test Controls -->
                <div class="websocket-demo">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5><i class="bi bi-play-circle"></i> Run WebSocket System Tests</h5>
                            <p class="text-muted mb-0">
                                This will test all WebSocket components including database structure, server configuration,
                                client library, real-time messaging, and system integration.
                                <br><strong>Note:</strong> Tests will create sample data for validation.
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="?event_id=<?php echo $event_id; ?>&run_tests=1" class="btn btn-primary btn-lg">
                                <i class="bi bi-play-circle"></i> Run All Tests
                            </a>
                        </div>
                    </div>
                </div>

                <!-- WebSocket Demo -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-list-check"></i> Test Coverage</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-database text-primary"></i> Database Structure Validation</li>
                                    <li><i class="bi bi-server text-success"></i> WebSocket Server Configuration</li>
                                    <li><i class="bi bi-code-square text-info"></i> Client Library Functionality</li>
                                    <li><i class="bi bi-activity text-warning"></i> Real-Time Activity Logging</li>
                                    <li><i class="bi bi-people text-secondary"></i> Connection Management</li>
                                    <li><i class="bi bi-broadcast text-danger"></i> Message Broadcasting</li>
                                    <li><i class="bi bi-puzzle text-dark"></i> System Integration</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-broadcast"></i> WebSocket Connection Test</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">Connection Status</label>
                                    <div>
                                        <span class="connection-indicator disconnected" id="connectionIndicator"></span>
                                        <span id="connectionStatus">Not Connected</span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <button class="btn btn-outline-primary" onclick="testWebSocketConnection()">
                                        <i class="bi bi-wifi"></i> Test Connection
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="disconnectWebSocket()">
                                        <i class="bi bi-wifi-off"></i> Disconnect
                                    </button>
                                </div>
                                <div id="connectionLog" class="border rounded p-2" style="height: 200px; overflow-y: auto; background: #f8f9fa;">
                                    <small class="text-muted">Connection log will appear here...</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            <?php else: ?>
                <!-- Test Results -->
                <div class="websocket-demo">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <h3 class="text-success"><?php echo $test_passed; ?></h3>
                            <p class="mb-0">Tests Passed</p>
                        </div>
                        <div class="col-md-4">
                            <h3 class="text-danger"><?php echo $test_failed; ?></h3>
                            <p class="mb-0">Tests Failed</p>
                        </div>
                        <div class="col-md-4">
                            <h3 class="text-primary"><?php echo count($test_results); ?></h3>
                            <p class="mb-0">Total Tests</p>
                        </div>
                    </div>

                    <div class="text-center mt-3">
                        <?php if ($test_failed === 0): ?>
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle"></i> <strong>All Tests Passed!</strong>
                                The WebSocket system is ready for production use.
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i> <strong>Some Tests Failed</strong>
                                Please review the results below and fix any issues.
                            </div>
                        <?php endif; ?>

                        <a href="?event_id=<?php echo $event_id; ?>" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-clockwise"></i> Run Tests Again
                        </a>
                    </div>
                </div>

                <!-- Detailed Results -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-clipboard-data"></i> Detailed Test Results</h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($test_results as $result): ?>
                            <div class="test-result <?php echo $result['status']; ?>">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">
                                            <?php
                                            $icon = $result['status'] === 'PASS' ? 'bi-check-circle' : 'bi-x-circle';
                                            ?>
                                            <i class="bi <?php echo $icon; ?>"></i>
                                            <?php echo htmlspecialchars($result['test']); ?>
                                        </h6>
                                        <p class="mb-0"><?php echo htmlspecialchars($result['message']); ?></p>

                                        <?php if (!empty($result['details'])): ?>
                                            <details class="mt-2">
                                                <summary class="text-muted" style="cursor: pointer;">View Details</summary>
                                                <pre class="mt-2 p-2 bg-light rounded" style="font-size: 0.8rem;"><?php echo htmlspecialchars(json_encode($result['details'], JSON_PRETTY_PRINT)); ?></pre>
                                            </details>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <span class="badge bg-<?php echo $result['status'] === 'PASS' ? 'success' : 'danger'; ?>">
                                            <?php echo $result['status']; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Next Steps -->
                <?php if ($test_failed === 0): ?>
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-rocket"></i> Next Steps</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Start WebSocket Server</h6>
                                    <p class="text-muted">Run the WebSocket server to enable real-time features:</p>
                                    <code>php websocket/realtime_server.php</code>
                                </div>
                                <div class="col-md-6">
                                    <h6>Test Real-Time Dashboard</h6>
                                    <p class="text-muted">Open the enhanced dashboard to see WebSocket in action:</p>
                                    <a href="../realtime_dashboard.php?event_id=<?php echo $event_id; ?>" class="btn btn-primary">
                                        <i class="bi bi-speedometer2"></i> Open Dashboard
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- WebSocket Client for Testing -->
<script src="../js/realtime-websocket-client.js"></script>
<script>
let testWebSocketClient = null;

function testWebSocketConnection() {
    const indicator = document.getElementById('connectionIndicator');
    const status = document.getElementById('connectionStatus');
    const log = document.getElementById('connectionLog');

    function addLog(message) {
        const timestamp = new Date().toLocaleTimeString();
        log.innerHTML += `<div><small class="text-muted">[${timestamp}]</small> ${message}</div>`;
        log.scrollTop = log.scrollHeight;
    }

    addLog('Attempting to connect to WebSocket server...');

    try {
        testWebSocketClient = new ChurchEventWebSocketClient({
            serverUrl: 'ws://localhost:8080',
            debug: true,
            autoReconnect: false
        });

        testWebSocketClient.on('connected', () => {
            indicator.className = 'connection-indicator connected';
            status.textContent = 'Connected';
            addLog('<span class="text-success">✓ Connected to WebSocket server</span>');

            // Test authentication
            const userId = <?php echo $_SESSION['user_id'] ?? 'null'; ?>;
            if (userId) {
                testWebSocketClient.authenticate(userId, 'test_token')
                    .then(() => {
                        addLog('<span class="text-success">✓ Authentication successful</span>');
                        return testWebSocketClient.joinEvent(<?php echo $event_id; ?>);
                    })
                    .then(() => {
                        addLog('<span class="text-success">✓ Joined event successfully</span>');
                        status.textContent = 'Connected & Authenticated';
                    })
                    .catch(error => {
                        addLog(`<span class="text-warning">⚠ Authentication failed: ${error.message}</span>`);
                    });
            }
        });

        testWebSocketClient.on('disconnected', () => {
            indicator.className = 'connection-indicator disconnected';
            status.textContent = 'Disconnected';
            addLog('<span class="text-warning">⚠ Disconnected from server</span>');
        });

        testWebSocketClient.on('error', (error) => {
            indicator.className = 'connection-indicator disconnected';
            status.textContent = 'Connection Error';
            addLog(`<span class="text-danger">✗ Error: ${error.message || 'Connection failed'}</span>`);
        });

        testWebSocketClient.on('message', (data) => {
            addLog(`<span class="text-info">📨 Received: ${data.type}</span>`);
        });

        testWebSocketClient.connect();

    } catch (error) {
        addLog(`<span class="text-danger">✗ Failed to initialize: ${error.message}</span>`);
        indicator.className = 'connection-indicator disconnected';
        status.textContent = 'Initialization Failed';
    }
}

function disconnectWebSocket() {
    if (testWebSocketClient) {
        testWebSocketClient.disconnect();
        testWebSocketClient = null;
    }

    document.getElementById('connectionIndicator').className = 'connection-indicator disconnected';
    document.getElementById('connectionStatus').textContent = 'Disconnected';

    const log = document.getElementById('connectionLog');
    const timestamp = new Date().toLocaleTimeString();
    log.innerHTML += `<div><small class="text-muted">[${timestamp}]</small> <span class="text-secondary">Disconnected by user</span></div>`;
    log.scrollTop = log.scrollHeight;
}

// Auto-scroll to results if tests were run
<?php if (!empty($test_results)): ?>
document.addEventListener('DOMContentLoaded', function() {
    document.querySelector('.websocket-demo').scrollIntoView({ behavior: 'smooth' });
});
<?php endif; ?>
</script>

<?php include '../includes/footer.php'; ?>
