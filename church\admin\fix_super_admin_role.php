<?php
// Fix Super Admin Role and Permissions
require_once '../config.php';

echo "<h2>🔧 Super Admin Role Fix</h2>";

try {
    // 1. Check current admin roles
    echo "<h3>📋 Current Admin Roles:</h3>";
    $stmt = $pdo->query("SELECT id, username, full_name, role FROM admins ORDER BY id");
    $admins = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Full Name</th><th>Current Role</th></tr>";
    foreach ($admins as $admin) {
        echo "<tr>";
        echo "<td>" . $admin['id'] . "</td>";
        echo "<td>" . htmlspecialchars($admin['username']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['full_name']) . "</td>";
        echo "<td>" . ($admin['role'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. Check role column exists
    echo "<h3>🔧 Checking Role Column:</h3>";
    try {
        $stmt = $pdo->query("DESCRIBE admins");
        $columns = $stmt->fetchAll();
        $hasRoleColumn = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'role') {
                $hasRoleColumn = true;
                break;
            }
        }

        if ($hasRoleColumn) {
            echo "✅ Role column exists in admins table<br>";
        } else {
            $pdo->exec("ALTER TABLE admins ADD COLUMN role VARCHAR(50) DEFAULT 'staff'");
            echo "✅ Added role column to admins table<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error checking role column: " . $e->getMessage() . "<br>";
    }
    
    // 3. Set main admin (ID 4) as super_admin
    echo "<h3>👑 Setting Main Admin as Super Admin:</h3>";
    $stmt = $pdo->prepare("UPDATE admins SET role = 'super_admin' WHERE id = 4");
    $result = $stmt->execute();
    
    if ($result) {
        echo "✅ Successfully set admin ID 4 as super_admin<br>";
    } else {
        echo "❌ Failed to update admin role<br>";
    }
    
    // 4. Also set the first admin as super_admin as backup
    $stmt = $pdo->prepare("UPDATE admins SET role = 'super_admin' WHERE id = 1");
    $stmt->execute();
    echo "✅ Also set admin ID 1 as super_admin (backup)<br>";
    
    // 5. Check updated roles
    echo "<h3>📋 Updated Admin Roles:</h3>";
    $stmt = $pdo->query("SELECT id, username, full_name, role FROM admins ORDER BY id");
    $admins = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Full Name</th><th>Updated Role</th><th>Status</th></tr>";
    foreach ($admins as $admin) {
        $status = ($admin['role'] === 'super_admin') ? '👑 Super Admin' : '👤 Regular Admin';
        echo "<tr>";
        echo "<td>" . $admin['id'] . "</td>";
        echo "<td>" . htmlspecialchars($admin['username']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['full_name']) . "</td>";
        echo "<td>" . ($admin['role'] ?? 'NULL') . "</td>";
        echo "<td>" . $status . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 6. Test permission functions
    echo "<h3>🧪 Testing Permission Functions:</h3>";
    
    // Simulate super admin session
    session_start();
    $_SESSION['admin_id'] = 4;
    $_SESSION['admin_username'] = 'admin';
    $_SESSION['admin_role'] = 'super_admin';
    
    require_once 'includes/rbac_access_control_granular.php';
    
    $test_permissions = [
        'dashboard.view',
        'dashboard.analytics',
        'members.view',
        'events.view',
        'email.templates'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Permission</th><th>Result</th><th>Status</th></tr>";
    
    foreach ($test_permissions as $permission) {
        $result = hasUserPermission(4, $permission);
        $status = $result ? '✅ PASS' : '❌ FAIL';
        echo "<tr>";
        echo "<td>" . htmlspecialchars($permission) . "</td>";
        echo "<td>" . ($result ? 'true' : 'false') . "</td>";
        echo "<td>" . $status . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🎯 Summary:</h3>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>✅ Fixes Applied:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Verified role column in admins table</li>";
    echo "<li>✅ Set admin ID 4 as super_admin</li>";
    echo "<li>✅ Set admin ID 1 as super_admin (backup)</li>";
    echo "<li>✅ Updated login.php to set admin_role session variable</li>";
    echo "<li>✅ Verified permission functions work for super admin</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>📝 Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li>Log out and log back in to get the new session with admin_role</li>";
    echo "<li>Check that all dashboard sections are now visible</li>";
    echo "<li>Verify that Events Management section appears in sidebar</li>";
    echo "<li>Confirm all analytics and statistics are displayed</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>❌ Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<p><a href='logout.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🚪 Logout</a>";
echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔑 Login Again</a></p>";
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
    h2, h3 { color: #333; }
    table { width: 100%; margin: 10px 0; }
    th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
    p { margin: 8px 0; }
    ul, ol { margin: 10px 0; padding-left: 20px; }
    li { margin: 5px 0; }
</style>
