<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

$event_id = $_GET['event_id'] ?? '';
if (empty($event_id)) {
    header("Location: event_attendance.php");
    exit();
}

$message = '';
$error = '';

// Get event details
try {
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        header("Location: event_attendance.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading event: " . $e->getMessage();
}

// Get comprehensive event statistics
$stmt = $pdo->prepare("
    SELECT 
        COUNT(*) as total_rsvps,
        COUNT(CASE WHEN actually_attended = 1 THEN 1 END) as event_attended,
        COUNT(CASE WHEN actually_attended = 0 THEN 1 END) as event_not_attended,
        COUNT(CASE WHEN actually_attended IS NULL THEN 1 END) as event_not_marked
    FROM (
        SELECT actually_attended FROM event_rsvps WHERE event_id = ? AND status = 'attending'
        UNION ALL
        SELECT actually_attended FROM event_rsvps_guests WHERE event_id = ? AND status = 'attending'
    ) combined_rsvps
");
$stmt->execute([$event_id, $event_id]);
$event_stats = $stmt->fetch(PDO::FETCH_ASSOC);

// Get session statistics with real-time data
$stmt = $pdo->prepare("
    SELECT 
        s.*,
        COUNT(sa.id) as total_registered,
        COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as total_attended,
        COUNT(CASE WHEN sa.attendance_status = 'no_show' THEN 1 END) as total_no_show,
        COUNT(CASE WHEN sa.attendance_status = 'registered' THEN 1 END) as total_pending,
        ROUND((COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) / NULLIF(COUNT(sa.id), 0)) * 100, 1) as attendance_rate,
        CASE 
            WHEN s.start_datetime > NOW() THEN 'upcoming'
            WHEN s.start_datetime <= NOW() AND s.end_datetime >= NOW() THEN 'active'
            ELSE 'completed'
        END as session_status
    FROM event_sessions s
    LEFT JOIN session_attendance sa ON s.id = sa.session_id
    WHERE s.event_id = ? AND s.status = 'active'
    GROUP BY s.id
    ORDER BY s.start_datetime
");
$stmt->execute([$event_id]);
$sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get attendance patterns - people who attended multiple sessions
$stmt = $pdo->prepare("
    SELECT 
        attendee_id,
        attendee_name,
        attendee_type,
        total_registered,
        total_attended,
        ROUND((total_attended / total_registered * 100), 1) as attendance_percentage,
        session_list
    FROM (
        SELECT 
            CASE 
                WHEN sa.member_id IS NOT NULL THEN CONCAT('member_', sa.member_id)
                ELSE CONCAT('guest_', sa.guest_name, '_', sa.guest_email)
            END as attendee_id,
            CASE 
                WHEN sa.member_id IS NOT NULL THEN COALESCE(m.full_name, 'Unknown Member')
                ELSE sa.guest_name
            END as attendee_name,
            CASE 
                WHEN sa.member_id IS NOT NULL THEN 'member'
                ELSE 'guest'
            END as attendee_type,
            COUNT(*) as total_registered,
            COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as total_attended,
            GROUP_CONCAT(
                CASE WHEN sa.attendance_status = 'attended' 
                THEN es.session_title 
                END SEPARATOR ', '
            ) as session_list
        FROM session_attendance sa
        JOIN event_sessions es ON sa.session_id = es.id
        LEFT JOIN members m ON sa.member_id = m.id
        WHERE es.event_id = ? AND es.status = 'active'
        GROUP BY attendee_id, attendee_name, attendee_type
        HAVING total_registered > 0
    ) attendance_patterns
    ORDER BY total_attended DESC, attendance_percentage DESC
    LIMIT 20
");
$stmt->execute([$event_id]);
$attendance_patterns = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get session conflicts (simultaneous sessions)
$stmt = $pdo->prepare("
    SELECT 
        s1.id as session1_id,
        s1.session_title as session1_title,
        s1.start_datetime as session1_start,
        s2.id as session2_id,
        s2.session_title as session2_title,
        s2.start_datetime as session2_start,
        COUNT(DISTINCT CASE WHEN sa1.attendance_status = 'attended' AND sa2.attendance_status = 'attended' 
                           THEN COALESCE(sa1.member_id, CONCAT(sa1.guest_name, sa1.guest_email)) END) as conflict_count
    FROM event_sessions s1
    JOIN event_sessions s2 ON s1.event_id = s2.event_id 
        AND s1.id < s2.id
        AND s1.start_datetime < s2.end_datetime 
        AND s1.end_datetime > s2.start_datetime
    LEFT JOIN session_attendance sa1 ON s1.id = sa1.session_id
    LEFT JOIN session_attendance sa2 ON s2.id = sa2.session_id
        AND (
            (sa1.member_id IS NOT NULL AND sa1.member_id = sa2.member_id)
            OR (sa1.member_id IS NULL AND sa1.guest_name = sa2.guest_name AND sa1.guest_email = sa2.guest_email)
        )
    WHERE s1.event_id = ? AND s1.status = 'active' AND s2.status = 'active'
    GROUP BY s1.id, s2.id
    HAVING conflict_count > 0
    ORDER BY conflict_count DESC
");
$stmt->execute([$event_id]);
$session_conflicts = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Calculate overall event health metrics
$total_session_capacity = array_sum(array_column($sessions, 'max_attendees'));
$total_session_registered = array_sum(array_column($sessions, 'total_registered'));
$total_session_attended = array_sum(array_column($sessions, 'total_attended'));

$event_health = [
    'registration_rate' => $event_stats['total_rsvps'] > 0 ? round(($total_session_registered / ($event_stats['total_rsvps'] * count($sessions))) * 100, 1) : 0,
    'attendance_rate' => $total_session_registered > 0 ? round(($total_session_attended / $total_session_registered) * 100, 1) : 0,
    'capacity_utilization' => $total_session_capacity > 0 ? round(($total_session_registered / $total_session_capacity) * 100, 1) : 0,
    'completion_rate' => $event_stats['total_rsvps'] > 0 ? round((($event_stats['event_attended'] + $event_stats['event_not_attended']) / $event_stats['total_rsvps']) * 100, 1) : 0
];

// Page title and header info
$page_title = 'Multi-Session Dashboard';
$page_header = 'Multi-Session Dashboard';
$page_description = 'Comprehensive real-time overview of multi-session event attendance and analytics';

// Include header
include 'includes/header.php';
?>

<style>
.dashboard-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}
.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
}
.session-card {
    border-left: 4px solid #dee2e6;
    transition: all 0.3s ease;
}
.session-card.upcoming { border-left-color: #ffc107; }
.session-card.active { border-left-color: #28a745; }
.session-card.completed { border-left-color: #6c757d; }
.attendance-bar {
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}
.attendance-progress {
    height: 100%;
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
    transition: width 0.5s ease;
}
.conflict-alert {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 5px;
}
.health-metric {
    text-align: center;
    padding: 15px;
    border-radius: 8px;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.pattern-row {
    transition: background-color 0.2s ease;
}
.pattern-row:hover {
    background-color: #f8f9fa;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <p class="text-muted mb-0">Event: <strong><?php echo htmlspecialchars($event['title']); ?></strong></p>
                <small class="text-muted"><?php echo date('F j, Y g:i A', strtotime($event['event_date'])); ?> • <?php echo htmlspecialchars($event['location']); ?></small>
            </div>
            <div>
                <div class="btn-group" role="group">
                    <a href="event_attendance_detail.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-primary">
                        <i class="bi bi-list-check"></i> Event Attendance
                    </a>
                    <a href="cross_session_attendance.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-warning">
                        <i class="bi bi-diagram-3"></i> Cross-Session
                    </a>
                    <a href="smart_attendance_rules.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-info">
                        <i class="bi bi-lightbulb"></i> Smart Rules
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Event Health Metrics -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card metric-card">
            <div class="card-body">
                <h5 class="card-title text-white mb-3">
                    <i class="bi bi-heart-pulse"></i> Event Health Metrics
                    <button class="btn btn-sm btn-outline-light float-end" onclick="refreshDashboard()">
                        <i class="bi bi-arrow-clockwise"></i> Refresh
                    </button>
                </h5>
                <div class="row">
                    <div class="col-md-3">
                        <div class="health-metric">
                            <h3 class="text-primary"><?php echo $event_health['registration_rate']; ?>%</h3>
                            <small class="text-muted">Session Registration Rate</small>
                            <div class="mt-1">
                                <small class="text-muted"><?php echo number_format($total_session_registered); ?> total registrations</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="health-metric">
                            <h3 class="text-success"><?php echo $event_health['attendance_rate']; ?>%</h3>
                            <small class="text-muted">Session Attendance Rate</small>
                            <div class="mt-1">
                                <small class="text-muted"><?php echo number_format($total_session_attended); ?> attended</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="health-metric">
                            <h3 class="text-info"><?php echo $event_health['capacity_utilization']; ?>%</h3>
                            <small class="text-muted">Capacity Utilization</small>
                            <div class="mt-1">
                                <small class="text-muted"><?php echo number_format($total_session_capacity); ?> total capacity</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="health-metric">
                            <h3 class="text-warning"><?php echo $event_health['completion_rate']; ?>%</h3>
                            <small class="text-muted">Event Completion Rate</small>
                            <div class="mt-1">
                                <small class="text-muted"><?php echo $event_stats['event_attended'] + $event_stats['event_not_attended']; ?>/<?php echo $event_stats['total_rsvps']; ?> marked</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Event Overview vs Sessions -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-event"></i> Event-Level Attendance
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h4 class="text-success"><?php echo $event_stats['event_attended']; ?></h4>
                        <small class="text-muted">Attended</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-danger"><?php echo $event_stats['event_not_attended']; ?></h4>
                        <small class="text-muted">No-Show</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-warning"><?php echo $event_stats['event_not_marked']; ?></h4>
                        <small class="text-muted">Not Marked</small>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="attendance-bar">
                        <div class="attendance-progress"
                             style="width: <?php echo $event_stats['total_rsvps'] > 0 ? round(($event_stats['event_attended'] / $event_stats['total_rsvps']) * 100, 1) : 0; ?>%"></div>
                    </div>
                    <div class="d-flex justify-content-between mt-1">
                        <small class="text-muted">0</small>
                        <small class="text-muted"><?php echo $event_stats['total_rsvps']; ?> Total RSVPs</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-diagram-3"></i> Session-Level Summary
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h4 class="text-primary"><?php echo count($sessions); ?></h4>
                        <small class="text-muted">Total Sessions</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-success"><?php echo number_format($total_session_attended); ?></h4>
                        <small class="text-muted">Total Attended</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-info"><?php echo number_format($total_session_registered); ?></h4>
                        <small class="text-muted">Total Registered</small>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="attendance-bar">
                        <div class="attendance-progress"
                             style="width: <?php echo $total_session_registered > 0 ? round(($total_session_attended / $total_session_registered) * 100, 1) : 0; ?>%"></div>
                    </div>
                    <div class="d-flex justify-content-between mt-1">
                        <small class="text-muted">0</small>
                        <small class="text-muted"><?php echo number_format($total_session_registered); ?> Total Registrations</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Session Breakdown -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list-ul"></i> Session Breakdown
                    <span class="badge bg-primary ms-2"><?php echo count($sessions); ?> Sessions</span>
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($sessions)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-calendar-x fs-1 d-block mb-2"></i>
                        <p>No sessions found for this event.</p>
                        <a href="event_sessions.php?event_id=<?php echo $event_id; ?>" class="btn btn-primary">
                            <i class="bi bi-plus"></i> Add Sessions
                        </a>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($sessions as $session): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card session-card <?php echo $session['session_status']; ?>">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0"><?php echo htmlspecialchars($session['session_title']); ?></h6>
                                            <span class="badge bg-<?php
                                                echo $session['session_status'] === 'upcoming' ? 'warning' :
                                                    ($session['session_status'] === 'active' ? 'success' : 'secondary');
                                            ?>">
                                                <?php echo ucfirst($session['session_status']); ?>
                                            </span>
                                        </div>

                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="bi bi-clock"></i> <?php echo date('M j, g:i A', strtotime($session['start_datetime'])); ?>
                                                <?php if ($session['location']): ?>
                                                    <br><i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($session['location']); ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>

                                        <div class="row text-center mb-2">
                                            <div class="col-4">
                                                <strong class="text-success"><?php echo $session['total_attended']; ?></strong>
                                                <br><small class="text-muted">Attended</small>
                                            </div>
                                            <div class="col-4">
                                                <strong class="text-danger"><?php echo $session['total_no_show']; ?></strong>
                                                <br><small class="text-muted">No-Show</small>
                                            </div>
                                            <div class="col-4">
                                                <strong class="text-warning"><?php echo $session['total_pending']; ?></strong>
                                                <br><small class="text-muted">Pending</small>
                                            </div>
                                        </div>

                                        <div class="attendance-bar mb-2">
                                            <div class="attendance-progress"
                                                 style="width: <?php echo $session['attendance_rate'] ?? 0; ?>%"></div>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <?php echo $session['attendance_rate'] ?? 0; ?>% attendance rate
                                            </small>
                                            <div class="btn-group btn-group-sm">
                                                <a href="session_attendance.php?session_id=<?php echo $session['id']; ?>"
                                                   class="btn btn-outline-primary btn-sm">
                                                    <i class="bi bi-list-check"></i>
                                                </a>
                                                <?php if ($session['session_status'] === 'active'): ?>
                                                    <button class="btn btn-outline-success btn-sm"
                                                            onclick="quickMarkSession(<?php echo $session['id']; ?>, 'attended')">
                                                        <i class="bi bi-check"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Patterns -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up"></i> Top Attendance Patterns
                    <small class="text-muted ms-2">Multi-session participants</small>
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($attendance_patterns)): ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-person-x fs-3 d-block mb-2"></i>
                        <p>No attendance patterns available yet.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Attendee</th>
                                    <th>Type</th>
                                    <th>Sessions</th>
                                    <th>Rate</th>
                                    <th>Attended Sessions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($attendance_patterns, 0, 10) as $pattern): ?>
                                    <tr class="pattern-row">
                                        <td>
                                            <strong><?php echo htmlspecialchars($pattern['attendee_name']); ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $pattern['attendee_type'] === 'member' ? 'primary' : 'secondary'; ?>">
                                                <?php echo ucfirst($pattern['attendee_type']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="text-success"><?php echo $pattern['total_attended']; ?></span>
                                            /
                                            <span class="text-muted"><?php echo $pattern['total_registered']; ?></span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="attendance-bar me-2" style="width: 60px;">
                                                    <div class="attendance-progress"
                                                         style="width: <?php echo $pattern['attendance_percentage']; ?>%"></div>
                                                </div>
                                                <small><?php echo $pattern['attendance_percentage']; ?>%</small>
                                            </div>
                                        </td>
                                        <td>
                                            <small class="text-muted" title="<?php echo htmlspecialchars($pattern['session_list'] ?? 'None'); ?>">
                                                <?php
                                                $sessions_text = $pattern['session_list'] ?? 'None';
                                                echo strlen($sessions_text) > 50 ? substr($sessions_text, 0, 50) . '...' : $sessions_text;
                                                ?>
                                            </small>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Session Conflicts -->
    <div class="col-md-4">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-exclamation-triangle"></i> Session Conflicts
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($session_conflicts)): ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-check-circle fs-3 d-block mb-2 text-success"></i>
                        <p>No scheduling conflicts detected!</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($session_conflicts as $conflict): ?>
                        <div class="conflict-alert p-2 mb-2">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <small class="fw-bold"><?php echo htmlspecialchars($conflict['session1_title']); ?></small>
                                    <br>
                                    <small class="text-muted">vs</small>
                                    <br>
                                    <small class="fw-bold"><?php echo htmlspecialchars($conflict['session2_title']); ?></small>
                                </div>
                                <span class="badge bg-warning"><?php echo $conflict['conflict_count']; ?></span>
                            </div>
                            <small class="text-muted">
                                <?php echo $conflict['conflict_count']; ?> people marked as attending both
                            </small>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Panel -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-grid">
                            <a href="advanced_bulk_attendance.php?event_id=<?php echo $event_id; ?>" class="btn btn-primary">
                                <i class="bi bi-diagram-3"></i><br>
                                <small>Advanced Bulk Operations</small>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <a href="smart_attendance_engine.php?event_id=<?php echo $event_id; ?>" class="btn btn-warning">
                                <i class="bi bi-lightbulb"></i><br>
                                <small>Smart Attendance Engine</small>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <button class="btn btn-success" onclick="exportDashboardData()">
                                <i class="bi bi-download"></i><br>
                                <small>Export Dashboard Data</small>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <button class="btn btn-primary" onclick="refreshDashboard()">
                                <i class="bi bi-arrow-clockwise"></i><br>
                                <small>Refresh All Data</small>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Phase 2 Advanced Tools -->
                <div class="row mt-3">
                    <div class="col-md-3">
                        <div class="d-grid">
                            <a href="cross_session_dashboard.php?event_id=<?php echo $event_id; ?>" class="btn btn-info">
                                <i class="bi bi-grid-3x3-gap"></i><br>
                                <small>Cross-Session Dashboard</small>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <a href="session_group_manager.php?event_id=<?php echo $event_id; ?>" class="btn btn-secondary">
                                <i class="bi bi-collection"></i><br>
                                <small>Session Groups</small>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <a href="realtime_dashboard.php?event_id=<?php echo $event_id; ?>" class="btn btn-dark">
                                <i class="bi bi-broadcast"></i><br>
                                <small>Real-Time Dashboard</small>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <a href="attendance_inheritance_engine.php?event_id=<?php echo $event_id; ?>" class="btn btn-purple" style="background-color: #6f42c1; border-color: #6f42c1; color: white;">
                                <i class="bi bi-diagram-3"></i><br>
                                <small>Inheritance Engine</small>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Additional Tools -->
                <div class="row mt-3">
                    <div class="col-md-3">
                        <div class="d-grid">
                            <a href="capacity_resource_manager.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-danger">
                                <i class="bi bi-speedometer2"></i> Capacity Manager
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <a href="mobile_session_manager.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-success">
                                <i class="bi bi-phone"></i> Mobile Manager
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <a href="session_qr_generator.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-info">
                                <i class="bi bi-qr-code"></i> QR Generator
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid">
                            <a href="advanced_analytics.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-graph-up"></i> Analytics
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Auto-refresh functionality
let refreshInterval;
let isAutoRefreshEnabled = false;

// Refresh dashboard data
function refreshDashboard() {
    const refreshBtn = document.querySelector('button[onclick="refreshDashboard()"]');
    const originalText = refreshBtn.innerHTML;

    refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i><br><small>Refreshing...</small>';
    refreshBtn.disabled = true;

    // Add spinning animation
    const style = document.createElement('style');
    style.textContent = '.spin { animation: spin 1s linear infinite; } @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
    document.head.appendChild(style);

    // Simulate refresh (in real implementation, this would be an AJAX call)
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// Quick mark session attendance
function quickMarkSession(sessionId, status) {
    if (!confirm(`Mark all registered attendees for this session as "${status}"?`)) {
        return;
    }

    // In real implementation, this would be an AJAX call
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = 'session_attendance.php';

    const sessionInput = document.createElement('input');
    sessionInput.type = 'hidden';
    sessionInput.name = 'session_id';
    sessionInput.value = sessionId;

    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'bulk_mark_' + status;

    form.appendChild(sessionInput);
    form.appendChild(actionInput);
    document.body.appendChild(form);
    form.submit();
}

// Export dashboard data
function exportDashboardData() {
    const eventId = <?php echo $event_id; ?>;
    window.open(`export_dashboard.php?event_id=${eventId}&format=csv`, '_blank');
}

// Toggle auto-refresh
function toggleAutoRefresh() {
    if (isAutoRefreshEnabled) {
        clearInterval(refreshInterval);
        isAutoRefreshEnabled = false;
        document.getElementById('auto-refresh-btn').innerHTML = '<i class="bi bi-play"></i> Enable Auto-Refresh';
    } else {
        refreshInterval = setInterval(refreshDashboard, 30000); // Refresh every 30 seconds
        isAutoRefreshEnabled = true;
        document.getElementById('auto-refresh-btn').innerHTML = '<i class="bi bi-pause"></i> Disable Auto-Refresh';
    }
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Add tooltips to all elements with title attribute
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add click handlers for session cards
    document.querySelectorAll('.session-card').forEach(function(card) {
        card.addEventListener('click', function(e) {
            if (e.target.tagName !== 'BUTTON' && e.target.tagName !== 'A') {
                const sessionId = this.querySelector('a[href*="session_id"]').href.match(/session_id=(\d+)/)[1];
                window.location.href = `session_attendance.php?session_id=${sessionId}`;
            }
        });
    });

    // Add hover effects for pattern rows
    document.querySelectorAll('.pattern-row').forEach(function(row) {
        row.style.cursor = 'pointer';
        row.addEventListener('click', function() {
            // In real implementation, this could show detailed attendance pattern
            console.log('Show detailed pattern for:', this.querySelector('strong').textContent);
        });
    });

    // Show last refresh time
    const now = new Date();
    const refreshTime = document.createElement('small');
    refreshTime.className = 'text-muted';
    refreshTime.textContent = `Last updated: ${now.toLocaleTimeString()}`;
    document.querySelector('.card-title').appendChild(refreshTime);
});

// Real-time updates (WebSocket simulation)
function simulateRealTimeUpdates() {
    // In a real implementation, this would connect to WebSocket or use Server-Sent Events
    // For now, we'll just refresh periodically if auto-refresh is enabled
    if (isAutoRefreshEnabled) {
        refreshDashboard();
    }
}
</script>

<?php include 'includes/footer.php'; ?>
