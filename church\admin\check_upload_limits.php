<?php
session_start();
require_once '../config.php';

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

function parseSize($size) {
    $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
    $size = preg_replace('/[^0-9\.]/', '', $size);
    if ($unit) {
        return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
    } else {
        return round($size);
    }
}

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

$upload_max_filesize = ini_get('upload_max_filesize');
$post_max_size = ini_get('post_max_size');
$max_execution_time = ini_get('max_execution_time');
$memory_limit = ini_get('memory_limit');
$max_file_uploads = ini_get('max_file_uploads');

$upload_max_bytes = parseSize($upload_max_filesize);
$post_max_bytes = parseSize($post_max_size);
$memory_limit_bytes = parseSize($memory_limit);

echo json_encode([
    'success' => true,
    'php_limits' => [
        'upload_max_filesize' => $upload_max_filesize,
        'upload_max_filesize_bytes' => $upload_max_bytes,
        'upload_max_filesize_formatted' => formatBytes($upload_max_bytes),
        'post_max_size' => $post_max_size,
        'post_max_size_bytes' => $post_max_bytes,
        'post_max_size_formatted' => formatBytes($post_max_bytes),
        'max_execution_time' => $max_execution_time,
        'memory_limit' => $memory_limit,
        'memory_limit_bytes' => $memory_limit_bytes,
        'memory_limit_formatted' => formatBytes($memory_limit_bytes),
        'max_file_uploads' => $max_file_uploads,
        'effective_upload_limit' => formatBytes(min($upload_max_bytes, $post_max_bytes))
    ],
    'session_info' => [
        'admin_id' => $_SESSION['admin_id'],
        'admin_name' => $_SESSION['admin_name'] ?? 'Not set',
        'session_id' => session_id()
    ]
]);
?>
