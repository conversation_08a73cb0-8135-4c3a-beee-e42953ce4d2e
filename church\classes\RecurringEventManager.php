<?php

/**
 * Recurring Event Manager
 * Handles creation and management of recurring events
 */
class RecurringEventManager {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Create recurring event instances
     * @param int $parentEventId Parent event ID
     * @param array $recurrenceConfig Recurrence configuration
     * @return array Result with success status and created instances
     */
    public function createRecurringInstances($parentEventId, $recurrenceConfig) {
        try {
            $this->pdo->beginTransaction();
            
            // Get parent event details
            $stmt = $this->pdo->prepare("SELECT * FROM events WHERE id = ?");
            $stmt->execute([$parentEventId]);
            $parentEvent = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$parentEvent) {
                throw new Exception("Parent event not found");
            }
            
            // Update parent event with recurrence settings
            $this->updateEventRecurrenceSettings($parentEventId, $recurrenceConfig);
            
            // NEW APPROACH: Don't create multiple database entries
            // Just store the recurrence configuration in the parent event
            // Occurrences will be calculated dynamically when needed
            
            $this->pdo->commit();
            
            return [
                'success' => true,
                'message' => 'Recurring event setup completed successfully',
                'parent_event_id' => $parentEventId
            ];
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error creating recurring instances: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Calculate recurring dates dynamically (NEW METHOD)
     * @param int $eventId Event ID
     * @param int $limit Maximum number of occurrences to return
     * @param DateTime $fromDate Start date for calculation (optional)
     * @return array Array of occurrence dates
     */
    public function calculateRecurringDates($eventId, $limit = 10, $fromDate = null) {
        try {
            // Get event details
            $stmt = $this->pdo->prepare("SELECT * FROM events WHERE id = ? AND is_recurring = 1");
            $stmt->execute([$eventId]);
            $event = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$event) {
                return [];
            }

            $startDate = new DateTime($event['event_date']);
            $currentDate = $fromDate ? clone $fromDate : new DateTime();

            // If current date is before event start date, start from event date
            if ($currentDate < $startDate) {
                $currentDate = clone $startDate;
            }

            $occurrences = [];
            $interval = (int)($event['recurrence_interval'] ?? 1);
            $endDate = $event['recurrence_end_date'] ? new DateTime($event['recurrence_end_date']) : null;
            $maxCount = (int)($event['recurrence_count'] ?? $limit);

            // Add the original event date if it's in the future
            if ($startDate >= $currentDate) {
                $occurrences[] = [
                    'date' => $startDate->format('Y-m-d H:i:s'),
                    'instance_number' => 1,
                    'is_original' => true
                ];
            }

            // Calculate future occurrences
            $instanceNumber = 2;
            $nextDate = clone $startDate;

            while (count($occurrences) < min($limit, $maxCount)) {
                // Calculate next occurrence
                switch ($event['recurrence_type']) {
                    case 'daily':
                        $nextDate->add(new DateInterval('P' . $interval . 'D'));
                        break;
                    case 'weekly':
                        $nextDate->add(new DateInterval('P' . ($interval * 7) . 'D'));
                        break;
                    case 'monthly':
                        $nextDate->add(new DateInterval('P' . $interval . 'M'));
                        break;
                    case 'yearly':
                        $nextDate->add(new DateInterval('P' . $interval . 'Y'));
                        break;
                    default:
                        break 2; // Exit the while loop
                }

                // Check if we've exceeded the end date
                if ($endDate && $nextDate > $endDate) {
                    break;
                }

                // Don't calculate too far in the future (2 years max)
                $maxFutureDate = new DateTime('+2 years');
                if ($nextDate > $maxFutureDate) {
                    break;
                }

                // Only include if it's in the future or current
                if ($nextDate >= $currentDate) {
                    $occurrences[] = [
                        'date' => $nextDate->format('Y-m-d H:i:s'),
                        'instance_number' => $instanceNumber,
                        'is_original' => false
                    ];
                }

                $instanceNumber++;
            }

            return $occurrences;

        } catch (Exception $e) {
            error_log("Error calculating recurring dates: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get next occurrence of a recurring event
     * @param int $eventId Event ID
     * @return array|null Next occurrence details or null
     */
    public function getNextOccurrence($eventId) {
        $occurrences = $this->calculateRecurringDates($eventId, 1, new DateTime());
        return !empty($occurrences) ? $occurrences[0] : null;
    }

    /**
     * Update event recurrence settings
     */
    private function updateEventRecurrenceSettings($eventId, $config) {
        $stmt = $this->pdo->prepare("
            UPDATE events 
            SET is_recurring = 1,
                recurrence_type = ?,
                recurrence_interval = ?,
                recurrence_end_date = ?,
                recurrence_count = ?,
                recurrence_data = ?
            WHERE id = ?
        ");
        
        $stmt->execute([
            $config['type'],
            $config['interval'] ?? 1,
            $config['end_date'] ?? null,
            $config['count'] ?? null,
            json_encode($config),
            $eventId
        ]);
    }
    
    /**
     * Generate recurrence instance dates
     */
    private function generateRecurrenceInstances($parentEvent, $config) {
        $instances = [];
        $startDate = new DateTime($parentEvent['event_date']);
        $currentDate = clone $startDate;
        
        $maxInstances = $config['count'] ?? 52; // Default max 52 instances
        $endDate = $config['end_date'] ? new DateTime($config['end_date']) : null;
        $interval = $config['interval'] ?? 1;
        
        for ($i = 0; $i < $maxInstances; $i++) {
            // Calculate next occurrence
            switch ($config['type']) {
                case 'daily':
                    $currentDate->add(new DateInterval('P' . $interval . 'D'));
                    break;
                case 'weekly':
                    $currentDate->add(new DateInterval('P' . ($interval * 7) . 'D'));
                    break;
                case 'monthly':
                    $currentDate->add(new DateInterval('P' . $interval . 'M'));
                    break;
                case 'yearly':
                    $currentDate->add(new DateInterval('P' . $interval . 'Y'));
                    break;
                default:
                    throw new Exception("Invalid recurrence type: " . $config['type']);
            }
            
            // Check if we've exceeded the end date
            if ($endDate && $currentDate > $endDate) {
                break;
            }
            
            // Don't create instances too far in the future (2 years max)
            $maxFutureDate = new DateTime('+2 years');
            if ($currentDate > $maxFutureDate) {
                break;
            }
            
            $instances[] = $currentDate->format('Y-m-d H:i:s');
        }
        
        return $instances;
    }
    
    // REMOVED: createEventInstance and recordRecurringInstance methods
    // These methods created duplicate database entries which we no longer want
    
    /**
     * Get recurring event instances (NEW DYNAMIC APPROACH)
     */
    public function getRecurringInstances($parentEventId, $limit = 10) {
        // Get the parent event details
        $stmt = $this->pdo->prepare("SELECT * FROM events WHERE id = ? AND is_recurring = 1");
        $stmt->execute([$parentEventId]);
        $parentEvent = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$parentEvent) {
            return [];
        }

        // Calculate dynamic occurrences
        $occurrences = $this->calculateRecurringDates($parentEventId, $limit);

        // Format for compatibility with existing code
        $instances = [];
        foreach ($occurrences as $occurrence) {
            $instances[] = [
                'parent_event_id' => $parentEventId,
                'instance_event_id' => null, // No longer creating separate events
                'instance_date' => date('Y-m-d', strtotime($occurrence['date'])),
                'instance_number' => $occurrence['instance_number'],
                'title' => $parentEvent['title'] . ($occurrence['is_original'] ? '' : ' #' . $occurrence['instance_number']),
                'event_date' => $occurrence['date'],
                'status' => $parentEvent['status']
            ];
        }

        return $instances;
    }
    
    /**
     * Delete recurring event series (SIMPLIFIED - NO INSTANCE EVENTS)
     */
    public function deleteRecurringSeries($parentEventId) {
        try {
            $this->pdo->beginTransaction();

            // Simply update parent event to remove recurrence
            $stmt = $this->pdo->prepare("
                UPDATE events
                SET is_recurring = 0, recurrence_type = 'none',
                    recurrence_interval = 1, recurrence_end_date = NULL,
                    recurrence_count = NULL, recurrence_data = NULL
                WHERE id = ?
            ");
            $stmt->execute([$parentEventId]);

            $this->pdo->commit();

            return [
                'success' => true,
                'message' => 'Recurring series deleted successfully'
            ];
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error deleting recurring series: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Update single instance (create exception)
     */
    public function updateInstance($instanceId, $changes, $reason = '') {
        try {
            $this->pdo->beginTransaction();
            
            // Update the instance event
            $setClauses = [];
            $params = [];
            
            foreach ($changes as $field => $value) {
                $setClauses[] = "$field = ?";
                $params[] = $value;
            }
            
            $params[] = $instanceId;
            
            $sql = "UPDATE events SET " . implode(', ', $setClauses) . " WHERE id = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            // Mark as exception in recurring_event_instances
            $stmt = $this->pdo->prepare("
                UPDATE recurring_event_instances 
                SET is_exception = 1, exception_reason = ?
                WHERE instance_event_id = ?
            ");
            $stmt->execute([$reason, $instanceId]);
            
            $this->pdo->commit();
            
            return [
                'success' => true,
                'message' => 'Instance updated successfully'
            ];
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            error_log("Error updating instance: " . $e->getMessage());
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Check if event is part of recurring series
     */
    public function isRecurringEvent($eventId) {
        $stmt = $this->pdo->prepare("
            SELECT is_recurring, parent_event_id 
            FROM events 
            WHERE id = ?
        ");
        $stmt->execute([$eventId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return [
            'is_parent' => $result && $result['is_recurring'],
            'is_instance' => $result && $result['parent_event_id'],
            'parent_id' => $result['parent_event_id'] ?? null
        ];
    }
}
