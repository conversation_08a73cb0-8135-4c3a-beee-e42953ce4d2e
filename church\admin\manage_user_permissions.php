<?php
session_start();

// Include route protection (this will handle login check and role verification)
require_once 'includes/route_protection.php';

// Protect this page - Super Admin only
protectSuperAdminRoute();

// Include the configuration file
require_once '../config.php';

$message = '';
$error = '';

// Handle permission updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'update_permissions') {
            $user_id = $_POST['user_id'] ?? '';
            $permissions = $_POST['permissions'] ?? [];

            if (empty($user_id)) {
                throw new Exception('User ID is required');
            }

            // Check if granular permission tables exist
            $stmt = $pdo->query("SHOW TABLES LIKE 'user_individual_permissions'");
            if ($stmt->rowCount() == 0) {
                throw new Exception('Granular permission system is not set up yet. Please run the setup first.');
            }

            $pdo->beginTransaction();
            
            // First, deactivate all current permissions for this user
            $stmt = $pdo->prepare("UPDATE user_individual_permissions SET is_active = 0 WHERE user_id = ?");
            $stmt->execute([$user_id]);
            
            // Then add/reactivate the selected permissions
            foreach ($permissions as $permission_id) {
                $stmt = $pdo->prepare("
                    INSERT INTO user_individual_permissions (user_id, permission_id, granted_by, is_active) 
                    VALUES (?, ?, ?, 1)
                    ON DUPLICATE KEY UPDATE is_active = 1, granted_by = ?, granted_at = NOW()
                ");
                $stmt->execute([$user_id, $permission_id, $_SESSION['admin_id'], $_SESSION['admin_id']]);
            }
            
            $pdo->commit();
            
            // Get username for message
            $stmt = $pdo->prepare("SELECT username FROM admins WHERE id = ?");
            $stmt->execute([$user_id]);
            $username = $stmt->fetchColumn();
            
            $message = "Permissions updated successfully for user: " . htmlspecialchars($username);
            
        } elseif ($_POST['action'] === 'copy_permissions') {
            $source_user_id = $_POST['source_user_id'] ?? '';
            $target_user_id = $_POST['target_user_id'] ?? '';

            if (empty($source_user_id) || empty($target_user_id)) {
                throw new Exception('Both source and target users are required');
            }

            // Check if granular permission tables exist
            $stmt = $pdo->query("SHOW TABLES LIKE 'user_individual_permissions'");
            if ($stmt->rowCount() == 0) {
                throw new Exception('Granular permission system is not set up yet. Please run the setup first.');
            }

            $pdo->beginTransaction();
            
            // Get source user permissions
            $stmt = $pdo->prepare("
                SELECT permission_id FROM user_individual_permissions 
                WHERE user_id = ? AND is_active = 1
            ");
            $stmt->execute([$source_user_id]);
            $source_permissions = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // Clear target user permissions
            $stmt = $pdo->prepare("UPDATE user_individual_permissions SET is_active = 0 WHERE user_id = ?");
            $stmt->execute([$target_user_id]);
            
            // Copy permissions to target user
            foreach ($source_permissions as $permission_id) {
                $stmt = $pdo->prepare("
                    INSERT INTO user_individual_permissions (user_id, permission_id, granted_by, is_active) 
                    VALUES (?, ?, ?, 1)
                    ON DUPLICATE KEY UPDATE is_active = 1, granted_by = ?, granted_at = NOW()
                ");
                $stmt->execute([$target_user_id, $permission_id, $_SESSION['admin_id'], $_SESSION['admin_id']]);
            }
            
            $pdo->commit();
            $message = "Permissions copied successfully!";
        }
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        $error = $e->getMessage();
    }
}

// Get all admin users
$stmt = $pdo->prepare("
    SELECT id, username, email, full_name, created_at 
    FROM admins 
    ORDER BY username
");
$stmt->execute();
$admin_users = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Check if granular permission tables exist
$granular_tables_exist = false;
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'permission_categories'");
    $granular_tables_exist = $stmt->rowCount() > 0;
} catch (Exception $e) {
    $granular_tables_exist = false;
}

// Get all permission categories and permissions (only if tables exist)
$permissions_data = [];
if ($granular_tables_exist) {
    try {
        $stmt = $pdo->prepare("
            SELECT
                pc.id as category_id,
                pc.category_name,
                pc.category_display_name,
                pc.icon_class,
                gp.id as permission_id,
                gp.permission_key,
                gp.permission_name,
                gp.permission_description,
                gp.sort_order
            FROM permission_categories pc
            LEFT JOIN granular_permissions gp ON pc.id = gp.category_id AND gp.is_active = 1
            WHERE pc.is_active = 1
            ORDER BY pc.sort_order, gp.sort_order
        ");
        $stmt->execute();
        $permissions_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error fetching permissions data: " . $e->getMessage());
        $permissions_data = [];
    }
}

// Organize permissions by category
$permissions_by_category = [];
foreach ($permissions_data as $row) {
    if (!isset($permissions_by_category[$row['category_id']])) {
        $permissions_by_category[$row['category_id']] = [
            'category_name' => $row['category_name'],
            'category_display_name' => $row['category_display_name'],
            'icon_class' => $row['icon_class'],
            'permissions' => []
        ];
    }
    
    if ($row['permission_id']) {
        $permissions_by_category[$row['category_id']]['permissions'][] = [
            'id' => $row['permission_id'],
            'key' => $row['permission_key'],
            'name' => $row['permission_name'],
            'description' => $row['permission_description']
        ];
    }
}

// Get current user permissions for each admin user (only if tables exist)
$user_permissions = [];
if ($granular_tables_exist) {
    foreach ($admin_users as $user) {
        try {
            $stmt = $pdo->prepare("
                SELECT permission_id
                FROM user_individual_permissions
                WHERE user_id = ? AND is_active = 1
            ");
            $stmt->execute([$user['id']]);
            $user_permissions[$user['id']] = $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (Exception $e) {
            error_log("Error fetching user permissions for user {$user['id']}: " . $e->getMessage());
            $user_permissions[$user['id']] = [];
        }
    }
} else {
    // Initialize empty permissions for all users if tables don't exist
    foreach ($admin_users as $user) {
        $user_permissions[$user['id']] = [];
    }
}

// Page title and header info
$page_title = 'Manage User Permissions';
$page_header = 'Granular Permission Management';
$page_description = 'Assign individual permissions to admin users';

// Include header
include 'includes/header.php';
?>

<style>
.permission-header {
    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
    color: white;
    border-radius: 10px;
}

.permission-grid {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.permission-category {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    position: sticky;
    top: 0;
    z-index: 10;
}

.permission-row {
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s ease;
}

.permission-row:hover {
    background-color: #f8f9fa;
}

.user-card {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.2s ease;
}

.user-card:hover {
    background-color: #ffffff;
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.1);
}

.user-card.selected {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.permission-checkbox {
    transform: scale(1.2);
    margin: 0;
}

.category-header {
    font-weight: 600;
    color: #495057;
    background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
}

.quick-actions {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="permission-header p-4 text-center">
            <h2 class="mb-2">
                <i class="bi bi-shield-lock"></i> <?php echo $page_header; ?>
            </h2>
            <p class="mb-0"><?php echo $page_description; ?></p>
        </div>
    </div>
</div>

<!-- Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (!$granular_tables_exist): ?>
    <div class="alert alert-warning alert-dismissible fade show" role="alert">
        <h4><i class="bi bi-exclamation-triangle"></i> Granular Permission System Not Set Up</h4>
        <p>The granular permission system has not been set up yet. You need to create the database tables first.</p>
        <hr>
        <p class="mb-0">
            <a href="setup_granular_permissions_system.php" class="btn btn-primary me-2">
                <i class="bi bi-gear-wide-connected"></i> Set Up Granular Permission System
            </a>
            <a href="apply_enhanced_permissions.php" class="btn btn-success">
                <i class="bi bi-stars"></i> Apply Enhanced Permissions
            </a>
        </p>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php else: ?>
    <!-- Enhanced Permissions Notice -->
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <h5><i class="bi bi-lightbulb"></i> Enhance Your Permission System</h5>
        <p>Want comprehensive permissions covering all admin functionality? Apply our enhanced permission set.</p>
        <a href="apply_enhanced_permissions.php" class="btn btn-outline-primary btn-sm">
            <i class="bi bi-stars"></i> Apply Enhanced Permissions
        </a>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card text-center">
            <h3><?php echo count($admin_users); ?></h3>
            <p class="mb-0">Admin Users</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <h3><?php echo count($permissions_by_category); ?></h3>
            <p class="mb-0">Permission Categories</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <h3><?php 
                $total_permissions = 0;
                foreach ($permissions_by_category as $category) {
                    $total_permissions += count($category['permissions']);
                }
                echo $total_permissions;
            ?></h3>
            <p class="mb-0">Total Permissions</p>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card text-center">
            <h3><?php 
                $active_permissions = 0;
                foreach ($user_permissions as $perms) {
                    $active_permissions += count($perms);
                }
                echo $active_permissions;
            ?></h3>
            <p class="mb-0">Active Assignments</p>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="quick-actions">
    <h5><i class="bi bi-lightning"></i> Quick Actions</h5>
    <div class="row">
        <div class="col-md-4">
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAllPermissions()">
                <i class="bi bi-check-all"></i> Select All
            </button>
            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearAllPermissions()">
                <i class="bi bi-x-circle"></i> Clear All
            </button>
        </div>
        <div class="col-md-4">
            <div class="dropdown d-inline-block">
                <button class="btn btn-outline-warning btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-person-badge"></i> Role Templates
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="applyRoleTemplate('super_admin')">Super Admin</a></li>
                    <li><a class="dropdown-item" href="#" onclick="applyRoleTemplate('limited_admin')">Limited Admin</a></li>
                    <li><a class="dropdown-item" href="#" onclick="applyRoleTemplate('event_coordinator')">Event Coordinator</a></li>
                    <li><a class="dropdown-item" href="#" onclick="applyRoleTemplate('session_moderator')">Session Moderator</a></li>
                    <li><a class="dropdown-item" href="#" onclick="applyRoleTemplate('staff')">Staff</a></li>
                </ul>
            </div>
        </div>
        <div class="col-md-4">
            <button type="button" class="btn btn-outline-info btn-sm" data-bs-toggle="modal" data-bs-target="#copyPermissionsModal">
                <i class="bi bi-files"></i> Copy Permissions
            </button>
            <button type="button" class="btn btn-outline-success btn-sm" onclick="savePermissions()">
                <i class="bi bi-save"></i> Save Changes
            </button>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <!-- User Selection -->
    <div class="col-md-4">
        <div class="card <?php echo !$granular_tables_exist ? 'opacity-50' : ''; ?>">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-people"></i> Select Admin User
                    <?php if (!$granular_tables_exist): ?>
                        <span class="badge bg-warning ms-2">Setup Required</span>
                    <?php endif; ?>
                </h5>
            </div>
            <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                <?php if ($granular_tables_exist): ?>
                    <?php foreach ($admin_users as $user): ?>
                        <div class="user-card" data-user-id="<?php echo $user['id']; ?>" onclick="selectUser(<?php echo $user['id']; ?>)">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                    <?php if ($user['full_name']): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($user['full_name']); ?></small>
                                    <?php endif; ?>
                                    <?php if ($user['email']): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($user['email']); ?></small>
                                    <?php endif; ?>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-primary">
                                        <?php echo count($user_permissions[$user['id']] ?? []); ?> permissions
                                    </span>
                                    <?php if ($user['id'] == $_SESSION['admin_id']): ?>
                                        <br><small class="text-success">Current User</small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-exclamation-triangle fs-3 d-block mb-2"></i>
                        <p>Granular permission system not set up yet.</p>
                        <a href="setup_granular_permissions_system.php" class="btn btn-sm btn-primary">
                            <i class="bi bi-gear-wide-connected"></i> Set Up Now
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Permission Grid -->
    <div class="col-md-8">
        <div class="card <?php echo !$granular_tables_exist ? 'opacity-50' : ''; ?>">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-grid-3x3-gap"></i> Permission Assignment
                        <?php if (!$granular_tables_exist): ?>
                            <span class="badge bg-warning ms-2">Setup Required</span>
                        <?php else: ?>
                            <span id="selected-user-name" class="text-muted">- Select a user to manage permissions</span>
                        <?php endif; ?>
                    </h5>
                    <?php if ($granular_tables_exist): ?>
                    <div class="d-flex align-items-center">
                        <input type="text" class="form-control form-control-sm me-2" id="permissionSearch"
                               placeholder="Search permissions..." style="width: 200px;">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearSearch()">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if ($granular_tables_exist): ?>
                    <form id="permissionForm" method="POST">
                        <input type="hidden" name="action" value="update_permissions">
                        <input type="hidden" name="user_id" id="selected_user_id">

                        <div class="permission-grid">
                            <?php foreach ($permissions_by_category as $category_id => $category): ?>
                                <div class="permission-category category-header p-3">
                                    <h6 class="mb-0">
                                        <i class="<?php echo $category['icon_class']; ?>"></i>
                                        <?php echo htmlspecialchars($category['category_display_name']); ?>
                                        <span class="badge bg-secondary ms-2"><?php echo count($category['permissions']); ?></span>
                                    </h6>
                                </div>

                                <?php foreach ($category['permissions'] as $permission): ?>
                                    <div class="permission-row p-3">
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox"
                                                   type="checkbox"
                                                   name="permissions[]"
                                                   value="<?php echo $permission['id']; ?>"
                                                   id="perm_<?php echo $permission['id']; ?>"
                                                   data-permission-key="<?php echo $permission['key']; ?>">
                                            <label class="form-check-label w-100" for="perm_<?php echo $permission['id']; ?>">
                                                <strong><?php echo htmlspecialchars($permission['name']); ?></strong>
                                                <?php if ($permission['description']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($permission['description']); ?></small>
                                                <?php endif; ?>
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endforeach; ?>
                        </div>

                        <div class="p-3 border-top">
                            <button type="submit" class="btn btn-primary" id="savePermissionsBtn" disabled>
                                <i class="bi bi-save"></i> Save Permissions
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetPermissions()">
                                <i class="bi bi-arrow-clockwise"></i> Reset
                            </button>
                        </div>
                    </form>
                <?php else: ?>
                    <div class="text-center text-muted py-5">
                        <i class="bi bi-gear-wide-connected fs-1 d-block mb-3"></i>
                        <h4>Granular Permission System Setup Required</h4>
                        <p>The granular permission system needs to be set up before you can manage individual user permissions.</p>
                        <div class="mt-4">
                            <a href="setup_granular_permissions_system.php" class="btn btn-primary btn-lg">
                                <i class="bi bi-gear-wide-connected"></i> Set Up Granular Permission System
                            </a>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                This will create the necessary database tables and initialize permissions based on existing user roles.
                            </small>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Copy Permissions Modal -->
<div class="modal fade" id="copyPermissionsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Copy Permissions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="copy_permissions">

                    <div class="mb-3">
                        <label for="source_user_id" class="form-label">Copy From User:</label>
                        <select class="form-select" name="source_user_id" required>
                            <option value="">Select source user...</option>
                            <?php foreach ($admin_users as $user): ?>
                                <option value="<?php echo $user['id']; ?>">
                                    <?php echo htmlspecialchars($user['username']); ?>
                                    (<?php echo count($user_permissions[$user['id']] ?? []); ?> permissions)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="target_user_id" class="form-label">Copy To User:</label>
                        <select class="form-select" name="target_user_id" required>
                            <option value="">Select target user...</option>
                            <?php foreach ($admin_users as $user): ?>
                                <option value="<?php echo $user['id']; ?>">
                                    <?php echo htmlspecialchars($user['username']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Warning:</strong> This will replace all existing permissions for the target user.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Copy Permissions</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let selectedUserId = null;
let userPermissions = <?php echo json_encode($user_permissions); ?>;

// Role-based permission templates
const roleTemplates = {
    'super_admin': [
        'dashboard.view', 'dashboard.analytics', 'dashboard.realtime', 'dashboard.super_admin',
        'members.view', 'members.add', 'members.edit', 'members.delete', 'members.family_management',
        'events.view', 'events.create', 'events.edit', 'events.delete', 'events.attendance', 'events.sessions',
        'email.bulk_send', 'email.templates', 'email.contacts', 'email.birthday_messages',
        'donations.view', 'donations.manage', 'donations.gifts',
        'settings.general', 'settings.organization_setup', 'settings.appearance', 'settings.security_settings',
        'admin.rbac_management', 'admin.create_users', 'admin.permission_management',
        'reports.analytics', 'ai.predictions'
    ],
    'limited_admin': [
        'dashboard.view', 'dashboard.analytics',
        'members.view', 'members.add', 'members.edit', 'members.family_management',
        'events.view', 'events.create', 'events.edit', 'events.attendance',
        'email.bulk_send', 'email.templates', 'email.contacts',
        'donations.view', 'donations.manage',
        'settings.profile', 'reports.analytics'
    ],
    'event_coordinator': [
        'dashboard.view', 'dashboard.realtime',
        'members.view', 'events.view', 'events.create', 'events.edit', 'events.attendance',
        'events.sessions', 'events.categories', 'events.reports',
        'email.bulk_send', 'email.templates', 'settings.profile', 'notifications.view'
    ],
    'session_moderator': [
        'dashboard.view', 'events.view', 'events.attendance', 'events.sessions',
        'settings.profile', 'notifications.view'
    ],
    'staff': [
        'dashboard.view', 'settings.profile', 'notifications.view'
    ]
};

function selectUser(userId) {
    selectedUserId = userId;

    // Update UI
    document.querySelectorAll('.user-card').forEach(card => {
        card.classList.remove('selected');
    });
    document.querySelector(`[data-user-id="${userId}"]`).classList.add('selected');

    // Update form
    document.getElementById('selected_user_id').value = userId;
    document.getElementById('savePermissionsBtn').disabled = false;

    // Update header
    const username = document.querySelector(`[data-user-id="${userId}"] strong`).textContent;
    document.getElementById('selected-user-name').textContent = `- Managing permissions for: ${username}`;

    // Load user permissions
    loadUserPermissions(userId);
}

function loadUserPermissions(userId) {
    // Clear all checkboxes first
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });

    // Check the user's permissions
    const permissions = userPermissions[userId] || [];
    permissions.forEach(permissionId => {
        const checkbox = document.getElementById(`perm_${permissionId}`);
        if (checkbox) {
            checkbox.checked = true;
        }
    });
}

function selectAllPermissions() {
    if (!selectedUserId) {
        alert('Please select a user first');
        return;
    }

    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
}

function clearAllPermissions() {
    if (!selectedUserId) {
        alert('Please select a user first');
        return;
    }

    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
}

function resetPermissions() {
    if (!selectedUserId) {
        alert('Please select a user first');
        return;
    }

    loadUserPermissions(selectedUserId);
}

function savePermissions() {
    if (!selectedUserId) {
        alert('Please select a user first');
        return;
    }

    document.getElementById('permissionForm').submit();
}

function applyRoleTemplate(roleName) {
    if (!selectedUserId) {
        alert('Please select a user first');
        return;
    }

    const template = roleTemplates[roleName];
    if (!template) {
        alert('Role template not found');
        return;
    }

    // Clear all checkboxes first
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });

    // Apply template permissions
    template.forEach(permissionKey => {
        const checkbox = document.querySelector(`[data-permission-key="${permissionKey}"]`);
        if (checkbox) {
            checkbox.checked = true;
        }
    });

    // Show confirmation
    const username = document.querySelector(`[data-user-id="${selectedUserId}"] strong`).textContent;
    const roleFriendlyName = roleName.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

    if (confirm(`Applied ${roleFriendlyName} permissions to ${username}. Save changes now?`)) {
        savePermissions();
    }
}

// Auto-save functionality (optional)
let autoSaveTimeout;
document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        if (selectedUserId) {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(() => {
                // Could implement auto-save here
                console.log('Permission changed for user', selectedUserId);
            }, 1000);
        }
    });
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
            case 's':
                e.preventDefault();
                savePermissions();
                break;
            case 'a':
                e.preventDefault();
                selectAllPermissions();
                break;
        }
    }
});

// Search functionality
function setupPermissionSearch() {
    const searchInput = document.getElementById('permissionSearch');
    if (!searchInput) return;

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const permissionRows = document.querySelectorAll('.permission-row');
        const categoryHeaders = document.querySelectorAll('.permission-category');

        permissionRows.forEach(row => {
            const permissionText = row.textContent.toLowerCase();
            const isVisible = permissionText.includes(searchTerm);
            row.style.display = isVisible ? 'block' : 'none';
        });

        // Show/hide category headers based on visible permissions
        categoryHeaders.forEach(header => {
            const categoryRows = header.parentNode.querySelectorAll('.permission-row');
            const hasVisibleRows = Array.from(categoryRows).some(row => row.style.display !== 'none');
            header.style.display = hasVisibleRows ? 'block' : 'none';
        });
    });
}

function clearSearch() {
    const searchInput = document.getElementById('permissionSearch');
    if (searchInput) {
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
    }
}

// Initialize tooltips and search
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Setup search functionality
    setupPermissionSearch();
});
</script>

<?php include 'includes/footer.php'; ?>
