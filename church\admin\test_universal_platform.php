<?php
/**
 * Universal Platform Diagnostic Test
 * Verify all Universal Platform components are working correctly
 */

require_once '../config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'Universal Platform Diagnostic';
include 'includes/header.php';

// Test URLs
$test_urls = [
    'AI Predictions Dashboard' => 'universal_ai_dashboard.php',
    'Universal Analytics Dashboard' => 'universal_analytics_dashboard.php',
    'Organization Setup' => 'universal_organization_setup.php',
    'Progressive Web App' => 'pwa/',
    'Unified Settings' => 'unified_settings.php',
    'Migration Tools' => 'migrate_settings_tables.php'
];

// Test file existence
$file_tests = [];
foreach ($test_urls as $name => $url) {
    $file_path = __DIR__ . '/' . $url;
    if ($url === 'pwa/') {
        $file_path = __DIR__ . '/pwa/index.php';
    }
    
    $file_tests[$name] = [
        'url' => $url,
        'exists' => file_exists($file_path),
        'readable' => file_exists($file_path) && is_readable($file_path),
        'path' => $file_path
    ];
}

// Test session variables
$session_tests = [
    'admin_id' => isset($_SESSION['admin_id']),
    'admin_name' => isset($_SESSION['admin_name']),
    'admin_username' => isset($_SESSION['admin_username'])
];

// Test database connection
$db_test = false;
try {
    $stmt = $pdo->query("SELECT 1");
    $db_test = true;
} catch (Exception $e) {
    $db_error = $e->getMessage();
}

// Test configuration
$config_tests = [
    'SITE_URL' => defined('SITE_URL'),
    'ADMIN_URL' => defined('ADMIN_URL'),
    'PDO Connection' => isset($pdo),
    'Database Connected' => $db_test
];
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2"><i class="bi bi-check-circle"></i> Universal Platform Diagnostic</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise"></i> Refresh Tests
                </button>
            </div>
        </div>
    </div>

    <!-- Overall Status -->
    <div class="row mb-4">
        <div class="col-12">
            <?php
            $all_files_ok = true;
            $all_sessions_ok = true;
            $all_config_ok = true;
            
            foreach ($file_tests as $test) {
                if (!$test['exists'] || !$test['readable']) {
                    $all_files_ok = false;
                    break;
                }
            }
            
            foreach ($session_tests as $test) {
                if (!$test) {
                    $all_sessions_ok = false;
                    break;
                }
            }
            
            foreach ($config_tests as $test) {
                if (!$test) {
                    $all_config_ok = false;
                    break;
                }
            }
            
            $overall_status = $all_files_ok && $all_sessions_ok && $all_config_ok;
            ?>
            
            <div class="alert <?php echo $overall_status ? 'alert-success' : 'alert-warning'; ?>">
                <h5>
                    <i class="bi bi-<?php echo $overall_status ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                    Overall Status: <?php echo $overall_status ? 'ALL SYSTEMS OPERATIONAL' : 'ISSUES DETECTED'; ?>
                </h5>
                <?php if ($overall_status): ?>
                    <p class="mb-0">All Universal Platform components are working correctly!</p>
                <?php else: ?>
                    <p class="mb-0">Some issues were detected. Please review the details below.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- File Tests -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-file-earmark-check"></i> File Accessibility Tests</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Component</th>
                            <th>URL</th>
                            <th>File Exists</th>
                            <th>Readable</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($file_tests as $name => $test): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($name); ?></strong></td>
                                <td><code><?php echo htmlspecialchars($test['url']); ?></code></td>
                                <td>
                                    <?php if ($test['exists']): ?>
                                        <span class="badge bg-success">✓ Yes</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">✗ No</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($test['readable']): ?>
                                        <span class="badge bg-success">✓ Yes</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">✗ No</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($test['exists'] && $test['readable']): ?>
                                        <a href="<?php echo htmlspecialchars($test['url']); ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                            <i class="bi bi-box-arrow-up-right"></i> Test
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">Not Available</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Session Tests -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-person-check"></i> Session Authentication Tests</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Session Variable</th>
                            <th>Status</th>
                            <th>Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($session_tests as $var => $status): ?>
                            <tr>
                                <td><code>$_SESSION['<?php echo htmlspecialchars($var); ?>']</code></td>
                                <td>
                                    <?php if ($status): ?>
                                        <span class="badge bg-success">✓ Set</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">✗ Not Set</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($status): ?>
                                        <code><?php echo htmlspecialchars($_SESSION[$var] ?? 'N/A'); ?></code>
                                    <?php else: ?>
                                        <span class="text-muted">Not Available</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Configuration Tests -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-gear-fill"></i> Configuration Tests</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Configuration</th>
                            <th>Status</th>
                            <th>Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($config_tests as $config => $status): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($config); ?></strong></td>
                                <td>
                                    <?php if ($status): ?>
                                        <span class="badge bg-success">✓ OK</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">✗ Error</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    if ($config === 'SITE_URL' && defined('SITE_URL')) {
                                        echo '<code>' . htmlspecialchars(SITE_URL) . '</code>';
                                    } elseif ($config === 'ADMIN_URL' && defined('ADMIN_URL')) {
                                        echo '<code>' . htmlspecialchars(ADMIN_URL) . '</code>';
                                    } elseif ($config === 'PDO Connection') {
                                        echo $status ? '<span class="text-success">Connected</span>' : '<span class="text-danger">Not Connected</span>';
                                    } elseif ($config === 'Database Connected') {
                                        echo $status ? '<span class="text-success">Working</span>' : '<span class="text-danger">Error: ' . htmlspecialchars($db_error ?? 'Unknown') . '</span>';
                                    } else {
                                        echo '<span class="text-muted">N/A</span>';
                                    }
                                    ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-lightning-fill"></i> Quick Actions</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="d-grid">
                        <a href="dashboard.php" class="btn btn-primary">
                            <i class="bi bi-house"></i> Return to Dashboard
                        </a>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-grid">
                        <a href="universal_ai_dashboard.php" class="btn btn-outline-primary">
                            <i class="bi bi-robot"></i> AI Predictions
                        </a>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-grid">
                        <a href="universal_analytics_dashboard.php" class="btn btn-outline-primary">
                            <i class="bi bi-graph-up"></i> Analytics
                        </a>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-grid">
                        <a href="universal_organization_setup.php" class="btn btn-outline-primary">
                            <i class="bi bi-gear"></i> Setup
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
