/**
 * Real-time Sync System for Session Attendance
 * Prevents conflicts when multiple staff mark attendance simultaneously
 */

class RealtimeSync {
    constructor(sessionId, options = {}) {
        this.sessionId = sessionId;
        this.lastSync = null;
        this.syncInterval = options.syncInterval || 5000; // 5 seconds
        this.conflictCheckEnabled = options.conflictCheck !== false;
        this.callbacks = {
            onUpdate: options.onUpdate || (() => {}),
            onConflict: options.onConflict || (() => {}),
            onError: options.onError || (() => {}),
            onStatsUpdate: options.onStatsUpdate || (() => {})
        };
        
        this.isActive = false;
        this.syncTimer = null;
        this.pendingUpdates = new Map();
        this.lockedRecords = new Set();
        
        this.init();
    }
    
    init() {
        this.start();
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pause();
            } else {
                this.resume();
            }
        });
        
        // Handle beforeunload to unlock records
        window.addEventListener('beforeunload', () => {
            this.unlockAllRecords();
        });
    }
    
    start() {
        if (this.isActive) return;
        
        this.isActive = true;
        this.lastSync = new Date().toISOString();
        this.scheduleSync();
        
        console.log(`RealtimeSync started for session ${this.sessionId}`);
    }
    
    pause() {
        if (this.syncTimer) {
            clearTimeout(this.syncTimer);
            this.syncTimer = null;
        }
        this.isActive = false;
        
        console.log('RealtimeSync paused');
    }
    
    resume() {
        if (!this.isActive) {
            this.start();
            console.log('RealtimeSync resumed');
        }
    }
    
    stop() {
        this.pause();
        this.unlockAllRecords();
        
        console.log('RealtimeSync stopped');
    }
    
    scheduleSync() {
        if (!this.isActive) return;
        
        this.syncTimer = setTimeout(() => {
            this.syncAttendanceStatus();
        }, this.syncInterval);
    }
    
    async syncAttendanceStatus() {
        if (!this.isActive) return;
        
        try {
            const response = await this.makeRequest('sync_attendance_status', {
                session_id: this.sessionId,
                last_sync: this.lastSync
            });
            
            if (response.success && response.data.updates.length > 0) {
                this.processUpdates(response.data.updates);
                this.callbacks.onUpdate(response.data.updates);
            }
            
            this.lastSync = response.data.sync_time;
            
            // Also sync session stats
            await this.syncSessionStats();
            
        } catch (error) {
            console.error('Sync error:', error);
            this.callbacks.onError(error);
        }
        
        this.scheduleSync();
    }
    
    async syncSessionStats() {
        try {
            const response = await this.makeRequest('get_session_stats', {
                session_id: this.sessionId
            });
            
            if (response.success) {
                this.callbacks.onStatsUpdate(response.data);
            }
        } catch (error) {
            console.error('Stats sync error:', error);
        }
    }
    
    processUpdates(updates) {
        updates.forEach(update => {
            // Update UI elements
            this.updateAttendeeStatus(update);
            
            // Remove from pending updates if it was pending
            this.pendingUpdates.delete(update.attendee_id);
        });
    }
    
    updateAttendeeStatus(update) {
        const attendeeElement = document.querySelector(`[data-attendee-id="${update.attendee_id}"]`);
        if (!attendeeElement) return;
        
        // Update status badge
        const statusBadge = attendeeElement.querySelector('.status-badge');
        if (statusBadge) {
            statusBadge.className = `badge status-badge bg-${this.getStatusColor(update.attendance_status)}`;
            statusBadge.textContent = this.capitalizeFirst(update.attendance_status);
        }
        
        // Update checkbox state
        const checkbox = attendeeElement.querySelector('.attendee-checkbox');
        if (checkbox) {
            if (update.attendance_status === 'attended') {
                checkbox.disabled = true;
                checkbox.checked = false;
                attendeeElement.classList.add('attended');
                attendeeElement.classList.remove('selected');
            } else {
                checkbox.disabled = false;
                attendeeElement.classList.remove('attended');
            }
        }
        
        // Show update notification
        this.showUpdateNotification(update);
    }
    
    async checkAttendanceConflict(memberId) {
        if (!this.conflictCheckEnabled || !memberId) return { hasConflict: false };
        
        try {
            const response = await this.makeRequest('check_attendance_conflict', {
                member_id: memberId,
                session_id: this.sessionId,
                check_time: new Date().toISOString()
            });
            
            if (response.success && response.data.has_conflict) {
                this.callbacks.onConflict(response.data);
                return { hasConflict: true, conflicts: response.data.conflicts };
            }
            
            return { hasConflict: false };
        } catch (error) {
            console.error('Conflict check error:', error);
            return { hasConflict: false };
        }
    }
    
    async lockAttendanceRecord(attendanceId) {
        try {
            const response = await this.makeRequest('lock_attendance_record', {
                attendance_id: attendanceId
            });
            
            if (response.success) {
                this.lockedRecords.add(attendanceId);
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('Lock error:', error);
            return false;
        }
    }
    
    async unlockAttendanceRecord(attendanceId) {
        try {
            await this.makeRequest('unlock_attendance_record', {
                attendance_id: attendanceId
            });
            
            this.lockedRecords.delete(attendanceId);
        } catch (error) {
            console.error('Unlock error:', error);
        }
    }
    
    async unlockAllRecords() {
        const unlockPromises = Array.from(this.lockedRecords).map(id => 
            this.unlockAttendanceRecord(id)
        );
        
        await Promise.all(unlockPromises);
    }
    
    async batchUpdateAttendance(updates) {
        try {
            // Mark updates as pending
            updates.forEach(update => {
                this.pendingUpdates.set(update.attendee_id, update);
            });
            
            const response = await this.makeRequest('batch_update_attendance', {
                session_id: this.sessionId,
                updates: updates
            });
            
            if (response.success) {
                const { success_count, conflict_count, conflicts } = response.data;
                
                if (conflict_count > 0) {
                    this.handleBatchConflicts(conflicts);
                }
                
                return {
                    success: true,
                    successCount: success_count,
                    conflictCount: conflict_count,
                    conflicts: conflicts
                };
            }
            
            return { success: false, message: response.message };
            
        } catch (error) {
            console.error('Batch update error:', error);
            return { success: false, message: error.message };
        }
    }
    
    handleBatchConflicts(conflicts) {
        conflicts.forEach(attendeeId => {
            const attendeeElement = document.querySelector(`[data-attendee-id="${attendeeId}"]`);
            if (attendeeElement) {
                // Highlight conflicted attendee
                attendeeElement.style.border = '2px solid #dc3545';
                attendeeElement.style.backgroundColor = '#fff5f5';
                
                // Show conflict indicator
                const conflictBadge = document.createElement('span');
                conflictBadge.className = 'badge bg-danger ms-2';
                conflictBadge.textContent = 'Conflict';
                conflictBadge.title = 'This attendee has a scheduling conflict';
                
                const nameElement = attendeeElement.querySelector('strong');
                if (nameElement && !nameElement.querySelector('.badge.bg-danger')) {
                    nameElement.appendChild(conflictBadge);
                }
            }
        });
        
        // Show conflict notification
        this.showConflictNotification(conflicts.length);
    }
    
    showUpdateNotification(update) {
        // Create a subtle notification
        const notification = document.createElement('div');
        notification.className = 'alert alert-info alert-dismissible fade show position-fixed';
        notification.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 300px;
            font-size: 0.9rem;
        `;
        
        notification.innerHTML = `
            <i class="bi bi-info-circle"></i>
            <strong>${update.attendee_name}</strong> marked as ${update.attendance_status}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }
    
    showConflictNotification(conflictCount) {
        const notification = document.createElement('div');
        notification.className = 'alert alert-warning alert-dismissible fade show position-fixed';
        notification.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 350px;
        `;
        
        notification.innerHTML = `
            <i class="bi bi-exclamation-triangle"></i>
            <strong>Scheduling Conflicts Detected</strong><br>
            ${conflictCount} attendee(s) have conflicts with other sessions.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    
    async makeRequest(action, data) {
        const formData = new FormData();
        formData.append('action', action);
        
        Object.keys(data).forEach(key => {
            if (Array.isArray(data[key])) {
                data[key].forEach(item => {
                    formData.append(`${key}[]`, typeof item === 'object' ? JSON.stringify(item) : item);
                });
            } else {
                formData.append(key, data[key]);
            }
        });
        
        const response = await fetch('ajax/realtime_sync.php', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.message || 'Request failed');
        }
        
        return result;
    }
    
    getStatusColor(status) {
        switch (status) {
            case 'attended': return 'success';
            case 'no_show': return 'danger';
            case 'registered': return 'secondary';
            default: return 'secondary';
        }
    }
    
    capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }
}

// Global sync manager
window.RealtimeSyncManager = {
    instances: new Map(),
    
    create(sessionId, options = {}) {
        if (this.instances.has(sessionId)) {
            this.instances.get(sessionId).stop();
        }
        
        const sync = new RealtimeSync(sessionId, options);
        this.instances.set(sessionId, sync);
        return sync;
    },
    
    get(sessionId) {
        return this.instances.get(sessionId);
    },
    
    destroy(sessionId) {
        const sync = this.instances.get(sessionId);
        if (sync) {
            sync.stop();
            this.instances.delete(sessionId);
        }
    },
    
    destroyAll() {
        this.instances.forEach((sync, sessionId) => {
            sync.stop();
        });
        this.instances.clear();
    }
};

// Auto-cleanup on page unload
window.addEventListener('beforeunload', () => {
    window.RealtimeSyncManager.destroyAll();
});
