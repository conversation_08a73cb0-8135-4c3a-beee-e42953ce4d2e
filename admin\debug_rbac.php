<?php
/**
 * Debug RBAC System - Check current database state
 */

require_once '../church/config.php';

echo "<h1>RBAC System Debug Information</h1>";

try {
    // Check if admin user exists
    echo "<h2>1. Admin User Check</h2>";
    $stmt = $pdo->prepare("SELECT id, username, full_name, created_at FROM admins WHERE username = 'admin'");
    $stmt->execute();
    $admin_user = $stmt->fetch();
    
    if ($admin_user) {
        echo "<p><strong>✓ Admin user found:</strong></p>";
        echo "<ul>";
        echo "<li>ID: " . $admin_user['id'] . "</li>";
        echo "<li>Username: " . $admin_user['username'] . "</li>";
        echo "<li>Full Name: " . $admin_user['full_name'] . "</li>";
        echo "<li>Created: " . $admin_user['created_at'] . "</li>";
        echo "</ul>";
        $admin_id = $admin_user['id'];
    } else {
        echo "<p><strong>✗ Admin user 'admin' not found!</strong></p>";
        $admin_id = null;
    }
    
    // Check if RBAC tables exist
    echo "<h2>2. RBAC Tables Check</h2>";
    $tables = ['user_roles', 'permissions', 'role_permissions', 'user_role_assignments'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p><strong>✓ Table '$table' exists</strong> - $count records</p>";
        } catch (Exception $e) {
            echo "<p><strong>✗ Table '$table' missing or error:</strong> " . $e->getMessage() . "</p>";
        }
    }
    
    // Check user roles
    echo "<h2>3. Available Roles</h2>";
    try {
        $stmt = $pdo->query("SELECT * FROM user_roles ORDER BY hierarchy_level");
        $roles = $stmt->fetchAll();
        
        if ($roles) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Role Name</th><th>Display Name</th><th>Hierarchy Level</th><th>Dashboard Route</th></tr>";
            foreach ($roles as $role) {
                echo "<tr>";
                echo "<td>" . $role['id'] . "</td>";
                echo "<td>" . $role['role_name'] . "</td>";
                echo "<td>" . $role['role_display_name'] . "</td>";
                echo "<td>" . $role['hierarchy_level'] . "</td>";
                echo "<td>" . $role['dashboard_route'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p><strong>✗ No roles found!</strong></p>";
        }
    } catch (Exception $e) {
        echo "<p><strong>✗ Error checking roles:</strong> " . $e->getMessage() . "</p>";
    }
    
    // Check role assignments for admin user
    if ($admin_id) {
        echo "<h2>4. Admin User Role Assignments</h2>";
        try {
            $stmt = $pdo->prepare("
                SELECT ur.role_name, ur.role_display_name, ur.hierarchy_level, 
                       ura.assigned_at, ura.expires_at, ura.is_active
                FROM user_role_assignments ura
                JOIN user_roles ur ON ura.role_id = ur.id
                WHERE ura.user_id = ?
                ORDER BY ur.hierarchy_level
            ");
            $stmt->execute([$admin_id]);
            $assignments = $stmt->fetchAll();
            
            if ($assignments) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>Role Name</th><th>Display Name</th><th>Hierarchy</th><th>Assigned At</th><th>Expires At</th><th>Active</th></tr>";
                foreach ($assignments as $assignment) {
                    echo "<tr>";
                    echo "<td>" . $assignment['role_name'] . "</td>";
                    echo "<td>" . $assignment['role_display_name'] . "</td>";
                    echo "<td>" . $assignment['hierarchy_level'] . "</td>";
                    echo "<td>" . $assignment['assigned_at'] . "</td>";
                    echo "<td>" . ($assignment['expires_at'] ?: 'Never') . "</td>";
                    echo "<td>" . ($assignment['is_active'] ? 'Yes' : 'No') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p><strong>✗ No role assignments found for admin user!</strong></p>";
            }
        } catch (Exception $e) {
            echo "<p><strong>✗ Error checking role assignments:</strong> " . $e->getMessage() . "</p>";
        }
    }
    
    // Test RBAC class
    echo "<h2>5. RBAC Class Test</h2>";
    if ($admin_id) {
        try {
            require_once '../church/admin/includes/rbac_access_control.php';
            $rbac = new RBACAccessControl($pdo, $admin_id);
            
            echo "<p><strong>Primary Role:</strong> " . ($rbac->getPrimaryRole() ?: 'None') . "</p>";
            echo "<p><strong>All Roles:</strong> " . implode(', ', array_column($rbac->getUserRoles(), 'role_name')) . "</p>";
            echo "<p><strong>Has super_admin role:</strong> " . ($rbac->hasRole('super_admin') ? 'Yes' : 'No') . "</p>";
            echo "<p><strong>Default Dashboard:</strong> " . $rbac->getDefaultDashboard() . "</p>";
            
        } catch (Exception $e) {
            echo "<p><strong>✗ Error testing RBAC class:</strong> " . $e->getMessage() . "</p>";
        }
    }
    
    // Check if RBAC system needs initialization
    echo "<h2>6. RBAC Initialization Status</h2>";
    try {
        require_once '../church/admin/includes/rbac_system.php';
        $rbac_system = new RBACSystem($pdo);
        echo "<p><strong>RBAC System Initialized:</strong> " . ($rbac_system->isInitialized() ? 'Yes' : 'No') . "</p>";
    } catch (Exception $e) {
        echo "<p><strong>✗ Error checking RBAC system:</strong> " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p><strong>✗ Database connection error:</strong> " . $e->getMessage() . "</p>";
}
?>
