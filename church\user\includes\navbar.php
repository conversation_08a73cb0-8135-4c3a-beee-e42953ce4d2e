<?php
/**
 * User Navigation Bar
 *
 * Centralized navigation for user interface with organized submenus
 */

// Include notification functions
require_once __DIR__ . '/../../includes/notification_functions.php';

// Get current page for active state
$current_page = basename($_SERVER['PHP_SELF']);

// Get unread notification count for current user
$unreadNotificationCount = 0;
if (isset($_SESSION['user_id'])) {
    $unreadNotificationCount = getUnreadNotificationCount($pdo, $_SESSION['user_id']);
}

// Helper function to check if current page is active
function isActivePage($page) {
    global $current_page;
    return $current_page === $page ? 'active' : '';
}

// Helper function to check if any page in array is active (for dropdown highlighting)
function isActiveSection($pages) {
    global $current_page;
    return in_array($current_page, $pages) ? 'active' : '';
}
?>

<nav class="navbar navbar-expand-lg">
    <div class="container">
        <a class="navbar-brand d-flex align-items-center" href="dashboard.php">
            <?php
            // Use the existing logo management system
            $headerLogo = get_site_setting('header_logo', '');
            $mainLogo = get_site_setting('main_logo', '');
            $logoToUse = !empty($headerLogo) ? $headerLogo : $mainLogo;

            if (!empty($logoToUse)): ?>
                <img src="<?php echo get_base_url() . '/' . htmlspecialchars($logoToUse); ?>"
                     alt="<?php echo get_organization_name(); ?>"
                     class="navbar-logo me-2">
                <span class="navbar-brand-text"><?php echo htmlspecialchars(get_organization_name()); ?></span>
            <?php else: ?>
                <i class="bi bi-house-heart me-2"></i>
                <span class="navbar-brand-text"><?php echo htmlspecialchars($sitename ?? 'Church Management'); ?></span>
            <?php endif; ?>
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <!-- Empty left side to push everything to the right -->
            <div class="navbar-nav me-auto"></div>

            <!-- Main navigation items on the right -->
            <ul class="navbar-nav me-3">
                <li class="nav-item">
                    <a class="nav-link <?php echo isActivePage('dashboard.php'); ?>" href="dashboard.php">
                        <i class="bi bi-speedometer2"></i> Dashboard
                    </a>
                </li>

                <!-- Events Dropdown -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo isActiveSection(['events.php', 'birthday_templates.php']); ?>"
                       href="#" id="eventsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-calendar-event"></i> Events
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="eventsDropdown">
                        <li><a class="dropdown-item <?php echo isActivePage('events.php'); ?>" href="events.php">
                            <i class="bi bi-calendar-event"></i> All Events
                        </a></li>
                        <li><a class="dropdown-item <?php echo isActivePage('birthday_templates.php'); ?>" href="birthday_templates.php">
                            <i class="bi bi-gift"></i> Birthdays
                        </a></li>
                    </ul>
                </li>

                <!-- Gifts Dropdown -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo isActiveSection(['send_gift.php', 'my_gifts.php']); ?>"
                       href="#" id="giftsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-gift"></i> Gifts
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="giftsDropdown">
                        <li><a class="dropdown-item <?php echo isActivePage('send_gift.php'); ?>" href="send_gift.php">
                            <i class="bi bi-send"></i> Send Gifts
                        </a></li>
                        <li><a class="dropdown-item <?php echo isActivePage('my_gifts.php'); ?>" href="my_gifts.php">
                            <i class="bi bi-gift-fill"></i> My Gifts
                        </a></li>
                    </ul>
                </li>

                <!-- Volunteer Dropdown -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo isActiveSection(['volunteer_opportunities.php', 'skills.php', 'family_management.php', 'enhanced_donate.php']); ?>"
                       href="#" id="volunteerDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-person-workspace"></i> Volunteer
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="volunteerDropdown">
                        <li><a class="dropdown-item <?php echo isActivePage('volunteer_opportunities.php'); ?>" href="volunteer_opportunities.php">
                            <i class="bi bi-person-workspace"></i> Opportunities
                        </a></li>
                        <li><a class="dropdown-item <?php echo isActivePage('skills.php'); ?>" href="skills.php">
                            <i class="bi bi-tools"></i> Skills
                        </a></li>
                        <li><a class="dropdown-item <?php echo isActivePage('family_management.php'); ?>" href="family_management.php">
                            <i class="bi bi-people"></i> Family
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item <?php echo isActivePage('enhanced_donate.php'); ?>" href="enhanced_donate.php">
                            <i class="bi bi-heart"></i> Donation
                        </a></li>
                    </ul>
                </li>

                <!-- Requests -->
                <li class="nav-item">
                    <a class="nav-link <?php echo isActivePage('requests.php'); ?>" href="requests.php">
                        <i class="bi bi-chat-heart"></i> Requests
                    </a>
                </li>
            </ul>

            <!-- Notification Bell -->
            <ul class="navbar-nav me-2">
                <li class="nav-item dropdown">
                    <a class="nav-link position-relative" href="#" id="notificationDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-bell fs-5"></i>
                        <?php if ($unreadNotificationCount > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                <?php echo $unreadNotificationCount > 99 ? '99+' : $unreadNotificationCount; ?>
                                <span class="visually-hidden">unread notifications</span>
                            </span>
                        <?php endif; ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationDropdown" style="width: 350px; max-height: 400px; overflow-y: auto;">
                        <li class="dropdown-header d-flex justify-content-between align-items-center">
                            <span>Notifications</span>
                            <?php if ($unreadNotificationCount > 0): ?>
                                <button class="btn btn-sm btn-link text-decoration-none p-0" onclick="markAllAsRead()">
                                    Mark all as read
                                </button>
                            <?php endif; ?>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <div id="notificationList">
                            <!-- Notifications will be loaded here via AJAX -->
                            <li class="dropdown-item text-center text-muted">
                                <i class="bi bi-arrow-clockwise spin"></i> Loading...
                            </li>
                        </div>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-center" href="notifications.php">
                                <i class="bi bi-list-ul"></i> View All Notifications
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>

            <!-- User dropdown -->
            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-person-circle"></i> 
                        <?php 
                        if (isset($userData) && !empty($userData)) {
                            echo htmlspecialchars($userData['first_name'] ?: $userData['full_name']);
                        } else {
                            echo 'User';
                        }
                        ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                        <li><a class="dropdown-item" href="profile.php">
                            <i class="bi bi-person"></i> My Profile
                        </a></li>
                        <li><a class="dropdown-item" href="settings.php">
                            <i class="bi bi-gear"></i> Settings
                        </a></li>
                        <li><a class="dropdown-item" href="family_management.php">
                            <i class="bi bi-people"></i> Family Management
                        </a></li>
                        <li><a class="dropdown-item" href="change_password.php">
                            <i class="bi bi-shield-lock"></i> Change Password
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Notification System Styles -->
<style>
.notification-dropdown {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f8f9fa;
    transition: background-color 0.2s;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
}

.notification-title {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.notification-message {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.75rem;
    color: #adb5bd;
}

.notification-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Notification badge styles - specific to navbar */
.navbar .badge {
    font-size: 0.6rem;
    min-width: 1.2rem;
    height: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Status badges - override for content areas */
.request-card .badge,
.request-item .badge,
.badge.bg-danger:not(.navbar .badge),
.badge.bg-secondary:not(.navbar .badge),
.badge.bg-success:not(.navbar .badge),
.badge.bg-warning:not(.navbar .badge),
.badge.bg-info:not(.navbar .badge) {
    border-radius: 6px !important;
    padding: 0.25rem 0.5rem !important;
    font-weight: 500 !important;
    font-size: 0.75rem !important;
    line-height: 1.2 !important;
    min-width: auto !important;
    height: auto !important;
    display: inline-block !important;
}
</style>

<!-- Notification System JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load notifications when dropdown is opened
    document.getElementById('notificationDropdown').addEventListener('click', function() {
        loadNotifications();
    });

    // Auto-refresh notifications every 30 seconds
    setInterval(function() {
        updateNotificationCount();
    }, 30000);
});

function loadNotifications() {
    const notificationList = document.getElementById('notificationList');

    fetch('ajax/get_notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayNotifications(data.notifications);
            } else {
                notificationList.innerHTML = '<li class="dropdown-item text-center text-muted">Error loading notifications</li>';
            }
        })
        .catch(error => {
            console.error('Error loading notifications:', error);
            notificationList.innerHTML = '<li class="dropdown-item text-center text-muted">Error loading notifications</li>';
        });
}

function displayNotifications(notifications) {
    const notificationList = document.getElementById('notificationList');

    if (notifications.length === 0) {
        notificationList.innerHTML = '<li class="dropdown-item text-center text-muted">No notifications</li>';
        return;
    }

    let html = '';
    notifications.forEach(notification => {
        const isUnread = notification.is_read == 0;
        const iconClass = getNotificationIcon(notification.notification_type);
        const colorClass = getNotificationColorClass(notification.priority);

        html += `
            <li class="notification-item ${isUnread ? 'unread' : ''}" data-id="${notification.id}">
                <div class="d-flex">
                    <div class="notification-icon bg-light ${colorClass}">
                        <i class="bi ${iconClass}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="notification-title">${escapeHtml(notification.title)}</div>
                        <div class="notification-message">${escapeHtml(notification.message.substring(0, 100))}${notification.message.length > 100 ? '...' : ''}</div>
                        <div class="notification-time">
                            <i class="bi bi-clock"></i> ${notification.formatted_time}
                            ${notification.sender_name ? ' • ' + escapeHtml(notification.sender_name) : ''}
                        </div>
                    </div>
                    ${isUnread ? '<div class="text-primary"><i class="bi bi-circle-fill" style="font-size: 0.5rem;"></i></div>' : ''}
                </div>
            </li>
        `;
    });

    notificationList.innerHTML = html;

    // Add click handlers to mark as read
    document.querySelectorAll('.notification-item').forEach(item => {
        item.addEventListener('click', function() {
            const notificationId = this.dataset.id;
            markAsRead(notificationId);
        });
    });
}

function markAsRead(notificationId) {
    fetch('ajax/mark_notification_read.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notification_id: notificationId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateNotificationCount();
            // Remove unread styling
            const item = document.querySelector(`[data-id="${notificationId}"]`);
            if (item) {
                item.classList.remove('unread');
                const unreadIndicator = item.querySelector('.bi-circle-fill');
                if (unreadIndicator) {
                    unreadIndicator.parentElement.remove();
                }
            }
        }
    })
    .catch(error => console.error('Error marking notification as read:', error));
}

function markAllAsRead() {
    fetch('ajax/mark_all_notifications_read.php', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateNotificationCount();
            loadNotifications(); // Reload to update styling
        }
    })
    .catch(error => console.error('Error marking all notifications as read:', error));
}

function updateNotificationCount() {
    fetch('ajax/get_notification_count.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const badge = document.querySelector('#notificationDropdown .badge');
                const markAllBtn = document.querySelector('.dropdown-header button');

                if (data.count > 0) {
                    if (badge) {
                        badge.textContent = data.count > 99 ? '99+' : data.count;
                    } else {
                        // Create badge if it doesn't exist
                        const bellIcon = document.querySelector('#notificationDropdown i');
                        const newBadge = document.createElement('span');
                        newBadge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger';
                        newBadge.innerHTML = `${data.count > 99 ? '99+' : data.count}<span class="visually-hidden">unread notifications</span>`;
                        bellIcon.parentElement.appendChild(newBadge);
                    }
                    if (markAllBtn) markAllBtn.style.display = 'block';
                } else {
                    if (badge) badge.remove();
                    if (markAllBtn) markAllBtn.style.display = 'none';
                }
            }
        })
        .catch(error => console.error('Error updating notification count:', error));
}

function getNotificationIcon(type) {
    const icons = {
        'announcement': 'bi-megaphone',
        'message': 'bi-chat-dots',
        'birthday': 'bi-gift',
        'event': 'bi-calendar-event',
        'session': 'bi-calendar-check',
        'session_reminder': 'bi-alarm',
        'session_update': 'bi-calendar-x',
        'session_registration': 'bi-calendar-plus',
        'reminder': 'bi-clock',
        'donation': 'bi-heart',
        'system': 'bi-gear'
    };
    return icons[type] || 'bi-bell';
}

function getNotificationColorClass(priority) {
    const colors = {
        'low': 'text-muted',
        'normal': 'text-primary',
        'high': 'text-warning',
        'urgent': 'text-danger'
    };
    return colors[priority] || 'text-primary';
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Sticky navigation scroll effect
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 50) {
        navbar.classList.add('scrolled');
    } else {
        navbar.classList.remove('scrolled');
    }
});
</script>

<style>
/* Sticky navigation - ensure it works properly across all user pages */
.navbar {
    position: -webkit-sticky !important;
    position: sticky !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1030 !important;
    transition: all 0.3s ease !important;
    width: 100% !important;
    background-color: var(--bs-primary, #667eea) !important;
    backdrop-filter: blur(10px) !important;
    margin: 0 !important;
}

.navbar.scrolled {
    box-shadow: 0 4px 20px rgba(0,0,0,0.25) !important;
    background-color: rgba(102, 126, 234, 0.95) !important;
}

/* Ensure body and html have proper setup for sticky header */
html, body {
    padding-top: 0 !important;
    margin: 0 !important;
    height: 100% !important;
    overflow-x: hidden !important;
}

/* Ensure the navbar container doesn't interfere */
.navbar .container {
    position: relative !important;
}

/* Custom styles for the navbar */
.navbar-nav .dropdown-menu {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.navbar-nav .dropdown-item {
    padding: 0.5rem 1rem;
    transition: background-color 0.15s ease-in-out;
}

.navbar-nav .dropdown-item:hover {
    background-color: #f8f9fa;
}

.navbar-nav .dropdown-item i {
    width: 16px;
    margin-right: 8px;
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.navbar-nav .dropdown-toggle.active {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

/* Navbar Logo Styling */
.navbar-logo {
    max-height: 40px;
    max-width: 150px;
    height: auto;
    width: auto;
    object-fit: contain;
}

.navbar-brand-text {
    font-weight: 600;
    font-size: 1.1rem;
}

/* Mobile responsive adjustments */
@media (max-width: 991.98px) {
    .navbar-nav .dropdown-menu {
        border: 1px solid rgba(255, 255, 255, 0.1);
        background-color: rgba(255, 255, 255, 0.95);
    }

    .navbar-nav .dropdown-item {
        color: #333;
    }
}

/* Hide brand text on mobile, show only logo */
@media (max-width: 768px) {
    .navbar-brand-text {
        display: none;
    }

    .navbar-logo {
        max-height: 35px;
        max-width: 120px;
    }

    .navbar-brand {
        padding: 0.5rem 0;
    }
}
</style>
