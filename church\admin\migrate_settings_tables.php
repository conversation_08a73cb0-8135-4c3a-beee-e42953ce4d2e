<?php
/**
 * Settings Tables Migration Script
 * Consolidates and migrates data from multiple settings tables
 */

require_once '../config.php';

// Start session and check admin access
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['migrate'])) {
    try {
        $pdo->beginTransaction();
        
        // 1. Ensure site_settings table exists
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS site_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_name VARCHAR(100) NOT NULL UNIQUE,
                setting_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        
        // 2. Ensure appearance_settings table exists
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS appearance_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_name VARCHAR(100) NOT NULL UNIQUE,
                setting_value TEXT,
                category VARCHAR(50) DEFAULT 'general',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        
        // 3. Check if old 'settings' table exists and migrate data
        $stmt = $pdo->query("SHOW TABLES LIKE 'settings'");
        if ($stmt->rowCount() > 0) {
            // Get all data from old settings table
            $stmt = $pdo->prepare("SELECT * FROM settings");
            $stmt->execute();
            $old_settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($old_settings as $setting) {
                // Determine the correct column names
                $setting_name = $setting['setting_name'] ?? $setting['name'] ?? '';
                $setting_value = $setting['setting_value'] ?? $setting['value'] ?? '';

                if (!empty($setting_name)) {
                    // Migrate to site_settings table
                    $stmt = $pdo->prepare("
                        INSERT INTO site_settings (setting_name, setting_value)
                        VALUES (?, ?)
                        ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                    ");
                    $stmt->execute([$setting_name, $setting_value]);
                }
            }

            $message .= "Migrated data from 'settings' table. ";
        }
        
        // 4. Set default organization settings if they don't exist
        $default_settings = [
            'site_title' => 'Church Management System',
            'admin_title' => 'Admin Panel',
            'organization_name' => 'Your Organization',
            'organization_type' => 'church',
            'member_term' => 'Member',
            'leader_term' => 'Pastor',
            'group_term' => 'Ministry',
            'event_term' => 'Service',
            'donation_term' => 'Offering',
            'contact_phone' => '',
            'contact_email' => '',
            'contact_address' => '',
            'website_url' => ''
        ];
        
        foreach ($default_settings as $key => $value) {
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO site_settings (setting_name, setting_value) 
                VALUES (?, ?)
            ");
            $stmt->execute([$key, $value]);
        }
        
        // 5. Set default appearance settings
        $default_appearance = [
            'primary_color' => '#007bff',
            'secondary_color' => '#6c757d',
            'success_color' => '#28a745',
            'danger_color' => '#dc3545',
            'warning_color' => '#ffc107',
            'info_color' => '#17a2b8',
            'background_color' => '#ffffff',
            'text_color' => '#212529',
            'primary_font' => 'Inter',
            'font_size_base' => '16',
            'sidebar_style' => 'default',
            'navbar_style' => 'default'
        ];
        
        foreach ($default_appearance as $key => $value) {
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO appearance_settings (setting_name, setting_value, category) 
                VALUES (?, ?, 'appearance')
            ");
            $stmt->execute([$key, $value]);
        }
        
        $pdo->commit();
        $message .= "Settings tables migration completed successfully!";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $error = "Migration failed: " . $e->getMessage();
    }
}

// Get current table status
$table_status = [];
$tables_to_check = ['site_settings', 'appearance_settings', 'settings'];

foreach ($tables_to_check as $table) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM `$table`");
            $stmt->execute();
            $count = $stmt->fetchColumn();
            $table_status[$table] = ['exists' => true, 'count' => $count];
        } else {
            $table_status[$table] = ['exists' => false, 'count' => 0];
        }
    } catch (Exception $e) {
        $table_status[$table] = ['exists' => false, 'count' => 0, 'error' => $e->getMessage()];
    }
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2"><i class="bi bi-database-gear"></i> Settings Tables Migration</h1>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Current Status -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-info-circle"></i> Current Settings Tables Status</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Table Name</th>
                            <th>Status</th>
                            <th>Record Count</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($table_status as $table => $status): ?>
                            <tr>
                                <td><code><?php echo $table; ?></code></td>
                                <td>
                                    <?php if ($status['exists']): ?>
                                        <span class="badge bg-success">Exists</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">Missing</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo number_format($status['count']); ?></td>
                                <td>
                                    <?php if ($table === 'site_settings'): ?>
                                        Primary settings table (recommended)
                                    <?php elseif ($table === 'appearance_settings'): ?>
                                        Appearance and branding settings
                                    <?php elseif ($table === 'settings'): ?>
                                        Legacy settings table (will be migrated)
                                    <?php endif; ?>
                                    
                                    <?php if (isset($status['error'])): ?>
                                        <br><small class="text-danger">Error: <?php echo htmlspecialchars($status['error']); ?></small>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Migration Actions -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="bi bi-arrow-repeat"></i> Migration Actions</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> What this migration will do:</h6>
                <ul class="mb-0">
                    <li>Create <code>site_settings</code> table if it doesn't exist</li>
                    <li>Create <code>appearance_settings</code> table if it doesn't exist</li>
                    <li>Migrate data from legacy <code>settings</code> table if it exists</li>
                    <li>Set up default organization and appearance settings</li>
                    <li>Ensure compatibility with the new unified settings system</li>
                </ul>
            </div>
            
            <form method="POST">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6>Ready to migrate settings tables?</h6>
                        <p class="text-muted mb-0">This operation is safe and will not delete existing data.</p>
                    </div>
                    <button type="submit" name="migrate" class="btn btn-primary btn-lg">
                        <i class="bi bi-database-gear"></i> Run Migration
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Quick Links -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <h6><i class="bi bi-gear"></i> Unified Settings</h6>
                    <p class="text-muted">Access the new unified settings interface</p>
                    <a href="<?php echo admin_url_for('unified_settings.php'); ?>" class="btn btn-outline-primary">
                        Open Unified Settings
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-body text-center">
                    <h6><i class="bi bi-database"></i> Database Status</h6>
                    <p class="text-muted">Check overall database health</p>
                    <a href="<?php echo admin_url_for('check_settings_tables.php'); ?>" class="btn btn-outline-secondary">
                        View Table Details
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
