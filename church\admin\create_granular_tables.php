<?php
// Direct table creation script
require_once '../config.php';

try {
    $pdo->beginTransaction();
    
    echo "Creating granular permission tables...\n";
    
    // 1. Create permission_categories table
    $sql1 = "CREATE TABLE IF NOT EXISTS `permission_categories` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `category_name` varchar(100) NOT NULL,
        `category_display_name` varchar(150) NOT NULL,
        `description` text,
        `icon_class` varchar(100) DEFAULT 'bi bi-gear',
        `sort_order` int(11) DEFAULT 0,
        `is_active` tinyint(1) DEFAULT 1,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `category_name` (`category_name`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    $pdo->exec($sql1);
    echo "✓ permission_categories table created\n";
    
    // 2. Create granular_permissions table
    $sql2 = "CREATE TABLE IF NOT EXISTS `granular_permissions` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `category_id` int(11) NOT NULL,
        `permission_key` varchar(100) NOT NULL,
        `permission_name` varchar(150) NOT NULL,
        `permission_description` text,
        `page_file` varchar(255) DEFAULT NULL,
        `sort_order` int(11) DEFAULT 0,
        `is_active` tinyint(1) DEFAULT 1,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `permission_key` (`permission_key`),
        KEY `category_id` (`category_id`),
        KEY `page_file` (`page_file`),
        CONSTRAINT `granular_permissions_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `permission_categories` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    $pdo->exec($sql2);
    echo "✓ granular_permissions table created\n";
    
    // 3. Create user_individual_permissions table
    $sql3 = "CREATE TABLE IF NOT EXISTS `user_individual_permissions` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `permission_id` int(11) NOT NULL,
        `granted_by` int(11) NOT NULL,
        `granted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `is_active` tinyint(1) DEFAULT 1,
        `notes` text,
        PRIMARY KEY (`id`),
        UNIQUE KEY `user_permission` (`user_id`, `permission_id`),
        KEY `permission_id` (`permission_id`),
        KEY `granted_by` (`granted_by`),
        CONSTRAINT `user_individual_permissions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE,
        CONSTRAINT `user_individual_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `granular_permissions` (`id`) ON DELETE CASCADE,
        CONSTRAINT `user_individual_permissions_ibfk_3` FOREIGN KEY (`granted_by`) REFERENCES `admins` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    $pdo->exec($sql3);
    echo "✓ user_individual_permissions table created\n";
    
    // 4. Insert permission categories
    $categories = [
        ['dashboard_access', 'Dashboard Access', 'Access to dashboard and analytics', 'bi bi-speedometer2', 1],
        ['member_management', 'Member Management', 'Manage church members and profiles', 'bi bi-people', 2],
        ['event_management', 'Event Management', 'Manage events and attendance', 'bi bi-calendar-event', 3],
        ['email_management', 'Email Management', 'Send emails and manage templates', 'bi bi-envelope', 4],
        ['sms_management', 'SMS Management', 'Send SMS messages and manage templates', 'bi bi-chat-text', 5],
        ['donations_finance', 'Donations & Finance', 'Manage donations and financial records', 'bi bi-currency-dollar', 6],
        ['integrations', 'Integrations', 'Manage third-party integrations', 'bi bi-link-45deg', 7],
        ['system_settings', 'System Settings', 'Configure system settings and preferences', 'bi bi-gear', 8],
        ['reports_analytics', 'Reports & Analytics', 'View reports and analytics', 'bi bi-graph-up', 9],
        ['notifications', 'Notifications', 'Manage notifications and alerts', 'bi bi-bell', 10],
        ['admin_management', 'Admin Management', 'Manage admin users and permissions', 'bi bi-shield-check', 11]
    ];
    
    foreach ($categories as $cat) {
        $stmt = $pdo->prepare("INSERT IGNORE INTO permission_categories (category_name, category_display_name, description, icon_class, sort_order) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute($cat);
    }
    echo "✓ Permission categories inserted\n";
    
    $pdo->commit();
    echo "\n🎉 All tables created successfully!\n";
    echo "You can now use the granular permission system.\n";
    
} catch (Exception $e) {
    $pdo->rollback();
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
