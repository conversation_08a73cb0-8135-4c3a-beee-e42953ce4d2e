<?php
/**
 * Assign Role to Missing Admin - Fix Session Moderator Dropdown
 * This script assigns the Admin role to the missing admin user
 */

// Include the configuration file
require_once '../config.php';

try {
    // Start transaction
    $pdo->beginTransaction();
    
    echo "<h2>Assigning Role to Missing Admin</h2>\n";
    
    // 1. Find the missing admin (admingb)
    $stmt = $pdo->prepare("SELECT id, username, email FROM admins WHERE username = 'admingb'");
    $stmt->execute();
    $missing_admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$missing_admin) {
        throw new Exception("Admin user 'admingb' not found");
    }
    
    echo "<p>Found admin: " . htmlspecialchars($missing_admin['username']) . " (ID: " . $missing_admin['id'] . ")</p>\n";
    
    // 2. Get the Admin role ID
    $stmt = $pdo->prepare("SELECT id FROM user_roles WHERE role_name = 'limited_admin'");
    $stmt->execute();
    $admin_role_id = $stmt->fetchColumn();
    
    if (!$admin_role_id) {
        throw new Exception("Admin role not found");
    }
    
    echo "<p>Found Admin role (ID: $admin_role_id)</p>\n";
    
    // 3. Assign the Admin role to the missing admin
    $stmt = $pdo->prepare("
        INSERT INTO user_role_assignments (user_id, role_id, assigned_by, is_active) 
        VALUES (?, ?, 4, 1)
        ON DUPLICATE KEY UPDATE 
        assigned_by = 4,
        is_active = 1
    ");
    $stmt->execute([$missing_admin['id'], $admin_role_id]);
    
    echo "<p>✅ Admin role assigned successfully!</p>\n";
    
    // 4. Verify the assignment
    $stmt = $pdo->query("
        SELECT DISTINCT a.id, a.username, a.email, ur.role_display_name
        FROM admins a
        JOIN user_role_assignments ura ON a.id = ura.user_id
        JOIN user_roles ur ON ura.role_id = ur.id
        WHERE ur.role_name IN ('session_moderator', 'event_coordinator', 'limited_admin', 'super_admin')
        AND ura.is_active = 1
        ORDER BY ur.hierarchy_level ASC, a.username
    ");
    $moderators = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Updated Session Moderator List</h3>\n";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>\n";
    echo "<tr style='background-color: #e6f3ff;'>";
    echo "<th style='padding: 8px;'>ID</th>";
    echo "<th style='padding: 8px;'>Username</th>";
    echo "<th style='padding: 8px;'>Email</th>";
    echo "<th style='padding: 8px;'>Role</th>";
    echo "</tr>\n";
    
    foreach ($moderators as $moderator) {
        $highlight = ($moderator['username'] === 'admingb') ? 'background-color: #d4edda;' : '';
        echo "<tr style='$highlight'>";
        echo "<td style='padding: 8px;'>" . $moderator['id'] . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($moderator['username']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($moderator['email']) . "</td>";
        echo "<td style='padding: 8px;'>" . htmlspecialchars($moderator['role_display_name']) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    echo "<p><strong>Total moderators now available:</strong> " . count($moderators) . "</p>\n";
    
    // Commit transaction
    $pdo->commit();
    echo "<h3 style='color: green;'>✅ Role Assignment Completed Successfully!</h3>\n";
    
    echo "<p><strong>Result:</strong> All 4 admin users now have roles assigned and will appear in the session moderator dropdown.</p>\n";
    
    echo "<p><a href='setup_rbac_system.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Go to RBAC Management</a></p>\n";
    echo "<p><a href='debug_admin_roles.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Verify Changes</a></p>\n";
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo "<h3 style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</h3>\n";
}
?>
