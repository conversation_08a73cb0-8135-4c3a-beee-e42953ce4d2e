<?php
/**
 * Real-Time WebSocket Server for Church Event Management
 * Enterprise-grade WebSocket implementation for instant staff coordination
 */

require_once __DIR__ . '/../config.php';

use Ratchet\Server\IoServer;
use Ratchet\Http\HttpServer;
use Ratchet\WebSocket\WsServer;
use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;

class ChurchEventWebSocketServer implements MessageComponentInterface {
    protected $clients;
    protected $pdo;
    protected $connections;
    protected $rooms;
    protected $userSessions;
    
    public function __construct($pdo) {
        $this->clients = new \SplObjectStorage;
        $this->pdo = $pdo;
        $this->connections = [];
        $this->rooms = []; // Event-based rooms
        $this->userSessions = [];
        
        echo "🚀 Church Event WebSocket Server Started\n";
        echo "Ready for real-time staff coordination...\n\n";
    }

    public function onOpen(ConnectionInterface $conn) {
        // Store the new connection
        $this->clients->attach($conn);
        $conn->authenticated = false;
        $conn->user_id = null;
        $conn->event_id = null;
        $conn->user_name = null;
        $conn->user_role = null;
        
        echo "New connection! ({$conn->resourceId})\n";
        
        // Send welcome message
        $conn->send(json_encode([
            'type' => 'welcome',
            'message' => 'Connected to Church Event Real-Time System',
            'connection_id' => $conn->resourceId,
            'timestamp' => date('Y-m-d H:i:s')
        ]));
    }

    public function onMessage(ConnectionInterface $from, $msg) {
        try {
            $data = json_decode($msg, true);
            
            if (!$data || !isset($data['type'])) {
                $this->sendError($from, 'Invalid message format');
                return;
            }
            
            switch ($data['type']) {
                case 'authenticate':
                    $this->handleAuthentication($from, $data);
                    break;
                    
                case 'join_event':
                    $this->handleJoinEvent($from, $data);
                    break;
                    
                case 'attendance_update':
                    $this->handleAttendanceUpdate($from, $data);
                    break;
                    
                case 'session_status_update':
                    $this->handleSessionStatusUpdate($from, $data);
                    break;
                    
                case 'staff_message':
                    $this->handleStaffMessage($from, $data);
                    break;
                    
                case 'heartbeat':
                    $this->handleHeartbeat($from, $data);
                    break;
                    
                case 'request_sync':
                    $this->handleSyncRequest($from, $data);
                    break;
                    
                default:
                    $this->sendError($from, 'Unknown message type: ' . $data['type']);
            }
            
        } catch (Exception $e) {
            echo "Error processing message: " . $e->getMessage() . "\n";
            $this->sendError($from, 'Server error processing message');
        }
    }

    public function onClose(ConnectionInterface $conn) {
        // Remove from rooms
        if (isset($conn->event_id)) {
            $this->leaveRoom($conn, $conn->event_id);
        }
        
        // Remove the connection
        $this->clients->detach($conn);
        unset($this->connections[$conn->resourceId]);
        
        echo "Connection {$conn->resourceId} has disconnected\n";
        
        // Notify other staff in the same event
        if (isset($conn->event_id) && isset($conn->user_name)) {
            $this->broadcastToRoom($conn->event_id, [
                'type' => 'staff_disconnected',
                'user_name' => $conn->user_name,
                'user_id' => $conn->user_id,
                'timestamp' => date('Y-m-d H:i:s')
            ], $conn);
        }
    }

    public function onError(ConnectionInterface $conn, \Exception $e) {
        echo "An error has occurred: {$e->getMessage()}\n";
        $conn->close();
    }
    
    private function handleAuthentication($conn, $data) {
        if (!isset($data['user_id']) || !isset($data['auth_token'])) {
            $this->sendError($conn, 'Missing authentication credentials');
            return;
        }
        
        // Verify user authentication
        try {
            $stmt = $this->pdo->prepare("
                SELECT m.id, m.full_name, 'member' as user_type
                FROM members m 
                WHERE m.id = ? AND m.status = 'active'
                
                UNION ALL
                
                SELECT a.id, a.username as full_name, 'admin' as user_type
                FROM admin_users a 
                WHERE a.id = ? AND a.is_active = 1
            ");
            $stmt->execute([$data['user_id'], $data['user_id']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                $this->sendError($conn, 'Invalid user credentials');
                return;
            }
            
            // Set connection properties
            $conn->authenticated = true;
            $conn->user_id = $user['id'];
            $conn->user_name = $user['full_name'];
            $conn->user_type = $user['user_type'];
            
            // Store connection
            $this->connections[$conn->resourceId] = $conn;
            $this->userSessions[$user['id']] = $conn->resourceId;
            
            echo "User authenticated: {$user['full_name']} ({$conn->resourceId})\n";
            
            // Send authentication success
            $conn->send(json_encode([
                'type' => 'authenticated',
                'user_id' => $user['id'],
                'user_name' => $user['full_name'],
                'user_type' => $user['user_type'],
                'timestamp' => date('Y-m-d H:i:s')
            ]));
            
        } catch (Exception $e) {
            echo "Authentication error: " . $e->getMessage() . "\n";
            $this->sendError($conn, 'Authentication failed');
        }
    }
    
    private function handleJoinEvent($conn, $data) {
        if (!$conn->authenticated) {
            $this->sendError($conn, 'Not authenticated');
            return;
        }
        
        if (!isset($data['event_id'])) {
            $this->sendError($conn, 'Missing event_id');
            return;
        }
        
        $event_id = (int)$data['event_id'];
        
        // Verify event exists
        try {
            $stmt = $this->pdo->prepare("SELECT id, title FROM events WHERE id = ?");
            $stmt->execute([$event_id]);
            $event = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$event) {
                $this->sendError($conn, 'Event not found');
                return;
            }
            
            // Leave previous room if any
            if (isset($conn->event_id)) {
                $this->leaveRoom($conn, $conn->event_id);
            }
            
            // Join new room
            $conn->event_id = $event_id;
            $this->joinRoom($conn, $event_id);
            
            echo "User {$conn->user_name} joined event: {$event['title']}\n";
            
            // Send join confirmation
            $conn->send(json_encode([
                'type' => 'joined_event',
                'event_id' => $event_id,
                'event_title' => $event['title'],
                'timestamp' => date('Y-m-d H:i:s')
            ]));
            
            // Notify other staff
            $this->broadcastToRoom($event_id, [
                'type' => 'staff_joined',
                'user_name' => $conn->user_name,
                'user_id' => $conn->user_id,
                'user_type' => $conn->user_type,
                'timestamp' => date('Y-m-d H:i:s')
            ], $conn);
            
            // Send current event status
            $this->sendEventStatus($conn, $event_id);
            
        } catch (Exception $e) {
            echo "Join event error: " . $e->getMessage() . "\n";
            $this->sendError($conn, 'Failed to join event');
        }
    }
    
    private function handleAttendanceUpdate($conn, $data) {
        if (!$conn->authenticated || !isset($conn->event_id)) {
            $this->sendError($conn, 'Not authenticated or not in event');
            return;
        }
        
        // Validate required fields
        $required = ['attendee_id', 'attendee_type', 'action', 'session_id'];
        foreach ($required as $field) {
            if (!isset($data[$field])) {
                $this->sendError($conn, "Missing required field: $field");
                return;
            }
        }
        
        try {
            // Log the attendance update
            $stmt = $this->pdo->prepare("
                INSERT INTO realtime_activity_log 
                (event_id, user_id, action_type, action_data, created_at)
                VALUES (?, ?, 'attendance_update', ?, NOW())
            ");
            $stmt->execute([
                $conn->event_id,
                $conn->user_id,
                json_encode($data)
            ]);
            
            // Broadcast to all staff in the event
            $this->broadcastToRoom($conn->event_id, [
                'type' => 'attendance_updated',
                'attendee_id' => $data['attendee_id'],
                'attendee_type' => $data['attendee_type'],
                'attendee_name' => $data['attendee_name'] ?? 'Unknown',
                'session_id' => $data['session_id'],
                'session_title' => $data['session_title'] ?? '',
                'action' => $data['action'],
                'updated_by' => $conn->user_name,
                'updated_by_id' => $conn->user_id,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            
            echo "Attendance update: {$data['action']} for {$data['attendee_id']} by {$conn->user_name}\n";
            
        } catch (Exception $e) {
            echo "Attendance update error: " . $e->getMessage() . "\n";
            $this->sendError($conn, 'Failed to process attendance update');
        }
    }
    
    private function joinRoom($conn, $event_id) {
        if (!isset($this->rooms[$event_id])) {
            $this->rooms[$event_id] = [];
        }
        $this->rooms[$event_id][$conn->resourceId] = $conn;
    }
    
    private function leaveRoom($conn, $event_id) {
        if (isset($this->rooms[$event_id][$conn->resourceId])) {
            unset($this->rooms[$event_id][$conn->resourceId]);
            
            // Clean up empty rooms
            if (empty($this->rooms[$event_id])) {
                unset($this->rooms[$event_id]);
            }
        }
    }
    
    private function broadcastToRoom($event_id, $message, $exclude = null) {
        if (!isset($this->rooms[$event_id])) {
            return;
        }
        
        $json_message = json_encode($message);
        
        foreach ($this->rooms[$event_id] as $conn) {
            if ($exclude && $conn === $exclude) {
                continue;
            }
            
            try {
                $conn->send($json_message);
            } catch (Exception $e) {
                echo "Error sending to connection {$conn->resourceId}: " . $e->getMessage() . "\n";
            }
        }
    }
    
    private function sendError($conn, $message) {
        $conn->send(json_encode([
            'type' => 'error',
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ]));
    }
    
    private function sendEventStatus($conn, $event_id) {
        try {
            // Get current event statistics
            $stmt = $this->pdo->prepare("
                SELECT 
                    COUNT(DISTINCT es.id) as total_sessions,
                    COUNT(DISTINCT sa.id) as total_attendance,
                    COUNT(DISTINCT er.user_id) as total_rsvps
                FROM events e
                LEFT JOIN event_sessions es ON e.id = es.event_id
                LEFT JOIN session_attendance sa ON es.id = sa.session_id
                LEFT JOIN event_rsvps er ON e.id = er.event_id
                WHERE e.id = ?
            ");
            $stmt->execute([$event_id]);
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Get connected staff count
            $staff_count = isset($this->rooms[$event_id]) ? count($this->rooms[$event_id]) : 0;
            
            $conn->send(json_encode([
                'type' => 'event_status',
                'event_id' => $event_id,
                'stats' => $stats,
                'connected_staff' => $staff_count,
                'timestamp' => date('Y-m-d H:i:s')
            ]));
            
        } catch (Exception $e) {
            echo "Error sending event status: " . $e->getMessage() . "\n";
        }
    }
    
    private function handleHeartbeat($conn, $data) {
        $conn->send(json_encode([
            'type' => 'heartbeat_response',
            'timestamp' => date('Y-m-d H:i:s')
        ]));
    }
}

// Create the WebSocket server
try {
    $websocket_server = new ChurchEventWebSocketServer($pdo);
    
    $server = IoServer::factory(
        new HttpServer(
            new WsServer($websocket_server)
        ),
        8080
    );
    
    echo "🌐 WebSocket Server running on port 8080\n";
    echo "Staff can connect for real-time coordination\n";
    echo "Press Ctrl+C to stop the server\n\n";
    
    $server->run();
    
} catch (Exception $e) {
    echo "❌ Failed to start WebSocket server: " . $e->getMessage() . "\n";
    echo "Make sure port 8080 is available and Ratchet is installed\n";
}
?>
