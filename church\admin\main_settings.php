<?php
/**
 * Main Settings - Unified Settings Management System
 * Consolidates all organization, appearance, branding, and system settings
 */

session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

require_once '../config.php';

$page_title = 'Main Settings';
$page_header = 'Main Settings';
$page_description = 'Comprehensive settings management for your organization';

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo->beginTransaction();
        
        // Organization Settings
        if (isset($_POST['organization_settings'])) {
            $org_settings = [
                'site_title' => $_POST['site_title'] ?? '',
                'admin_title' => $_POST['admin_title'] ?? '',
                'organization_name' => $_POST['organization_name'] ?? '',
                'organization_type' => $_POST['organization_type'] ?? 'church',
                'organization_mission' => $_POST['organization_mission'] ?? '',
                'organization_vision' => $_POST['organization_vision'] ?? '',
                'organization_values' => $_POST['organization_values'] ?? '',
                'member_term' => $_POST['member_term'] ?? 'Member',
                'leader_term' => $_POST['leader_term'] ?? 'Pastor',
                'group_term' => $_POST['group_term'] ?? 'Ministry',
                'event_term' => $_POST['event_term'] ?? 'Service',
                'donation_term' => $_POST['donation_term'] ?? 'Offering',
                'custom_attendee_term' => $_POST['custom_attendee_term'] ?? '',
                'custom_session_term' => $_POST['custom_session_term'] ?? '',
                'custom_event_term' => $_POST['custom_event_term'] ?? ''
            ];
            
            foreach ($org_settings as $key => $value) {
                $stmt = $pdo->prepare("
                    INSERT INTO site_settings (setting_name, setting_value) 
                    VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $stmt->execute([$key, $value]);
            }
        }
        
        // Contact Information
        if (isset($_POST['contact_settings'])) {
            $contact_settings = [
                'contact_phone' => $_POST['contact_phone'] ?? '',
                'contact_email' => $_POST['contact_email'] ?? '',
                'contact_address' => $_POST['contact_address'] ?? '',
                'contact_city' => $_POST['contact_city'] ?? '',
                'contact_state' => $_POST['contact_state'] ?? '',
                'contact_zip' => $_POST['contact_zip'] ?? '',
                'contact_country' => $_POST['contact_country'] ?? '',
                'office_hours' => $_POST['office_hours'] ?? '',
                'emergency_contact' => $_POST['emergency_contact'] ?? '',
                'website_url' => $_POST['website_url'] ?? ''
            ];
            
            foreach ($contact_settings as $key => $value) {
                $stmt = $pdo->prepare("
                    INSERT INTO site_settings (setting_name, setting_value) 
                    VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $stmt->execute([$key, $value]);
            }
        }
        
        // Appearance & Branding Settings
        if (isset($_POST['appearance_settings'])) {
            $appearance_settings = [
                'primary_color' => $_POST['primary_color'] ?? '#007bff',
                'secondary_color' => $_POST['secondary_color'] ?? '#6c757d',
                'success_color' => $_POST['success_color'] ?? '#28a745',
                'danger_color' => $_POST['danger_color'] ?? '#dc3545',
                'warning_color' => $_POST['warning_color'] ?? '#ffc107',
                'info_color' => $_POST['info_color'] ?? '#17a2b8',
                'light_color' => $_POST['light_color'] ?? '#f8f9fa',
                'dark_color' => $_POST['dark_color'] ?? '#343a40',
                'background_color' => $_POST['background_color'] ?? '#ffffff',
                'text_color' => $_POST['text_color'] ?? '#212529',
                'link_color' => $_POST['link_color'] ?? '#007bff',
                'link_hover_color' => $_POST['link_hover_color'] ?? '#0056b3',
                'primary_font' => $_POST['primary_font'] ?? 'Inter',
                'secondary_font' => $_POST['secondary_font'] ?? 'Inter',
                'font_size_base' => $_POST['font_size_base'] ?? '16',
                'line_height_base' => $_POST['line_height_base'] ?? '1.5',
                'border_radius' => $_POST['border_radius'] ?? '0.375',
                'sidebar_bg_color' => $_POST['sidebar_bg_color'] ?? '#343a40',
                'sidebar_text_color' => $_POST['sidebar_text_color'] ?? '#ffffff',
                'sidebar_hover_color' => $_POST['sidebar_hover_color'] ?? '#007bff',
                'sidebar_width' => $_POST['sidebar_width'] ?? '280',
                'container_max_width' => $_POST['container_max_width'] ?? '1200',
                'content_spacing' => $_POST['content_spacing'] ?? '30'
            ];
            
            // Store appearance settings in both tables for backward compatibility
            foreach ($appearance_settings as $key => $value) {
                // Store in site_settings
                $stmt = $pdo->prepare("
                    INSERT INTO site_settings (setting_name, setting_value) 
                    VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $stmt->execute([$key, $value]);
                
                // Also store in appearance_settings if table exists
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO appearance_settings (setting_name, setting_value, category) 
                        VALUES (?, ?, 'appearance') 
                        ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                    ");
                    $stmt->execute([$key, $value]);
                } catch (Exception $e) {
                    // Table might not exist, continue
                }
            }
        }
        
        // System Settings
        if (isset($_POST['system_settings'])) {
            $system_settings = [
                'timezone' => $_POST['timezone'] ?? 'America/New_York',
                'date_format' => $_POST['date_format'] ?? 'Y-m-d',
                'time_format' => $_POST['time_format'] ?? 'H:i',
                'currency_symbol' => $_POST['currency_symbol'] ?? '$',
                'currency_code' => $_POST['currency_code'] ?? 'USD',
                'language' => $_POST['language'] ?? 'en',
                'items_per_page' => $_POST['items_per_page'] ?? '25',
                'session_timeout' => $_POST['session_timeout'] ?? '3600',
                'max_upload_size' => $_POST['max_upload_size'] ?? '10',
                'backup_retention_days' => $_POST['backup_retention_days'] ?? '30',
                'enable_dark_mode' => isset($_POST['enable_dark_mode']) ? '1' : '0',
                'enable_theme_switcher' => isset($_POST['enable_theme_switcher']) ? '1' : '0'
            ];
            
            foreach ($system_settings as $key => $value) {
                $stmt = $pdo->prepare("
                    INSERT INTO site_settings (setting_name, setting_value) 
                    VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $stmt->execute([$key, $value]);
            }
        }
        
        // Email Settings
        if (isset($_POST['email_settings'])) {
            $email_settings = [
                'smtp_host' => $_POST['smtp_host'] ?? '',
                'smtp_port' => $_POST['smtp_port'] ?? '587',
                'smtp_username' => $_POST['smtp_username'] ?? '',
                'smtp_password' => $_POST['smtp_password'] ?? '',
                'smtp_encryption' => $_POST['smtp_encryption'] ?? 'tls',
                'from_email' => $_POST['from_email'] ?? '',
                'from_name' => $_POST['from_name'] ?? '',
                'reply_to_email' => $_POST['reply_to_email'] ?? '',
                'email_signature' => $_POST['email_signature'] ?? '',
                'enable_email_queue' => isset($_POST['enable_email_queue']) ? '1' : '0'
            ];
            
            foreach ($email_settings as $key => $value) {
                $stmt = $pdo->prepare("
                    INSERT INTO site_settings (setting_name, setting_value) 
                    VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $stmt->execute([$key, $value]);
            }
        }
        
        // Social Media Settings
        if (isset($_POST['social_settings'])) {
            $social_settings = [
                'facebook_url' => $_POST['facebook_url'] ?? '',
                'twitter_url' => $_POST['twitter_url'] ?? '',
                'instagram_url' => $_POST['instagram_url'] ?? '',
                'youtube_url' => $_POST['youtube_url'] ?? '',
                'linkedin_url' => $_POST['linkedin_url'] ?? '',
                'tiktok_url' => $_POST['tiktok_url'] ?? ''
            ];
            
            foreach ($social_settings as $key => $value) {
                $stmt = $pdo->prepare("
                    INSERT INTO site_settings (setting_name, setting_value) 
                    VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $stmt->execute([$key, $value]);
            }
        }
        
        $pdo->commit();
        $message = "Settings updated successfully!";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $error = "Error updating settings: " . $e->getMessage();
    }
}

// Get current settings
$current_settings = [];
try {
    $stmt = $pdo->prepare("SELECT setting_name, setting_value FROM site_settings");
    $stmt->execute();
    $current_settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
} catch (Exception $e) {
    $current_settings = [];
}

// Set defaults for missing settings
$defaults = [
    'site_title' => 'Organization Management System',
    'admin_title' => 'Admin Panel',
    'organization_name' => 'Your Organization',
    'organization_type' => 'church',
    'primary_color' => '#007bff',
    'secondary_color' => '#6c757d',
    'member_term' => 'Member',
    'leader_term' => 'Pastor',
    'group_term' => 'Ministry',
    'event_term' => 'Service',
    'donation_term' => 'Offering',
    'timezone' => 'America/New_York',
    'currency_symbol' => '$',
    'language' => 'en'
];

foreach ($defaults as $key => $default_value) {
    if (!isset($current_settings[$key])) {
        $current_settings[$key] = $default_value;
    }
}

include 'includes/header.php';
?>

<style>
.settings-nav {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 2rem;
}

.settings-nav .nav-link {
    color: #6c757d;
    border: none;
    border-bottom: 3px solid transparent;
    border-radius: 0;
    padding: 1rem 1.5rem;
}

.settings-nav .nav-link.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: none;
}

.settings-section {
    display: none;
}

.settings-section.active {
    display: block;
}

.color-preview {
    width: 40px;
    height: 40px;
    border-radius: 0.375rem;
    border: 2px solid #dee2e6;
    display: inline-block;
    margin-left: 10px;
    vertical-align: middle;
}

.form-group {
    margin-bottom: 1.5rem;
}

.section-header {
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}
</style>

<div class="container-fluid">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2"><i class="bi bi-gear-fill"></i> <?php echo $page_header; ?></h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2">
                <a href="migrate_settings_tables.php" class="btn btn-sm btn-outline-secondary">
                    <i class="bi bi-database-gear"></i> Migration Tools
                </a>
            </div>
        </div>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Settings Navigation -->
    <ul class="nav nav-tabs settings-nav" id="settingsNav" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="organization-tab" data-bs-toggle="tab" data-bs-target="#organization" type="button" role="tab">
                <i class="bi bi-building"></i> Organization
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab">
                <i class="bi bi-telephone"></i> Contact
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="appearance-tab" data-bs-toggle="tab" data-bs-target="#appearance" type="button" role="tab">
                <i class="bi bi-palette"></i> Appearance
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                <i class="bi bi-cpu"></i> System
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
                <i class="bi bi-envelope"></i> Email
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="social-tab" data-bs-toggle="tab" data-bs-target="#social" type="button" role="tab">
                <i class="bi bi-share"></i> Social Media
            </button>
        </li>
    </ul>

    <div class="tab-content" id="settingsTabContent">
        <!-- Organization Settings -->
        <div class="tab-pane fade show active" id="organization" role="tabpanel">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="organization_settings" value="1">

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-building"></i> Organization Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="site_title" class="form-label">Site Title</label>
                                            <input type="text" class="form-control" id="site_title" name="site_title"
                                                   value="<?php echo htmlspecialchars($current_settings['site_title'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="admin_title" class="form-label">Admin Panel Title</label>
                                            <input type="text" class="form-control" id="admin_title" name="admin_title"
                                                   value="<?php echo htmlspecialchars($current_settings['admin_title'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="organization_name" class="form-label">Organization Name</label>
                                            <input type="text" class="form-control" id="organization_name" name="organization_name"
                                                   value="<?php echo htmlspecialchars($current_settings['organization_name'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="organization_type" class="form-label">Organization Type</label>
                                            <select class="form-select" id="organization_type" name="organization_type" required>
                                                <option value="church" <?php echo ($current_settings['organization_type'] ?? '') === 'church' ? 'selected' : ''; ?>>Church</option>
                                                <option value="nonprofit" <?php echo ($current_settings['organization_type'] ?? '') === 'nonprofit' ? 'selected' : ''; ?>>Non-Profit</option>
                                                <option value="school" <?php echo ($current_settings['organization_type'] ?? '') === 'school' ? 'selected' : ''; ?>>School</option>
                                                <option value="business" <?php echo ($current_settings['organization_type'] ?? '') === 'business' ? 'selected' : ''; ?>>Business</option>
                                                <option value="community" <?php echo ($current_settings['organization_type'] ?? '') === 'community' ? 'selected' : ''; ?>>Community Group</option>
                                                <option value="sports" <?php echo ($current_settings['organization_type'] ?? '') === 'sports' ? 'selected' : ''; ?>>Sports Club</option>
                                                <option value="other" <?php echo ($current_settings['organization_type'] ?? '') === 'other' ? 'selected' : ''; ?>>Other</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="organization_mission" class="form-label">Mission Statement</label>
                                    <textarea class="form-control" id="organization_mission" name="organization_mission" rows="3"><?php echo htmlspecialchars($current_settings['organization_mission'] ?? ''); ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="organization_vision" class="form-label">Vision Statement</label>
                                    <textarea class="form-control" id="organization_vision" name="organization_vision" rows="3"><?php echo htmlspecialchars($current_settings['organization_vision'] ?? ''); ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="organization_values" class="form-label">Core Values</label>
                                    <textarea class="form-control" id="organization_values" name="organization_values" rows="3"><?php echo htmlspecialchars($current_settings['organization_values'] ?? ''); ?></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-tags"></i> Custom Terminology</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="member_term" class="form-label">Member Term</label>
                                            <input type="text" class="form-control" id="member_term" name="member_term"
                                                   value="<?php echo htmlspecialchars($current_settings['member_term'] ?? 'Member'); ?>"
                                                   placeholder="e.g., Member, Student, Employee">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="leader_term" class="form-label">Leader Term</label>
                                            <input type="text" class="form-control" id="leader_term" name="leader_term"
                                                   value="<?php echo htmlspecialchars($current_settings['leader_term'] ?? 'Pastor'); ?>"
                                                   placeholder="e.g., Pastor, Director, Manager">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="group_term" class="form-label">Group Term</label>
                                            <input type="text" class="form-control" id="group_term" name="group_term"
                                                   value="<?php echo htmlspecialchars($current_settings['group_term'] ?? 'Ministry'); ?>"
                                                   placeholder="e.g., Ministry, Department, Team">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="event_term" class="form-label">Event Term</label>
                                            <input type="text" class="form-control" id="event_term" name="event_term"
                                                   value="<?php echo htmlspecialchars($current_settings['event_term'] ?? 'Service'); ?>"
                                                   placeholder="e.g., Service, Meeting, Class">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="donation_term" class="form-label">Donation Term</label>
                                            <input type="text" class="form-control" id="donation_term" name="donation_term"
                                                   value="<?php echo htmlspecialchars($current_settings['donation_term'] ?? 'Offering'); ?>"
                                                   placeholder="e.g., Offering, Donation, Contribution">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="universal_organization_setup.php" class="btn btn-outline-primary">
                                        <i class="bi bi-gear"></i> Universal Setup
                                    </a>
                                    <a href="appearance_settings.php" class="btn btn-outline-primary">
                                        <i class="bi bi-palette"></i> Appearance & Branding
                                    </a>
                                    <a href="settings.php" class="btn btn-outline-secondary">
                                        <i class="bi bi-sliders"></i> Legacy Settings
                                    </a>
                                </div>

                                <hr>

                                <h6><i class="bi bi-lightbulb"></i> Tips</h6>
                                <ul class="small text-muted">
                                    <li>Organization type affects terminology suggestions</li>
                                    <li>Custom terms appear throughout the system</li>
                                    <li>Mission/Vision statements appear on public pages</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-lg"></i> Save Organization Settings
                    </button>
                    <button type="reset" class="btn btn-outline-secondary ms-2">
                        <i class="bi bi-arrow-clockwise"></i> Reset
                    </button>
                </div>
            </form>
        </div>

        <!-- Contact Settings -->
        <div class="tab-pane fade" id="contact" role="tabpanel">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="contact_settings" value="1">

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-telephone"></i> Contact Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="contact_phone" class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control" id="contact_phone" name="contact_phone"
                                                   value="<?php echo htmlspecialchars($current_settings['contact_phone'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="contact_email" class="form-label">Email Address</label>
                                            <input type="email" class="form-control" id="contact_email" name="contact_email"
                                                   value="<?php echo htmlspecialchars($current_settings['contact_email'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="contact_address" class="form-label">Street Address</label>
                                    <input type="text" class="form-control" id="contact_address" name="contact_address"
                                           value="<?php echo htmlspecialchars($current_settings['contact_address'] ?? ''); ?>">
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="contact_city" class="form-label">City</label>
                                            <input type="text" class="form-control" id="contact_city" name="contact_city"
                                                   value="<?php echo htmlspecialchars($current_settings['contact_city'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="contact_state" class="form-label">State/Province</label>
                                            <input type="text" class="form-control" id="contact_state" name="contact_state"
                                                   value="<?php echo htmlspecialchars($current_settings['contact_state'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="contact_zip" class="form-label">ZIP/Postal Code</label>
                                            <input type="text" class="form-control" id="contact_zip" name="contact_zip"
                                                   value="<?php echo htmlspecialchars($current_settings['contact_zip'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="contact_country" class="form-label">Country</label>
                                            <input type="text" class="form-control" id="contact_country" name="contact_country"
                                                   value="<?php echo htmlspecialchars($current_settings['contact_country'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="website_url" class="form-label">Website URL</label>
                                            <input type="url" class="form-control" id="website_url" name="website_url"
                                                   value="<?php echo htmlspecialchars($current_settings['website_url'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="office_hours" class="form-label">Office Hours</label>
                                    <textarea class="form-control" id="office_hours" name="office_hours" rows="3"
                                              placeholder="e.g., Monday-Friday: 9:00 AM - 5:00 PM"><?php echo htmlspecialchars($current_settings['office_hours'] ?? ''); ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="emergency_contact" class="form-label">Emergency Contact</label>
                                    <input type="text" class="form-control" id="emergency_contact" name="emergency_contact"
                                           value="<?php echo htmlspecialchars($current_settings['emergency_contact'] ?? ''); ?>"
                                           placeholder="Emergency phone number or contact info">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Contact Info Usage</h5>
                            </div>
                            <div class="card-body">
                                <h6><i class="bi bi-eye"></i> Where This Appears</h6>
                                <ul class="small text-muted">
                                    <li>Public website footer</li>
                                    <li>Email signatures</li>
                                    <li>Event invitations</li>
                                    <li>Contact us pages</li>
                                    <li>Member communications</li>
                                </ul>

                                <hr>

                                <h6><i class="bi bi-shield-check"></i> Privacy Note</h6>
                                <p class="small text-muted">
                                    Contact information may be visible to members and visitors.
                                    Only include information you want to be publicly accessible.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-lg"></i> Save Contact Settings
                    </button>
                    <button type="reset" class="btn btn-outline-secondary ms-2">
                        <i class="bi bi-arrow-clockwise"></i> Reset
                    </button>
                </div>
            </form>
        </div>

        <!-- Appearance Settings -->
        <div class="tab-pane fade" id="appearance" role="tabpanel">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="appearance_settings" value="1">

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-palette"></i> Color Scheme</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="primary_color" class="form-label">Primary Color</label>
                                            <div class="input-group">
                                                <input type="color" class="form-control form-control-color" id="primary_color" name="primary_color"
                                                       value="<?php echo htmlspecialchars($current_settings['primary_color'] ?? '#007bff'); ?>"
                                                       onchange="updateColorPreview('primary_color')">
                                                <input type="text" class="form-control" value="<?php echo htmlspecialchars($current_settings['primary_color'] ?? '#007bff'); ?>"
                                                       onchange="document.getElementById('primary_color').value = this.value">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="secondary_color" class="form-label">Secondary Color</label>
                                            <div class="input-group">
                                                <input type="color" class="form-control form-control-color" id="secondary_color" name="secondary_color"
                                                       value="<?php echo htmlspecialchars($current_settings['secondary_color'] ?? '#6c757d'); ?>"
                                                       onchange="updateColorPreview('secondary_color')">
                                                <input type="text" class="form-control" value="<?php echo htmlspecialchars($current_settings['secondary_color'] ?? '#6c757d'); ?>"
                                                       onchange="document.getElementById('secondary_color').value = this.value">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="success_color" class="form-label">Success Color</label>
                                            <input type="color" class="form-control form-control-color" id="success_color" name="success_color"
                                                   value="<?php echo htmlspecialchars($current_settings['success_color'] ?? '#28a745'); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="warning_color" class="form-label">Warning Color</label>
                                            <input type="color" class="form-control form-control-color" id="warning_color" name="warning_color"
                                                   value="<?php echo htmlspecialchars($current_settings['warning_color'] ?? '#ffc107'); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="danger_color" class="form-label">Danger Color</label>
                                            <input type="color" class="form-control form-control-color" id="danger_color" name="danger_color"
                                                   value="<?php echo htmlspecialchars($current_settings['danger_color'] ?? '#dc3545'); ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-type"></i> Typography</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="primary_font" class="form-label">Primary Font</label>
                                            <select class="form-select" id="primary_font" name="primary_font">
                                                <option value="Inter" <?php echo ($current_settings['primary_font'] ?? 'Inter') === 'Inter' ? 'selected' : ''; ?>>Inter</option>
                                                <option value="Arial" <?php echo ($current_settings['primary_font'] ?? '') === 'Arial' ? 'selected' : ''; ?>>Arial</option>
                                                <option value="Helvetica" <?php echo ($current_settings['primary_font'] ?? '') === 'Helvetica' ? 'selected' : ''; ?>>Helvetica</option>
                                                <option value="Georgia" <?php echo ($current_settings['primary_font'] ?? '') === 'Georgia' ? 'selected' : ''; ?>>Georgia</option>
                                                <option value="Times New Roman" <?php echo ($current_settings['primary_font'] ?? '') === 'Times New Roman' ? 'selected' : ''; ?>>Times New Roman</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="font_size_base" class="form-label">Base Font Size (px)</label>
                                            <input type="number" class="form-control" id="font_size_base" name="font_size_base"
                                                   value="<?php echo htmlspecialchars($current_settings['font_size_base'] ?? '16'); ?>"
                                                   min="12" max="24">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="line_height_base" class="form-label">Line Height</label>
                                            <input type="number" class="form-control" id="line_height_base" name="line_height_base"
                                                   value="<?php echo htmlspecialchars($current_settings['line_height_base'] ?? '1.5'); ?>"
                                                   min="1" max="3" step="0.1">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="border_radius" class="form-label">Border Radius (rem)</label>
                                            <input type="number" class="form-control" id="border_radius" name="border_radius"
                                                   value="<?php echo htmlspecialchars($current_settings['border_radius'] ?? '0.375'); ?>"
                                                   min="0" max="2" step="0.125">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-layout-sidebar"></i> Layout Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="sidebar_width" class="form-label">Sidebar Width (px)</label>
                                            <input type="number" class="form-control" id="sidebar_width" name="sidebar_width"
                                                   value="<?php echo htmlspecialchars($current_settings['sidebar_width'] ?? '280'); ?>"
                                                   min="200" max="400">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="container_max_width" class="form-label">Max Width (px)</label>
                                            <input type="number" class="form-control" id="container_max_width" name="container_max_width"
                                                   value="<?php echo htmlspecialchars($current_settings['container_max_width'] ?? '1200'); ?>"
                                                   min="800" max="1600">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="content_spacing" class="form-label">Content Spacing (px)</label>
                                            <input type="number" class="form-control" id="content_spacing" name="content_spacing"
                                                   value="<?php echo htmlspecialchars($current_settings['content_spacing'] ?? '30'); ?>"
                                                   min="10" max="60">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="sidebar_bg_color" class="form-label">Sidebar Background</label>
                                            <input type="color" class="form-control form-control-color" id="sidebar_bg_color" name="sidebar_bg_color"
                                                   value="<?php echo htmlspecialchars($current_settings['sidebar_bg_color'] ?? '#343a40'); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="sidebar_text_color" class="form-label">Sidebar Text</label>
                                            <input type="color" class="form-control form-control-color" id="sidebar_text_color" name="sidebar_text_color"
                                                   value="<?php echo htmlspecialchars($current_settings['sidebar_text_color'] ?? '#ffffff'); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="sidebar_hover_color" class="form-label">Sidebar Hover</label>
                                            <input type="color" class="form-control form-control-color" id="sidebar_hover_color" name="sidebar_hover_color"
                                                   value="<?php echo htmlspecialchars($current_settings['sidebar_hover_color'] ?? '#007bff'); ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-eye"></i> Live Preview</h5>
                            </div>
                            <div class="card-body">
                                <div class="preview-container p-3 border rounded" style="background: var(--bs-light);">
                                    <div class="preview-header p-2 rounded mb-2" style="background: var(--bs-primary); color: white;">
                                        <strong>Header Preview</strong>
                                    </div>
                                    <div class="preview-content">
                                        <p class="mb-2">This is how your content will look with the selected colors and fonts.</p>
                                        <button class="btn btn-sm me-1" style="background: var(--bs-primary); color: white;">Primary</button>
                                        <button class="btn btn-sm me-1" style="background: var(--bs-secondary); color: white;">Secondary</button>
                                        <button class="btn btn-sm" style="background: var(--bs-success); color: white;">Success</button>
                                    </div>
                                </div>

                                <hr>

                                <h6><i class="bi bi-lightbulb"></i> Tips</h6>
                                <ul class="small text-muted">
                                    <li>Primary color is used for buttons and links</li>
                                    <li>Choose colors that provide good contrast</li>
                                    <li>Test your colors with different screen settings</li>
                                    <li>Consider accessibility guidelines</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-lg"></i> Save Appearance Settings
                    </button>
                    <button type="reset" class="btn btn-outline-secondary ms-2">
                        <i class="bi bi-arrow-clockwise"></i> Reset
                    </button>
                </div>
            </form>
        </div>

        <!-- System Settings -->
        <div class="tab-pane fade" id="system" role="tabpanel">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="system_settings" value="1">

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-cpu"></i> System Preferences</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="timezone" class="form-label">Timezone</label>
                                            <select class="form-select" id="timezone" name="timezone">
                                                <option value="America/New_York" <?php echo ($current_settings['timezone'] ?? 'America/New_York') === 'America/New_York' ? 'selected' : ''; ?>>Eastern Time</option>
                                                <option value="America/Chicago" <?php echo ($current_settings['timezone'] ?? '') === 'America/Chicago' ? 'selected' : ''; ?>>Central Time</option>
                                                <option value="America/Denver" <?php echo ($current_settings['timezone'] ?? '') === 'America/Denver' ? 'selected' : ''; ?>>Mountain Time</option>
                                                <option value="America/Los_Angeles" <?php echo ($current_settings['timezone'] ?? '') === 'America/Los_Angeles' ? 'selected' : ''; ?>>Pacific Time</option>
                                                <option value="UTC" <?php echo ($current_settings['timezone'] ?? '') === 'UTC' ? 'selected' : ''; ?>>UTC</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="language" class="form-label">Language</label>
                                            <select class="form-select" id="language" name="language">
                                                <option value="en" <?php echo ($current_settings['language'] ?? 'en') === 'en' ? 'selected' : ''; ?>>English</option>
                                                <option value="es" <?php echo ($current_settings['language'] ?? '') === 'es' ? 'selected' : ''; ?>>Spanish</option>
                                                <option value="fr" <?php echo ($current_settings['language'] ?? '') === 'fr' ? 'selected' : ''; ?>>French</option>
                                                <option value="de" <?php echo ($current_settings['language'] ?? '') === 'de' ? 'selected' : ''; ?>>German</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="currency_symbol" class="form-label">Currency Symbol</label>
                                            <input type="text" class="form-control" id="currency_symbol" name="currency_symbol"
                                                   value="<?php echo htmlspecialchars($current_settings['currency_symbol'] ?? '$'); ?>" maxlength="5">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="currency_code" class="form-label">Currency Code</label>
                                            <input type="text" class="form-control" id="currency_code" name="currency_code"
                                                   value="<?php echo htmlspecialchars($current_settings['currency_code'] ?? 'USD'); ?>" maxlength="3">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="items_per_page" class="form-label">Items Per Page</label>
                                            <select class="form-select" id="items_per_page" name="items_per_page">
                                                <option value="10" <?php echo ($current_settings['items_per_page'] ?? '') === '10' ? 'selected' : ''; ?>>10</option>
                                                <option value="25" <?php echo ($current_settings['items_per_page'] ?? '25') === '25' ? 'selected' : ''; ?>>25</option>
                                                <option value="50" <?php echo ($current_settings['items_per_page'] ?? '') === '50' ? 'selected' : ''; ?>>50</option>
                                                <option value="100" <?php echo ($current_settings['items_per_page'] ?? '') === '100' ? 'selected' : ''; ?>>100</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="session_timeout" class="form-label">Session Timeout (seconds)</label>
                                            <input type="number" class="form-control" id="session_timeout" name="session_timeout"
                                                   value="<?php echo htmlspecialchars($current_settings['session_timeout'] ?? '3600'); ?>"
                                                   min="300" max="86400">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="max_upload_size" class="form-label">Max Upload Size (MB)</label>
                                            <input type="number" class="form-control" id="max_upload_size" name="max_upload_size"
                                                   value="<?php echo htmlspecialchars($current_settings['max_upload_size'] ?? '10'); ?>"
                                                   min="1" max="100">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="backup_retention_days" class="form-label">Backup Retention (days)</label>
                                            <input type="number" class="form-control" id="backup_retention_days" name="backup_retention_days"
                                                   value="<?php echo htmlspecialchars($current_settings['backup_retention_days'] ?? '30'); ?>"
                                                   min="1" max="365">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="enable_dark_mode" name="enable_dark_mode"
                                           <?php echo ($current_settings['enable_dark_mode'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="enable_dark_mode">
                                        Enable Dark Mode Support
                                    </label>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="enable_theme_switcher" name="enable_theme_switcher"
                                           <?php echo ($current_settings['enable_theme_switcher'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="enable_theme_switcher">
                                        Enable Theme Switcher for Users
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-info-circle"></i> System Info</h5>
                            </div>
                            <div class="card-body">
                                <h6><i class="bi bi-server"></i> Current Settings</h6>
                                <ul class="small text-muted">
                                    <li><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></li>
                                    <li><strong>Server Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
                                    <li><strong>Upload Limit:</strong> <?php echo ini_get('upload_max_filesize'); ?></li>
                                    <li><strong>Memory Limit:</strong> <?php echo ini_get('memory_limit'); ?></li>
                                </ul>

                                <hr>

                                <h6><i class="bi bi-lightbulb"></i> Tips</h6>
                                <ul class="small text-muted">
                                    <li>Session timeout affects admin login duration</li>
                                    <li>Upload size limit affects file attachments</li>
                                    <li>Backup retention helps manage storage</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-lg"></i> Save System Settings
                    </button>
                    <button type="reset" class="btn btn-outline-secondary ms-2">
                        <i class="bi bi-arrow-clockwise"></i> Reset
                    </button>
                </div>
            </form>
        </div>

        <!-- Email Settings -->
        <div class="tab-pane fade" id="email" role="tabpanel">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="email_settings" value="1">

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-envelope"></i> SMTP Configuration</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label for="smtp_host" class="form-label">SMTP Host</label>
                                            <input type="text" class="form-control" id="smtp_host" name="smtp_host"
                                                   value="<?php echo htmlspecialchars($current_settings['smtp_host'] ?? ''); ?>"
                                                   placeholder="smtp.gmail.com">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="smtp_port" class="form-label">SMTP Port</label>
                                            <input type="number" class="form-control" id="smtp_port" name="smtp_port"
                                                   value="<?php echo htmlspecialchars($current_settings['smtp_port'] ?? '587'); ?>"
                                                   placeholder="587">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="smtp_username" class="form-label">SMTP Username</label>
                                            <input type="text" class="form-control" id="smtp_username" name="smtp_username"
                                                   value="<?php echo htmlspecialchars($current_settings['smtp_username'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="smtp_password" class="form-label">SMTP Password</label>
                                            <input type="password" class="form-control" id="smtp_password" name="smtp_password"
                                                   value="<?php echo htmlspecialchars($current_settings['smtp_password'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="smtp_encryption" class="form-label">Encryption</label>
                                            <select class="form-select" id="smtp_encryption" name="smtp_encryption">
                                                <option value="tls" <?php echo ($current_settings['smtp_encryption'] ?? 'tls') === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                                <option value="ssl" <?php echo ($current_settings['smtp_encryption'] ?? '') === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                                <option value="none" <?php echo ($current_settings['smtp_encryption'] ?? '') === 'none' ? 'selected' : ''; ?>>None</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check mt-4">
                                            <input class="form-check-input" type="checkbox" id="enable_email_queue" name="enable_email_queue"
                                                   <?php echo ($current_settings['enable_email_queue'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="enable_email_queue">
                                                Enable Email Queue
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-person-lines-fill"></i> Email Identity</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="from_email" class="form-label">From Email</label>
                                            <input type="email" class="form-control" id="from_email" name="from_email"
                                                   value="<?php echo htmlspecialchars($current_settings['from_email'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="from_name" class="form-label">From Name</label>
                                            <input type="text" class="form-control" id="from_name" name="from_name"
                                                   value="<?php echo htmlspecialchars($current_settings['from_name'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="reply_to_email" class="form-label">Reply-To Email</label>
                                    <input type="email" class="form-control" id="reply_to_email" name="reply_to_email"
                                           value="<?php echo htmlspecialchars($current_settings['reply_to_email'] ?? ''); ?>">
                                </div>

                                <div class="form-group">
                                    <label for="email_signature" class="form-label">Email Signature</label>
                                    <textarea class="form-control" id="email_signature" name="email_signature" rows="4"><?php echo htmlspecialchars($current_settings['email_signature'] ?? ''); ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Email Setup Help</h5>
                            </div>
                            <div class="card-body">
                                <h6><i class="bi bi-envelope-check"></i> Common SMTP Settings</h6>
                                <div class="small text-muted mb-3">
                                    <strong>Gmail:</strong><br>
                                    Host: smtp.gmail.com<br>
                                    Port: 587<br>
                                    Encryption: TLS
                                </div>

                                <div class="small text-muted mb-3">
                                    <strong>Outlook:</strong><br>
                                    Host: smtp-mail.outlook.com<br>
                                    Port: 587<br>
                                    Encryption: TLS
                                </div>

                                <hr>

                                <h6><i class="bi bi-shield-check"></i> Security Note</h6>
                                <p class="small text-muted">
                                    Use app-specific passwords for Gmail and enable 2FA for better security.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-lg"></i> Save Email Settings
                    </button>
                    <button type="reset" class="btn btn-outline-secondary ms-2">
                        <i class="bi bi-arrow-clockwise"></i> Reset
                    </button>
                </div>
            </form>
        </div>

        <!-- Social Media Settings -->
        <div class="tab-pane fade" id="social" role="tabpanel">
            <form method="POST" class="needs-validation" novalidate>
                <input type="hidden" name="social_settings" value="1">

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-share"></i> Social Media Links</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="facebook_url" class="form-label">
                                                <i class="bi bi-facebook"></i> Facebook URL
                                            </label>
                                            <input type="url" class="form-control" id="facebook_url" name="facebook_url"
                                                   value="<?php echo htmlspecialchars($current_settings['facebook_url'] ?? ''); ?>"
                                                   placeholder="https://facebook.com/yourpage">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="twitter_url" class="form-label">
                                                <i class="bi bi-twitter"></i> Twitter URL
                                            </label>
                                            <input type="url" class="form-control" id="twitter_url" name="twitter_url"
                                                   value="<?php echo htmlspecialchars($current_settings['twitter_url'] ?? ''); ?>"
                                                   placeholder="https://twitter.com/youraccount">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="instagram_url" class="form-label">
                                                <i class="bi bi-instagram"></i> Instagram URL
                                            </label>
                                            <input type="url" class="form-control" id="instagram_url" name="instagram_url"
                                                   value="<?php echo htmlspecialchars($current_settings['instagram_url'] ?? ''); ?>"
                                                   placeholder="https://instagram.com/youraccount">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="youtube_url" class="form-label">
                                                <i class="bi bi-youtube"></i> YouTube URL
                                            </label>
                                            <input type="url" class="form-control" id="youtube_url" name="youtube_url"
                                                   value="<?php echo htmlspecialchars($current_settings['youtube_url'] ?? ''); ?>"
                                                   placeholder="https://youtube.com/yourchannel">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="linkedin_url" class="form-label">
                                                <i class="bi bi-linkedin"></i> LinkedIn URL
                                            </label>
                                            <input type="url" class="form-control" id="linkedin_url" name="linkedin_url"
                                                   value="<?php echo htmlspecialchars($current_settings['linkedin_url'] ?? ''); ?>"
                                                   placeholder="https://linkedin.com/company/yourcompany">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="tiktok_url" class="form-label">
                                                <i class="bi bi-tiktok"></i> TikTok URL
                                            </label>
                                            <input type="url" class="form-control" id="tiktok_url" name="tiktok_url"
                                                   value="<?php echo htmlspecialchars($current_settings['tiktok_url'] ?? ''); ?>"
                                                   placeholder="https://tiktok.com/@youraccount">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Social Media Usage</h5>
                            </div>
                            <div class="card-body">
                                <h6><i class="bi bi-eye"></i> Where Links Appear</h6>
                                <ul class="small text-muted">
                                    <li>Website footer</li>
                                    <li>Contact pages</li>
                                    <li>Email signatures</li>
                                    <li>Event invitations</li>
                                    <li>Member communications</li>
                                </ul>

                                <hr>

                                <h6><i class="bi bi-lightbulb"></i> Tips</h6>
                                <ul class="small text-muted">
                                    <li>Use full URLs including https://</li>
                                    <li>Test links after saving</li>
                                    <li>Keep profiles updated and active</li>
                                    <li>Consider your organization's social media policy</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-lg"></i> Save Social Media Settings
                    </button>
                    <button type="reset" class="btn btn-outline-secondary ms-2">
                        <i class="bi bi-arrow-clockwise"></i> Reset
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Tab switching functionality
document.addEventListener('DOMContentLoaded', function() {
    // Handle tab switching
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
    tabButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all tabs and content
            document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('show', 'active');
            });

            // Add active class to clicked tab
            this.classList.add('active');

            // Show corresponding content
            const targetId = this.getAttribute('data-bs-target');
            const targetPane = document.querySelector(targetId);
            if (targetPane) {
                targetPane.classList.add('show', 'active');
            }
        });
    });

    // Color preview updates
    window.updateColorPreview = function(colorInputId) {
        const colorInput = document.getElementById(colorInputId);
        const textInput = colorInput.nextElementSibling;
        if (textInput) {
            textInput.value = colorInput.value;
        }

        // Update CSS custom properties for live preview
        document.documentElement.style.setProperty('--bs-primary', colorInput.value);
    };

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
