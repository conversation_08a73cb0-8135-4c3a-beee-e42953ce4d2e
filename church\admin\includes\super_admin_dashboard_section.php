<?php
/**
 * Super Admin Dashboard Section
 * Integrated into main dashboard for super admin users
 */

// Only show for super admin users
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'super_admin') {
    return;
}

// Include RBAC system
require_once 'rbac_system.php';
$rbac = new RBACSystem($pdo);

// Get comprehensive system statistics
$system_stats = [];

try {
    // Total events and sessions
    $stmt = $pdo->query("SELECT COUNT(*) FROM events WHERE is_active = 1");
    $system_stats['total_events'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM event_sessions WHERE status = 'active'");
    $system_stats['total_sessions'] = $stmt->fetchColumn();
    
    // Total users and role assignments
    $stmt = $pdo->query("SELECT COUNT(*) FROM admins");
    $system_stats['total_users'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM user_role_assignments WHERE is_active = 1");
    $system_stats['active_role_assignments'] = $stmt->fetchColumn();
    
    // Recent dashboard access
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM dashboard_access_log
        WHERE access_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $stmt->execute();
    $system_stats['recent_dashboard_access'] = $stmt->fetchColumn();
    
    // Event and session statistics
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_rsvps,
            COUNT(CASE WHEN actually_attended = 1 THEN 1 END) as total_attended,
            COUNT(CASE WHEN actually_attended = 0 THEN 1 END) as total_no_show
        FROM (
            SELECT actually_attended FROM event_rsvps WHERE status = 'attending'
            UNION ALL
            SELECT actually_attended FROM event_rsvps_guests WHERE status = 'attending'
        ) combined_rsvps
    ");
    $attendance_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $system_stats = [
        'total_events' => 0,
        'total_sessions' => 0,
        'total_users' => 0,
        'active_role_assignments' => 0,
        'recent_dashboard_access' => 0
    ];
    $attendance_stats = [
        'total_rsvps' => 0,
        'total_attended' => 0,
        'total_no_show' => 0
    ];
}
?>

<!-- Super Admin Dashboard Section -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-shield-check"></i> Super Admin System Overview
                </h5>
            </div>
            <div class="card-body">
                <!-- System Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="bi bi-calendar-event text-primary" style="font-size: 2rem;"></i>
                                <h4 class="mt-2"><?php echo number_format($system_stats['total_events']); ?></h4>
                                <p class="text-muted mb-0">Active Events</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="bi bi-clock text-success" style="font-size: 2rem;"></i>
                                <h4 class="mt-2"><?php echo number_format($system_stats['total_sessions']); ?></h4>
                                <p class="text-muted mb-0">Active Sessions</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="bi bi-people text-info" style="font-size: 2rem;"></i>
                                <h4 class="mt-2"><?php echo number_format($system_stats['total_users']); ?></h4>
                                <p class="text-muted mb-0">Total Users</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <i class="bi bi-shield-check text-warning" style="font-size: 2rem;"></i>
                                <h4 class="mt-2"><?php echo number_format($system_stats['active_role_assignments']); ?></h4>
                                <p class="text-muted mb-0">Role Assignments</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Attendance Statistics -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h4><?php echo number_format($attendance_stats['total_rsvps'] ?? 0); ?></h4>
                                <p class="mb-0">Total RSVPs</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h4><?php echo number_format($attendance_stats['total_attended'] ?? 0); ?></h4>
                                <p class="mb-0">Attended</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <h4><?php echo number_format($attendance_stats['total_no_show'] ?? 0); ?></h4>
                                <p class="mb-0">No Shows</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Admin Actions -->
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="bi bi-lightning"></i> Quick Admin Actions</h6>
                        <div class="list-group">
                            <a href="<?php echo admin_url_for('setup_rbac_system.php'); ?>" class="list-group-item list-group-item-action">
                                <i class="bi bi-shield-check"></i> RBAC Management
                            </a>
                            <a href="<?php echo admin_url_for('create_admin_users.php'); ?>" class="list-group-item list-group-item-action">
                                <i class="bi bi-person-plus"></i> Create Admin Users
                            </a>
                            <a href="<?php echo admin_url_for('system_test_dashboard.php'); ?>" class="list-group-item list-group-item-action">
                                <i class="bi bi-bug"></i> System Testing
                            </a>
                            <a href="<?php echo admin_url_for('database_maintenance.php'); ?>" class="list-group-item list-group-item-action">
                                <i class="bi bi-database"></i> Database Maintenance
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="bi bi-robot"></i> Universal Platform</h6>
                        <div class="list-group">
                            <a href="<?php echo admin_url_for('universal_ai_dashboard.php'); ?>" class="list-group-item list-group-item-action">
                                <i class="bi bi-robot"></i> AI Predictions
                            </a>
                            <a href="<?php echo admin_url_for('universal_analytics_dashboard.php'); ?>" class="list-group-item list-group-item-action">
                                <i class="bi bi-graph-up"></i> Universal Analytics
                            </a>
                            <a href="<?php echo admin_url_for('universal_organization_setup.php'); ?>" class="list-group-item list-group-item-action">
                                <i class="bi bi-gear"></i> Organization Setup
                            </a>
                            <a href="<?php echo admin_url_for('pwa/'); ?>" class="list-group-item list-group-item-action" target="_blank">
                                <i class="bi bi-phone"></i> Mobile PWA
                            </a>
                        </div>
                    </div>
                </div>

                <!-- System Health -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h6><i class="bi bi-activity"></i> System Health</h6>
                        <div class="alert alert-info">
                            <strong>Dashboard Access (24h):</strong> <?php echo number_format($system_stats['recent_dashboard_access']); ?> visits
                            <br>
                            <strong>System Status:</strong> 
                            <?php if ($system_stats['total_events'] > 0): ?>
                                <span class="badge bg-success">Active</span>
                            <?php else: ?>
                                <span class="badge bg-warning">No Active Events</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
