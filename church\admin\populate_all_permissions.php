<?php
// Populate comprehensive granular permissions
require_once '../config.php';

try {
    echo "Populating comprehensive granular permissions...\n\n";
    
    // Get category IDs
    $stmt = $pdo->query("SELECT category_name, id FROM permission_categories ORDER BY sort_order");
    $categories = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
    echo "Found categories:\n";
    foreach ($categories as $id => $name) {
        echo "- {$id}: {$name}\n";
    }
    echo "\n";
    
    // Comprehensive permissions for all categories
    $all_permissions = [
        // Dashboard Access (category_id will be resolved)
        ['dashboard_access', 'dashboard.view', 'View Dashboard', 'Access to main dashboard', 'dashboard.php', 1],
        ['dashboard_access', 'dashboard.analytics', 'View Analytics', 'Access to analytics and reports', 'dashboard.php', 2],
        
        // Member Management
        ['member_management', 'members.view', 'View Members', 'View member list and profiles', 'members.php', 1],
        ['member_management', 'members.add', 'Add Members', 'Add new members to the system', 'add_member.php', 2],
        ['member_management', 'members.edit', 'Edit Members', 'Edit existing member information', 'edit_member.php', 3],
        ['member_management', 'members.delete', 'Delete Members', 'Delete members from the system', 'members.php', 4],
        ['member_management', 'members.view_profile', 'View Member Profiles', 'View detailed member profiles', 'member_profile.php', 5],
        ['member_management', 'members.family_management', 'Family Management', 'Manage family relationships', 'family_management.php', 6],
        ['member_management', 'members.skills', 'Member Skills', 'Manage member skills and talents', 'skills.php', 7],
        ['member_management', 'members.requests', 'Prayer Requests', 'Manage prayer requests', 'requests.php', 8],
        ['member_management', 'members.volunteers', 'Volunteer Management', 'Manage volunteer opportunities', 'volunteer_opportunities.php', 9],
        
        // Event Management
        ['event_management', 'events.view', 'View Events', 'View event list and details', 'events.php', 1],
        ['event_management', 'events.create', 'Create Events', 'Create new events', 'add_event.php', 2],
        ['event_management', 'events.edit', 'Edit Events', 'Edit existing events', 'edit_event.php', 3],
        ['event_management', 'events.delete', 'Delete Events', 'Delete events from the system', 'events.php', 4],
        ['event_management', 'events.attendance', 'Event Attendance', 'Manage event attendance', 'event_attendance.php', 5],
        ['event_management', 'events.sessions', 'Event Sessions', 'Manage event sessions', 'event_sessions.php', 6],
        ['event_management', 'events.categories', 'Event Categories', 'Manage event categories', 'event_categories.php', 7],
        ['event_management', 'events.reports', 'Event Reports', 'View event reports and analytics', 'event_reports.php', 8],
        
        // Email Management
        ['email_management', 'email.bulk_send', 'Bulk Email', 'Send bulk emails to members', 'bulk_email.php', 1],
        ['email_management', 'email.scheduler', 'Email Scheduler', 'Schedule emails for later sending', 'email_scheduler.php', 2],
        ['email_management', 'email.templates', 'Email Templates', 'Manage email templates', 'email_templates.php', 3],
        ['email_management', 'email.contacts', 'Email Contacts', 'Manage email contacts', 'contacts.php', 4],
        ['email_management', 'email.contact_groups', 'Contact Groups', 'Manage contact groups', 'contact_groups.php', 5],
        ['email_management', 'email.birthday', 'Birthday Messages', 'Manage birthday email messages', 'birthday.php', 6],
        ['email_management', 'email.birthday_send', 'Send Birthday Emails', 'Send bulk birthday emails', 'send_birthday_emails.php', 7],
        ['email_management', 'email.birthday_test', 'Test Birthday Emails', 'Test birthday email functionality', 'test_birthday_email.php', 8],
        ['email_management', 'email.birthday_notifications', 'Birthday Notifications', 'Send birthday notifications', 'send_birthday_notification.php', 9],
        ['email_management', 'email.automated_templates', 'Automated Templates', 'Manage automated email templates', 'automated_templates.php', 10],
        ['email_management', 'email.whatsapp', 'WhatsApp Messages', 'Send WhatsApp messages', 'whatsapp_messages.php', 11],
        ['email_management', 'email.analytics', 'Email Analytics', 'View email analytics and reports', 'email_analytics.php', 12],
        
        // SMS Management
        ['sms_management', 'sms.single_send', 'Single SMS', 'Send individual SMS messages', 'single_sms.php', 1],
        ['sms_management', 'sms.bulk_send', 'Bulk SMS', 'Send bulk SMS messages', 'bulk_sms.php', 2],
        ['sms_management', 'sms.templates', 'SMS Templates', 'Manage SMS templates', 'sms_templates.php', 3],
        ['sms_management', 'sms.analytics', 'SMS Analytics', 'View SMS analytics and reports', 'sms_analytics.php', 4],
        
        // Donations & Finance
        ['donations_finance', 'donations.view', 'View Donations', 'View donation records', 'donations.php', 1],
        ['donations_finance', 'donations.manage', 'Manage Donations', 'Manage donation records', 'donations.php', 2],
        ['donations_finance', 'donations.gifts', 'Gift Management', 'Manage gifts and gift records', 'gift_management.php', 3],
        ['donations_finance', 'donations.enhanced', 'Enhanced Donations', 'Access enhanced donation features', 'enhanced_donate.php', 4],
        ['donations_finance', 'donations.payment_integration', 'Payment Integration', 'Manage payment integrations', 'payment_integration.php', 5],
        ['donations_finance', 'donations.payment_tables', 'Payment Tables', 'Manage payment table configurations', 'payment_tables.php', 6],
        
        // Integrations
        ['integrations', 'integrations.calendar', 'Calendar Integration', 'Manage calendar integrations', 'calendar_integration.php', 1],
        ['integrations', 'integrations.social_media', 'Social Media', 'Manage social media integrations', 'social_media_integration.php', 2],
        
        // System Settings
        ['system_settings', 'settings.general', 'General Settings', 'Manage general system settings', 'settings.php', 1],
        ['system_settings', 'settings.appearance', 'Appearance Settings', 'Manage appearance and theme settings', 'appearance_settings.php', 2],
        ['system_settings', 'settings.branding', 'Branding Settings', 'Manage branding and logo settings', 'branding_settings.php', 3],
        ['system_settings', 'settings.logo', 'Logo Management', 'Manage logos and branding assets', 'logo_management_consolidated.php', 4],
        ['system_settings', 'settings.custom_fields', 'Custom Fields', 'Manage custom fields', 'custom_fields.php', 5],
        ['system_settings', 'settings.security_audit', 'Security Audit', 'View security audit logs', 'security_audit.php', 6],
        ['system_settings', 'settings.security_settings', 'Security Settings', 'Manage security settings', 'security_settings.php', 7],
        ['system_settings', 'settings.backup', 'Database Backup', 'Manage database backups', 'backup_management.php', 8],
        ['system_settings', 'settings.profile', 'My Profile', 'Manage personal profile', 'profile.php', 9],
        
        // Reports & Analytics
        ['reports_analytics', 'reports.member_reports', 'Member Reports', 'View member reports and statistics', 'member_reports.php', 1],
        ['reports_analytics', 'reports.event_reports', 'Event Reports', 'View event reports and analytics', 'event_reports.php', 2],
        ['reports_analytics', 'reports.email_analytics', 'Email Analytics', 'View email analytics and reports', 'email_analytics.php', 3],
        ['reports_analytics', 'reports.sms_analytics', 'SMS Analytics', 'View SMS analytics and reports', 'sms_analytics.php', 4],
        ['reports_analytics', 'reports.donation_reports', 'Donation Reports', 'View donation reports and analytics', 'donation_reports.php', 5],
        
        // Notifications
        ['notifications', 'notifications.view', 'View Notifications', 'View system notifications', 'notifications.php', 1],
        ['notifications', 'notifications.manage', 'Manage Notifications', 'Manage notification settings', 'notifications.php', 2],
        
        // Admin Management
        ['admin_management', 'admin.rbac_management', 'RBAC Management', 'Manage role-based access control', 'setup_rbac_system.php', 1],
        ['admin_management', 'admin.create_users', 'Create Admin Users', 'Create new admin users', 'create_admin_users.php', 2],
        ['admin_management', 'admin.super_dashboard', 'Super Admin Dashboard', 'Access super admin dashboard', 'super_admin_dashboard.php', 3],
        ['admin_management', 'admin.permission_management', 'Permission Management', 'Manage individual user permissions', 'manage_user_permissions.php', 4],
        ['admin_management', 'admin.granular_setup', 'Granular System Setup', 'Set up granular permission system', 'setup_granular_permissions_system.php', 5]
    ];
    
    // Insert permissions
    $inserted = 0;
    $skipped = 0;
    
    foreach ($all_permissions as $perm) {
        $category_id = $categories[$perm[0]] ?? null;
        if ($category_id) {
            // Check if permission already exists
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM granular_permissions WHERE permission_key = ?");
            $stmt->execute([$perm[1]]);
            
            if ($stmt->fetchColumn() == 0) {
                // Insert new permission
                $stmt = $pdo->prepare("INSERT INTO granular_permissions (category_id, permission_key, permission_name, permission_description, page_file, sort_order, is_active) VALUES (?, ?, ?, ?, ?, ?, 1)");
                $result = $stmt->execute([$category_id, $perm[1], $perm[2], $perm[3], $perm[4], $perm[5]]);
                if ($result) {
                    $inserted++;
                    echo "✓ Added: {$perm[2]}\n";
                }
            } else {
                $skipped++;
                echo "- Skipped (exists): {$perm[2]}\n";
            }
        } else {
            echo "❌ Category not found: {$perm[0]}\n";
        }
    }
    
    echo "\n📊 Summary:\n";
    echo "- Permissions added: {$inserted}\n";
    echo "- Permissions skipped: {$skipped}\n";
    
    // Check final counts
    $stmt = $pdo->query("
        SELECT pc.category_display_name, COUNT(gp.id) as permission_count
        FROM permission_categories pc
        LEFT JOIN granular_permissions gp ON pc.id = gp.category_id AND gp.is_active = 1
        GROUP BY pc.id, pc.category_display_name
        ORDER BY pc.sort_order
    ");
    $category_counts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\n📋 Permissions per category:\n";
    foreach ($category_counts as $cat) {
        echo "- {$cat['category_display_name']}: {$cat['permission_count']} permissions\n";
    }
    
    echo "\n🎉 Comprehensive permissions populated successfully!\n";
    echo "The permission management interface should now show all permissions.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
}
?>
