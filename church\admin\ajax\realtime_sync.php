<?php
session_start();

// Include the configuration file
require_once '../../config.php';

header('Content-Type: application/json');

$response = ['success' => false, 'message' => '', 'data' => []];

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests allowed');
    }
    
    $action = $_POST['action'] ?? '';
    
    if ($action === 'sync_attendance_status') {
        // Get current attendance status for a session
        $session_id = $_POST['session_id'] ?? '';
        $last_sync = $_POST['last_sync'] ?? '';
        
        if (empty($session_id)) {
            throw new Exception('Session ID is required');
        }
        
        // Get attendance records updated since last sync
        $where_clause = '';
        $params = [$session_id];
        
        if (!empty($last_sync)) {
            $where_clause = 'AND (sa.updated_at > ? OR sa.attendance_date > ?)';
            $params[] = $last_sync;
            $params[] = $last_sync;
        }
        
        $stmt = $pdo->prepare("
            SELECT 
                sa.id,
                sa.session_id,
                sa.member_id,
                sa.guest_name,
                sa.guest_email,
                sa.attendance_status,
                sa.attendance_date,
                sa.updated_at,
                CASE 
                    WHEN sa.member_id IS NOT NULL THEN m.full_name
                    ELSE sa.guest_name
                END as attendee_name,
                CASE 
                    WHEN sa.member_id IS NOT NULL THEN CONCAT('member_', sa.member_id)
                    ELSE CONCAT('guest_', sa.guest_name, '_', sa.guest_email)
                END as attendee_id
            FROM session_attendance sa
            LEFT JOIN members m ON sa.member_id = m.id
            WHERE sa.session_id = ? {$where_clause}
            ORDER BY sa.updated_at DESC
        ");
        $stmt->execute($params);
        $updates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $response['success'] = true;
        $response['data'] = [
            'updates' => $updates,
            'sync_time' => date('Y-m-d H:i:s'),
            'update_count' => count($updates)
        ];
        
    } elseif ($action === 'check_attendance_conflict') {
        // Check for simultaneous session conflicts
        $member_id = $_POST['member_id'] ?? '';
        $session_id = $_POST['session_id'] ?? '';
        $check_time = $_POST['check_time'] ?? date('Y-m-d H:i:s');
        
        if (empty($member_id) || empty($session_id)) {
            throw new Exception('Member ID and Session ID are required');
        }
        
        // Get session time details
        $stmt = $pdo->prepare("
            SELECT start_datetime, end_datetime 
            FROM event_sessions 
            WHERE id = ?
        ");
        $stmt->execute([$session_id]);
        $current_session = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$current_session) {
            throw new Exception('Session not found');
        }
        
        // Check for conflicting sessions where member is marked as attended
        $stmt = $pdo->prepare("
            SELECT 
                es.id,
                es.session_title,
                es.start_datetime,
                es.end_datetime,
                sa.attendance_date
            FROM session_attendance sa
            JOIN event_sessions es ON sa.session_id = es.id
            WHERE sa.member_id = ? 
            AND sa.attendance_status = 'attended'
            AND es.id != ?
            AND (
                (es.start_datetime <= ? AND es.end_datetime >= ?) OR
                (es.start_datetime <= ? AND es.end_datetime >= ?)
            )
        ");
        $stmt->execute([
            $member_id, 
            $session_id,
            $current_session['start_datetime'], $current_session['start_datetime'],
            $current_session['end_datetime'], $current_session['end_datetime']
        ]);
        $conflicts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $response['success'] = true;
        $response['data'] = [
            'has_conflict' => !empty($conflicts),
            'conflicts' => $conflicts,
            'current_session' => $current_session
        ];
        
    } elseif ($action === 'lock_attendance_record') {
        // Lock an attendance record for editing
        $attendance_id = $_POST['attendance_id'] ?? '';
        $user_id = $_SESSION['admin_id'] ?? '';
        
        if (empty($attendance_id) || empty($user_id)) {
            throw new Exception('Attendance ID and User ID are required');
        }
        
        // Create attendance_locks table if it doesn't exist
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS attendance_locks (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                attendance_id INT(11) NOT NULL,
                locked_by INT(11) NOT NULL,
                locked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                INDEX idx_attendance_id (attendance_id),
                INDEX idx_expires_at (expires_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ");
        
        // Clean up expired locks
        $pdo->exec("DELETE FROM attendance_locks WHERE expires_at < NOW()");
        
        // Check if record is already locked
        $stmt = $pdo->prepare("
            SELECT locked_by, locked_at, expires_at 
            FROM attendance_locks 
            WHERE attendance_id = ? AND expires_at > NOW()
        ");
        $stmt->execute([$attendance_id]);
        $existing_lock = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existing_lock && $existing_lock['locked_by'] != $user_id) {
            throw new Exception('Record is currently being edited by another user');
        }
        
        // Create or update lock
        $expires_at = date('Y-m-d H:i:s', strtotime('+5 minutes'));
        $stmt = $pdo->prepare("
            INSERT INTO attendance_locks (attendance_id, locked_by, expires_at)
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE
            locked_by = VALUES(locked_by),
            locked_at = NOW(),
            expires_at = VALUES(expires_at)
        ");
        $stmt->execute([$attendance_id, $user_id, $expires_at]);
        
        $response['success'] = true;
        $response['data'] = [
            'locked' => true,
            'expires_at' => $expires_at
        ];
        
    } elseif ($action === 'unlock_attendance_record') {
        // Unlock an attendance record
        $attendance_id = $_POST['attendance_id'] ?? '';
        $user_id = $_SESSION['admin_id'] ?? '';
        
        if (empty($attendance_id) || empty($user_id)) {
            throw new Exception('Attendance ID and User ID are required');
        }
        
        $stmt = $pdo->prepare("
            DELETE FROM attendance_locks 
            WHERE attendance_id = ? AND locked_by = ?
        ");
        $stmt->execute([$attendance_id, $user_id]);
        
        $response['success'] = true;
        $response['data'] = ['unlocked' => true];
        
    } elseif ($action === 'get_session_stats') {
        // Get real-time session statistics
        $session_id = $_POST['session_id'] ?? '';
        
        if (empty($session_id)) {
            throw new Exception('Session ID is required');
        }
        
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_registered,
                COUNT(CASE WHEN attendance_status = 'attended' THEN 1 END) as total_attended,
                COUNT(CASE WHEN attendance_status = 'no_show' THEN 1 END) as total_no_show,
                COUNT(CASE WHEN attendance_status = 'registered' THEN 1 END) as total_pending,
                MAX(attendance_date) as last_checkin,
                MAX(updated_at) as last_update
            FROM session_attendance 
            WHERE session_id = ?
        ");
        $stmt->execute([$session_id]);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Calculate attendance rate
        $stats['attendance_rate'] = $stats['total_registered'] > 0 
            ? round(($stats['total_attended'] / $stats['total_registered']) * 100, 1) 
            : 0;
        
        $response['success'] = true;
        $response['data'] = $stats;
        
    } elseif ($action === 'batch_update_attendance') {
        // Batch update multiple attendance records with conflict checking
        $updates = $_POST['updates'] ?? [];
        $session_id = $_POST['session_id'] ?? '';
        
        if (empty($updates) || empty($session_id)) {
            throw new Exception('Updates and Session ID are required');
        }
        
        $pdo->beginTransaction();
        
        $success_count = 0;
        $conflict_count = 0;
        $conflicts = [];
        
        foreach ($updates as $update) {
            $attendee_id = $update['attendee_id'] ?? '';
            $status = $update['status'] ?? '';
            
            if (empty($attendee_id) || empty($status)) {
                continue;
            }
            
            try {
                // Check for conflicts if marking as attended
                if ($status === 'attended' && strpos($attendee_id, 'member_') === 0) {
                    $member_id = substr($attendee_id, 7);
                    
                    // Use the conflict check logic
                    $conflict_check = $_POST;
                    $conflict_check['action'] = 'check_attendance_conflict';
                    $conflict_check['member_id'] = $member_id;
                    $conflict_check['session_id'] = $session_id;
                    
                    // Simulate conflict check (simplified for batch processing)
                    $stmt = $pdo->prepare("
                        SELECT COUNT(*) as conflict_count
                        FROM session_attendance sa
                        JOIN event_sessions es ON sa.session_id = es.id
                        JOIN event_sessions current_es ON current_es.id = ?
                        WHERE sa.member_id = ? 
                        AND sa.attendance_status = 'attended'
                        AND es.id != ?
                        AND (
                            (es.start_datetime <= current_es.end_datetime AND es.end_datetime >= current_es.start_datetime)
                        )
                    ");
                    $stmt->execute([$session_id, $member_id, $session_id]);
                    $conflict_result = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($conflict_result['conflict_count'] > 0) {
                        $conflicts[] = $attendee_id;
                        $conflict_count++;
                        continue;
                    }
                }
                
                // Update attendance record
                if (strpos($attendee_id, 'member_') === 0) {
                    $member_id = substr($attendee_id, 7);
                    $stmt = $pdo->prepare("
                        UPDATE session_attendance 
                        SET attendance_status = ?, 
                            attendance_date = CASE WHEN ? = 'attended' THEN NOW() ELSE attendance_date END,
                            updated_at = NOW()
                        WHERE session_id = ? AND member_id = ?
                    ");
                    $stmt->execute([$status, $status, $session_id, $member_id]);
                } else {
                    // Handle guest format
                    $parts = explode('_', $attendee_id, 3);
                    if (count($parts) >= 3) {
                        $guest_name = $parts[1];
                        $guest_email = $parts[2];
                        $stmt = $pdo->prepare("
                            UPDATE session_attendance 
                            SET attendance_status = ?, 
                                attendance_date = CASE WHEN ? = 'attended' THEN NOW() ELSE attendance_date END,
                                updated_at = NOW()
                            WHERE session_id = ? AND guest_name = ? AND guest_email = ?
                        ");
                        $stmt->execute([$status, $status, $session_id, $guest_name, $guest_email]);
                    }
                }
                
                $success_count++;
                
            } catch (Exception $e) {
                // Log individual update errors but continue processing
                error_log("Batch update error for {$attendee_id}: " . $e->getMessage());
            }
        }
        
        $pdo->commit();
        
        $response['success'] = true;
        $response['data'] = [
            'success_count' => $success_count,
            'conflict_count' => $conflict_count,
            'conflicts' => $conflicts,
            'total_processed' => count($updates)
        ];
        
    } else {
        throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    $response['success'] = false;
    $response['message'] = $e->getMessage();
}

echo json_encode($response);
